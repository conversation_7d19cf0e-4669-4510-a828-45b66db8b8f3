package com.android.car.developeroptions.password;

import android.content.Intent;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Parcelable;
import android.os.SystemClock;
import android.os.UserManager;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AnimationUtils;
import android.view.animation.Interpolator;
import android.widget.TextView;
import androidx.fragment.app.FragmentTransaction;
import com.android.car.developeroptions.R;
import com.android.car.developeroptions.password.ConfirmDeviceCredentialBaseActivity;
import com.android.car.developeroptions.password.ConfirmLockPattern;
import com.android.car.developeroptions.password.CredentialCheckResultTracker;
import com.android.internal.widget.LockPatternChecker;
import com.android.internal.widget.LockPatternView;
import com.android.internal.widget.LockscreenCredential;
import com.android.settingslib.animation.AppearAnimationCreator;
import com.android.settingslib.animation.AppearAnimationUtils;
import com.android.settingslib.animation.DisappearAnimationUtils;
import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/* loaded from: classes.dex */
public class ConfirmLockPattern extends ConfirmDeviceCredentialBaseActivity {

    public static class InternalActivity extends ConfirmLockPattern {
    }

    private enum Stage {
        NeedToUnlock,
        NeedToUnlockWrong,
        LockedOut
    }

    @Override // com.android.car.developeroptions.SettingsActivity, android.app.Activity
    public Intent getIntent() {
        Intent intent = new Intent(super.getIntent());
        intent.putExtra(":settings:show_fragment", ConfirmLockPatternFragment.class.getName());
        return intent;
    }

    @Override // com.android.car.developeroptions.SettingsActivity
    protected boolean isValidFragment(String str) {
        return ConfirmLockPatternFragment.class.getName().equals(str);
    }

    public static class ConfirmLockPatternFragment extends ConfirmDeviceCredentialBaseFragment implements AppearAnimationCreator<Object>, CredentialCheckResultTracker.Listener {
        private AppearAnimationUtils mAppearAnimationUtils;
        private CountDownTimer mCountdownTimer;
        private CredentialCheckResultTracker mCredentialCheckResultTracker;
        private CharSequence mDetailsText;
        private TextView mDetailsTextView;
        private DisappearAnimationUtils mDisappearAnimationUtils;
        private CharSequence mHeaderText;
        private TextView mHeaderTextView;
        private LockPatternView mLockPatternView;
        private AsyncTask<?, ?, ?> mPendingLockCheck;
        private boolean mDisappearing = false;
        private Runnable mClearPatternRunnable = new Runnable() { // from class: com.android.car.developeroptions.password.ConfirmLockPattern.ConfirmLockPatternFragment.2
            @Override // java.lang.Runnable
            public void run() {
                ConfirmLockPatternFragment.this.mLockPatternView.clearPattern();
            }
        };
        private LockPatternView.OnPatternListener mConfirmExistingLockPatternListener = new LockPatternView.OnPatternListener() { // from class: com.android.car.developeroptions.password.ConfirmLockPattern.ConfirmLockPatternFragment.3
            public void onPatternCellAdded(List<LockPatternView.Cell> list) {
            }

            public void onPatternStart() {
                ConfirmLockPatternFragment.this.mLockPatternView.removeCallbacks(ConfirmLockPatternFragment.this.mClearPatternRunnable);
            }

            public void onPatternCleared() {
                ConfirmLockPatternFragment.this.mLockPatternView.removeCallbacks(ConfirmLockPatternFragment.this.mClearPatternRunnable);
            }

            public void onPatternDetected(List<LockPatternView.Cell> list) {
                if (ConfirmLockPatternFragment.this.mPendingLockCheck != null || ConfirmLockPatternFragment.this.mDisappearing) {
                    return;
                }
                ConfirmLockPatternFragment.this.mLockPatternView.setEnabled(false);
                boolean booleanExtra = ConfirmLockPatternFragment.this.getActivity().getIntent().getBooleanExtra("has_challenge", false);
                LockscreenCredential createPattern = LockscreenCredential.createPattern(list);
                Intent intent = new Intent();
                if (booleanExtra) {
                    if (!isInternalActivity()) {
                        ConfirmLockPatternFragment.this.mCredentialCheckResultTracker.setResult(false, intent, 0, ConfirmLockPatternFragment.this.mEffectiveUserId);
                        return;
                    } else {
                        startVerifyPattern(createPattern, intent);
                        return;
                    }
                }
                startCheckPattern(createPattern, intent);
            }

            /* JADX INFO: Access modifiers changed from: private */
            public boolean isInternalActivity() {
                return ConfirmLockPatternFragment.this.getActivity() instanceof InternalActivity;
            }

            private void startVerifyPattern(LockscreenCredential lockscreenCredential, final Intent intent) {
                AsyncTask verifyTiedProfileChallenge;
                ConfirmLockPatternFragment confirmLockPatternFragment = ConfirmLockPatternFragment.this;
                final int i = confirmLockPatternFragment.mEffectiveUserId;
                int i2 = confirmLockPatternFragment.mUserId;
                long longExtra = confirmLockPatternFragment.getActivity().getIntent().getLongExtra("challenge", 0L);
                LockPatternChecker.OnVerifyCallback onVerifyCallback = new LockPatternChecker.OnVerifyCallback() { // from class: com.android.car.developeroptions.password.ConfirmLockPattern.ConfirmLockPatternFragment.3.1
                    public void onVerified(byte[] bArr, int i3) {
                        boolean z;
                        ConfirmLockPatternFragment.this.mPendingLockCheck = null;
                        if (bArr != null) {
                            z = true;
                            if (ConfirmLockPatternFragment.this.mReturnCredentials) {
                                intent.putExtra("hw_auth_token", bArr);
                            }
                        } else {
                            z = false;
                        }
                        ConfirmLockPatternFragment.this.mCredentialCheckResultTracker.setResult(z, intent, i3, i);
                    }
                };
                ConfirmLockPatternFragment confirmLockPatternFragment2 = ConfirmLockPatternFragment.this;
                if (i == i2) {
                    verifyTiedProfileChallenge = LockPatternChecker.verifyCredential(confirmLockPatternFragment2.mLockPatternUtils, lockscreenCredential, longExtra, i2, onVerifyCallback);
                } else {
                    verifyTiedProfileChallenge = LockPatternChecker.verifyTiedProfileChallenge(confirmLockPatternFragment2.mLockPatternUtils, lockscreenCredential, longExtra, i2, onVerifyCallback);
                }
                confirmLockPatternFragment2.mPendingLockCheck = verifyTiedProfileChallenge;
            }

            private void startCheckPattern(final LockscreenCredential lockscreenCredential, final Intent intent) {
                if (lockscreenCredential.size() < 4) {
                    ConfirmLockPatternFragment confirmLockPatternFragment = ConfirmLockPatternFragment.this;
                    confirmLockPatternFragment.onPatternChecked(false, intent, 0, confirmLockPatternFragment.mEffectiveUserId, false);
                } else {
                    ConfirmLockPatternFragment confirmLockPatternFragment2 = ConfirmLockPatternFragment.this;
                    final int i = confirmLockPatternFragment2.mEffectiveUserId;
                    confirmLockPatternFragment2.mPendingLockCheck = LockPatternChecker.checkCredential(confirmLockPatternFragment2.mLockPatternUtils, lockscreenCredential, i, new LockPatternChecker.OnCheckCallback() { // from class: com.android.car.developeroptions.password.ConfirmLockPattern.ConfirmLockPatternFragment.3.2
                        public void onChecked(boolean z, int i2) {
                            ConfirmLockPatternFragment.this.mPendingLockCheck = null;
                            if (z && isInternalActivity() && ConfirmLockPatternFragment.this.mReturnCredentials) {
                                intent.putExtra("type", 2);
                                intent.putExtra("password", (Parcelable) lockscreenCredential);
                            }
                            ConfirmLockPatternFragment.this.mCredentialCheckResultTracker.setResult(z, intent, i2, i);
                        }
                    });
                }
            }
        };

        @Override // com.android.settingslib.core.instrumentation.Instrumentable
        public int getMetricsCategory() {
            return 31;
        }

        @Override // com.android.settingslib.core.lifecycle.ObservableFragment, androidx.fragment.app.Fragment
        public void onSaveInstanceState(Bundle bundle) {
        }

        @Override // com.android.car.developeroptions.password.ConfirmDeviceCredentialBaseFragment
        protected void onShowError() {
        }

        @Override // androidx.fragment.app.Fragment
        public View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
            View inflate = layoutInflater.inflate(((ConfirmLockPattern) getActivity()).getConfirmCredentialTheme() == ConfirmDeviceCredentialBaseActivity.ConfirmCredentialTheme.NORMAL ? R.layout.confirm_lock_pattern_normal : R.layout.confirm_lock_pattern, viewGroup, false);
            this.mHeaderTextView = (TextView) inflate.findViewById(R.id.headerText);
            this.mLockPatternView = inflate.findViewById(R.id.lockPattern);
            this.mDetailsTextView = (TextView) inflate.findViewById(R.id.detailsText);
            this.mErrorTextView = (TextView) inflate.findViewById(R.id.errorText);
            inflate.findViewById(R.id.leftSpacer);
            inflate.findViewById(R.id.rightSpacer);
            inflate.findViewById(R.id.topLayout).setDefaultTouchRecepient(this.mLockPatternView);
            Intent intent = getActivity().getIntent();
            if (intent != null) {
                this.mHeaderText = intent.getCharSequenceExtra("com.android.car.developeroptions.ConfirmCredentials.header");
                this.mDetailsText = intent.getCharSequenceExtra("com.android.car.developeroptions.ConfirmCredentials.details");
            }
            this.mLockPatternView.setTactileFeedbackEnabled(this.mLockPatternUtils.isTactileFeedbackEnabled());
            this.mLockPatternView.setInStealthMode(!this.mLockPatternUtils.isVisiblePatternEnabled(this.mEffectiveUserId));
            this.mLockPatternView.setOnPatternListener(this.mConfirmExistingLockPatternListener);
            updateStage(Stage.NeedToUnlock);
            if (bundle == null && !this.mFrp && !this.mLockPatternUtils.isLockPatternEnabled(this.mEffectiveUserId)) {
                getActivity().setResult(-1);
                getActivity().finish();
            }
            this.mAppearAnimationUtils = new AppearAnimationUtils(getContext(), 220L, 2.0f, 1.3f, AnimationUtils.loadInterpolator(getContext(), android.R.interpolator.linear_out_slow_in));
            this.mDisappearAnimationUtils = new DisappearAnimationUtils(getContext(), 125L, 4.0f, 0.3f, AnimationUtils.loadInterpolator(getContext(), android.R.interpolator.fast_out_linear_in), new AppearAnimationUtils.RowTranslationScaler(this) { // from class: com.android.car.developeroptions.password.ConfirmLockPattern.ConfirmLockPatternFragment.1
                @Override // com.android.settingslib.animation.AppearAnimationUtils.RowTranslationScaler
                public float getRowTranslationScale(int i, int i2) {
                    return (i2 - i) / i2;
                }
            });
            setAccessibilityTitle(this.mHeaderTextView.getText());
            CredentialCheckResultTracker credentialCheckResultTracker = (CredentialCheckResultTracker) getFragmentManager().findFragmentByTag("check_lock_result");
            this.mCredentialCheckResultTracker = credentialCheckResultTracker;
            if (credentialCheckResultTracker == null) {
                this.mCredentialCheckResultTracker = new CredentialCheckResultTracker();
                FragmentTransaction beginTransaction = getFragmentManager().beginTransaction();
                beginTransaction.add(this.mCredentialCheckResultTracker, "check_lock_result");
                beginTransaction.commit();
            }
            return inflate;
        }

        @Override // com.android.car.developeroptions.password.ConfirmDeviceCredentialBaseFragment, com.android.settingslib.core.lifecycle.ObservableFragment, androidx.fragment.app.Fragment
        public void onPause() {
            super.onPause();
            CountDownTimer countDownTimer = this.mCountdownTimer;
            if (countDownTimer != null) {
                countDownTimer.cancel();
            }
            this.mCredentialCheckResultTracker.setListener(null);
        }

        @Override // com.android.car.developeroptions.password.ConfirmDeviceCredentialBaseFragment, com.android.car.developeroptions.core.InstrumentedFragment, com.android.settingslib.core.lifecycle.ObservableFragment, androidx.fragment.app.Fragment
        public void onResume() {
            super.onResume();
            long lockoutAttemptDeadline = this.mLockPatternUtils.getLockoutAttemptDeadline(this.mEffectiveUserId);
            if (lockoutAttemptDeadline != 0) {
                this.mCredentialCheckResultTracker.clearResult();
                handleAttemptLockout(lockoutAttemptDeadline);
            } else if (!this.mLockPatternView.isEnabled()) {
                updateStage(Stage.NeedToUnlock);
            }
            this.mCredentialCheckResultTracker.setListener(this);
        }

        @Override // com.android.car.developeroptions.password.ConfirmDeviceCredentialBaseFragment
        public void prepareEnterAnimation() {
            super.prepareEnterAnimation();
            this.mHeaderTextView.setAlpha(0.0f);
            this.mCancelButton.setAlpha(0.0f);
            this.mLockPatternView.setAlpha(0.0f);
            this.mDetailsTextView.setAlpha(0.0f);
        }

        private int getDefaultDetails() {
            if (this.mFrp) {
                return R.string.lockpassword_confirm_your_pattern_details_frp;
            }
            boolean isStrongAuthRequired = isStrongAuthRequired();
            return UserManager.get(getActivity()).isManagedProfile(this.mEffectiveUserId) ? isStrongAuthRequired ? R.string.lockpassword_strong_auth_required_work_pattern : R.string.lockpassword_confirm_your_pattern_generic_profile : isStrongAuthRequired ? R.string.lockpassword_strong_auth_required_device_pattern : R.string.lockpassword_confirm_your_pattern_generic;
        }

        private Object[][] getActiveViews() {
            ArrayList arrayList = new ArrayList();
            arrayList.add(new ArrayList(Collections.singletonList(this.mHeaderTextView)));
            arrayList.add(new ArrayList(Collections.singletonList(this.mDetailsTextView)));
            if (this.mCancelButton.getVisibility() == 0) {
                arrayList.add(new ArrayList(Collections.singletonList(this.mCancelButton)));
            }
            LockPatternView.CellState[][] cellStates = this.mLockPatternView.getCellStates();
            for (int i = 0; i < cellStates.length; i++) {
                ArrayList arrayList2 = new ArrayList();
                for (int i2 = 0; i2 < cellStates[i].length; i2++) {
                    arrayList2.add(cellStates[i][i2]);
                }
                arrayList.add(arrayList2);
            }
            Object[][] objArr = (Object[][]) Array.newInstance((Class<?>) Object.class, arrayList.size(), cellStates[0].length);
            for (int i3 = 0; i3 < arrayList.size(); i3++) {
                ArrayList arrayList3 = (ArrayList) arrayList.get(i3);
                for (int i4 = 0; i4 < arrayList3.size(); i4++) {
                    objArr[i3][i4] = arrayList3.get(i4);
                }
            }
            return objArr;
        }

        @Override // com.android.car.developeroptions.password.ConfirmDeviceCredentialBaseFragment
        public void startEnterAnimation() {
            super.startEnterAnimation();
            this.mLockPatternView.setAlpha(1.0f);
            this.mAppearAnimationUtils.startAnimation2d(getActiveViews(), null, this);
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void updateStage(Stage stage) {
            int i = AnonymousClass1.$SwitchMap$com$android$car$developeroptions$password$ConfirmLockPattern$Stage[stage.ordinal()];
            if (i == 1) {
                CharSequence charSequence = this.mHeaderText;
                if (charSequence != null) {
                    this.mHeaderTextView.setText(charSequence);
                } else {
                    this.mHeaderTextView.setText(getDefaultHeader());
                }
                CharSequence charSequence2 = this.mDetailsText;
                if (charSequence2 != null) {
                    this.mDetailsTextView.setText(charSequence2);
                } else {
                    this.mDetailsTextView.setText(getDefaultDetails());
                }
                this.mErrorTextView.setText("");
                updateErrorMessage(this.mLockPatternUtils.getCurrentFailedPasswordAttempts(this.mEffectiveUserId));
                this.mLockPatternView.setEnabled(true);
                this.mLockPatternView.enableInput();
                this.mLockPatternView.clearPattern();
            } else if (i == 2) {
                showError(R.string.lockpattern_need_to_unlock_wrong, 3000L);
                this.mLockPatternView.setDisplayMode(LockPatternView.DisplayMode.Wrong);
                this.mLockPatternView.setEnabled(true);
                this.mLockPatternView.enableInput();
            } else if (i == 3) {
                this.mLockPatternView.clearPattern();
                this.mLockPatternView.setEnabled(false);
            }
            TextView textView = this.mHeaderTextView;
            textView.announceForAccessibility(textView.getText());
        }

        private int getDefaultHeader() {
            return this.mFrp ? R.string.lockpassword_confirm_your_pattern_header_frp : R.string.lockpassword_confirm_your_pattern_header;
        }

        private void postClearPatternRunnable() {
            this.mLockPatternView.removeCallbacks(this.mClearPatternRunnable);
            this.mLockPatternView.postDelayed(this.mClearPatternRunnable, 3000L);
        }

        private void startDisappearAnimation(final Intent intent) {
            if (this.mDisappearing) {
                return;
            }
            this.mDisappearing = true;
            final ConfirmLockPattern confirmLockPattern = (ConfirmLockPattern) getActivity();
            if (confirmLockPattern == null || confirmLockPattern.isFinishing()) {
                return;
            }
            if (confirmLockPattern.getConfirmCredentialTheme() == ConfirmDeviceCredentialBaseActivity.ConfirmCredentialTheme.DARK) {
                this.mLockPatternView.clearPattern();
                this.mDisappearAnimationUtils.startAnimation2d(getActiveViews(), new Runnable() { // from class: com.android.car.developeroptions.password.-$$Lambda$ConfirmLockPattern$ConfirmLockPatternFragment$OiGkGSM7cqEgVjk4gLh1dfsxrwo
                    @Override // java.lang.Runnable
                    public final void run() {
                        ConfirmLockPattern.ConfirmLockPatternFragment.lambda$startDisappearAnimation$0(ConfirmLockPattern.this, intent);
                    }
                }, this);
            } else {
                confirmLockPattern.setResult(-1, intent);
                confirmLockPattern.finish();
            }
        }

        static /* synthetic */ void lambda$startDisappearAnimation$0(ConfirmLockPattern confirmLockPattern, Intent intent) {
            confirmLockPattern.setResult(-1, intent);
            confirmLockPattern.finish();
            confirmLockPattern.overridePendingTransition(R.anim.confirm_credential_close_enter, R.anim.confirm_credential_close_exit);
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void onPatternChecked(boolean z, Intent intent, int i, int i2, boolean z2) {
            this.mLockPatternView.setEnabled(true);
            if (z) {
                if (z2) {
                    ConfirmDeviceCredentialUtils.reportSuccessfulAttempt(this.mLockPatternUtils, this.mUserManager, this.mEffectiveUserId);
                }
                startDisappearAnimation(intent);
                ConfirmDeviceCredentialUtils.checkForPendingIntent(getActivity());
                return;
            }
            if (i > 0) {
                refreshLockScreen();
                handleAttemptLockout(this.mLockPatternUtils.setLockoutAttemptDeadline(i2, i));
            } else {
                updateStage(Stage.NeedToUnlockWrong);
                postClearPatternRunnable();
            }
            if (z2) {
                reportFailedAttempt();
            }
        }

        @Override // com.android.car.developeroptions.password.CredentialCheckResultTracker.Listener
        public void onCredentialChecked(boolean z, Intent intent, int i, int i2, boolean z2) {
            onPatternChecked(z, intent, i, i2, z2);
        }

        @Override // com.android.car.developeroptions.password.ConfirmDeviceCredentialBaseFragment
        protected int getLastTryErrorMessage(int i) {
            if (i == 1) {
                return R.string.lock_last_pattern_attempt_before_wipe_device;
            }
            if (i == 2) {
                return R.string.lock_last_pattern_attempt_before_wipe_profile;
            }
            if (i == 3) {
                return R.string.lock_last_pattern_attempt_before_wipe_user;
            }
            throw new IllegalArgumentException("Unrecognized user type:" + i);
        }

        private void handleAttemptLockout(long j) {
            updateStage(Stage.LockedOut);
            this.mCountdownTimer = new CountDownTimer(j - SystemClock.elapsedRealtime(), 1000L) { // from class: com.android.car.developeroptions.password.ConfirmLockPattern.ConfirmLockPatternFragment.4
                @Override // android.os.CountDownTimer
                public void onTick(long j2) {
                    ConfirmLockPatternFragment confirmLockPatternFragment = ConfirmLockPatternFragment.this;
                    confirmLockPatternFragment.mErrorTextView.setText(confirmLockPatternFragment.getString(R.string.lockpattern_too_many_failed_confirmation_attempts, Integer.valueOf((int) (j2 / 1000))));
                }

                @Override // android.os.CountDownTimer
                public void onFinish() {
                    ConfirmLockPatternFragment.this.updateStage(Stage.NeedToUnlock);
                }
            }.start();
        }

        @Override // com.android.settingslib.animation.AppearAnimationCreator
        public void createAnimation(Object obj, long j, long j2, float f, boolean z, Interpolator interpolator, Runnable runnable) {
            if (obj instanceof LockPatternView.CellState) {
                this.mLockPatternView.startCellStateAnimation((LockPatternView.CellState) obj, 1.0f, z ? 1.0f : 0.0f, z ? f : 0.0f, z ? 0.0f : f, z ? 0.0f : 1.0f, 1.0f, j, j2, interpolator, runnable);
            } else {
                this.mAppearAnimationUtils.createAnimation((View) obj, j, j2, f, z, interpolator, runnable);
            }
        }
    }

    /* renamed from: com.android.car.developeroptions.password.ConfirmLockPattern$1, reason: invalid class name */
    static /* synthetic */ class AnonymousClass1 {
        static final /* synthetic */ int[] $SwitchMap$com$android$car$developeroptions$password$ConfirmLockPattern$Stage;

        static {
            int[] iArr = new int[Stage.values().length];
            $SwitchMap$com$android$car$developeroptions$password$ConfirmLockPattern$Stage = iArr;
            try {
                iArr[Stage.NeedToUnlock.ordinal()] = 1;
            } catch (NoSuchFieldError unused) {
            }
            try {
                $SwitchMap$com$android$car$developeroptions$password$ConfirmLockPattern$Stage[Stage.NeedToUnlockWrong.ordinal()] = 2;
            } catch (NoSuchFieldError unused2) {
            }
            try {
                $SwitchMap$com$android$car$developeroptions$password$ConfirmLockPattern$Stage[Stage.LockedOut.ordinal()] = 3;
            } catch (NoSuchFieldError unused3) {
            }
        }
    }
}
