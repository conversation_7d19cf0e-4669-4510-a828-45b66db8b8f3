package com.android.car.developeroptions.privacy;

import android.accessibilityservice.AccessibilityServiceInfo;
import android.content.Context;
import android.content.IntentFilter;
import android.provider.DeviceConfig;
import android.view.accessibility.AccessibilityManager;
import com.android.car.developeroptions.R;
import com.android.car.developeroptions.core.BasePreferenceController;
import com.android.car.developeroptions.slices.SliceBackgroundWorker;
import java.util.List;

/* loaded from: classes.dex */
public class AccessibilityUsagePreferenceController extends BasePreferenceController {
    private final List<AccessibilityServiceInfo> mEnabledServiceInfos;

    @Override // com.android.car.developeroptions.slices.Sliceable
    public /* bridge */ /* synthetic */ void copy() {
        super.copy();
    }

    @Override // com.android.car.developeroptions.slices.Sliceable
    public /* bridge */ /* synthetic */ Class<? extends SliceBackgroundWorker> getBackgroundWorkerClass() {
        return super.getBackgroundWorkerClass();
    }

    @Override // com.android.car.developeroptions.slices.Sliceable
    public /* bridge */ /* synthetic */ IntentFilter getIntentFilter() {
        return super.getIntentFilter();
    }

    @Override // com.android.car.developeroptions.slices.Sliceable
    public /* bridge */ /* synthetic */ boolean hasAsyncUpdate() {
        return super.hasAsyncUpdate();
    }

    @Override // com.android.car.developeroptions.slices.Sliceable
    public /* bridge */ /* synthetic */ boolean isCopyableSlice() {
        return super.isCopyableSlice();
    }

    @Override // com.android.car.developeroptions.slices.Sliceable
    public /* bridge */ /* synthetic */ boolean isSliceable() {
        return super.isSliceable();
    }

    @Override // com.android.car.developeroptions.slices.Sliceable
    public /* bridge */ /* synthetic */ boolean useDynamicSliceSummary() {
        return super.useDynamicSliceSummary();
    }

    public AccessibilityUsagePreferenceController(Context context, String str) {
        super(context, str);
        this.mEnabledServiceInfos = ((AccessibilityManager) context.getSystemService(AccessibilityManager.class)).getEnabledAccessibilityServiceList(-1);
    }

    @Override // com.android.car.developeroptions.core.BasePreferenceController
    public int getAvailabilityStatus() {
        return (this.mEnabledServiceInfos.isEmpty() || !Boolean.parseBoolean(DeviceConfig.getProperty("privacy", "permissions_hub_enabled"))) ? 3 : 0;
    }

    @Override // com.android.settingslib.core.AbstractPreferenceController
    public CharSequence getSummary() {
        return this.mContext.getResources().getQuantityString(R.plurals.accessibility_usage_summary, this.mEnabledServiceInfos.size(), Integer.valueOf(this.mEnabledServiceInfos.size()));
    }
}
