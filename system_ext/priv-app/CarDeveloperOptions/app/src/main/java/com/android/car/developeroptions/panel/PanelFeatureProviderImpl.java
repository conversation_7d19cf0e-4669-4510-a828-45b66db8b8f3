package com.android.car.developeroptions.panel;

import android.content.Context;

/* loaded from: classes.dex */
public class PanelFeatureProviderImpl implements PanelFeatureProvider {
    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    @Override // com.android.car.developeroptions.panel.PanelFeatureProvider
    public PanelContent getPanel(Context context, String str, String str2) {
        char c;
        switch (str.hashCode()) {
            case 66351017:
                if (str.equals("android.settings.panel.action.NFC")) {
                    c = 2;
                    break;
                }
                c = 65535;
                break;
            case 464243859:
                if (str.equals("android.settings.panel.action.INTERNET_CONNECTIVITY")) {
                    c = 0;
                    break;
                }
                c = 65535;
                break;
            case **********:
                if (str.equals("android.settings.panel.action.VOLUME")) {
                    c = 4;
                    break;
                }
                c = 65535;
                break;
            case **********:
                if (str.equals("com.android.settings.panel.action.MEDIA_OUTPUT")) {
                    c = 1;
                    break;
                }
                c = 65535;
                break;
            case 2057152695:
                if (str.equals("android.settings.panel.action.WIFI")) {
                    c = 3;
                    break;
                }
                c = 65535;
                break;
            default:
                c = 65535;
                break;
        }
        if (c == 0) {
            return InternetConnectivityPanel.create(context);
        }
        if (c == 1) {
            return MediaOutputPanel.create(context, str2);
        }
        if (c == 2) {
            return NfcPanel.create(context);
        }
        if (c == 3) {
            return WifiPanel.create(context);
        }
        if (c == 4) {
            return VolumePanel.create(context);
        }
        throw new IllegalStateException("No matching panel for: " + str);
    }
}
