package com.hozon.settings.utils;

import android.app.HozonPermissionManager;
import android.app.IHozonPermissionCallBack;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.RemoteException;
import android.provider.Settings;
import android.util.Log;
import android.view.View;
import com.hozon.settings.APP;
import com.hozon.settings.BuildConfig;
import com.hozon.settings.system.manager.DeepLinkManager;
import com.hozonauto.widget.dialog.StandardDialog;
import com.orhanobut.logger.Logger;
import java.util.Arrays;
import java.util.List;
import libcore.icu.RelativeDateTimeFormatter;

/* loaded from: classes2.dex */
public class PermissionUtil {
    public static final String ACCESS_COARSE_LOCATION = "android.permission.ACCESS_COARSE_LOCATION";
    public static final String ACCESS_FINE_LOCATION = "android.permission.ACCESS_FINE_LOCATION";
    public static final String ACTION_STEALTH_MODE_CHANGE = "com.qiku.android.stealthmode.action.STEALTH_MODE_CHANGE";
    public static final String ARG_PERMISSION_GROUP = "permission_group";
    public static final String ARG_STEALTH_MODE = "stealth_mode";
    public static final Uri AUTHORITY_URI = Uri.parse("content://com.qiku.stealthmode");
    public static final String BROADCAST_PERMISSION_ON_CAMERA = "com.hozon.settings.broadcast.permission_on.CAMERA";
    public static final String BROADCAST_PERMISSION_ON_LOCATION = "com.hozon.settings.broadcast.permission_on.LOCATION";
    public static final String BROADCAST_PERMISSION_ON_RECORD_AUDIO = "com.hozon.settings.broadcast.permission_on.RECORD_AUDIO";
    public static final String CAMERA = "android.permission.CAMERA";
    public static boolean IS_PRIVACY_CONTROL_FRAGMENT_SHOW = false;
    public static final String METHOD_GET_STEALTH_MODE = "get_stealth_mode";
    public static final String RECORD_AUDIO = "android.permission.RECORD_AUDIO";
    public static final String SENSOR_CAMERA_AUTHORIZED_TIME = "SENSOR_CAMERA_AUTHORIZED_TIME";
    public static final String SENSOR_LOCATION_AUTHORIZED_TIME = "SENSOR_LOCATION_AUTHORIZED_TIME";
    public static final String SENSOR_MIC_AUTHORIZED_TIME = "SENSOR_MIC_AUTHORIZED_TIME";
    public static final int STEALTH_MODE_OFF = 0;
    public static final int STEALTH_MODE_ON = 1;
    public static final String SWITCH_PERMISSION_CAMERA_CHECKED = "SWITCH_PERMISSION_CAMERA_CHECKED";
    public static final String SWITCH_PERMISSION_LOCATION_CHECKED = "SWITCH_PERMISSION_LOCATION_CHECKED";
    public static final String SWITCH_PERMISSION_MICROPHONE_CHECKED = "SWITCH_PERMISSION_MICROPHONE_CHECKED";
    public static final String SWITCH_SENSOR_CAMERA_CHECKED = "SWITCH_SENSOR_CAMERA_CHECKED";
    public static final String SWITCH_SENSOR_MICROPHONE_CHECKED = "SWITCH_SENSOR_MICROPHONE_CHECKED";
    private static final String TAG = "PermissionUtil";
    private static PermissionUtil instance;
    private static StandardDialog mPermissionDialog;
    private HozonPermissionManager mHozonPermissionManager = (HozonPermissionManager) APP.getInstance().getSystemService("hozon_permission");

    public interface OnCheckResultCallback {
        void onCheckResult(String[] strArr, String str, int i);
    }

    public static PermissionUtil getInstance() {
        if (instance == null) {
            synchronized (PermissionUtil.class) {
                if (instance == null) {
                    instance = new PermissionUtil();
                }
            }
        }
        return instance;
    }

    private PermissionUtil() {
    }

    public void grantRuntimePermission(final String str) {
        if (this.mHozonPermissionManager != null) {
            Log.i(TAG, "grantRuntimePermission---" + str);
            RunUtils.async(new Runnable() { // from class: com.hozon.settings.utils.PermissionUtil.1
                @Override // java.lang.Runnable
                public void run() {
                    PermissionUtil.this.mHozonPermissionManager.grantRuntimePermission(str);
                }
            });
        }
    }

    public void revokeRuntimePermission(final String str) {
        if (this.mHozonPermissionManager != null) {
            Log.i(TAG, "revokeRuntimePermission---" + str);
            RunUtils.async(new Runnable() { // from class: com.hozon.settings.utils.PermissionUtil.2
                @Override // java.lang.Runnable
                public void run() {
                    PermissionUtil.this.mHozonPermissionManager.revokeRuntimePermission(str);
                }
            });
            if (CAMERA.equals(str)) {
                Intent intent = new Intent();
                intent.setAction(BROADCAST_PERMISSION_ON_CAMERA);
                intent.putExtra("PermissionCamera", false);
                APP.getInstance().sendBroadcast(intent);
            }
        }
    }

    public void sendPermissionBroadcast(String str) {
        try {
            Intent intent = new Intent();
            intent.setPackage("com.aispeech.lyra.daemon");
            intent.setAction(str);
            APP.getInstance().sendBroadcast(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void registerHozonPermissionCallback(final OnCheckResultCallback onCheckResultCallback) {
        if (this.mHozonPermissionManager != null) {
            Log.i(TAG, "registerHozonPermissionCallback---");
            this.mHozonPermissionManager.registerHozonPermissionCallback(new IHozonPermissionCallBack.Stub() { // from class: com.hozon.settings.utils.PermissionUtil.3
                public void onCheckResult(String[] strArr, String str, int i) throws RemoteException {
                    if (onCheckResultCallback != null) {
                        Log.i(PermissionUtil.TAG, "onCheckResult---" + str + "---" + Arrays.toString(strArr));
                        onCheckResultCallback.onCheckResult(strArr, str, i);
                    }
                }
            });
        }
    }

    public int checkLocationAuthorizedTime() {
        long j = Settings.Global.getLong(APP.getInstance().getContentResolver(), SENSOR_LOCATION_AUTHORIZED_TIME, 0L);
        Log.i(TAG, "locationAuthorizedTime---getValue---" + j);
        if (j != 0) {
            long currentTimeMillis = j - System.currentTimeMillis();
            if (currentTimeMillis > 0) {
                return ((int) ((currentTimeMillis / RelativeDateTimeFormatter.DAY_IN_MILLIS) + 1)) < 8 ? 1 : -1;
            }
            Settings.Global.putInt(APP.getInstance().getContentResolver(), SWITCH_PERMISSION_LOCATION_CHECKED, 0);
            Settings.Global.putLong(APP.getInstance().getContentResolver(), SENSOR_LOCATION_AUTHORIZED_TIME, 0L);
            getInstance().revokeRuntimePermission(ACCESS_FINE_LOCATION);
            getInstance().revokeRuntimePermission(ACCESS_COARSE_LOCATION);
            return 0;
        }
        boolean z = Settings.Global.getInt(APP.getInstance().getContentResolver(), SWITCH_PERMISSION_LOCATION_CHECKED, 0) == 1;
        Log.i(TAG, "isLocationChecked=" + z);
        if (!z) {
            return -1;
        }
        Settings.Global.putLong(APP.getInstance().getContentResolver(), SENSOR_LOCATION_AUTHORIZED_TIME, System.currentTimeMillis() + RelativeDateTimeFormatter.YEAR_IN_MILLIS);
        return -1;
    }

    public int checkMicAuthorizedTime() {
        long j = Settings.Global.getLong(APP.getInstance().getContentResolver(), SENSOR_MIC_AUTHORIZED_TIME, 0L);
        Log.i(TAG, "micAuthorizedTime---getValue---" + j);
        if (j == 0) {
            return -1;
        }
        long currentTimeMillis = j - System.currentTimeMillis();
        if (currentTimeMillis > 0) {
            return ((int) ((currentTimeMillis / RelativeDateTimeFormatter.DAY_IN_MILLIS) + 1)) < 8 ? 1 : -1;
        }
        Settings.Global.putInt(APP.getInstance().getContentResolver(), SWITCH_SENSOR_MICROPHONE_CHECKED, 0);
        Settings.Global.putLong(APP.getInstance().getContentResolver(), SENSOR_MIC_AUTHORIZED_TIME, 0L);
        getInstance().revokeRuntimePermission(RECORD_AUDIO);
        return 0;
    }

    public int checkCameraAuthorizedTime() {
        long j = Settings.Global.getLong(APP.getInstance().getContentResolver(), SENSOR_CAMERA_AUTHORIZED_TIME, 0L);
        Log.i(TAG, "cameraAuthorizedTime---getValue---" + j);
        if (j == 0) {
            return -1;
        }
        long currentTimeMillis = j - System.currentTimeMillis();
        if (currentTimeMillis > 0) {
            return ((int) ((currentTimeMillis / RelativeDateTimeFormatter.DAY_IN_MILLIS) + 1)) < 8 ? 1 : -1;
        }
        Settings.Global.putInt(APP.getInstance().getContentResolver(), SWITCH_SENSOR_CAMERA_CHECKED, 0);
        Settings.Global.putLong(APP.getInstance().getContentResolver(), SENSOR_CAMERA_AUTHORIZED_TIME, 0L);
        getInstance().revokeRuntimePermission(CAMERA);
        return 0;
    }

    public int getMode(Context context) {
        int i;
        try {
            Bundle call = context.getContentResolver().call(AUTHORITY_URI, METHOD_GET_STEALTH_MODE, (String) null, (Bundle) null);
            i = call == null ? 0 : call.getInt(ARG_STEALTH_MODE, 0);
        } catch (Exception e) {
            e = e;
            i = 0;
        }
        try {
            Logger.t(TAG).d("getMode: %d", Integer.valueOf(i));
        } catch (Exception e2) {
            e = e2;
            Logger.t(TAG).e("getMode: %s", e);
            return i;
        }
        return i;
    }

    public static void showPermissionDialog(final Context context, String str, String[] strArr) {
        StandardDialog standardDialog = mPermissionDialog;
        if (standardDialog != null && standardDialog.isShowing()) {
            Log.i(TAG, "PermissionDialog---showing");
            return;
        }
        StandardDialog build = new StandardDialog.Builder().setCancelable(false).setContentText(convertName(strArr) + "权限未开启，无法使用相关服务").setPrimaryBtnText("设置").setSecondaryBtnText("取消").setContentGravity(17).setPrimaryBtnStyle(0).setSecondaryBtnStyle(1).build(context);
        mPermissionDialog = build;
        build.show();
        mPermissionDialog.setOnPrimaryBtnClickListener(new View.OnClickListener() { // from class: com.hozon.settings.utils.PermissionUtil.4
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                Log.i(PermissionUtil.TAG, "PermissionDialog---onPositiveClick");
                try {
                    Intent intent = new Intent();
                    intent.setPackage(BuildConfig.APPLICATION_ID);
                    intent.setFlags(268435456);
                    intent.setClassName(BuildConfig.APPLICATION_ID, "com.hozon.settings.MainActivity");
                    intent.putExtra("typepage", DeepLinkManager.SETTING_PRIVACY_CONTROL);
                    context.startActivity(intent);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                PermissionUtil.mPermissionDialog.dismiss();
            }
        });
        mPermissionDialog.setOnSecondaryBtnClickListener(new View.OnClickListener() { // from class: com.hozon.settings.utils.PermissionUtil.5
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                PermissionUtil.mPermissionDialog.dismiss();
            }
        });
        Log.i(TAG, "PermissionDialog---show()");
    }

    private static String convertName(String[] strArr) {
        boolean z;
        if (strArr == null) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        List asList = Arrays.asList(strArr);
        boolean z2 = true;
        if (asList.contains(CAMERA)) {
            sb.append("摄像头");
            z = true;
        } else {
            z = false;
        }
        if (asList.contains(RECORD_AUDIO)) {
            if (z) {
                sb.append("、");
            }
            sb.append("麦克风");
        } else {
            z2 = false;
        }
        if (asList.contains(ACCESS_FINE_LOCATION) || asList.contains(ACCESS_COARSE_LOCATION)) {
            if (z || z2) {
                sb.append("、");
            }
            sb.append("车内定位");
        }
        return sb.toString();
    }

    public static void initPermission() {
        int i = Settings.Global.getInt(APP.getInstance().getContentResolver(), SWITCH_SENSOR_MICROPHONE_CHECKED, 0);
        int i2 = Settings.Global.getInt(APP.getInstance().getContentResolver(), SWITCH_SENSOR_CAMERA_CHECKED, 0);
        int i3 = Settings.Global.getInt(APP.getInstance().getContentResolver(), SWITCH_PERMISSION_LOCATION_CHECKED, 0);
        Logger.t(TAG).i("Mic:" + i + ",Camera:" + i2 + ",Location:" + i3, new Object[0]);
        if (i == 1) {
            getInstance().grantRuntimePermission(RECORD_AUDIO);
        } else {
            getInstance().revokeRuntimePermission(RECORD_AUDIO);
        }
        if (i2 == 1) {
            getInstance().grantRuntimePermission(CAMERA);
        } else {
            getInstance().revokeRuntimePermission(CAMERA);
        }
        if (i3 == 1) {
            getInstance().grantRuntimePermission(ACCESS_FINE_LOCATION);
            getInstance().grantRuntimePermission(ACCESS_COARSE_LOCATION);
        } else {
            getInstance().revokeRuntimePermission(ACCESS_FINE_LOCATION);
            getInstance().revokeRuntimePermission(ACCESS_COARSE_LOCATION);
        }
    }
}
