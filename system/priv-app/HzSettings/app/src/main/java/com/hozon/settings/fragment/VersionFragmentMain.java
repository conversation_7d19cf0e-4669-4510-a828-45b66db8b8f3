package com.hozon.settings.fragment;

import android.content.ComponentName;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.database.ContentObserver;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.provider.Settings;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.widget.TextView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.hozon.settings.CarInfoViewModel;
import com.hozon.settings.R;
import com.hozon.settings.adapter.VersionInfoAdapter;
import com.hozon.settings.fragment.VersionFragmentMain;
import com.hozon.settings.hal.Hal<PERSON>anager;
import com.hozon.settings.kt.manager.PermissionLocationManager;
import com.hozon.settings.system.consts.Constant;
import com.hozon.settings.system.manager.AccountManagerSetting;
import com.hozon.settings.system.manager.ContentObserverUtil;
import com.hozon.settings.system.manager.TboxManagerX;
import com.hozon.settings.utils.AppUtils;
import com.hozon.settings.utils.AudioUtil;
import com.hozon.settings.utils.CommonUtil;
import com.hozon.settings.utils.EventTrackUtils;
import com.hozon.settings.utils.NetWorkUtils;
import com.hozon.settings.utils.PermissionUtil;
import com.hozon.settings.utils.SystemInfoUtils;
import com.hozon.settings.utils.ThreadUtils;
import com.hozon.settings.utils.ToastUtils;
import com.hozonauto.account.IAccountCertificationCallback;
import com.hozonauto.widget.RadioButtonContainer;
import com.hozonauto.widget.SwitchButton;
import com.hozonauto.widget.btn.ContainerButton;
import com.hozonauto.widget.dialog.StandardDialog;
import com.hozonauto.widget.dialog.VerificationDialogFragment;
import com.hozonauto.widget.interfaces.IOnItemClickListener;
import com.hozonauto.widget.list.MultiContainerListItemView;
import com.orhanobut.logger.Logger;
import java.util.ArrayList;
import libcore.icu.RelativeDateTimeFormatter;
import org.json.JSONArray;
import org.json.JSONObject;

/* loaded from: classes2.dex */
public class VersionFragmentMain extends BaseFragment implements View.OnClickListener, SwitchButton.OnCheckedChangeListener {
    private static final int COUNTS = 5;
    private static final long DURATION = 3000;
    private static final String TAG = "VersionFragmentMain";
    private StandardDialog authorityDialog;
    private SwitchButton cameraSwitch;
    private View contentView;
    private ArrayList<String> dataList;
    private TextView detail;
    private StandardDialog ejectionDialog;
    private StandardDialog extendedDialog;
    private MultiContainerListItemView itemCamera;
    private MultiContainerListItemView itemLocation;
    private MultiContainerListItemView itemMic;
    private ContainerButton itemNetworkReset;
    private ContainerButton itemRestoreFactory;
    private String lastTag;
    private SwitchButton locationSwitch;
    private int mCameraAuthorizedRemainDay;
    private Context mContext;
    private int mLocationAuthorizedRemainDay;
    private int mMicAuthorizedRemainDay;
    private StandardDialog mNetworkResetDialog;
    private StandardDialog mRestoreDialog;
    private StandardDialog mVersionDialog;
    private SwitchButton micSwitch;
    private RadioButtonContainer multiSelectContainer;
    private TextView privacyAgreement;
    private TextView serviceAgreement;
    private TextView softVersion;
    private UserProtocolFragment userProtocolFragment;
    private VerificationDialogFragment verifyDialogFragment;
    private VersionInfoAdapter versionInfoAdapter;
    private RecyclerView versionInfoRecycler;
    private Handler mHandler = new Handler(Looper.getMainLooper());
    private ContentObserver otaSoftVersionObserver = new ContentObserver(this.mHandler) { // from class: com.hozon.settings.fragment.VersionFragmentMain.1
        @Override // android.database.ContentObserver
        public void onChange(boolean z) {
            super.onChange(z);
            String string = Settings.Global.getString(VersionFragmentMain.this.mContext.getContentResolver(), "com.hozonauto.fota.versionInfo");
            Logger.t(VersionFragmentMain.TAG).i("otaSoftVersionObserver mVersion=" + string, new Object[0]);
            VersionFragmentMain.this.softVersion.setText(VersionFragmentMain.this.getString(R.string.current_software_version) + (TextUtils.isEmpty(string) ? "NETA OS V1.0.0" : "NETA OS " + string));
        }
    };
    private ContentObserver ecuInfoObserver = new ContentObserver(this.mHandler) { // from class: com.hozon.settings.fragment.VersionFragmentMain.2
        @Override // android.database.ContentObserver
        public void onChange(boolean z) {
            super.onChange(z);
            Logger.t(VersionFragmentMain.TAG).i("ecuInfoObserver versionDatas.size=" + VersionFragmentMain.this.versionDatas.size(), new Object[0]);
            VersionFragmentMain.this.versionDatas.clear();
            String version = SystemInfoUtils.getVersion();
            if (TextUtils.isEmpty(version)) {
                version = TboxManagerX.getTboxVersion();
            }
            VersionFragmentMain.this.versionDatas.add("IHU：" + version);
        }
    };
    OnPositiveClickListener mRestoreListener = new OnPositiveClickListener() { // from class: com.hozon.settings.fragment.VersionFragmentMain.14
        @Override // com.hozon.settings.fragment.VersionFragmentMain.OnPositiveClickListener
        public void OnPositiveClick() {
            if (Constant.RESTORE_FACTORY_DEBUG || "0".equals(AccountManagerSetting.getInstance(VersionFragmentMain.this.mContext).getOwnerFlg())) {
                try {
                    VersionFragmentMain.this.setResetCustom(2);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                try {
                    HalManager.getInstance().setCarLanHalType(4101, 2, "{\"RecoveryMode\":1}");
                    ThreadUtils.postDelayed(new Runnable() { // from class: com.hozon.settings.fragment.VersionFragmentMain.14.1
                        @Override // java.lang.Runnable
                        public void run() {
                            try {
                                JSONObject jSONObject = new JSONObject();
                                jSONObject.put("camera_permission", 0);
                                HalManager.getInstance().setCarLanHalType(4106, 12, jSONObject.toString());
                            } catch (Exception e2) {
                                e2.printStackTrace();
                            }
                        }
                    }, 500L);
                    Logger.t(VersionFragmentMain.TAG).d("OnPositiveClick:RecoveryMode 1");
                    return;
                } catch (Exception e2) {
                    Logger.t(VersionFragmentMain.TAG).e("OnPositiveClick: e=%s", e2);
                    return;
                }
            }
            ToastUtils.showToast(VersionFragmentMain.this.mContext.getString(R.string.toast_login_owner_account), 0);
        }
    };
    AccountManagerSetting.Callback callback = new AccountManagerSetting.Callback() { // from class: com.hozon.settings.fragment.VersionFragmentMain.15
        @Override // com.hozon.settings.system.manager.AccountManagerSetting.Callback
        public void onServiceConnected() {
            Logger.t(VersionFragmentMain.TAG).d("onServiceConnected: onSendVerificationCode");
        }

        @Override // com.hozon.settings.system.manager.AccountManagerSetting.Callback
        public void onServiceDisconnected() {
            Logger.t(VersionFragmentMain.TAG).d("onServiceDisconnected: onSendVerificationCode");
        }
    };
    private long[] mHits = new long[5];
    private ArrayList<String> versionDatas = new ArrayList<>();
    private boolean hasClickAuthorized = false;
    private int mSelectPosition = -1;
    private String lastType = "";
    private boolean hasClickEjection = false;

    interface OnPositiveClickListener {
        void OnPositiveClick();
    }

    @Override // com.hozon.settings.fragment.BaseFragment
    public int setTargetView() {
        return R.layout.fragment_system_main;
    }

    public static VersionFragmentMain newInstance() {
        return new VersionFragmentMain();
    }

    @Override // androidx.fragment.app.Fragment
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        FragmentActivity activity = getActivity();
        this.mContext = activity;
        if (!AccountManagerSetting.getInstance(activity).isBind()) {
            AccountManagerSetting.getInstance(this.mContext).bindAccountService(this.callback);
        }
        ContentObserverUtil.registerContentObserver(Settings.Global.getUriFor("com.hozonauto.fota.versionInfo"), this.otaSoftVersionObserver);
        ContentObserverUtil.registerContentObserver(Settings.Global.getUriFor("com.hozonauto.fota.allEcuDidInfo"), this.ecuInfoObserver);
    }

    @Override // androidx.fragment.app.Fragment
    public void onDestroy() {
        super.onDestroy();
        ContentObserverUtil.unregisterContentObserver(this.otaSoftVersionObserver);
        ContentObserverUtil.unregisterContentObserver(this.ecuInfoObserver);
    }

    @Override // androidx.fragment.app.Fragment
    public void onResume() {
        super.onResume();
        ThreadUtils.postDelayed(new Runnable() { // from class: com.hozon.settings.fragment.VersionFragmentMain.3
            @Override // java.lang.Runnable
            public void run() {
                VersionFragmentMain.this.initSwitchButton();
            }
        }, 200L);
        this.mHits = new long[5];
        initFactoryRecovery();
        initCameraState();
    }

    private void initFactoryRecovery() {
        if (this.itemRestoreFactory != null) {
            if (AccountManagerSetting.getInstance(this.mContext).isOwner()) {
                this.itemRestoreFactory.setVisibility(0);
                ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) this.itemNetworkReset.getLayoutParams();
                layoutParams.leftMargin = 24;
                this.itemNetworkReset.setLayoutParams(layoutParams);
                return;
            }
            this.itemRestoreFactory.setVisibility(8);
            ConstraintLayout.LayoutParams layoutParams2 = (ConstraintLayout.LayoutParams) this.itemNetworkReset.getLayoutParams();
            layoutParams2.leftMargin = 0;
            this.itemNetworkReset.setLayoutParams(layoutParams2);
        }
    }

    @Override // androidx.fragment.app.Fragment
    public void onPause() {
        super.onPause();
        hideDialog();
    }

    private void hideDialog() {
        StandardDialog standardDialog = this.authorityDialog;
        if (standardDialog != null && standardDialog.isShowing()) {
            this.authorityDialog.dismiss();
        }
        StandardDialog standardDialog2 = this.ejectionDialog;
        if (standardDialog2 != null && standardDialog2.isShowing()) {
            this.ejectionDialog.dismiss();
        }
        StandardDialog standardDialog3 = this.mRestoreDialog;
        if (standardDialog3 != null && standardDialog3.isShowing()) {
            this.mRestoreDialog.dismiss();
        }
        StandardDialog standardDialog4 = this.mNetworkResetDialog;
        if (standardDialog4 != null && standardDialog4.isShowing()) {
            this.mNetworkResetDialog.dismiss();
        }
        StandardDialog standardDialog5 = this.extendedDialog;
        if (standardDialog5 != null && standardDialog5.isShowing()) {
            this.extendedDialog.dismiss();
        }
        VerificationDialogFragment verificationDialogFragment = this.verifyDialogFragment;
        if (verificationDialogFragment != null && verificationDialogFragment.isAdded()) {
            this.verifyDialogFragment.dismiss();
        }
        StandardDialog standardDialog6 = this.mVersionDialog;
        if (standardDialog6 == null || !standardDialog6.isShowing()) {
            return;
        }
        this.mVersionDialog.dismiss();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void initSwitchButton() {
        if (getContext() == null) {
            Logger.t(TAG).d("initSwitchButton: context is null");
            return;
        }
        boolean z = Settings.Global.getInt(this.mContext.getContentResolver(), PermissionUtil.SWITCH_PERMISSION_LOCATION_CHECKED, 0) == 1;
        SwitchButton switchButton = this.locationSwitch;
        if (switchButton != null) {
            switchButton.setChecked(z);
        }
        if (z) {
            PermissionUtil.getInstance().grantRuntimePermission(PermissionUtil.ACCESS_FINE_LOCATION);
            PermissionUtil.getInstance().grantRuntimePermission(PermissionUtil.ACCESS_COARSE_LOCATION);
        } else {
            PermissionUtil.getInstance().revokeRuntimePermission(PermissionUtil.ACCESS_FINE_LOCATION);
            PermissionUtil.getInstance().revokeRuntimePermission(PermissionUtil.ACCESS_COARSE_LOCATION);
        }
        long j = Settings.Global.getLong(this.mContext.getContentResolver(), PermissionUtil.SENSOR_LOCATION_AUTHORIZED_TIME, 0L);
        if (j != 0) {
            long currentTimeMillis = j - System.currentTimeMillis();
            Logger.t(TAG).i("initAuthorizedTime: locationAuthorizedTimeRemain %d", Long.valueOf(currentTimeMillis));
            if (currentTimeMillis > 0) {
                int i = (int) ((currentTimeMillis / RelativeDateTimeFormatter.DAY_IN_MILLIS) + 1);
                this.mLocationAuthorizedRemainDay = i;
                if (i > 364) {
                    this.mLocationAuthorizedRemainDay = 364;
                }
                MultiContainerListItemView multiContainerListItemView = this.itemLocation;
                if (multiContainerListItemView != null) {
                    multiContainerListItemView.setAssitText(this.mLocationAuthorizedRemainDay + getString(R.string.next_annual_day));
                }
            } else {
                SwitchButton switchButton2 = this.locationSwitch;
                if (switchButton2 != null) {
                    switchButton2.setChecked(false);
                }
                Settings.Global.putInt(this.mContext.getContentResolver(), PermissionUtil.SWITCH_PERMISSION_LOCATION_CHECKED, 0);
                Settings.Global.putLong(this.mContext.getContentResolver(), PermissionUtil.SENSOR_LOCATION_AUTHORIZED_TIME, 0L);
                MultiContainerListItemView multiContainerListItemView2 = this.itemLocation;
                if (multiContainerListItemView2 != null) {
                    multiContainerListItemView2.setAssitText(getString(R.string.unauthorized));
                }
                PermissionUtil.getInstance().revokeRuntimePermission(PermissionUtil.ACCESS_FINE_LOCATION);
                PermissionUtil.getInstance().revokeRuntimePermission(PermissionUtil.ACCESS_COARSE_LOCATION);
            }
        } else {
            SwitchButton switchButton3 = this.locationSwitch;
            if (switchButton3 != null) {
                switchButton3.setChecked(false);
            }
            Settings.Global.putInt(this.mContext.getContentResolver(), PermissionUtil.SWITCH_PERMISSION_LOCATION_CHECKED, 0);
            Settings.Global.putLong(this.mContext.getContentResolver(), PermissionUtil.SENSOR_LOCATION_AUTHORIZED_TIME, 0L);
            MultiContainerListItemView multiContainerListItemView3 = this.itemLocation;
            if (multiContainerListItemView3 != null) {
                multiContainerListItemView3.setAssitText(getString(R.string.unauthorized));
            }
            PermissionUtil.getInstance().revokeRuntimePermission(PermissionUtil.ACCESS_FINE_LOCATION);
            PermissionUtil.getInstance().revokeRuntimePermission(PermissionUtil.ACCESS_COARSE_LOCATION);
        }
        boolean z2 = Settings.Global.getInt(this.mContext.getContentResolver(), PermissionUtil.SWITCH_SENSOR_MICROPHONE_CHECKED, 0) == 1;
        SwitchButton switchButton4 = this.micSwitch;
        if (switchButton4 != null) {
            switchButton4.setChecked(z2);
        }
        if (z2) {
            PermissionUtil.getInstance().grantRuntimePermission(PermissionUtil.RECORD_AUDIO);
        } else {
            PermissionUtil.getInstance().revokeRuntimePermission(PermissionUtil.RECORD_AUDIO);
        }
        long j2 = Settings.Global.getLong(this.mContext.getContentResolver(), PermissionUtil.SENSOR_MIC_AUTHORIZED_TIME, 0L);
        if (j2 != 0) {
            long currentTimeMillis2 = j2 - System.currentTimeMillis();
            Logger.t(TAG).i("initAuthorizedTime: micAuthorizedTimeRemain %d", Long.valueOf(currentTimeMillis2));
            if (currentTimeMillis2 > 0) {
                int i2 = (int) ((currentTimeMillis2 / RelativeDateTimeFormatter.DAY_IN_MILLIS) + 1);
                this.mMicAuthorizedRemainDay = i2;
                if (i2 > 364) {
                    this.mMicAuthorizedRemainDay = 364;
                }
                MultiContainerListItemView multiContainerListItemView4 = this.itemMic;
                if (multiContainerListItemView4 != null) {
                    multiContainerListItemView4.setAssitText(this.mMicAuthorizedRemainDay + getString(R.string.next_annual_day));
                }
            } else {
                SwitchButton switchButton5 = this.micSwitch;
                if (switchButton5 != null) {
                    switchButton5.setChecked(false);
                }
                Settings.Global.putInt(this.mContext.getContentResolver(), PermissionUtil.SWITCH_SENSOR_MICROPHONE_CHECKED, 0);
                Settings.Global.putLong(this.mContext.getContentResolver(), PermissionUtil.SENSOR_MIC_AUTHORIZED_TIME, 0L);
                MultiContainerListItemView multiContainerListItemView5 = this.itemMic;
                if (multiContainerListItemView5 != null) {
                    multiContainerListItemView5.setAssitText(getString(R.string.unauthorized));
                }
                PermissionUtil.getInstance().revokeRuntimePermission(PermissionUtil.RECORD_AUDIO);
            }
        } else {
            SwitchButton switchButton6 = this.micSwitch;
            if (switchButton6 != null) {
                switchButton6.setChecked(false);
            }
            Settings.Global.putInt(this.mContext.getContentResolver(), PermissionUtil.SWITCH_SENSOR_MICROPHONE_CHECKED, 0);
            Settings.Global.putLong(this.mContext.getContentResolver(), PermissionUtil.SENSOR_MIC_AUTHORIZED_TIME, 0L);
            MultiContainerListItemView multiContainerListItemView6 = this.itemMic;
            if (multiContainerListItemView6 != null) {
                multiContainerListItemView6.setAssitText(getString(R.string.unauthorized));
            }
            PermissionUtil.getInstance().revokeRuntimePermission(PermissionUtil.RECORD_AUDIO);
        }
        boolean z3 = Settings.Global.getInt(this.mContext.getContentResolver(), PermissionUtil.SWITCH_SENSOR_CAMERA_CHECKED, 0) == 1;
        SwitchButton switchButton7 = this.cameraSwitch;
        if (switchButton7 != null) {
            switchButton7.setChecked(z3);
        }
        if (z3) {
            PermissionUtil.getInstance().grantRuntimePermission(PermissionUtil.CAMERA);
        } else {
            PermissionUtil.getInstance().revokeRuntimePermission(PermissionUtil.CAMERA);
        }
        long j3 = Settings.Global.getLong(this.mContext.getContentResolver(), PermissionUtil.SENSOR_CAMERA_AUTHORIZED_TIME, 0L);
        if (j3 != 0) {
            long currentTimeMillis3 = j3 - System.currentTimeMillis();
            Logger.t(TAG).i("initAuthorizedTime: cameraAuthorizedTimeRemain %d", Long.valueOf(currentTimeMillis3));
            if (currentTimeMillis3 > 0) {
                int i3 = (int) ((currentTimeMillis3 / RelativeDateTimeFormatter.DAY_IN_MILLIS) + 1);
                this.mCameraAuthorizedRemainDay = i3;
                if (i3 > 364) {
                    this.mCameraAuthorizedRemainDay = 364;
                }
                MultiContainerListItemView multiContainerListItemView7 = this.itemCamera;
                if (multiContainerListItemView7 != null) {
                    multiContainerListItemView7.setAssitText(this.mCameraAuthorizedRemainDay + getString(R.string.next_annual_day));
                }
            } else {
                SwitchButton switchButton8 = this.cameraSwitch;
                if (switchButton8 != null) {
                    switchButton8.setChecked(false);
                }
                Settings.Global.putInt(this.mContext.getContentResolver(), PermissionUtil.SWITCH_SENSOR_CAMERA_CHECKED, 0);
                Settings.Global.putLong(this.mContext.getContentResolver(), PermissionUtil.SENSOR_CAMERA_AUTHORIZED_TIME, 0L);
                MultiContainerListItemView multiContainerListItemView8 = this.itemCamera;
                if (multiContainerListItemView8 != null) {
                    multiContainerListItemView8.setAssitText(getString(R.string.unauthorized));
                }
                PermissionUtil.getInstance().revokeRuntimePermission(PermissionUtil.CAMERA);
            }
        } else {
            SwitchButton switchButton9 = this.cameraSwitch;
            if (switchButton9 != null) {
                switchButton9.setChecked(false);
            }
            Settings.Global.putInt(this.mContext.getContentResolver(), PermissionUtil.SWITCH_SENSOR_CAMERA_CHECKED, 0);
            Settings.Global.putLong(this.mContext.getContentResolver(), PermissionUtil.SENSOR_CAMERA_AUTHORIZED_TIME, 0L);
            MultiContainerListItemView multiContainerListItemView9 = this.itemCamera;
            if (multiContainerListItemView9 != null) {
                multiContainerListItemView9.setAssitText(getString(R.string.unauthorized));
            }
            PermissionUtil.getInstance().revokeRuntimePermission(PermissionUtil.CAMERA);
        }
        Logger.t(TAG).d("initSwitchButton: %s, %s, %s, %s, %s", Boolean.valueOf(z), Boolean.valueOf(z2), Boolean.valueOf(z3), Long.valueOf(j2), Long.valueOf(j3));
    }

    @Override // com.hozon.settings.fragment.BaseFragment
    public void initTargetView(View view) {
        this.softVersion = (TextView) view.findViewById(R.id.softVersion);
        String string = Settings.Global.getString(this.mContext.getContentResolver(), "com.hozonauto.fota.versionInfo");
        Logger.t(TAG).d("initTargetView: mVersion=" + string);
        this.softVersion.setText(getString(R.string.current_software_version) + (TextUtils.isEmpty(string) ? "NETA OS V1.0.0" : "NETA OS " + string));
        this.detail = (TextView) view.findViewById(R.id.detail);
        this.itemLocation = (MultiContainerListItemView) view.findViewById(R.id.itemLocation);
        this.locationSwitch = (SwitchButton) View.inflate(this.mContext, R.layout.item_location_switchbutton, null);
        View inflate = View.inflate(this.mContext, R.layout.item_right_arrow, null);
        MultiContainerListItemView multiContainerListItemView = this.itemLocation;
        if (multiContainerListItemView != null) {
            multiContainerListItemView.addViewToLeftContainer(this.locationSwitch);
            this.itemLocation.addViewToRightContainer(inflate);
        }
        this.itemMic = (MultiContainerListItemView) view.findViewById(R.id.itemMic);
        this.micSwitch = (SwitchButton) View.inflate(this.mContext, R.layout.item_mic_switchbutton, null);
        View inflate2 = View.inflate(this.mContext, R.layout.item_right_arrow, null);
        MultiContainerListItemView multiContainerListItemView2 = this.itemMic;
        if (multiContainerListItemView2 != null) {
            multiContainerListItemView2.addViewToLeftContainer(this.micSwitch);
            this.itemMic.addViewToRightContainer(inflate2);
        }
        this.itemCamera = (MultiContainerListItemView) view.findViewById(R.id.itemCamera);
        this.cameraSwitch = (SwitchButton) View.inflate(this.mContext, R.layout.item_camera_switchbutton, null);
        View inflate3 = View.inflate(this.mContext, R.layout.item_right_arrow, null);
        MultiContainerListItemView multiContainerListItemView3 = this.itemCamera;
        if (multiContainerListItemView3 != null) {
            multiContainerListItemView3.addViewToLeftContainer(this.cameraSwitch);
            this.itemCamera.addViewToRightContainer(inflate3);
        }
        initCameraState();
        this.serviceAgreement = (TextView) view.findViewById(R.id.serviceAgreement);
        this.privacyAgreement = (TextView) view.findViewById(R.id.privacyAgreement);
        ContainerButton containerButton = (ContainerButton) view.findViewById(R.id.itemRestoreFactory);
        this.itemRestoreFactory = containerButton;
        if (containerButton != null) {
            containerButton.setBtnStyle(1);
        }
        this.itemNetworkReset = (ContainerButton) view.findViewById(R.id.itemNetworkReset);
        initFactoryRecovery();
        this.userProtocolFragment = UserProtocolFragment.newInstance();
        initLiveData();
        initOnClickListener();
        initSwitchButton();
    }

    private void initCameraState() {
        if (this.itemCamera != null) {
            if (SystemInfoUtils.getDmsConfig() == 1 && SystemInfoUtils.getOmsConfig() == 0) {
                this.itemCamera.setVisibility(8);
            } else {
                this.itemCamera.setVisibility(0);
            }
        }
    }

    @Override // com.hozon.settings.fragment.BaseFragment
    public void initOnClickListener() {
        this.softVersion.setOnClickListener(this);
        this.detail.setOnClickListener(this);
        this.serviceAgreement.setOnClickListener(this);
        this.privacyAgreement.setOnClickListener(this);
        this.locationSwitch.setOnCheckedChangeListener(this);
        this.micSwitch.setOnCheckedChangeListener(this);
        this.cameraSwitch.setOnCheckedChangeListener(this);
        this.itemLocation.setOnItemClickListener(new IOnItemClickListener() { // from class: com.hozon.settings.fragment.VersionFragmentMain.4
            @Override // com.hozonauto.widget.interfaces.IOnItemClickListener
            public void itemClick(View view) {
                Logger.t(VersionFragmentMain.TAG).d("itemClick:mLocationAuthorizedRemainDay=%s", Integer.valueOf(VersionFragmentMain.this.mLocationAuthorizedRemainDay));
                VersionFragmentMain versionFragmentMain = VersionFragmentMain.this;
                versionFragmentMain.showMultiSelectDialog(versionFragmentMain.getString(R.string.location_authorization_duration), "itemSensorLocation");
            }
        });
        this.itemMic.setOnItemClickListener(new IOnItemClickListener() { // from class: com.hozon.settings.fragment.VersionFragmentMain.5
            @Override // com.hozonauto.widget.interfaces.IOnItemClickListener
            public void itemClick(View view) {
                Logger.t(VersionFragmentMain.TAG).d("itemClick:mMicAuthorizedRemainDay=%s", Integer.valueOf(VersionFragmentMain.this.mMicAuthorizedRemainDay));
                VersionFragmentMain versionFragmentMain = VersionFragmentMain.this;
                versionFragmentMain.showMultiSelectDialog(versionFragmentMain.getString(R.string.mic_authorization_duration), "itemSensorMicrophone");
            }
        });
        this.itemCamera.setOnItemClickListener(new IOnItemClickListener() { // from class: com.hozon.settings.fragment.VersionFragmentMain.6
            @Override // com.hozonauto.widget.interfaces.IOnItemClickListener
            public void itemClick(View view) {
                Logger.t(VersionFragmentMain.TAG).d("itemClick:mCameraAuthorizedRemainDay=%s", Integer.valueOf(VersionFragmentMain.this.mCameraAuthorizedRemainDay));
                VersionFragmentMain versionFragmentMain = VersionFragmentMain.this;
                versionFragmentMain.showMultiSelectDialog(versionFragmentMain.getString(R.string.camera_authorization_duration), "itemSensorCamera");
            }
        });
        this.itemRestoreFactory.setOnClickListener(new View.OnClickListener() { // from class: com.hozon.settings.fragment.VersionFragmentMain.7
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                Logger.t(VersionFragmentMain.TAG).d("  %s", "onClick: rlRestoreFactory");
                if (VersionFragmentMain.this.showRestoreFactoryPreCheck()) {
                    VersionFragmentMain.this.showRestoreFactory();
                }
            }
        });
        this.itemNetworkReset.setOnClickListener(new View.OnClickListener() { // from class: com.hozon.settings.fragment.VersionFragmentMain.8
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                VersionFragmentMain.this.showNetworkReset();
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void showNetworkReset() {
        StandardDialog standardDialog = this.mRestoreDialog;
        if (standardDialog != null && standardDialog.isShowing()) {
            this.mRestoreDialog.dismiss();
        }
        StandardDialog standardDialog2 = this.mNetworkResetDialog;
        if (standardDialog2 != null && standardDialog2.isShowing()) {
            this.mNetworkResetDialog.dismiss();
        }
        StandardDialog build = new StandardDialog.Builder().setTitle(getString(R.string.network_reset)).setContentText(getString(R.string.network_reset_content)).setContentGravity(17).setContentTextGravity(3).setPrimaryBtnStyle(0).setSecondaryBtnStyle(1).setPrimaryBtnText(getString(R.string.confirm)).setSecondaryBtnText(getString(R.string.cancel)).build(getContext());
        this.mNetworkResetDialog = build;
        build.show();
        this.mNetworkResetDialog.setOnPrimaryBtnClickListener(new View.OnClickListener() { // from class: com.hozon.settings.fragment.VersionFragmentMain.9
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                Log.d(VersionFragmentMain.TAG, "mNetworkResetDialog: PrimaryBtnClick");
                CarInfoViewModel.getInstance().setCarHalType(557844157, 1);
                VersionFragmentMain.this.itemNetworkReset.setBtnStyle(0);
                VersionFragmentMain.this.itemNetworkReset.setLeftIconResId(R.drawable.ic_loading_network_reset);
                VersionFragmentMain.this.itemNetworkReset.setCanClick(false);
                VersionFragmentMain.this.mNetworkResetDialog.dismiss();
            }
        });
        this.mNetworkResetDialog.setOnSecondaryBtnClickListener(new View.OnClickListener() { // from class: com.hozon.settings.fragment.VersionFragmentMain.10
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                Log.d(VersionFragmentMain.TAG, "mNetworkResetDialog: SecondaryBtnClick");
                VersionFragmentMain.this.mNetworkResetDialog.dismiss();
            }
        });
    }

    public boolean showRestoreFactory() {
        Logger.t(TAG).d("  %s", "showRestoreFactory: >>");
        StandardDialog standardDialog = this.mNetworkResetDialog;
        if (standardDialog != null && standardDialog.isShowing()) {
            this.mNetworkResetDialog.dismiss();
        }
        StandardDialog standardDialog2 = this.mRestoreDialog;
        if (standardDialog2 != null && standardDialog2.isShowing()) {
            this.mRestoreDialog.dismiss();
        }
        StandardDialog build = new StandardDialog.Builder().setTitle(getString(R.string.restore_factory_title)).setContentText(getString(R.string.restore_factory_content)).setContentGravity(17).setContentTextGravity(3).setPrimaryBtnStyle(2).setSecondaryBtnStyle(1).setPrimaryBtnText(getString(R.string.confirm)).setSecondaryBtnText(getString(R.string.cancel)).build(getContext());
        this.mRestoreDialog = build;
        build.show();
        this.mRestoreDialog.setOnPrimaryBtnClickListener(new View.OnClickListener() { // from class: com.hozon.settings.fragment.VersionFragmentMain.11
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                Logger.t(VersionFragmentMain.TAG).d("  %s", "OnPositiveClick: mResetSettingListener");
                Logger.t(VersionFragmentMain.TAG).d("  %s", "OnPositiveClick: mRestorePreListener");
                if (!AccountManagerSetting.getInstance(VersionFragmentMain.this.mContext).isOwner()) {
                    ToastUtils.showToast(VersionFragmentMain.this.mContext.getString(R.string.toast_login_owner_account), 0);
                    return;
                }
                VersionFragmentMain.this.mRestoreDialog.dismiss();
                EventTrackUtils.eventTrackValueChange("恢复出厂", "成功");
                VersionFragmentMain.this.verifyDialogFragment = new VerificationDialogFragment.Builder().setMaxLength(6).setWarnText(VersionFragmentMain.this.getString(R.string.reinput_verification_code)).setWarnTextVisible(false).setCountDownMilliSec(60).setPrimaryBtnText(VersionFragmentMain.this.getString(R.string.sure)).setSecondaryBtnText(VersionFragmentMain.this.getString(R.string.cancel)).setBtnIndexEnterKeyEqualsWidth(0).setInputType(2).setPrimaryBtnClickable(false).setMaskType(1).build();
                VersionFragmentMain.this.verifyDialogFragment.registerEditTextChangeListener(new TextWatcher() { // from class: com.hozon.settings.fragment.VersionFragmentMain.11.1
                    @Override // android.text.TextWatcher
                    public void beforeTextChanged(CharSequence charSequence, int i, int i2, int i3) {
                    }

                    @Override // android.text.TextWatcher
                    public void onTextChanged(CharSequence charSequence, int i, int i2, int i3) {
                    }

                    @Override // android.text.TextWatcher
                    public void afterTextChanged(Editable editable) {
                        if (editable.length() == 0) {
                            VersionFragmentMain.this.verifyDialogFragment.getPrimaryBtn().setCanClick(false);
                        } else {
                            VersionFragmentMain.this.verifyDialogFragment.getPrimaryBtn().setCanClick(true);
                        }
                    }
                });
                VersionFragmentMain.this.verifyDialogFragment.setOnPrimaryBtnClickListener(new View.OnClickListener() { // from class: com.hozon.settings.fragment.VersionFragmentMain.11.2
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view2) {
                        Log.d(VersionFragmentMain.TAG, "verifyDialogFragment: PrimaryBtnClick");
                        VersionFragmentMain.this.checkAuthCode(VersionFragmentMain.this.verifyDialogFragment.getVerfyCode());
                    }
                });
                VersionFragmentMain.this.verifyDialogFragment.setOnSecondaryBtnClickListener(new View.OnClickListener() { // from class: com.hozon.settings.fragment.VersionFragmentMain.11.3
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view2) {
                        Log.d(VersionFragmentMain.TAG, "verifyDialogFragment: SecondaryBtnClick");
                        VersionFragmentMain.this.verifyDialogFragment.dismiss();
                    }
                });
                VersionFragmentMain.this.verifyDialogFragment.show(VersionFragmentMain.this.getFragmentManager(), "verify");
                VersionFragmentMain.this.verifyDialogFragment.registerSendVerifyCodeClickListener(new View.OnClickListener() { // from class: com.hozon.settings.fragment.VersionFragmentMain.11.4
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view2) {
                        VersionFragmentMain.this.onSendVerificationCode();
                        VersionFragmentMain.this.verifyDialogFragment.setSubTitle(VersionFragmentMain.this.getString(R.string.verification_code_to_bound_phone));
                    }
                });
                ThreadUtils.postDelayed(new Runnable() { // from class: com.hozon.settings.fragment.VersionFragmentMain.11.5
                    @Override // java.lang.Runnable
                    public void run() {
                        VersionFragmentMain.this.verifyDialogFragment.performVerfiyBtnClick();
                    }
                }, 50L);
            }
        });
        this.mRestoreDialog.setOnSecondaryBtnClickListener(new View.OnClickListener() { // from class: com.hozon.settings.fragment.VersionFragmentMain.12
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                Log.d(VersionFragmentMain.TAG, "mRestoreDialog: SecondaryBtnClick");
                VersionFragmentMain.this.mRestoreDialog.dismiss();
            }
        });
        Logger.t(TAG).d("  %s", "showRestoreFactory: <<");
        return true;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void onSendVerificationCode() {
        Logger.t(TAG).d("onSendVerificationCode: >>");
        if (AccountManagerSetting.getInstance(getContext()).isBind()) {
            Logger.t(TAG).d("onSendVerificationCode: bind true");
            getAuthCode();
        } else {
            Logger.t(TAG).d("onSendVerificationCode: bind false");
            AccountManagerSetting.getInstance(getContext()).bindAccountService(this.callback);
            AppUtils.runOnUIDelayed(new Runnable() { // from class: com.hozon.settings.fragment.VersionFragmentMain.13
                @Override // java.lang.Runnable
                public void run() {
                    Logger.t(VersionFragmentMain.TAG).d("run: onSendVerificationCode");
                    VersionFragmentMain.this.onSendVerificationCode();
                }
            }, 2000L);
        }
        Logger.t(TAG).d("onSendVerificationCode: <<");
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void checkAuthCode(String str) {
        if (Constant.RESTORE_FACTORY_DEBUG) {
            this.mRestoreListener.OnPositiveClick();
        } else {
            AccountManagerSetting.getInstance(getContext()).checkAuthCode(str, new CheckAuthCodeCallback());
        }
    }

    class CheckAuthCodeCallback extends IAccountCertificationCallback.Stub {
        CheckAuthCodeCallback() {
        }

        @Override // com.hozonauto.account.IAccountCertificationCallback
        public void succeed() {
            Logger.t(VersionFragmentMain.TAG).d("  %s", "checkAuthCode success");
            AppUtils.runOnUI(new Runnable() { // from class: com.hozon.settings.fragment.VersionFragmentMain.CheckAuthCodeCallback.1
                @Override // java.lang.Runnable
                public void run() {
                    try {
                        VersionFragmentMain.this.verifyDialogFragment.setWarnTextVisible(false);
                        if (VersionFragmentMain.this.verifyDialogFragment.isAdded() && !VersionFragmentMain.this.getActivity().isFinishing()) {
                            VersionFragmentMain.this.verifyDialogFragment.dismiss();
                        }
                        VersionFragmentMain.this.mRestoreListener.OnPositiveClick();
                    } catch (Exception e) {
                        Logger.t(VersionFragmentMain.TAG).e("%s", "run: e", e.getCause());
                    }
                }
            });
        }

        @Override // com.hozonauto.account.IAccountCertificationCallback
        public void failed(String str) {
            Logger.t(VersionFragmentMain.TAG).d("  %s", "checkAuthCode failed msg : " + str);
            AppUtils.runOnUI(new Runnable() { // from class: com.hozon.settings.fragment.VersionFragmentMain.CheckAuthCodeCallback.2
                @Override // java.lang.Runnable
                public void run() {
                    if (VersionFragmentMain.this.getActivity() == null) {
                        return;
                    }
                    VersionFragmentMain.this.verifyDialogFragment.setWarnText(VersionFragmentMain.this.getString(R.string.reinput_verification_code));
                    VersionFragmentMain.this.verifyDialogFragment.setWarnTextVisible(true);
                }
            });
        }
    }

    private void getAuthCode() {
        AccountManagerSetting.getInstance(getContext()).getAuthCode(new GetAuthCodeCallback());
    }

    class GetAuthCodeCallback extends IAccountCertificationCallback.Stub {
        GetAuthCodeCallback() {
        }

        @Override // com.hozonauto.account.IAccountCertificationCallback
        public void succeed() {
            Logger.t(VersionFragmentMain.TAG).d("  %s", "getAuthCode success");
        }

        @Override // com.hozonauto.account.IAccountCertificationCallback
        public void failed(final String str) {
            Logger.t(VersionFragmentMain.TAG).d("  %s", "getAuthCode failed  msg: " + str);
            AppUtils.runOnUI(new Runnable() { // from class: com.hozon.settings.fragment.-$$Lambda$VersionFragmentMain$GetAuthCodeCallback$Zu92ErsRq3eNg7q5MYi80Y3zcGk
                @Override // java.lang.Runnable
                public final void run() {
                    VersionFragmentMain.GetAuthCodeCallback.this.lambda$failed$0$VersionFragmentMain$GetAuthCodeCallback(str);
                }
            });
        }

        public /* synthetic */ void lambda$failed$0$VersionFragmentMain$GetAuthCodeCallback(String str) {
            if (VersionFragmentMain.this.getActivity() == null) {
                return;
            }
            Logger.t(VersionFragmentMain.TAG).d("  %s", "getAuthCode failed");
            ToastUtils.showToast(VersionFragmentMain.this.getString(R.string.failed_get_verification_code) + str, 0);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* JADX WARN: Can't wrap try/catch for region: R(14:0|1|2|3|(2:5|(2:11|(9:13|(1:15)|16|18|19|20|(1:22)(1:26)|23|24)))|30|(0)|16|18|19|20|(0)(0)|23|24) */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x00e4, code lost:
    
        r8 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:29:0x00e5, code lost:
    
        r8.printStackTrace();
     */
    /* JADX WARN: Removed duplicated region for block: B:15:0x00b5 A[Catch: Exception -> 0x00cd, TryCatch #0 {Exception -> 0x00cd, blocks: (B:3:0x002b, B:5:0x0037, B:7:0x006f, B:9:0x0073, B:11:0x007d, B:13:0x008d, B:15:0x00b5, B:16:0x00ba), top: B:2:0x002b }] */
    /* JADX WARN: Removed duplicated region for block: B:22:0x0110  */
    /* JADX WARN: Removed duplicated region for block: B:26:0x0114  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void setResetCustom(int r9) {
        /*
            Method dump skipped, instructions count: 283
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: com.hozon.settings.fragment.VersionFragmentMain.setResetCustom(int):void");
    }

    public boolean showRestoreFactoryPreCheck() {
        if (Constant.RESTORE_FACTORY_DEBUG) {
            return true;
        }
        int intValue = CarInfoViewModel.getInstance().getGearPositionObservable().getValue().intValue();
        Logger.t(TAG).d("showRestoreFactoryPreCheck: gear=%d", Integer.valueOf(intValue));
        if (intValue != 1) {
            ToastUtils.showToast(getString(R.string.hang_p_before_operating), 0);
            return false;
        }
        if (!AccountManagerSetting.getInstance(this.mContext).isBind()) {
            AccountManagerSetting.getInstance(this.mContext).bindAccountService(this.callback);
        }
        if (AccountManagerSetting.getInstance(this.mContext).isOwner()) {
            return true;
        }
        ToastUtils.showToast(this.mContext.getString(R.string.toast_login_owner_account), 0);
        return false;
    }

    @Override // com.hozon.settings.fragment.BaseFragment
    public void initLiveData() {
        CarInfoViewModel.getInstance().getGearPositionObservable().observe(getViewLifecycleOwner(), new Observer<Integer>() { // from class: com.hozon.settings.fragment.VersionFragmentMain.16
            @Override // androidx.lifecycle.Observer
            public void onChanged(Integer num) {
                if (num.intValue() != 1) {
                    VersionFragmentMain.this.itemRestoreFactory.setAlpha(0.5f);
                } else {
                    VersionFragmentMain.this.itemRestoreFactory.setAlpha(1.0f);
                }
            }
        });
        CarInfoViewModel.getInstance().getNetworkResetLiveData().observe(getViewLifecycleOwner(), new Observer<Integer>() { // from class: com.hozon.settings.fragment.VersionFragmentMain.17
            @Override // androidx.lifecycle.Observer
            public void onChanged(Integer num) {
                Logger.t(VersionFragmentMain.TAG).i("NetworkResetLiveData onChanged: value=%s", num);
                if (num.intValue() == -1 || num.intValue() == 1) {
                    VersionFragmentMain.this.itemNetworkReset.setBtnStyle(1);
                    VersionFragmentMain.this.itemNetworkReset.setLeftIconResId(R.drawable.ic_netreset_refresh);
                    VersionFragmentMain.this.itemNetworkReset.setCanClick(true);
                }
            }
        });
    }

    private void initVersionDialog() {
        this.versionDatas.clear();
        String version = SystemInfoUtils.getVersion();
        if (TextUtils.isEmpty(version)) {
            version = TboxManagerX.getTboxVersion();
        }
        this.versionDatas.add("IHU：" + version);
        String string = Settings.Global.getString(getContext().getContentResolver(), "com.hozonauto.fota.allEcuDidInfo");
        Logger.t(TAG).i("initVersionDialog: ecuDidInfo=" + string, new Object[0]);
        if (!TextUtils.isEmpty(string)) {
            try {
                JSONArray jSONArray = new JSONArray(string);
                for (int i = 0; i < jSONArray.length(); i++) {
                    JSONObject optJSONObject = jSONArray.optJSONObject(i);
                    String optString = optJSONObject.optString("ecuCode");
                    if (!TextUtils.equals(optString, "IHU")) {
                        JSONArray optJSONArray = optJSONObject.optJSONArray("didList");
                        int i2 = 0;
                        while (true) {
                            if (i2 < optJSONArray.length()) {
                                String optString2 = optJSONArray.optJSONObject(i2).optString("f1c0");
                                if (!TextUtils.isEmpty(optString2)) {
                                    this.versionDatas.add(optString + "：" + optString2);
                                    break;
                                }
                                i2++;
                            }
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        View inflate = View.inflate(this.mContext, R.layout.layout_version_info, null);
        this.contentView = inflate;
        this.versionInfoRecycler = (RecyclerView) inflate.findViewById(R.id.versionInfoRecycler);
        VersionInfoAdapter versionInfoAdapter = this.versionInfoAdapter;
        if (versionInfoAdapter == null) {
            this.versionInfoAdapter = new VersionInfoAdapter(this.versionDatas);
        } else {
            versionInfoAdapter.update(this.versionDatas);
        }
        this.versionInfoRecycler.setAdapter(this.versionInfoAdapter);
        this.versionInfoRecycler.setLayoutManager(new LinearLayoutManager(this.mContext));
        this.mVersionDialog = new StandardDialog.Builder().setTitle(getString(R.string.version_info_title)).addContentView(this.contentView).setContentGravity(17).setPrimaryBtnStyle(0).setPrimaryBtnText(getString(R.string.i_know)).setCancelable(false).build(getContext());
    }

    private void showVersionDialog() {
        if (this.mVersionDialog == null) {
            initVersionDialog();
        } else if (this.versionDatas.size() == 1) {
            String string = Settings.Global.getString(getContext().getContentResolver(), "com.hozonauto.fota.allEcuDidInfo");
            Logger.t(TAG).i("showVersionDialog: ecuDidInfo=" + string, new Object[0]);
            if (!TextUtils.isEmpty(string)) {
                try {
                    JSONArray jSONArray = new JSONArray(string);
                    for (int i = 0; i < jSONArray.length(); i++) {
                        JSONObject optJSONObject = jSONArray.optJSONObject(i);
                        String optString = optJSONObject.optString("ecuCode");
                        if (!TextUtils.equals(optString, "IHU")) {
                            JSONArray optJSONArray = optJSONObject.optJSONArray("didList");
                            int i2 = 0;
                            while (true) {
                                if (i2 < optJSONArray.length()) {
                                    String optString2 = optJSONArray.optJSONObject(i2).optString("f1c0");
                                    if (!TextUtils.isEmpty(optString2)) {
                                        this.versionDatas.add(optString + "：" + optString2);
                                        break;
                                    }
                                    i2++;
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                this.versionInfoAdapter.update(this.versionDatas);
                this.mVersionDialog = new StandardDialog.Builder().setTitle(getString(R.string.version_info_title)).addContentView(this.contentView).setContentGravity(17).setPrimaryBtnStyle(0).setPrimaryBtnText(getString(R.string.i_know)).setCancelable(false).build(getContext());
            }
        }
        StandardDialog standardDialog = this.mVersionDialog;
        if (standardDialog != null && standardDialog.isShowing()) {
            this.mVersionDialog.dismiss();
        }
        this.mVersionDialog.show();
        this.mVersionDialog.setOnPrimaryBtnClickListener(new View.OnClickListener() { // from class: com.hozon.settings.fragment.VersionFragmentMain.18
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                VersionFragmentMain.this.mVersionDialog.dismiss();
            }
        });
    }

    @Override // android.view.View.OnClickListener
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.detail /* 2131362111 */:
                Intent intent = new Intent();
                intent.setComponent(new ComponentName("com.hozonauto.fota", "com.hozonauto.fota.hmi.view.activity.MainActivity"));
                intent.addFlags(268435456);
                startActivity(intent);
                break;
            case R.id.privacyAgreement /* 2131362648 */:
                Logger.t(TAG).d("  %s", "onClick: privacyAgreement");
                try {
                    if (!CommonUtil.isFastDoubleClick(800L)) {
                        if (NetWorkUtils.isNetWorkEnableExt()) {
                            this.userProtocolFragment.initData(getString(R.string.user_privacy_agreement), 1);
                            getParentFragment().getChildFragmentManager().beginTransaction().replace(R.id.sflContainer, this.userProtocolFragment).commitAllowingStateLoss();
                        } else {
                            ToastUtils.showToast(getString(R.string.network_not_good_check_network), 3500);
                        }
                    }
                    EventTrackUtils.eventTrackValueChange(getString(R.string.user_privacy_agreement));
                    break;
                } catch (Exception e) {
                    e.printStackTrace();
                    return;
                }
            case R.id.serviceAgreement /* 2131362879 */:
                Logger.t(TAG).d("  %s", "onClick: serviceAgreement");
                try {
                    if (!CommonUtil.isFastDoubleClick(800L)) {
                        if (NetWorkUtils.isNetWorkEnableExt()) {
                            this.userProtocolFragment.initData(getString(R.string.version_user_service_agreement), 0);
                            getParentFragment().getChildFragmentManager().beginTransaction().replace(R.id.sflContainer, this.userProtocolFragment).commitAllowingStateLoss();
                        } else {
                            ToastUtils.showToast(getString(R.string.network_not_good_check_network), 3500);
                        }
                    }
                    EventTrackUtils.eventTrackValueChange(getString(R.string.version_user_service_agreement));
                    break;
                } catch (Exception e2) {
                    e2.printStackTrace();
                    return;
                }
            case R.id.softVersion /* 2131362905 */:
                long[] jArr = this.mHits;
                System.arraycopy(jArr, 1, jArr, 0, jArr.length - 1);
                long[] jArr2 = this.mHits;
                jArr2[jArr2.length - 1] = SystemClock.uptimeMillis();
                if (this.mHits[0] >= SystemClock.uptimeMillis() - DURATION) {
                    showVersionDialog();
                    this.mHits = new long[5];
                    break;
                }
                break;
        }
    }

    @Override // com.hozonauto.widget.SwitchButton.OnCheckedChangeListener
    public void onCheckedChanged(SwitchButton switchButton, boolean z, boolean z2) {
        if (getContext() == null) {
            Logger.t(TAG).i("onCheckedChanged: context is null", new Object[0]);
            return;
        }
        if (z2) {
            int id = switchButton.getId();
            if (id == R.id.cameraSwitch) {
                if (z) {
                    showMultiSelectDialog(getString(R.string.camera_authorization_duration), "switchSensorCamera");
                    return;
                } else {
                    showAuthorizedDialog(getString(R.string.camera_permission_disabled), getString(R.string.not_able_to_enjoy_smart_service), "switchSensorCamera");
                    return;
                }
            }
            if (id == R.id.locationSwitch) {
                if (z) {
                    showMultiSelectDialog(getString(R.string.location_authorization_duration), "switchSensorLocation");
                    return;
                } else {
                    showAuthorizedDialog(getString(R.string.location_permission_disabled), getString(R.string.not_able_to_enjoy_smart_service), "permissionLocation");
                    return;
                }
            }
            if (id != R.id.micSwitch) {
                return;
            }
            if (z) {
                showMultiSelectDialog(getString(R.string.mic_authorization_duration), "switchSensorMicrophone");
            } else {
                showAuthorizedDialog(getString(R.string.mic_permission_disabled), getString(R.string.not_able_to_enjoy_smart_service), "switchSensorMicrophone");
            }
        }
    }

    public void showAuthorizedDialog(String str, String str2, final String str3) {
        if (getActivity() == null) {
            Logger.t(TAG).d("showAuthorizedDialog:getActivity() is NULL");
            return;
        }
        StandardDialog standardDialog = this.ejectionDialog;
        if (standardDialog != null && standardDialog.isShowing()) {
            this.ejectionDialog.dismiss();
        }
        StandardDialog standardDialog2 = this.authorityDialog;
        if (standardDialog2 != null && standardDialog2.isShowing()) {
            this.authorityDialog.dismiss();
            if (TextUtils.equals(this.lastTag, "switchSensorMicrophone")) {
                ThreadUtils.postDelayed(new Runnable() { // from class: com.hozon.settings.fragment.VersionFragmentMain.19
                    @Override // java.lang.Runnable
                    public void run() {
                        VersionFragmentMain.this.micSwitch.setChecked(Settings.Global.getInt(VersionFragmentMain.this.mContext.getContentResolver(), PermissionUtil.SWITCH_SENSOR_MICROPHONE_CHECKED, 0) == 1);
                    }
                }, 50L);
            } else if (TextUtils.equals(this.lastTag, "switchSensorCamera")) {
                ThreadUtils.postDelayed(new Runnable() { // from class: com.hozon.settings.fragment.VersionFragmentMain.20
                    @Override // java.lang.Runnable
                    public void run() {
                        VersionFragmentMain.this.cameraSwitch.setChecked(Settings.Global.getInt(VersionFragmentMain.this.mContext.getContentResolver(), PermissionUtil.SWITCH_SENSOR_CAMERA_CHECKED, 0) == 1);
                    }
                }, 50L);
            } else if (TextUtils.equals(this.lastTag, "permissionLocation")) {
                ThreadUtils.postDelayed(new Runnable() { // from class: com.hozon.settings.fragment.VersionFragmentMain.21
                    @Override // java.lang.Runnable
                    public void run() {
                        VersionFragmentMain.this.locationSwitch.setChecked(Settings.Global.getInt(VersionFragmentMain.this.mContext.getContentResolver(), PermissionUtil.SWITCH_PERMISSION_LOCATION_CHECKED, 0) == 1);
                    }
                }, 50L);
            }
            this.lastTag = null;
        }
        this.lastTag = str3;
        StandardDialog build = new StandardDialog.Builder().setTitle(str).setContentText(str2).setContentGravity(17).setContentTextGravity(3).setBtnNum(2).setPrimaryBtnText(getString(R.string.confirm)).setSecondaryBtnText(getString(R.string.cancel)).setPrimaryBtnStyle(0).setSecondaryBtnStyle(1).build(getActivity());
        this.authorityDialog = build;
        build.show();
        this.authorityDialog.setOnPrimaryBtnClickListener(new View.OnClickListener() { // from class: com.hozon.settings.fragment.VersionFragmentMain.22
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                Log.i(VersionFragmentMain.TAG, "authorityDialog: PrimaryBtnClick");
                VersionFragmentMain.this.hasClickAuthorized = true;
                if (VersionFragmentMain.this.extendedDialog != null && VersionFragmentMain.this.extendedDialog.isShowing()) {
                    VersionFragmentMain.this.extendedDialog.dismiss();
                }
                if (VersionFragmentMain.this.ejectionDialog != null && VersionFragmentMain.this.ejectionDialog.isShowing()) {
                    VersionFragmentMain.this.ejectionDialog.dismiss();
                }
                if (str3.equals("switchSensorMicrophone")) {
                    Settings.Global.putInt(VersionFragmentMain.this.mContext.getContentResolver(), PermissionUtil.SWITCH_SENSOR_MICROPHONE_CHECKED, 0);
                    Settings.Global.putLong(VersionFragmentMain.this.mContext.getContentResolver(), PermissionUtil.SENSOR_MIC_AUTHORIZED_TIME, 0L);
                    VersionFragmentMain.this.itemMic.setAssitText(VersionFragmentMain.this.getString(R.string.unauthorized));
                    PermissionUtil.getInstance().revokeRuntimePermission(PermissionUtil.RECORD_AUDIO);
                    VersionFragmentMain.this.micSwitch.setChecked(false);
                    VersionFragmentMain.this.mMicAuthorizedRemainDay = 0;
                    EventTrackUtils.eventTrackValueChange("麦克风", false);
                } else if (str3.equals("switchSensorCamera")) {
                    Settings.Global.putInt(VersionFragmentMain.this.mContext.getContentResolver(), PermissionUtil.SWITCH_SENSOR_CAMERA_CHECKED, 0);
                    Settings.Global.putLong(VersionFragmentMain.this.mContext.getContentResolver(), PermissionUtil.SENSOR_CAMERA_AUTHORIZED_TIME, 0L);
                    VersionFragmentMain.this.itemCamera.setAssitText(VersionFragmentMain.this.getString(R.string.unauthorized));
                    PermissionUtil.getInstance().revokeRuntimePermission(PermissionUtil.CAMERA);
                    VersionFragmentMain.this.cameraSwitch.setChecked(false);
                    VersionFragmentMain.this.mCameraAuthorizedRemainDay = 0;
                    EventTrackUtils.eventTrackValueChange("摄像头", false);
                } else if (str3.equals("permissionLocation")) {
                    Settings.Global.putInt(VersionFragmentMain.this.mContext.getContentResolver(), PermissionUtil.SWITCH_PERMISSION_LOCATION_CHECKED, 0);
                    Settings.Global.putLong(VersionFragmentMain.this.mContext.getContentResolver(), PermissionUtil.SENSOR_LOCATION_AUTHORIZED_TIME, 0L);
                    VersionFragmentMain.this.itemLocation.setAssitText(VersionFragmentMain.this.getString(R.string.unauthorized));
                    PermissionUtil.getInstance().revokeRuntimePermission(PermissionUtil.ACCESS_FINE_LOCATION);
                    PermissionUtil.getInstance().revokeRuntimePermission(PermissionUtil.ACCESS_COARSE_LOCATION);
                    VersionFragmentMain.this.locationSwitch.setChecked(false);
                    VersionFragmentMain.this.mLocationAuthorizedRemainDay = 0;
                    PermissionLocationManager.INSTANCE.getInstance().doLocation();
                    EventTrackUtils.eventTrackValueChange("权限设置_定位", false);
                }
                VersionFragmentMain.this.authorityDialog.dismiss();
            }
        });
        this.authorityDialog.setOnSecondaryBtnClickListener(new View.OnClickListener() { // from class: com.hozon.settings.fragment.VersionFragmentMain.23
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                Log.d(VersionFragmentMain.TAG, "authorityDialog: SecondaryBtnClick");
                VersionFragmentMain.this.hasClickAuthorized = true;
                if (str3.equals("switchSensorMicrophone")) {
                    VersionFragmentMain.this.micSwitch.setChecked(true ^ VersionFragmentMain.this.micSwitch.isChecked());
                } else if (str3.equals("switchSensorCamera")) {
                    VersionFragmentMain.this.cameraSwitch.setChecked(true ^ VersionFragmentMain.this.cameraSwitch.isChecked());
                } else if (str3.equals("permissionLocation")) {
                    VersionFragmentMain.this.locationSwitch.setChecked(true ^ VersionFragmentMain.this.locationSwitch.isChecked());
                }
                VersionFragmentMain.this.authorityDialog.dismiss();
            }
        });
        this.authorityDialog.setOnDismissListener(new DialogInterface.OnDismissListener() { // from class: com.hozon.settings.fragment.-$$Lambda$VersionFragmentMain$lN7QFzv5nIIuPc0GcvQK1fM6H6k
            @Override // android.content.DialogInterface.OnDismissListener
            public final void onDismiss(DialogInterface dialogInterface) {
                VersionFragmentMain.this.lambda$showAuthorizedDialog$0$VersionFragmentMain(str3, dialogInterface);
            }
        });
    }

    public /* synthetic */ void lambda$showAuthorizedDialog$0$VersionFragmentMain(String str, DialogInterface dialogInterface) {
        if (!this.hasClickAuthorized) {
            if (str.equals("switchSensorMicrophone")) {
                this.micSwitch.setChecked(!r1.isChecked());
            } else if (str.equals("switchSensorCamera")) {
                this.cameraSwitch.setChecked(!r1.isChecked());
            } else if (str.equals("permissionLocation")) {
                this.locationSwitch.setChecked(!r1.isChecked());
            }
        }
        this.hasClickAuthorized = false;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void showMultiSelectDialog(final String str, final String str2) {
        StandardDialog standardDialog = this.authorityDialog;
        if (standardDialog != null && standardDialog.isShowing()) {
            this.authorityDialog.dismiss();
        }
        StandardDialog standardDialog2 = this.ejectionDialog;
        if (standardDialog2 == null) {
            View inflate = View.inflate(getActivity(), R.layout.layout_ejection_content, null);
            ArrayList<String> arrayList = new ArrayList<>();
            this.dataList = arrayList;
            arrayList.add("120" + getString(R.string.next_annual_day));
            this.dataList.add("364" + getString(R.string.next_annual_day));
            RadioButtonContainer radioButtonContainer = (RadioButtonContainer) inflate.findViewById(R.id.driver_mode_radio);
            this.multiSelectContainer = radioButtonContainer;
            radioButtonContainer.setData(this.dataList);
            this.multiSelectContainer.addSelectListener(new RadioButtonContainer.OnRadioSelectListener() { // from class: com.hozon.settings.fragment.VersionFragmentMain.24
                @Override // com.hozonauto.widget.RadioButtonContainer.OnRadioSelectListener
                public void onSelect(String str3, int i) {
                    Logger.t(VersionFragmentMain.TAG).d("onSelect: %s %s %s", str, str3, Integer.valueOf(i));
                    VersionFragmentMain.this.mSelectPosition = i + 1;
                    VersionFragmentMain.this.ejectionDialog.setPrimaryBtnClickable(VersionFragmentMain.this.mSelectPosition != -1);
                }
            });
            this.ejectionDialog = new StandardDialog.Builder().setTitle(str).addContentView(inflate).setPrimaryBtnStyle(0).setSecondaryBtnStyle(1).setPrimaryBtnText(getString(R.string.confirm)).setSecondaryBtnText(getString(R.string.cancel)).setPrimaryBtnClickable(false).build(getActivity());
        } else {
            if (standardDialog2.isShowing()) {
                if (this.lastType.equals("switchSensorLocation")) {
                    ThreadUtils.postDelayed(new Runnable() { // from class: com.hozon.settings.fragment.VersionFragmentMain.25
                        @Override // java.lang.Runnable
                        public void run() {
                            VersionFragmentMain.this.locationSwitch.setChecked(!VersionFragmentMain.this.locationSwitch.isChecked());
                        }
                    }, 50L);
                } else if (this.lastType.equals("switchSensorMicrophone")) {
                    ThreadUtils.postDelayed(new Runnable() { // from class: com.hozon.settings.fragment.VersionFragmentMain.26
                        @Override // java.lang.Runnable
                        public void run() {
                            VersionFragmentMain.this.micSwitch.setChecked(!VersionFragmentMain.this.micSwitch.isChecked());
                        }
                    }, 50L);
                } else if (this.lastType.equals("switchSensorCamera")) {
                    ThreadUtils.postDelayed(new Runnable() { // from class: com.hozon.settings.fragment.VersionFragmentMain.27
                        @Override // java.lang.Runnable
                        public void run() {
                            VersionFragmentMain.this.cameraSwitch.setChecked(!VersionFragmentMain.this.cameraSwitch.isChecked());
                        }
                    }, 50L);
                }
            }
            this.ejectionDialog.setTitle(str);
            RadioButtonContainer radioButtonContainer2 = this.multiSelectContainer;
            if (radioButtonContainer2 != null) {
                radioButtonContainer2.clear();
                this.mSelectPosition = -1;
                this.ejectionDialog.setPrimaryBtnClickable(false);
            }
        }
        str2.hashCode();
        switch (str2) {
            case "switchSensorCamera":
            case "switchSensorMicrophone":
            case "switchSensorLocation":
                this.lastType = str2;
                break;
        }
        if (!this.ejectionDialog.isShowing()) {
            this.ejectionDialog.show();
        }
        this.ejectionDialog.setOnPrimaryBtnClickListener(new View.OnClickListener() { // from class: com.hozon.settings.fragment.-$$Lambda$VersionFragmentMain$5hmNQjlE1UZayydMyjTa9v0nuL8
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                VersionFragmentMain.this.lambda$showMultiSelectDialog$1$VersionFragmentMain(str2, view);
            }
        });
        this.ejectionDialog.setOnSecondaryBtnClickListener(new View.OnClickListener() { // from class: com.hozon.settings.fragment.-$$Lambda$VersionFragmentMain$GjrjkNyd9F6xi0WKNyw3njzVjso
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                VersionFragmentMain.this.lambda$showMultiSelectDialog$2$VersionFragmentMain(str2, view);
            }
        });
        this.ejectionDialog.setOnDismissListener(new DialogInterface.OnDismissListener() { // from class: com.hozon.settings.fragment.-$$Lambda$VersionFragmentMain$IqhJr2nL3Rv4g1aNO29ITNABoHY
            @Override // android.content.DialogInterface.OnDismissListener
            public final void onDismiss(DialogInterface dialogInterface) {
                VersionFragmentMain.this.lambda$showMultiSelectDialog$3$VersionFragmentMain(str2, dialogInterface);
            }
        });
    }

    public /* synthetic */ void lambda$showMultiSelectDialog$1$VersionFragmentMain(String str, View view) {
        Log.i(TAG, "Select: " + this.mSelectPosition);
        this.hasClickEjection = true;
        handleSelect(str, this.mSelectPosition);
        this.ejectionDialog.dismiss();
    }

    public /* synthetic */ void lambda$showMultiSelectDialog$2$VersionFragmentMain(String str, View view) {
        this.hasClickEjection = true;
        if (str.equals("switchSensorLocation")) {
            SwitchButton switchButton = this.locationSwitch;
            switchButton.setChecked(true ^ switchButton.isChecked());
        } else if (str.equals("switchSensorMicrophone")) {
            SwitchButton switchButton2 = this.micSwitch;
            switchButton2.setChecked(true ^ switchButton2.isChecked());
        } else if (str.equals("switchSensorCamera")) {
            SwitchButton switchButton3 = this.cameraSwitch;
            switchButton3.setChecked(true ^ switchButton3.isChecked());
        }
        this.ejectionDialog.dismiss();
    }

    public /* synthetic */ void lambda$showMultiSelectDialog$3$VersionFragmentMain(String str, DialogInterface dialogInterface) {
        this.lastType = "";
        if (!this.hasClickEjection) {
            if (str.equals("switchSensorLocation")) {
                this.locationSwitch.setChecked(!r1.isChecked());
            } else if (str.equals("switchSensorMicrophone")) {
                this.micSwitch.setChecked(!r1.isChecked());
            } else if (str.equals("switchSensorCamera")) {
                this.cameraSwitch.setChecked(!r1.isChecked());
            }
        }
        this.hasClickEjection = false;
    }

    private void handleSelect(String str, int i) {
        int i2;
        i2 = i == 1 ? 120 : 364;
        str.hashCode();
        switch (str) {
            case "itemSensorCamera":
            case "switchSensorCamera":
                Settings.Global.putInt(this.mContext.getContentResolver(), PermissionUtil.SWITCH_SENSOR_CAMERA_CHECKED, 1);
                this.itemCamera.setAssitText(i2 + getString(R.string.next_annual_day));
                this.mCameraAuthorizedRemainDay = i2;
                Settings.Global.putLong(this.mContext.getContentResolver(), PermissionUtil.SENSOR_CAMERA_AUTHORIZED_TIME, System.currentTimeMillis() + (i2 * 24 * 60 * 60 * 1000));
                PermissionUtil.getInstance().grantRuntimePermission(PermissionUtil.CAMERA);
                this.cameraSwitch.setChecked(true);
                EventTrackUtils.eventTrackValueChange("摄像头", true);
                EventTrackUtils.eventTrackValueChange("摄像头授权时长", i2 + getString(R.string.next_annual_day));
                break;
            case "itemSensorMicrophone":
            case "switchSensorMicrophone":
                Settings.Global.putInt(this.mContext.getContentResolver(), PermissionUtil.SWITCH_SENSOR_MICROPHONE_CHECKED, 1);
                this.itemMic.setAssitText(i2 + getString(R.string.next_annual_day));
                this.mMicAuthorizedRemainDay = i2;
                Settings.Global.putLong(this.mContext.getContentResolver(), PermissionUtil.SENSOR_MIC_AUTHORIZED_TIME, System.currentTimeMillis() + (i2 * 24 * 60 * 60 * 1000));
                PermissionUtil.getInstance().grantRuntimePermission(PermissionUtil.RECORD_AUDIO);
                this.micSwitch.setChecked(true);
                Logger.t(TAG).i("onChange: send mic broadcast", new Object[0]);
                PermissionUtil.getInstance().sendPermissionBroadcast(PermissionUtil.BROADCAST_PERMISSION_ON_RECORD_AUDIO);
                EventTrackUtils.eventTrackValueChange("麦克风", true);
                EventTrackUtils.eventTrackValueChange("麦克风授权时长", i2 + getString(R.string.next_annual_day));
                break;
            case "itemSensorLocation":
            case "switchSensorLocation":
                Settings.Global.putInt(this.mContext.getContentResolver(), PermissionUtil.SWITCH_PERMISSION_LOCATION_CHECKED, 1);
                this.itemLocation.setAssitText(i2 + getString(R.string.next_annual_day));
                this.mLocationAuthorizedRemainDay = i2;
                Settings.Global.putLong(this.mContext.getContentResolver(), PermissionUtil.SENSOR_LOCATION_AUTHORIZED_TIME, System.currentTimeMillis() + (i2 * 24 * 60 * 60 * 1000));
                PermissionUtil.getInstance().grantRuntimePermission(PermissionUtil.ACCESS_FINE_LOCATION);
                PermissionUtil.getInstance().grantRuntimePermission(PermissionUtil.ACCESS_COARSE_LOCATION);
                this.locationSwitch.setChecked(true);
                EventTrackUtils.eventTrackValueChange("权限设置_定位", true);
                EventTrackUtils.eventTrackValueChange("定位授权时长", i2 + getString(R.string.next_annual_day));
                break;
        }
    }

    private void showExtendedDialog(String str, String str2, final String str3) {
        StandardDialog standardDialog = this.extendedDialog;
        if (standardDialog != null && standardDialog.isShowing()) {
            this.extendedDialog.dismiss();
        }
        StandardDialog build = new StandardDialog.Builder().setTitle(str).setContentText(str2).setContentGravity(17).setContentTextGravity(3).setBtnNum(2).setPrimaryBtnText(getString(R.string.confirm)).setSecondaryBtnText(getString(R.string.cancel)).setPrimaryBtnStyle(0).setSecondaryBtnStyle(1).setCancelable(false).build(getContext());
        this.extendedDialog = build;
        build.show();
        this.extendedDialog.setOnPrimaryBtnClickListener(new View.OnClickListener() { // from class: com.hozon.settings.fragment.VersionFragmentMain.28
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                String str4 = str3;
                str4.hashCode();
                if (str4.equals("cameraAuthorizedTime")) {
                    VersionFragmentMain.this.mCameraAuthorizedRemainDay = 364;
                    VersionFragmentMain.this.itemCamera.setAssitText("364" + VersionFragmentMain.this.getString(R.string.next_annual_day));
                    Settings.Global.putLong(VersionFragmentMain.this.mContext.getContentResolver(), PermissionUtil.SENSOR_CAMERA_AUTHORIZED_TIME, System.currentTimeMillis() + RelativeDateTimeFormatter.YEAR_IN_MILLIS);
                } else if (str4.equals("micAuthorizedTime")) {
                    VersionFragmentMain.this.mMicAuthorizedRemainDay = 364;
                    VersionFragmentMain.this.itemMic.setAssitText("364" + VersionFragmentMain.this.getString(R.string.next_annual_day));
                    Settings.Global.putLong(VersionFragmentMain.this.mContext.getContentResolver(), PermissionUtil.SENSOR_MIC_AUTHORIZED_TIME, System.currentTimeMillis() + RelativeDateTimeFormatter.YEAR_IN_MILLIS);
                }
                VersionFragmentMain.this.extendedDialog.dismiss();
            }
        });
        this.extendedDialog.setOnSecondaryBtnClickListener(new View.OnClickListener() { // from class: com.hozon.settings.fragment.VersionFragmentMain.29
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                VersionFragmentMain.this.extendedDialog.dismiss();
            }
        });
    }

    public void setVolumeAsync(int i, int i2, int i3, int i4) {
        Logger.t(TAG).d("run: setVolumeAsync %d,%d,%d,%d", Integer.valueOf(i), Integer.valueOf(i2), Integer.valueOf(i3), Integer.valueOf(i4));
        AudioUtil.setDefaultMediaVolume(i);
        AudioUtil.setDefaultNavVolume(i2);
        AudioUtil.setDefaultVoiceVolume(i3);
        AudioUtil.setDefaultCallVolume(i4);
    }
}
