<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    android:sharedUserId="android.uid.system"
    android:versionCode="100320"
    android:versionName="1.3.20"
    android:compileSdkVersion="30"
    android:compileSdkVersionCodename="11"
    package="com.hozon.settings"
    platformBuildVersionCode="30"
    platformBuildVersionName="11">
    <uses-sdk
        android:minSdkVersion="30"
        android:targetSdkVersion="30"/>
    <uses-permission android:name="com.hozon.recommend.script"/>
    <uses-permission android:name="android.permission.BLUETOOTH"/>
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN"/>
    <uses-permission android:name="android.permission.CALL_PHONE"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="Manifest.permission.BLUETOOTH_PRIVILEGED"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.REQUEST_NETWORK_SCORES"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.BACKUP"/>
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>
    <uses-permission android:name="android.permission.CLEAR_APP_USER_DATA"/>
    <uses-permission android:name="android.permission.DELETE_CACHE_FILES"/>
    <uses-permission android:name="android.permission.DUMP"/>
    <uses-permission android:name="android.permission.FORCE_STOP_PACKAGES"/>
    <uses-permission android:name="android.permission.GET_ACCOUNTS_PRIVILEGED"/>
    <uses-permission android:name="android.permission.GET_PACKAGE_SIZE"/>
    <uses-permission android:name="android.permission.INJECT_EVENTS"/>
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.MANAGE_ACCOUNTS"/>
    <uses-permission android:name="android.permission.MANAGE_USERS"/>
    <uses-permission android:name="android.permission.MASTER_CLEAR"/>
    <uses-permission android:name="android.permission.NETWORK_SETTINGS"/>
    <uses-permission android:name="android.permission.OVERRIDE_WIFI_CONFIG"/>
    <uses-permission android:name="android.permission.READ_CONTACTS"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.REBOOT"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.SET_PREFERRED_APPLICATIONS"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS"/>
    <uses-permission android:name="android.permission.START_FOREGROUND"/>
    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE"/>
    <uses-permission android:name="android.permission.WRITE_SETTINGS"/>
    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.CONTROL_DISPLAY_BRIGHTNESS"/>
    <uses-permission android:name="android.car.permission.CONTROL_CAR_CLIMATE"/>
    <uses-permission android:name="android.car.permission.CAR_VENDOR_EXTENSION"/>
    <uses-permission android:name="android.car.permission.CONTROL_CARLAN"/>
    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>
    <uses-permission android:name="android.Manifest.permission.NETWORK_SETTINGS"/>
    <uses-permission android:name="android.Manifest.permission.UPDATE_DEVICE_STATS"/>
    <uses-permission android:name="com.tencent.wecarflow.PLAY_CONTROL"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"/>
    <uses-permission
        android:name="com.hozon.permission.push"
        android:protectionLevel="normal"/>
    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW"/>
    <uses-permission android:name="com.qiku.permission.READ_STEALTH_MODE"/>
    <uses-permission android:name="hozon.permission.settings.personalization"/>
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false"/>
    <uses-feature
        android:name="android.hardware.camera.front"
        android:required="false"/>
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false"/>
    <uses-feature
        android:name="android.hardware.camera.flash"
        android:required="false"/>
    <uses-feature
        android:name="android.hardware.screen.landscape"
        android:required="false"/>
    <uses-feature
        android:name="android.hardware.wifi"
        android:required="false"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <application
        android:theme="@style/card_activity_styles"
        android:label="@string/app_name"
        android:icon="@mipmap/ic_launcher"
        android:name="com.hozon.settings.APP"
        android:persistent="true"
        android:debuggable="false"
        android:allowBackup="true"
        android:supportsRtl="true"
        android:extractNativeLibs="false"
        android:usesCleartextTraffic="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory">
        <uses-library
            android:name="cn.com.jit.iot.v2x.V2XSdkImpl"
            android:required="false"/>
        <uses-library android:name="com.hozon.tboxmanagerlib"/>
        <activity
            android:name="com.hozon.settings.MainActivity"
            android:launchMode="singleTask"
            android:configChanges="fontScale|density|smallestScreenSize|screenSize|uiMode|screenLayout|orientation">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.settings.BLUETOOTH_SETTINGS"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.settings.WIFI_SETTINGS"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.settings.AP_SETTINGS"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
            <intent-filter>
                <action android:name="hozonauto.intent.action.LINK_IN_SETTING"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
            <intent-filter>
                <action android:name="com.hozon.SOUND_FRAGMENT"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>
        <activity
            android:name="com.hozon.settings.TractionActivity"
            android:launchMode="singleTask"
            android:configChanges="fontScale|density|smallestScreenSize|screenSize|uiMode|screenLayout|orientation"/>
        <receiver android:name="com.hozon.settings.system.receiver.BootCompletedReceiver">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
                <action android:name="hozon.intent.action.SWC_CUSTOM_KEY"/>
            </intent-filter>
        </receiver>
        <receiver android:name="com.hozon.settings.widget.OneClickShoutWidgetProvider">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE"/>
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/one_click_shout_widget_info"/>
        </receiver>
        <service
            android:name="com.hozon.settings.SettingService"
            android:permission="hozon.permission.settings.settingService"
            android:singleUser="true">
            <intent-filter>
                <action android:name="com.hozon.settings.personalization.settingService"/>
            </intent-filter>
        </service>
        <service
            android:name="com.hozon.settings.system.bluetooth.BtService"
            android:enabled="true"/>
        <service
            android:name="com.hozon.settings.system.wlan.WlanService"
            android:enabled="true"/>
        <service
            android:name="com.hozon.settings.system.hotspot.ApService"
            android:enabled="true"/>
        <activity
            android:label="@string/title_activity_debug"
            android:name="com.hozon.settings.DebugActivity"
            android:exported="true"
            android:configChanges="fontScale|density|smallestScreenSize|screenSize|screenLayout|orientation">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <service
            android:name="com.hozon.settings.personalization.PersonalizedService"
            android:permission="hozon.permission.settings.personalization"
            android:exported="true">
            <intent-filter>
                <action android:name="com.hozon.settings.personalization.PersonalizedService"/>
            </intent-filter>
        </service>
        <service
            android:name="com.hozon.settings.widget.WidgetService"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.hozon.settings.widget.WeightService"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </service>
        <receiver
            android:name="com.hozon.settings.system.receiver.NetworkChangedReceiver"
            android:enabled="true"
            android:exported="true"/>
        <receiver android:name="com.hozon.settings.widget.RearFogLampWidgetProvider">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE"/>
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/rear_fog_lamp_widget_info"/>
        </receiver>
        <receiver android:name="com.hozon.settings.widget.LightModeWidgetProvider">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE"/>
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/light_widget_info"/>
        </receiver>
        <receiver android:name="com.hozon.settings.widget.EnergyModeWidgetProvider">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE"/>
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/energy_widget_info"/>
        </receiver>
        <receiver android:name="com.hozon.settings.widget.ShutDownWidgetProvider">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE"/>
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/shut_down_widget_info"/>
        </receiver>
        <receiver android:name="com.hozon.settings.widget.WifiWidgetProvider">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE"/>
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/wifi_widget_info"/>
        </receiver>
        <receiver android:name="com.hozon.settings.widget.HotspotWidgetProvider">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE"/>
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/hotspot_widget_info"/>
        </receiver>
        <receiver android:name="com.hozon.settings.widget.BluetoothWidgetProvider">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE"/>
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/bluetooth_widget_info"/>
        </receiver>
        <receiver android:name="com.hozon.settings.widget.HdcWidgetProvider">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE"/>
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/hdc_widget_info"/>
        </receiver>
        <receiver android:name="com.hozon.settings.widget.AutoHoldWidgetProvider">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE"/>
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/hdc_widget_info"/>
        </receiver>
        <receiver
            android:name="com.hozon.settings.system.receiver.ThemeChangedReceiver"
            android:enabled="true"
            android:exported="true"/>
        <receiver android:name="com.hozon.settings.widget.SkyWindowWidgetProvider">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE"/>
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/sky_window_widget_info"/>
        </receiver>
        <receiver android:name="com.hozon.settings.widget.SunshadeWidgetProvider">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE"/>
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/sunshade_widget_info"/>
        </receiver>
        <receiver android:name="com.hozon.settings.widget.EffectModeWidgetProvider">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE"/>
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/effect_mode_widget_info"/>
        </receiver>
        <activity
            android:theme="@style/zxing_CaptureTheme"
            android:name="com.journeyapps.barcodescanner.CaptureActivity"
            android:clearTaskOnLaunch="true"
            android:stateNotNeeded="true"
            android:screenOrientation="sensorLandscape"
            android:windowSoftInputMode="stateAlwaysHidden"/>
        <provider
            android:name="androidx.work.impl.WorkManagerInitializer"
            android:exported="false"
            android:multiprocess="true"
            android:authorities="com.hozon.settings.workmanager-init"
            android:directBootAware="false"/>
        <service
            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false"
            android:directBootAware="false"/>
        <service
            android:name="androidx.work.impl.background.systemjob.SystemJobService"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:enabled="@bool/enable_system_job_service_default"
            android:exported="true"
            android:directBootAware="false"/>
        <service
            android:name="androidx.work.impl.foreground.SystemForegroundService"
            android:enabled="@bool/enable_system_foreground_service_default"
            android:exported="false"
            android:directBootAware="false"/>
        <receiver
            android:name="androidx.work.impl.utils.ForceStopRunnable.BroadcastReceiver"
            android:enabled="true"
            android:exported="false"
            android:directBootAware="false"/>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryChargingProxy"
            android:enabled="false"
            android:exported="false"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED"/>
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryNotLowProxy"
            android:enabled="false"
            android:exported="false"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="android.intent.action.BATTERY_OKAY"/>
                <action android:name="android.intent.action.BATTERY_LOW"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.StorageNotLowProxy"
            android:enabled="false"
            android:exported="false"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="android.intent.action.DEVICE_STORAGE_LOW"/>
                <action android:name="android.intent.action.DEVICE_STORAGE_OK"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.NetworkStateProxy"
            android:enabled="false"
            android:exported="false"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
            android:enabled="false"
            android:exported="false"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
                <action android:name="android.intent.action.TIME_SET"/>
                <action android:name="android.intent.action.TIMEZONE_CHANGED"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
            android:permission="android.permission.DUMP"
            android:enabled="true"
            android:exported="true"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS"/>
            </intent-filter>
        </receiver>
        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:exported="false"
            android:directBootAware="true"/>
    </application>
</manifest>
