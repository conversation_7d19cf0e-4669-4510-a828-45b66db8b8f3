{"schemaVersion": "1.0.0", "buildSystem": "<PERSON><PERSON><PERSON>", "buildSystemVersion": "6.5", "buildPlugin": "org.jetbrains.kotlin.gradle.plugin.KotlinAndroidPluginWrapper", "buildPluginVersion": "1.6.10", "projectSettings": {"isHmppEnabled": false, "isCompatibilityMetadataVariantEnabled": true}, "projectTargets": [{"target": "org.jetbrains.kotlin.gradle.plugin.mpp.KotlinAndroidTarget", "platformType": "androidJvm", "extras": {"android": {"sourceCompatibility": "1.8", "targetCompatibility": "1.8"}}}]}