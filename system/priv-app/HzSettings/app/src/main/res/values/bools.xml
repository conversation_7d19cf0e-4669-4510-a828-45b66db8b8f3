<?xml version="1.0" encoding="utf-8"?>
<resources>
    <bool name="abc_action_bar_embed_tabs">true</bool>
    <bool name="abc_allow_stacked_button_bar">false</bool>
    <bool name="abc_config_actionMenuItemAllCaps">true</bool>
    <bool name="enable_system_alarm_service_default">false</bool>
    <bool name="enable_system_foreground_service_default">true</bool>
    <bool name="enable_system_job_service_default">true</bool>
    <bool name="mtrl_btn_textappearance_all_caps">true</bool>
    <bool name="ro.hozon.car.adas.aeb">false</bool>
    <bool name="ro.hozon.car.adas.dow">false</bool>
    <bool name="ro.hozon.car.adas.fcta">false</bool>
    <bool name="ro.hozon.car.adas.fcw">false</bool>
    <bool name="ro.hozon.car.adas.hba">false</bool>
    <bool name="ro.hozon.car.adas.hwa">false</bool>
    <bool name="ro.hozon.car.adas.ica">false</bool>
    <bool name="ro.hozon.car.adas.lca">false</bool>
    <bool name="ro.hozon.car.adas.ldw">false</bool>
    <bool name="ro.hozon.car.adas.lka">false</bool>
    <bool name="ro.hozon.car.adas.ncp.city">false</bool>
    <bool name="ro.hozon.car.adas.nnp.highway">false</bool>
    <bool name="ro.hozon.car.adas.rcta">false</bool>
    <bool name="ro.hozon.car.adas.rcw">false</bool>
    <bool name="ro.hozon.car.adas.tsr">false</bool>
    <bool name="ro.hozon.car.sentinelmode">false</bool>
    <bool name="workmanager_test_configuration">false</bool>
</resources>
