<?xml version="1.0" encoding="utf-8"?>
<resources>
    <attr name="SharedValue" format="integer">
    </attr>
    <attr name="SharedValueId" format="reference">
    </attr>
    <attr name="actionBarDivider" format="reference">
    </attr>
    <attr name="actionBarItemBackground" format="reference">
    </attr>
    <attr name="actionBarPopupTheme" format="reference">
    </attr>
    <attr name="actionBarSize" format="dimension">
        <enum name="wrap_content" value="0" />
    </attr>
    <attr name="actionBarSplitStyle" format="reference">
    </attr>
    <attr name="actionBarStyle" format="reference">
    </attr>
    <attr name="actionBarTabBarStyle" format="reference">
    </attr>
    <attr name="actionBarTabStyle" format="reference">
    </attr>
    <attr name="actionBarTabTextStyle" format="reference">
    </attr>
    <attr name="actionBarTheme" format="reference">
    </attr>
    <attr name="actionBarWidgetTheme" format="reference">
    </attr>
    <attr name="actionButtonStyle" format="reference">
    </attr>
    <attr name="actionDropDownStyle" format="reference">
    </attr>
    <attr name="actionLayout" format="reference">
    </attr>
    <attr name="actionMenuTextAppearance" format="reference">
    </attr>
    <attr name="actionMenuTextColor" format="reference|color">
    </attr>
    <attr name="actionModeBackground" format="reference">
    </attr>
    <attr name="actionModeCloseButtonStyle" format="reference">
    </attr>
    <attr name="actionModeCloseDrawable" format="reference">
    </attr>
    <attr name="actionModeCopyDrawable" format="reference">
    </attr>
    <attr name="actionModeCutDrawable" format="reference">
    </attr>
    <attr name="actionModeFindDrawable" format="reference">
    </attr>
    <attr name="actionModePasteDrawable" format="reference">
    </attr>
    <attr name="actionModePopupWindowStyle" format="reference">
    </attr>
    <attr name="actionModeSelectAllDrawable" format="reference">
    </attr>
    <attr name="actionModeShareDrawable" format="reference">
    </attr>
    <attr name="actionModeSplitBackground" format="reference">
    </attr>
    <attr name="actionModeStyle" format="reference">
    </attr>
    <attr name="actionModeWebSearchDrawable" format="reference">
    </attr>
    <attr name="actionOverflowButtonStyle" format="reference">
    </attr>
    <attr name="actionOverflowMenuStyle" format="reference">
    </attr>
    <attr name="actionProviderClass" format="string">
    </attr>
    <attr name="actionTextColorAlpha" format="float">
    </attr>
    <attr name="actionViewClass" format="string">
    </attr>
    <attr name="activityChooserViewStyle" format="reference">
    </attr>
    <attr name="actualImageResource" format="reference">
    </attr>
    <attr name="actualImageScaleType">
        <enum name="center" value="4" />
        <enum name="centerCrop" value="6" />
        <enum name="centerInside" value="5" />
        <enum name="fitBottomStart" value="8" />
        <enum name="fitCenter" value="2" />
        <enum name="fitEnd" value="3" />
        <enum name="fitStart" value="1" />
        <enum name="fitXY" value="0" />
        <enum name="focusCrop" value="7" />
        <enum name="none" value="-1" />
    </attr>
    <attr name="actualImageUri" format="string">
    </attr>
    <attr name="aids_card_assist_text" format="string">
    </attr>
    <attr name="aids_card_icon" format="reference">
    </attr>
    <attr name="aids_card_title" format="string">
    </attr>
    <attr name="alertDialogButtonGroupStyle" format="reference">
    </attr>
    <attr name="alertDialogCenterButtons" format="boolean">
    </attr>
    <attr name="alertDialogStyle" format="reference">
    </attr>
    <attr name="alertDialogTheme" format="reference">
    </attr>
    <attr name="allowStacking" format="boolean">
    </attr>
    <attr name="alpha" format="float">
    </attr>
    <attr name="alphabeticModifiers">
        <flag name="ALT" value="0x2" />
        <flag name="CTRL" value="0x1000" />
        <flag name="FUNCTION" value="0x8" />
        <flag name="META" value="0x10000" />
        <flag name="SHIFT" value="0x1" />
        <flag name="SYM" value="0x4" />
    </attr>
    <attr name="altSrc" format="reference">
    </attr>
    <attr name="amPmBackgroundColor" format="color">
    </attr>
    <attr name="amPmTextColor" format="color">
    </attr>
    <attr name="animateCircleAngleTo">
        <enum name="antiClockwise" value="3" />
        <enum name="bestChoice" value="0" />
        <enum name="clockwise" value="2" />
        <enum name="closest" value="1" />
        <enum name="constraint" value="4" />
    </attr>
    <attr name="animateRelativeTo" format="reference">
    </attr>
    <attr name="animationMode">
        <enum name="fade" value="1" />
        <enum name="slide" value="0" />
    </attr>
    <attr name="appBarLayoutStyle" format="reference">
    </attr>
    <attr name="appWidgetBackgroundColor" format="color">
    </attr>
    <attr name="appWidgetTextColor" format="color">
    </attr>
    <attr name="applyMotionScene" format="boolean">
    </attr>
    <attr name="arcMode">
        <enum name="flip" value="2" />
        <enum name="startHorizontal" value="1" />
        <enum name="startVertical" value="0" />
    </attr>
    <attr name="arrowHeadLength" format="dimension">
    </attr>
    <attr name="arrowShaftLength" format="dimension">
    </attr>
    <attr name="attributeName" format="string">
    </attr>
    <attr name="autoCompleteMode">
        <enum name="continuousVelocity" value="0" />
        <enum name="spring" value="1" />
    </attr>
    <attr name="autoCompleteTextViewStyle" format="reference">
    </attr>
    <attr name="autoSizeMaxTextSize" format="dimension">
    </attr>
    <attr name="autoSizeMinTextSize" format="dimension">
    </attr>
    <attr name="autoSizePresetSizes" format="reference">
    </attr>
    <attr name="autoSizeStepGranularity" format="dimension">
    </attr>
    <attr name="autoSizeTextType">
        <enum name="none" value="0" />
        <enum name="uniform" value="1" />
    </attr>
    <attr name="autoTransition">
        <enum name="animateToEnd" value="4" />
        <enum name="animateToStart" value="3" />
        <enum name="jumpToEnd" value="2" />
        <enum name="jumpToStart" value="1" />
        <enum name="none" value="0" />
    </attr>
    <attr name="awv_centerTextColor" format="integer">
    </attr>
    <attr name="awv_dividerTextColor" format="integer">
    </attr>
    <attr name="awv_initialPosition" format="integer">
    </attr>
    <attr name="awv_isCurve" format="boolean">
    </attr>
    <attr name="awv_isLoop" format="boolean">
    </attr>
    <attr name="awv_itemsVisibleCount" format="integer">
    </attr>
    <attr name="awv_lineSpace" format="float">
    </attr>
    <attr name="awv_outerTextColor" format="integer">
    </attr>
    <attr name="awv_scaleX" format="float">
    </attr>
    <attr name="awv_textsize" format="integer">
    </attr>
    <attr name="background" format="reference">
    </attr>
    <attr name="backgroundColor" format="color">
    </attr>
    <attr name="backgroundImage" format="reference">
    </attr>
    <attr name="backgroundInsetBottom" format="dimension">
    </attr>
    <attr name="backgroundInsetEnd" format="dimension">
    </attr>
    <attr name="backgroundInsetStart" format="dimension">
    </attr>
    <attr name="backgroundInsetTop" format="dimension">
    </attr>
    <attr name="backgroundOverlayColorAlpha" format="float">
    </attr>
    <attr name="backgroundSplit" format="reference|color">
    </attr>
    <attr name="backgroundStacked" format="reference|color">
    </attr>
    <attr name="backgroundTint" format="color">
    </attr>
    <attr name="backgroundTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="badgeGravity">
        <enum name="BOTTOM_END" value="8388693" />
        <enum name="BOTTOM_START" value="8388691" />
        <enum name="TOP_END" value="8388661" />
        <enum name="TOP_START" value="8388659" />
    </attr>
    <attr name="badgeStyle" format="reference">
    </attr>
    <attr name="badgeTextColor" format="color">
    </attr>
    <attr name="banner_auto_loop" format="boolean">
    </attr>
    <attr name="banner_indicator_gravity">
        <enum name="center" value="1" />
        <enum name="left" value="0" />
        <enum name="right" value="2" />
    </attr>
    <attr name="banner_indicator_height" format="dimension">
    </attr>
    <attr name="banner_indicator_margin" format="dimension">
    </attr>
    <attr name="banner_indicator_marginBottom" format="dimension">
    </attr>
    <attr name="banner_indicator_marginLeft" format="dimension">
    </attr>
    <attr name="banner_indicator_marginRight" format="dimension">
    </attr>
    <attr name="banner_indicator_marginTop" format="dimension">
    </attr>
    <attr name="banner_indicator_normal_color" format="reference|color">
    </attr>
    <attr name="banner_indicator_normal_width" format="dimension">
    </attr>
    <attr name="banner_indicator_radius" format="dimension">
    </attr>
    <attr name="banner_indicator_selected_color" format="reference|color">
    </attr>
    <attr name="banner_indicator_selected_width" format="dimension">
    </attr>
    <attr name="banner_indicator_space" format="dimension">
    </attr>
    <attr name="banner_infinite_loop" format="boolean">
    </attr>
    <attr name="banner_loop_time" format="integer">
    </attr>
    <attr name="banner_orientation">
        <enum name="horizontal" value="0" />
        <enum name="vertical" value="1" />
    </attr>
    <attr name="banner_radius" format="dimension">
    </attr>
    <attr name="banner_round_bottom_left" format="boolean">
    </attr>
    <attr name="banner_round_bottom_right" format="boolean">
    </attr>
    <attr name="banner_round_top_left" format="boolean">
    </attr>
    <attr name="banner_round_top_right" format="boolean">
    </attr>
    <attr name="barLength" format="dimension">
    </attr>
    <attr name="barrierAllowsGoneWidgets" format="boolean">
    </attr>
    <attr name="barrierDirection">
        <enum name="bottom" value="3" />
        <enum name="end" value="6" />
        <enum name="left" value="0" />
        <enum name="right" value="1" />
        <enum name="start" value="5" />
        <enum name="top" value="2" />
    </attr>
    <attr name="barrierMargin" format="dimension">
    </attr>
    <attr name="behavior_autoHide" format="boolean">
    </attr>
    <attr name="behavior_autoShrink" format="boolean">
    </attr>
    <attr name="behavior_draggable" format="boolean">
    </attr>
    <attr name="behavior_expandedOffset" format="reference|dimension">
    </attr>
    <attr name="behavior_fitToContents" format="boolean">
    </attr>
    <attr name="behavior_halfExpandedRatio" format="reference|float">
    </attr>
    <attr name="behavior_hideable" format="boolean">
    </attr>
    <attr name="behavior_overlapTop" format="dimension">
    </attr>
    <attr name="behavior_peekHeight" format="dimension">
        <enum name="auto" value="-1" />
    </attr>
    <attr name="behavior_saveFlags">
        <flag name="all" value="-1" />
        <flag name="fitToContents" value="0x2" />
        <flag name="hideable" value="0x4" />
        <flag name="none" value="0" />
        <flag name="peekHeight" value="0x1" />
        <flag name="skipCollapsed" value="0x8" />
    </attr>
    <attr name="behavior_skipCollapsed" format="boolean">
    </attr>
    <attr name="blendSrc" format="reference">
    </attr>
    <attr name="borderColor" format="color">
    </attr>
    <attr name="borderCover" format="boolean">
    </attr>
    <attr name="borderRound" format="dimension">
    </attr>
    <attr name="borderRoundPercent" format="float">
    </attr>
    <attr name="borderWidth" format="dimension">
    </attr>
    <attr name="borderlessButtonStyle" format="reference">
    </attr>
    <attr name="bottomAppBarStyle" format="reference">
    </attr>
    <attr name="bottomLeftRadius" format="dimension">
    </attr>
    <attr name="bottomNavigationStyle" format="reference">
    </attr>
    <attr name="bottomRightRadius" format="dimension">
    </attr>
    <attr name="bottomSheetDialogTheme" format="reference">
    </attr>
    <attr name="bottomSheetStyle" format="reference">
    </attr>
    <attr name="boxBackgroundColor" format="color">
    </attr>
    <attr name="boxBackgroundMode">
        <enum name="filled" value="1" />
        <enum name="none" value="0" />
        <enum name="outline" value="2" />
    </attr>
    <attr name="boxCollapsedPaddingTop" format="dimension">
    </attr>
    <attr name="boxCornerRadiusBottomEnd" format="dimension">
    </attr>
    <attr name="boxCornerRadiusBottomStart" format="dimension">
    </attr>
    <attr name="boxCornerRadiusTopEnd" format="dimension">
    </attr>
    <attr name="boxCornerRadiusTopStart" format="dimension">
    </attr>
    <attr name="boxStrokeColor" format="color">
    </attr>
    <attr name="boxStrokeErrorColor" format="color">
    </attr>
    <attr name="boxStrokeWidth" format="dimension">
    </attr>
    <attr name="boxStrokeWidthFocused" format="dimension">
    </attr>
    <attr name="brightness" format="float">
    </attr>
    <attr name="buttonBarButtonStyle" format="reference">
    </attr>
    <attr name="buttonBarNegativeButtonStyle" format="reference">
    </attr>
    <attr name="buttonBarNeutralButtonStyle" format="reference">
    </attr>
    <attr name="buttonBarPositiveButtonStyle" format="reference">
    </attr>
    <attr name="buttonBarStyle" format="reference">
    </attr>
    <attr name="buttonCompat" format="reference">
    </attr>
    <attr name="buttonGravity">
        <flag name="bottom" value="0x50" />
        <flag name="center_vertical" value="0x10" />
        <flag name="top" value="0x30" />
    </attr>
    <attr name="buttonIconDimen" format="dimension">
    </attr>
    <attr name="buttonPanelSideLayout" format="reference">
    </attr>
    <attr name="buttonStyle" format="reference">
    </attr>
    <attr name="buttonStyleSmall" format="reference">
    </attr>
    <attr name="buttonTint" format="color">
    </attr>
    <attr name="buttonTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="bvp_auto_play" format="boolean">
    </attr>
    <attr name="bvp_can_loop" format="boolean">
    </attr>
    <attr name="bvp_indicator_checked_color" format="color">
    </attr>
    <attr name="bvp_indicator_gravity">
        <enum name="center" value="0" />
        <enum name="end" value="4" />
        <enum name="start" value="2" />
    </attr>
    <attr name="bvp_indicator_normal_color" format="color">
    </attr>
    <attr name="bvp_indicator_radius" format="dimension">
    </attr>
    <attr name="bvp_indicator_slide_mode">
        <enum name="color" value="5" />
        <enum name="normal" value="0" />
        <enum name="scale" value="4" />
        <enum name="smooth" value="2" />
        <enum name="worm" value="3" />
    </attr>
    <attr name="bvp_indicator_style">
        <enum name="circle" value="0" />
        <enum name="dash" value="2" />
        <enum name="round_rect" value="4" />
    </attr>
    <attr name="bvp_indicator_visibility">
        <enum name="gone" value="8" />
        <enum name="invisible" value="4" />
        <enum name="visible" value="0" />
    </attr>
    <attr name="bvp_interval" format="integer">
    </attr>
    <attr name="bvp_page_margin" format="dimension">
    </attr>
    <attr name="bvp_page_style">
        <enum name="multi_page" value="2" />
        <enum name="multi_page_overlap" value="4" />
        <enum name="multi_page_scale" value="8" />
        <enum name="normal" value="0" />
    </attr>
    <attr name="bvp_reveal_width" format="dimension">
    </attr>
    <attr name="bvp_round_corner" format="dimension">
    </attr>
    <attr name="bvp_scroll_duration" format="integer">
    </attr>
    <attr name="calendarTextColor" format="color">
    </attr>
    <attr name="calendarViewShown" format="boolean">
    </attr>
    <attr name="cardBackgroundColor" format="color">
    </attr>
    <attr name="cardCornerRadius" format="dimension">
    </attr>
    <attr name="cardElevation" format="dimension">
    </attr>
    <attr name="cardForegroundColor" format="color">
    </attr>
    <attr name="cardMaxElevation" format="dimension">
    </attr>
    <attr name="cardPreventCornerOverlap" format="boolean">
    </attr>
    <attr name="cardUseCompatPadding" format="boolean">
    </attr>
    <attr name="cardViewStyle" format="reference">
    </attr>
    <attr name="carousel_backwardTransition" format="reference">
    </attr>
    <attr name="carousel_emptyViewsBehavior">
        <enum name="gone" value="8" />
        <enum name="invisible" value="4" />
    </attr>
    <attr name="carousel_firstView" format="reference">
    </attr>
    <attr name="carousel_forwardTransition" format="reference">
    </attr>
    <attr name="carousel_infinite" format="boolean">
    </attr>
    <attr name="carousel_nextState" format="reference">
    </attr>
    <attr name="carousel_previousState" format="reference">
    </attr>
    <attr name="carousel_touchUpMode">
        <enum name="carryVelocity" value="2" />
        <enum name="immediateStop" value="1" />
    </attr>
    <attr name="carousel_touchUp_dampeningFactor" format="float">
    </attr>
    <attr name="carousel_touchUp_velocityThreshold" format="float">
    </attr>
    <attr name="chainUseRtl" format="boolean">
    </attr>
    <attr name="checkboxStyle" format="reference">
    </attr>
    <attr name="checkedButton" format="reference">
    </attr>
    <attr name="checkedChip" format="reference">
    </attr>
    <attr name="checkedIcon" format="reference">
    </attr>
    <attr name="checkedIconEnabled" format="boolean">
    </attr>
    <attr name="checkedIconTint" format="color">
    </attr>
    <attr name="checkedIconVisible" format="boolean">
    </attr>
    <attr name="checkedTextViewStyle" format="reference">
    </attr>
    <attr name="chipBackgroundColor" format="color">
    </attr>
    <attr name="chipCornerRadius" format="dimension">
    </attr>
    <attr name="chipEndPadding" format="dimension">
    </attr>
    <attr name="chipGroupStyle" format="reference">
    </attr>
    <attr name="chipIcon" format="reference">
    </attr>
    <attr name="chipIconEnabled" format="boolean">
    </attr>
    <attr name="chipIconSize" format="dimension">
    </attr>
    <attr name="chipIconTint" format="color">
    </attr>
    <attr name="chipIconVisible" format="boolean">
    </attr>
    <attr name="chipMinHeight" format="dimension">
    </attr>
    <attr name="chipMinTouchTargetSize" format="dimension">
    </attr>
    <attr name="chipSpacing" format="dimension">
    </attr>
    <attr name="chipSpacingHorizontal" format="dimension">
    </attr>
    <attr name="chipSpacingVertical" format="dimension">
    </attr>
    <attr name="chipStandaloneStyle" format="reference">
    </attr>
    <attr name="chipStartPadding" format="dimension">
    </attr>
    <attr name="chipStrokeColor" format="color">
    </attr>
    <attr name="chipStrokeWidth" format="dimension">
    </attr>
    <attr name="chipStyle" format="reference">
    </attr>
    <attr name="chipSurfaceColor" format="color">
    </attr>
    <attr name="circle" format="boolean">
    </attr>
    <attr name="circleRadius" format="dimension">
    </attr>
    <attr name="circularflow_angles" format="string">
    </attr>
    <attr name="circularflow_defaultAngle" format="float">
    </attr>
    <attr name="circularflow_defaultRadius" format="dimension">
    </attr>
    <attr name="circularflow_radiusInDP" format="string">
    </attr>
    <attr name="circularflow_viewCenter" format="reference">
    </attr>
    <attr name="clearsTag" format="reference">
    </attr>
    <attr name="clickAction">
        <flag name="jumpToEnd" value="0x100" />
        <flag name="jumpToStart" value="0x1000" />
        <flag name="toggle" value="0x11" />
        <flag name="transitionToEnd" value="0x1" />
        <flag name="transitionToStart" value="0x10" />
    </attr>
    <attr name="closeIcon" format="reference">
    </attr>
    <attr name="closeIconEnabled" format="boolean">
    </attr>
    <attr name="closeIconEndPadding" format="dimension">
    </attr>
    <attr name="closeIconSize" format="dimension">
    </attr>
    <attr name="closeIconStartPadding" format="dimension">
    </attr>
    <attr name="closeIconTint" format="color">
    </attr>
    <attr name="closeIconVisible" format="boolean">
    </attr>
    <attr name="closeItemLayout" format="reference">
    </attr>
    <attr name="collapseContentDescription" format="string">
    </attr>
    <attr name="collapseIcon" format="reference">
    </attr>
    <attr name="collapsedTitleGravity">
        <flag name="bottom" value="0x50" />
        <flag name="center" value="0x11" />
        <flag name="center_horizontal" value="0x1" />
        <flag name="center_vertical" value="0x10" />
        <flag name="end" value="0x800005" />
        <flag name="fill_vertical" value="0x70" />
        <flag name="left" value="0x3" />
        <flag name="right" value="0x5" />
        <flag name="start" value="0x800003" />
        <flag name="top" value="0x30" />
    </attr>
    <attr name="collapsedTitleTextAppearance" format="reference">
    </attr>
    <attr name="color" format="color">
    </attr>
    <attr name="colorAccent" format="color">
    </attr>
    <attr name="colorBackgroundFloating" format="color">
    </attr>
    <attr name="colorButtonNormal" format="color">
    </attr>
    <attr name="colorControlActivated" format="color">
    </attr>
    <attr name="colorControlHighlight" format="color">
    </attr>
    <attr name="colorControlNormal" format="color">
    </attr>
    <attr name="colorError" format="reference|color">
    </attr>
    <attr name="colorOnBackground" format="reference|string|integer|boolean|color|float|dimension|fraction">
    </attr>
    <attr name="colorOnError" format="color">
    </attr>
    <attr name="colorOnPrimary" format="color">
    </attr>
    <attr name="colorOnPrimarySurface" format="color">
    </attr>
    <attr name="colorOnSecondary" format="color">
    </attr>
    <attr name="colorOnSurface" format="color">
    </attr>
    <attr name="colorPrimary" format="color">
    </attr>
    <attr name="colorPrimaryDark" format="color">
    </attr>
    <attr name="colorPrimarySurface" format="color">
    </attr>
    <attr name="colorPrimaryVariant" format="color">
    </attr>
    <attr name="colorSecondary" format="color">
    </attr>
    <attr name="colorSecondaryVariant" format="color">
    </attr>
    <attr name="colorSurface" format="color">
    </attr>
    <attr name="colorSwitchThumbNormal" format="color">
    </attr>
    <attr name="commitIcon" format="reference">
    </attr>
    <attr name="constraintRotate">
        <enum name="left" value="2" />
        <enum name="none" value="0" />
        <enum name="right" value="1" />
        <enum name="x_left" value="4" />
        <enum name="x_right" value="3" />
    </attr>
    <attr name="constraintSet" format="reference">
    </attr>
    <attr name="constraintSetEnd" format="reference">
    </attr>
    <attr name="constraintSetStart" format="reference">
    </attr>
    <attr name="constraint_referenced_ids" format="string">
    </attr>
    <attr name="constraint_referenced_tags" format="string">
    </attr>
    <attr name="constraints" format="reference">
    </attr>
    <attr name="content" format="reference">
    </attr>
    <attr name="contentDescription" format="string">
    </attr>
    <attr name="contentInsetEnd" format="dimension">
    </attr>
    <attr name="contentInsetEndWithActions" format="dimension">
    </attr>
    <attr name="contentInsetLeft" format="dimension">
    </attr>
    <attr name="contentInsetRight" format="dimension">
    </attr>
    <attr name="contentInsetStart" format="dimension">
    </attr>
    <attr name="contentInsetStartWithNavigation" format="dimension">
    </attr>
    <attr name="contentPadding" format="dimension">
    </attr>
    <attr name="contentPaddingBottom" format="dimension">
    </attr>
    <attr name="contentPaddingLeft" format="dimension">
    </attr>
    <attr name="contentPaddingRight" format="dimension">
    </attr>
    <attr name="contentPaddingTop" format="dimension">
    </attr>
    <attr name="contentScrim" format="color">
    </attr>
    <attr name="contrast" format="float">
    </attr>
    <attr name="controlBackground" format="reference">
    </attr>
    <attr name="coordinatorLayoutStyle" format="reference">
    </attr>
    <attr name="cornerFamily">
        <enum name="cut" value="1" />
        <enum name="rounded" value="0" />
    </attr>
    <attr name="cornerFamilyBottomLeft">
        <enum name="cut" value="1" />
        <enum name="rounded" value="0" />
    </attr>
    <attr name="cornerFamilyBottomRight">
        <enum name="cut" value="1" />
        <enum name="rounded" value="0" />
    </attr>
    <attr name="cornerFamilyTopLeft">
        <enum name="cut" value="1" />
        <enum name="rounded" value="0" />
    </attr>
    <attr name="cornerFamilyTopRight">
        <enum name="cut" value="1" />
        <enum name="rounded" value="0" />
    </attr>
    <attr name="cornerRadius" format="dimension">
    </attr>
    <attr name="cornerSize" format="dimension|fraction">
    </attr>
    <attr name="cornerSizeBottomLeft" format="dimension|fraction">
    </attr>
    <attr name="cornerSizeBottomRight" format="dimension|fraction">
    </attr>
    <attr name="cornerSizeTopLeft" format="dimension|fraction">
    </attr>
    <attr name="cornerSizeTopRight" format="dimension|fraction">
    </attr>
    <attr name="countdownTime" format="integer">
    </attr>
    <attr name="counterEnabled" format="boolean">
    </attr>
    <attr name="counterMaxLength" format="integer">
    </attr>
    <attr name="counterOverflowTextAppearance" format="reference">
    </attr>
    <attr name="counterOverflowTextColor" format="reference">
    </attr>
    <attr name="counterTextAppearance" format="reference">
    </attr>
    <attr name="counterTextColor" format="reference">
    </attr>
    <attr name="crossfade" format="float">
    </attr>
    <attr name="currentDay" format="integer">
    </attr>
    <attr name="currentHour" format="integer">
    </attr>
    <attr name="currentMinute" format="integer">
    </attr>
    <attr name="currentMonth" format="integer">
    </attr>
    <attr name="currentState" format="reference">
    </attr>
    <attr name="currentYear" format="integer">
    </attr>
    <attr name="curveFit">
        <enum name="linear" value="1" />
        <enum name="spline" value="0" />
    </attr>
    <attr name="customBoolean" format="boolean">
    </attr>
    <attr name="customColorDrawableValue" format="color">
    </attr>
    <attr name="customColorValue" format="color">
    </attr>
    <attr name="customDimension" format="dimension">
    </attr>
    <attr name="customFloatValue" format="float">
    </attr>
    <attr name="customIntegerValue" format="integer">
    </attr>
    <attr name="customNavigationLayout" format="reference">
    </attr>
    <attr name="customPixelDimension" format="dimension">
    </attr>
    <attr name="customReference" format="reference">
    </attr>
    <attr name="customStringValue" format="string">
    </attr>
    <attr name="datePickerDialogTheme" format="reference">
    </attr>
    <attr name="datePickerMode">
        <enum name="calendar" value="2" />
        <enum name="spinner" value="1" />
    </attr>
    <attr name="datePickerStyle" format="reference">
    </attr>
    <attr name="dayInvalidStyle" format="reference">
    </attr>
    <attr name="dayOfWeekBackground" format="color">
    </attr>
    <attr name="dayOfWeekTextAppearance" format="reference">
    </attr>
    <attr name="daySelectedStyle" format="reference">
    </attr>
    <attr name="dayStyle" format="reference">
    </attr>
    <attr name="dayTodayStyle" format="reference">
    </attr>
    <attr name="defaultDuration" format="integer">
    </attr>
    <attr name="defaultQueryHint" format="string">
    </attr>
    <attr name="defaultState" format="reference">
    </attr>
    <attr name="deltaPolarAngle" format="float">
    </attr>
    <attr name="deltaPolarRadius" format="float">
    </attr>
    <attr name="deriveConstraintsFrom" format="reference">
    </attr>
    <attr name="dialogCornerRadius" format="dimension">
    </attr>
    <attr name="dialogMode" format="boolean">
    </attr>
    <attr name="dialogPreferredPadding" format="dimension">
    </attr>
    <attr name="dialogTheme" format="reference">
    </attr>
    <attr name="direction" format="boolean">
    </attr>
    <attr name="displayOptions">
        <flag name="disableHome" value="0x20" />
        <flag name="homeAsUp" value="0x4" />
        <flag name="none" value="0" />
        <flag name="showCustom" value="0x10" />
        <flag name="showHome" value="0x2" />
        <flag name="showTitle" value="0x8" />
        <flag name="useLogo" value="0x1" />
    </attr>
    <attr name="divide_line_style" format="integer">
    </attr>
    <attr name="divider" format="reference">
    </attr>
    <attr name="dividerHorizontal" format="reference">
    </attr>
    <attr name="dividerLineVisibility">
        <enum name="gone" value="2" />
        <enum name="visible" value="0" />
    </attr>
    <attr name="dividerPadding" format="dimension">
    </attr>
    <attr name="dividerVertical" format="reference">
    </attr>
    <attr name="dragDirection">
        <enum name="dragAnticlockwise" value="7" />
        <enum name="dragClockwise" value="6" />
        <enum name="dragDown" value="1" />
        <enum name="dragEnd" value="5" />
        <enum name="dragLeft" value="2" />
        <enum name="dragRight" value="3" />
        <enum name="dragStart" value="4" />
        <enum name="dragUp" value="0" />
    </attr>
    <attr name="dragScale" format="float">
    </attr>
    <attr name="dragThreshold" format="float">
    </attr>
    <attr name="drawPath">
        <enum name="asConfigured" value="4" />
        <enum name="deltaRelative" value="3" />
        <enum name="none" value="0" />
        <enum name="path" value="1" />
        <enum name="pathRelative" value="2" />
        <enum name="rectangles" value="5" />
    </attr>
    <attr name="drawableBottomCompat" format="reference">
    </attr>
    <attr name="drawableEndCompat" format="reference">
    </attr>
    <attr name="drawableLeftCompat" format="reference">
    </attr>
    <attr name="drawableRightCompat" format="reference">
    </attr>
    <attr name="drawableSize" format="dimension">
    </attr>
    <attr name="drawableStartCompat" format="reference">
    </attr>
    <attr name="drawableTint" format="color">
    </attr>
    <attr name="drawableTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="drawableTopCompat" format="reference">
    </attr>
    <attr name="drawable_left" format="reference">
    </attr>
    <attr name="drawable_padding" format="dimension">
    </attr>
    <attr name="drawable_right" format="reference">
    </attr>
    <attr name="drawerArrowStyle" format="reference">
    </attr>
    <attr name="dropDownListViewStyle" format="reference">
    </attr>
    <attr name="dropdownListPreferredItemHeight" format="dimension">
    </attr>
    <attr name="duration" format="integer">
    </attr>
    <attr name="editTextBackground" format="reference">
    </attr>
    <attr name="editTextColor" format="reference|color">
    </attr>
    <attr name="editTextStyle" format="reference">
    </attr>
    <attr name="edit_text" format="string">
    </attr>
    <attr name="edittext_index" format="integer">
    </attr>
    <attr name="elevation" format="dimension">
    </attr>
    <attr name="elevationOverlayColor" format="color">
    </attr>
    <attr name="elevationOverlayEnabled" format="boolean">
    </attr>
    <attr name="enable" format="boolean">
    </attr>
    <attr name="endDay" format="integer">
    </attr>
    <attr name="endHour" format="integer">
    </attr>
    <attr name="endIconCheckable" format="boolean">
    </attr>
    <attr name="endIconContentDescription" format="string">
    </attr>
    <attr name="endIconDrawable" format="reference">
    </attr>
    <attr name="endIconMode">
        <enum name="clear_text" value="2" />
        <enum name="custom" value="-1" />
        <enum name="dropdown_menu" value="3" />
        <enum name="none" value="0" />
        <enum name="password_toggle" value="1" />
    </attr>
    <attr name="endIconTint" format="color">
    </attr>
    <attr name="endIconTintMode">
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="endMinute" format="integer">
    </attr>
    <attr name="endMonth" format="integer">
    </attr>
    <attr name="endYear" format="integer">
    </attr>
    <attr name="end_icon" format="reference">
    </attr>
    <attr name="enforceMaterialTheme" format="boolean">
    </attr>
    <attr name="enforceTextAppearance" format="boolean">
    </attr>
    <attr name="ensureMinTouchTargetSize" format="boolean">
    </attr>
    <attr name="errorContentDescription" format="string">
    </attr>
    <attr name="errorEnabled" format="boolean">
    </attr>
    <attr name="errorIconDrawable" format="reference">
    </attr>
    <attr name="errorIconTint" format="reference">
    </attr>
    <attr name="errorIconTintMode">
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="errorTextAppearance" format="reference">
    </attr>
    <attr name="errorTextColor" format="color">
    </attr>
    <attr name="expandActivityOverflowButtonDrawable" format="reference">
    </attr>
    <attr name="expanded" format="boolean">
    </attr>
    <attr name="expandedTitleGravity">
        <flag name="bottom" value="0x50" />
        <flag name="center" value="0x11" />
        <flag name="center_horizontal" value="0x1" />
        <flag name="center_vertical" value="0x10" />
        <flag name="end" value="0x800005" />
        <flag name="fill_vertical" value="0x70" />
        <flag name="left" value="0x3" />
        <flag name="right" value="0x5" />
        <flag name="start" value="0x800003" />
        <flag name="top" value="0x30" />
    </attr>
    <attr name="expandedTitleMargin" format="dimension">
    </attr>
    <attr name="expandedTitleMarginBottom" format="dimension">
    </attr>
    <attr name="expandedTitleMarginEnd" format="dimension">
    </attr>
    <attr name="expandedTitleMarginStart" format="dimension">
    </attr>
    <attr name="expandedTitleMarginTop" format="dimension">
    </attr>
    <attr name="expandedTitleTextAppearance" format="reference">
    </attr>
    <attr name="extendMotionSpec" format="reference">
    </attr>
    <attr name="extendedFloatingActionButtonStyle" format="reference">
    </attr>
    <attr name="fabAlignmentMode">
        <enum name="center" value="0" />
        <enum name="end" value="1" />
    </attr>
    <attr name="fabAnimationMode">
        <enum name="scale" value="0" />
        <enum name="slide" value="1" />
    </attr>
    <attr name="fabCradleMargin" format="dimension">
    </attr>
    <attr name="fabCradleRoundedCornerRadius" format="dimension">
    </attr>
    <attr name="fabCradleVerticalOffset" format="dimension">
    </attr>
    <attr name="fabCustomSize" format="dimension">
    </attr>
    <attr name="fabSize">
        <enum name="auto" value="-1" />
        <enum name="mini" value="1" />
        <enum name="normal" value="0" />
    </attr>
    <attr name="fadeDuration" format="integer">
    </attr>
    <attr name="failureImage" format="reference">
    </attr>
    <attr name="failureImageScaleType">
        <enum name="center" value="4" />
        <enum name="centerCrop" value="6" />
        <enum name="centerInside" value="5" />
        <enum name="fitBottomStart" value="8" />
        <enum name="fitCenter" value="2" />
        <enum name="fitEnd" value="3" />
        <enum name="fitStart" value="1" />
        <enum name="fitXY" value="0" />
        <enum name="focusCrop" value="7" />
        <enum name="none" value="-1" />
    </attr>
    <attr name="fastScrollEnabled" format="boolean">
    </attr>
    <attr name="fastScrollHorizontalThumbDrawable" format="reference">
    </attr>
    <attr name="fastScrollHorizontalTrackDrawable" format="reference">
    </attr>
    <attr name="fastScrollVerticalThumbDrawable" format="reference">
    </attr>
    <attr name="fastScrollVerticalTrackDrawable" format="reference">
    </attr>
    <attr name="firstBaselineToTopHeight" format="dimension">
    </attr>
    <attr name="firstDayOfWeek" format="integer">
    </attr>
    <attr name="first_desc" format="string">
    </attr>
    <attr name="first_icon" format="reference">
    </attr>
    <attr name="first_text" format="string">
    </attr>
    <attr name="five_desc" format="string">
    </attr>
    <attr name="five_text" format="string">
    </attr>
    <attr name="floatingActionButtonStyle" format="reference">
    </attr>
    <attr name="flow_firstHorizontalBias" format="float">
    </attr>
    <attr name="flow_firstHorizontalStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="flow_firstVerticalBias" format="float">
    </attr>
    <attr name="flow_firstVerticalStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="flow_horizontalAlign">
        <enum name="center" value="2" />
        <enum name="end" value="1" />
        <enum name="start" value="0" />
    </attr>
    <attr name="flow_horizontalBias" format="float">
    </attr>
    <attr name="flow_horizontalGap" format="dimension">
    </attr>
    <attr name="flow_horizontalStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="flow_lastHorizontalBias" format="float">
    </attr>
    <attr name="flow_lastHorizontalStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="flow_lastVerticalBias" format="float">
    </attr>
    <attr name="flow_lastVerticalStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="flow_maxElementsWrap" format="integer">
    </attr>
    <attr name="flow_padding" format="dimension">
    </attr>
    <attr name="flow_verticalAlign">
        <enum name="baseline" value="3" />
        <enum name="bottom" value="1" />
        <enum name="center" value="2" />
        <enum name="top" value="0" />
    </attr>
    <attr name="flow_verticalBias" format="float">
    </attr>
    <attr name="flow_verticalGap" format="dimension">
    </attr>
    <attr name="flow_verticalStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="flow_wrapMode">
        <enum name="aligned" value="2" />
        <enum name="chain" value="1" />
        <enum name="chain2" value="3" />
        <enum name="none" value="0" />
    </attr>
    <attr name="font" format="reference">
    </attr>
    <attr name="fontFamily" format="string">
    </attr>
    <attr name="fontProviderAuthority" format="string">
    </attr>
    <attr name="fontProviderCerts" format="reference">
    </attr>
    <attr name="fontProviderFetchStrategy">
        <enum name="async" value="1" />
        <enum name="blocking" value="0" />
    </attr>
    <attr name="fontProviderFetchTimeout" format="integer">
        <enum name="forever" value="-1" />
    </attr>
    <attr name="fontProviderPackage" format="string">
    </attr>
    <attr name="fontProviderQuery" format="string">
    </attr>
    <attr name="fontStyle">
        <enum name="italic" value="1" />
        <enum name="normal" value="0" />
    </attr>
    <attr name="fontVariationSettings" format="string">
    </attr>
    <attr name="fontWeight" format="integer">
    </attr>
    <attr name="foregroundInsidePadding" format="boolean">
    </attr>
    <attr name="four_desc" format="string">
    </attr>
    <attr name="four_icon" format="reference">
    </attr>
    <attr name="four_text" format="string">
    </attr>
    <attr name="framePosition" format="integer">
    </attr>
    <attr name="gapBetweenBars" format="dimension">
    </attr>
    <attr name="gestureInsetBottomIgnored" format="boolean">
    </attr>
    <attr name="goIcon" format="reference">
    </attr>
    <attr name="guidelineUseRtl" format="boolean">
    </attr>
    <attr name="haloColor" format="color">
    </attr>
    <attr name="haloRadius" format="dimension">
    </attr>
    <attr name="headerAmPmTextAppearance" format="reference">
    </attr>
    <attr name="headerBackground" format="reference|color">
    </attr>
    <attr name="headerDayOfMonthTextAppearance" format="reference">
    </attr>
    <attr name="headerLayout" format="reference">
    </attr>
    <attr name="headerMonthTextAppearance" format="reference">
    </attr>
    <attr name="headerTextColor" format="color">
    </attr>
    <attr name="headerTimeTextAppearance" format="reference">
    </attr>
    <attr name="headerYearTextAppearance" format="reference">
    </attr>
    <attr name="height" format="dimension">
    </attr>
    <attr name="heightSpace" format="dimension">
    </attr>
    <attr name="helperText" format="string">
    </attr>
    <attr name="helperTextEnabled" format="boolean">
    </attr>
    <attr name="helperTextTextAppearance" format="reference">
    </attr>
    <attr name="helperTextTextColor" format="color">
    </attr>
    <attr name="hideMotionSpec" format="reference">
    </attr>
    <attr name="hideOnContentScroll" format="boolean">
    </attr>
    <attr name="hideOnScroll" format="boolean">
    </attr>
    <attr name="hideWheelUntilFocused" format="boolean">
    </attr>
    <attr name="hint" format="string">
    </attr>
    <attr name="hintAnimationEnabled" format="boolean">
    </attr>
    <attr name="hintEnabled" format="boolean">
    </attr>
    <attr name="hintTextAppearance" format="reference">
    </attr>
    <attr name="hintTextColor" format="color">
    </attr>
    <attr name="hint_text" format="string">
    </attr>
    <attr name="homeAsUpIndicator" format="reference">
    </attr>
    <attr name="homeLayout" format="reference">
    </attr>
    <attr name="horizontalOffset" format="dimension">
    </attr>
    <attr name="hoveredFocusedTranslationZ" format="dimension">
    </attr>
    <attr name="hz_animStyle">
        <enum name="priority" value="2" />
        <enum name="promptly" value="1" />
        <enum name="smoothScroll" value="0" />
    </attr>
    <attr name="hz_arcGradientEndColor" format="reference">
    </attr>
    <attr name="hz_arcGradientStartColor" format="reference">
    </attr>
    <attr name="hz_arcStrokeWidth" format="integer">
    </attr>
    <attr name="hz_arrowIconCDesc" format="string">
    </attr>
    <attr name="hz_assitIcon" format="reference">
    </attr>
    <attr name="hz_assitText" format="string">
    </attr>
    <attr name="hz_backCircleColor" format="reference">
    </attr>
    <attr name="hz_background_color" format="reference|color">
    </attr>
    <attr name="hz_background_drawable" format="reference">
    </attr>
    <attr name="hz_bgColor" format="reference">
    </attr>
    <attr name="hz_bgDrawable" format="reference">
    </attr>
    <attr name="hz_bgStyle">
        <enum name="gray" value="0" />
        <enum name="white" value="1" />
    </attr>
    <attr name="hz_bg_padding_bottom" format="reference|dimension">
    </attr>
    <attr name="hz_bg_padding_left" format="reference|dimension">
    </attr>
    <attr name="hz_bg_padding_right" format="reference|dimension">
    </attr>
    <attr name="hz_bg_padding_top" format="reference|dimension">
    </attr>
    <attr name="hz_border_width" format="reference|dimension">
    </attr>
    <attr name="hz_btnIcon" format="reference">
    </attr>
    <attr name="hz_btnStyle">
        <enum name="primary" value="0" />
        <enum name="secondary" value="1" />
        <enum name="warn" value="2" />
        <enum name="weak" value="3" />
    </attr>
    <attr name="hz_btnText" format="string">
    </attr>
    <attr name="hz_btnTheme">
        <enum name="black" value="0" />
        <enum name="blue" value="1" />
    </attr>
    <attr name="hz_charConvertSize" format="integer">
    </attr>
    <attr name="hz_checked" format="reference|boolean">
    </attr>
    <attr name="hz_checked_color" format="reference|color">
    </attr>
    <attr name="hz_click_interval" format="reference|integer">
    </attr>
    <attr name="hz_close" format="boolean">
    </attr>
    <attr name="hz_container_title" format="reference|string">
    </attr>
    <attr name="hz_contentDescription" format="string">
    </attr>
    <attr name="hz_countDownSeconds" format="integer">
    </attr>
    <attr name="hz_custom_orientation">
        <enum name="horizontal" value="0" />
        <enum name="vertical" value="1" />
    </attr>
    <attr name="hz_divider_out" format="boolean">
    </attr>
    <attr name="hz_effect_duration" format="reference|integer">
    </attr>
    <attr name="hz_enable_effect" format="reference|boolean">
    </attr>
    <attr name="hz_enabled" format="boolean">
    </attr>
    <attr name="hz_endColor" format="reference|color">
    </attr>
    <attr name="hz_endIcon" format="reference">
    </attr>
    <attr name="hz_endIconCDesc" format="string">
    </attr>
    <attr name="hz_endLeftIcon" format="reference">
    </attr>
    <attr name="hz_endLeftIconCDesc" format="string">
    </attr>
    <attr name="hz_endLeftText" format="string">
    </attr>
    <attr name="hz_endText" format="reference|string">
    </attr>
    <attr name="hz_endText_suffix" format="reference|string">
    </attr>
    <attr name="hz_hasPop" format="boolean">
    </attr>
    <attr name="hz_height" format="reference|dimension">
    </attr>
    <attr name="hz_hintText" format="string">
    </attr>
    <attr name="hz_icon" format="reference">
    </attr>
    <attr name="hz_icon_disable" format="reference">
    </attr>
    <attr name="hz_icon_normal" format="reference">
    </attr>
    <attr name="hz_icon_pressed" format="reference">
    </attr>
    <attr name="hz_isEnable" format="boolean">
    </attr>
    <attr name="hz_isLeftShowBtn" format="boolean">
    </attr>
    <attr name="hz_isRightShowBtn" format="boolean">
    </attr>
    <attr name="hz_isShowHint" format="boolean">
    </attr>
    <attr name="hz_isShowPic" format="boolean">
    </attr>
    <attr name="hz_itemMaxWidth" format="reference|dimension">
    </attr>
    <attr name="hz_itemType">
        <enum name="singleLineTitleArrow" value="0" />
        <enum name="singleLineTitleAssit" value="1" />
        <enum name="singleLineTitleAssitArrow" value="2" />
    </attr>
    <attr name="hz_layoutStyle">
        <enum name="grid" value="2" />
        <enum name="horizontal" value="0" />
        <enum name="vertical" value="1" />
    </attr>
    <attr name="hz_leftBtnText" format="string">
    </attr>
    <attr name="hz_leftIcon" format="reference">
    </attr>
    <attr name="hz_loading_bg_color" format="reference|color">
    </attr>
    <attr name="hz_loading_drawable" format="reference">
    </attr>
    <attr name="hz_loading_gradient_end_colors" format="reference|color">
    </attr>
    <attr name="hz_loading_gradient_start_colors" format="reference|color">
    </attr>
    <attr name="hz_loading_radius" format="reference|dimension">
    </attr>
    <attr name="hz_loading_stroke_width" format="reference|dimension">
    </attr>
    <attr name="hz_loading_timeout" format="reference|integer">
    </attr>
    <attr name="hz_loop" format="boolean">
    </attr>
    <attr name="hz_maxCD" format="string">
    </attr>
    <attr name="hz_maxCharSize" format="integer">
    </attr>
    <attr name="hz_maxProgress" format="float">
    </attr>
    <attr name="hz_maxValue" format="float">
    </attr>
    <attr name="hz_maxWidth" format="dimension">
    </attr>
    <attr name="hz_maxlines" format="integer">
    </attr>
    <attr name="hz_messageText" format="string">
    </attr>
    <attr name="hz_minCD" format="string">
    </attr>
    <attr name="hz_minControl" format="integer">
    </attr>
    <attr name="hz_minValue" format="float">
    </attr>
    <attr name="hz_minWidth" format="dimension">
    </attr>
    <attr name="hz_mode">
        <enum name="slider" value="1" />
        <enum name="tab" value="0" />
    </attr>
    <attr name="hz_msg" format="string">
    </attr>
    <attr name="hz_new_style" format="reference|boolean">
    </attr>
    <attr name="hz_patten" format="string">
    </attr>
    <attr name="hz_popWidth" format="integer">
    </attr>
    <attr name="hz_position" format="integer">
    </attr>
    <attr name="hz_progress" format="float">
    </attr>
    <attr name="hz_progressBarBgColor" format="reference|color">
    </attr>
    <attr name="hz_progressBarHeight" format="dimension">
    </attr>
    <attr name="hz_progress_hasPop" format="boolean">
    </attr>
    <attr name="hz_progress_isEnable" format="boolean">
    </attr>
    <attr name="hz_progress_minControl" format="integer">
    </attr>
    <attr name="hz_progress_mode">
        <enum name="mode_long" value="0" />
        <enum name="mode_short" value="1" />
    </attr>
    <attr name="hz_progress_startIcon" format="reference">
    </attr>
    <attr name="hz_progress_startText" format="reference|string">
    </attr>
    <attr name="hz_rightBtnText" format="string">
    </attr>
    <attr name="hz_rightIcon" format="reference">
    </attr>
    <attr name="hz_row" format="integer">
    </attr>
    <attr name="hz_selectedImage" format="reference">
    </attr>
    <attr name="hz_shadow_color" format="reference|color">
    </attr>
    <attr name="hz_shadow_effect" format="reference|boolean">
    </attr>
    <attr name="hz_shadow_offset" format="reference|dimension">
    </attr>
    <attr name="hz_shadow_radius" format="reference|dimension">
    </attr>
    <attr name="hz_show_loading" format="reference|boolean">
    </attr>
    <attr name="hz_singleLine" format="boolean">
    </attr>
    <attr name="hz_startColor" format="reference|color">
    </attr>
    <attr name="hz_startIcon" format="reference">
    </attr>
    <attr name="hz_startText" format="reference|string">
    </attr>
    <attr name="hz_step" format="float">
    </attr>
    <attr name="hz_styleType">
        <enum name="style_one" value="1" />
        <enum name="style_two" value="2" />
    </attr>
    <attr name="hz_subContent" format="string">
    </attr>
    <attr name="hz_tabMaxWidth" format="reference|dimension">
    </attr>
    <attr name="hz_tabMinWidth" format="reference|dimension">
    </attr>
    <attr name="hz_tab_align">
        <enum name="center" value="1" />
        <enum name="left" value="0" />
        <enum name="right" value="2" />
    </attr>
    <attr name="hz_tab_background" format="reference">
    </attr>
    <attr name="hz_text" format="string">
    </attr>
    <attr name="hz_textColor" format="color">
    </attr>
    <attr name="hz_textSize" format="dimension">
    </attr>
    <attr name="hz_thumbCircleColor" format="reference|color">
    </attr>
    <attr name="hz_thumbCircleRadius" format="float">
    </attr>
    <attr name="hz_thumbColor" format="reference|color">
    </attr>
    <attr name="hz_thumb_color" format="reference|color">
    </attr>
    <attr name="hz_thumb_drawable" format="reference">
    </attr>
    <attr name="hz_thumb_radius" format="reference|dimension">
    </attr>
    <attr name="hz_title" format="string">
    </attr>
    <attr name="hz_titleCDesc" format="string">
    </attr>
    <attr name="hz_titleIcon" format="reference">
    </attr>
    <attr name="hz_titleSingeLine" format="boolean">
    </attr>
    <attr name="hz_titleText" format="string">
    </attr>
    <attr name="hz_uncheck_color" format="reference|color">
    </attr>
    <attr name="hz_unit" format="string">
    </attr>
    <attr name="hz_unselectedImage" format="reference">
    </attr>
    <attr name="hz_valueArray" format="reference">
    </attr>
    <attr name="hz_verticalDrawable" format="reference">
    </attr>
    <attr name="hz_vertical_enable" format="boolean">
    </attr>
    <attr name="hz_vertical_icon" format="reference">
    </attr>
    <attr name="hz_vertical_limit" format="boolean">
    </attr>
    <attr name="hz_vertical_max" format="integer">
    </attr>
    <attr name="hz_vertical_min" format="integer">
    </attr>
    <attr name="hz_vertical_minchangecolor" format="integer">
    </attr>
    <attr name="hz_vertical_name" format="string">
    </attr>
    <attr name="hz_vertical_progress" format="integer">
    </attr>
    <attr name="hz_visibility">
        <enum name="gone" value="2" />
        <enum name="invisible" value="1" />
        <enum name="visible" value="0" />
    </attr>
    <attr name="hz_visibleCount" format="integer">
    </attr>
    <attr name="hz_width" format="reference|dimension">
    </attr>
    <attr name="icon" format="reference">
    </attr>
    <attr name="iconEndPadding" format="dimension">
    </attr>
    <attr name="iconGravity">
        <flag name="end" value="0x3" />
        <flag name="start" value="0x1" />
        <flag name="textEnd" value="0x4" />
        <flag name="textStart" value="0x2" />
    </attr>
    <attr name="iconPadding" format="dimension">
    </attr>
    <attr name="iconSize" format="dimension">
    </attr>
    <attr name="iconStartPadding" format="dimension">
    </attr>
    <attr name="iconTint" format="color">
    </attr>
    <attr name="iconTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="icon_left_height" format="dimension">
    </attr>
    <attr name="icon_left_width" format="dimension">
    </attr>
    <attr name="icon_reference" format="reference">
    </attr>
    <attr name="icon_right_height" format="dimension">
    </attr>
    <attr name="icon_right_width" format="dimension">
    </attr>
    <attr name="icon_uri" format="string">
    </attr>
    <attr name="iconifiedByDefault" format="boolean">
    </attr>
    <attr name="ifTagNotSet" format="reference">
    </attr>
    <attr name="ifTagSet" format="reference">
    </attr>
    <attr name="imageButtonStyle" format="reference">
    </attr>
    <attr name="imagePanX" format="float">
    </attr>
    <attr name="imagePanY" format="float">
    </attr>
    <attr name="imageRotate" format="float">
    </attr>
    <attr name="imageZoom" format="float">
    </attr>
    <attr name="indeterminateProgressStyle" format="reference">
    </attr>
    <attr name="indicatorColor" format="color">
    </attr>
    <attr name="indicatorEnable" format="boolean">
    </attr>
    <attr name="initialActivityCount" format="string">
    </attr>
    <attr name="innerBorderColor" format="color">
    </attr>
    <attr name="innerBorderWidth" format="dimension">
    </attr>
    <attr name="insetForeground" format="reference|color">
    </attr>
    <attr name="internalLayout" format="reference">
    </attr>
    <attr name="internalMaxHeight" format="dimension">
    </attr>
    <attr name="internalMaxWidth" format="dimension">
    </attr>
    <attr name="internalMinHeight" format="dimension">
    </attr>
    <attr name="internalMinWidth" format="dimension">
    </attr>
    <attr name="isLightTheme" format="boolean">
    </attr>
    <attr name="isMaterialTheme" format="boolean">
    </attr>
    <attr name="isOpen" format="boolean">
    </attr>
    <attr name="itemBackground" format="reference">
    </attr>
    <attr name="itemCount" format="integer">
    </attr>
    <attr name="itemDistance" format="dimension">
    </attr>
    <attr name="itemFillColor" format="color">
    </attr>
    <attr name="itemHorizontalPadding" format="dimension">
    </attr>
    <attr name="itemHorizontalTranslationEnabled" format="boolean">
    </attr>
    <attr name="itemIconPadding" format="dimension">
    </attr>
    <attr name="itemIconSize" format="dimension">
    </attr>
    <attr name="itemIconTint" format="color">
    </attr>
    <attr name="itemMaxLines" format="integer" min="1">
    </attr>
    <attr name="itemPadding" format="dimension">
    </attr>
    <attr name="itemRippleColor" format="color">
    </attr>
    <attr name="itemShapeAppearance" format="reference">
    </attr>
    <attr name="itemShapeAppearanceOverlay" format="reference">
    </attr>
    <attr name="itemShapeFillColor" format="color">
    </attr>
    <attr name="itemShapeInsetBottom" format="dimension">
    </attr>
    <attr name="itemShapeInsetEnd" format="dimension">
    </attr>
    <attr name="itemShapeInsetStart" format="dimension">
    </attr>
    <attr name="itemShapeInsetTop" format="dimension">
    </attr>
    <attr name="itemSpacing" format="dimension">
    </attr>
    <attr name="itemStrokeColor" format="color">
    </attr>
    <attr name="itemStrokeWidth" format="dimension">
    </attr>
    <attr name="itemTextAppearance" format="reference">
    </attr>
    <attr name="itemTextAppearanceActive" format="reference">
    </attr>
    <attr name="itemTextAppearanceInactive" format="reference">
    </attr>
    <attr name="itemTextColor" format="color">
    </attr>
    <attr name="jump" format="reference">
    </attr>
    <attr name="jump_url" format="string">
    </attr>
    <attr name="keyPositionType">
        <enum name="deltaRelative" value="0" />
        <enum name="parentRelative" value="2" />
        <enum name="pathRelative" value="1" />
    </attr>
    <attr name="keylines" format="reference">
    </attr>
    <attr name="labelBehavior">
        <enum name="floating" value="0" />
        <enum name="gone" value="2" />
        <enum name="withinBounds" value="1" />
    </attr>
    <attr name="labelStyle" format="reference">
    </attr>
    <attr name="labelVisibilityMode">
        <enum name="auto" value="-1" />
        <enum name="labeled" value="1" />
        <enum name="selected" value="0" />
        <enum name="unlabeled" value="2" />
    </attr>
    <attr name="lastBaselineToBottomHeight" format="dimension">
    </attr>
    <attr name="layout" format="reference">
    </attr>
    <attr name="layoutDescription" format="reference">
    </attr>
    <attr name="layoutDuringTransition">
        <enum name="callMeasure" value="2" />
        <enum name="honorRequest" value="1" />
        <enum name="ignoreRequest" value="0" />
    </attr>
    <attr name="layoutManager" format="string">
    </attr>
    <attr name="layout_anchor" format="reference">
    </attr>
    <attr name="layout_anchorGravity">
        <flag name="bottom" value="0x50" />
        <flag name="center" value="0x11" />
        <flag name="center_horizontal" value="0x1" />
        <flag name="center_vertical" value="0x10" />
        <flag name="clip_horizontal" value="0x8" />
        <flag name="clip_vertical" value="0x80" />
        <flag name="end" value="0x800005" />
        <flag name="fill" value="0x77" />
        <flag name="fill_horizontal" value="0x7" />
        <flag name="fill_vertical" value="0x70" />
        <flag name="left" value="0x3" />
        <flag name="right" value="0x5" />
        <flag name="start" value="0x800003" />
        <flag name="top" value="0x30" />
    </attr>
    <attr name="layout_behavior" format="string">
    </attr>
    <attr name="layout_collapseMode">
        <enum name="none" value="0" />
        <enum name="parallax" value="2" />
        <enum name="pin" value="1" />
    </attr>
    <attr name="layout_collapseParallaxMultiplier" format="float">
    </attr>
    <attr name="layout_constrainedHeight" format="boolean">
    </attr>
    <attr name="layout_constrainedWidth" format="boolean">
    </attr>
    <attr name="layout_constraintBaseline_creator" format="integer">
    </attr>
    <attr name="layout_constraintBaseline_toBaselineOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBaseline_toBottomOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBaseline_toTopOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBottom_creator" format="integer">
    </attr>
    <attr name="layout_constraintBottom_toBottomOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBottom_toTopOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintCircle" format="reference">
    </attr>
    <attr name="layout_constraintCircleAngle" format="float">
    </attr>
    <attr name="layout_constraintCircleRadius" format="dimension">
    </attr>
    <attr name="layout_constraintDimensionRatio" format="string">
    </attr>
    <attr name="layout_constraintEnd_toEndOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintEnd_toStartOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintGuide_begin" format="dimension">
    </attr>
    <attr name="layout_constraintGuide_end" format="dimension">
    </attr>
    <attr name="layout_constraintGuide_percent" format="float">
    </attr>
    <attr name="layout_constraintHeight" format="string|dimension">
        <enum name="match_constraint" value="-3" />
        <enum name="match_parent" value="-1" />
        <enum name="wrap_content" value="-2" />
        <enum name="wrap_content_constrained" value="-4" />
    </attr>
    <attr name="layout_constraintHeight_default">
        <enum name="percent" value="2" />
        <enum name="spread" value="0" />
        <enum name="wrap" value="1" />
    </attr>
    <attr name="layout_constraintHeight_max" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintHeight_min" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintHeight_percent" format="float">
    </attr>
    <attr name="layout_constraintHorizontal_bias" format="float">
    </attr>
    <attr name="layout_constraintHorizontal_chainStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="layout_constraintHorizontal_weight" format="float">
    </attr>
    <attr name="layout_constraintLeft_creator" format="integer">
    </attr>
    <attr name="layout_constraintLeft_toLeftOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintLeft_toRightOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintRight_creator" format="integer">
    </attr>
    <attr name="layout_constraintRight_toLeftOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintRight_toRightOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintStart_toEndOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintStart_toStartOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintTag" format="string">
    </attr>
    <attr name="layout_constraintTop_creator" format="integer">
    </attr>
    <attr name="layout_constraintTop_toBottomOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintTop_toTopOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintVertical_bias" format="float">
    </attr>
    <attr name="layout_constraintVertical_chainStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="layout_constraintVertical_weight" format="float">
    </attr>
    <attr name="layout_constraintWidth" format="string|dimension">
        <enum name="match_constraint" value="-3" />
        <enum name="match_parent" value="-1" />
        <enum name="wrap_content" value="-2" />
        <enum name="wrap_content_constrained" value="-4" />
    </attr>
    <attr name="layout_constraintWidth_default">
        <enum name="percent" value="2" />
        <enum name="spread" value="0" />
        <enum name="wrap" value="1" />
    </attr>
    <attr name="layout_constraintWidth_max" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintWidth_min" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintWidth_percent" format="float">
    </attr>
    <attr name="layout_dodgeInsetEdges">
        <flag name="all" value="0x77" />
        <flag name="bottom" value="0x50" />
        <flag name="end" value="0x800005" />
        <flag name="left" value="0x3" />
        <flag name="none" value="0x0" />
        <flag name="right" value="0x5" />
        <flag name="start" value="0x800003" />
        <flag name="top" value="0x30" />
    </attr>
    <attr name="layout_editor_absoluteX" format="dimension">
    </attr>
    <attr name="layout_editor_absoluteY" format="dimension">
    </attr>
    <attr name="layout_goneMarginBaseline" format="dimension">
    </attr>
    <attr name="layout_goneMarginBottom" format="dimension">
    </attr>
    <attr name="layout_goneMarginEnd" format="dimension">
    </attr>
    <attr name="layout_goneMarginLeft" format="dimension">
    </attr>
    <attr name="layout_goneMarginRight" format="dimension">
    </attr>
    <attr name="layout_goneMarginStart" format="dimension">
    </attr>
    <attr name="layout_goneMarginTop" format="dimension">
    </attr>
    <attr name="layout_insetEdge">
        <enum name="bottom" value="0x50" />
        <enum name="end" value="0x800005" />
        <enum name="left" value="0x3" />
        <enum name="none" value="0x0" />
        <enum name="right" value="0x5" />
        <enum name="start" value="0x800003" />
        <enum name="top" value="0x30" />
    </attr>
    <attr name="layout_keyline" format="integer">
    </attr>
    <attr name="layout_marginBaseline" format="dimension">
    </attr>
    <attr name="layout_optimizationLevel">
        <flag name="barrier" value="2" />
        <flag name="cache_measures" value="256" />
        <flag name="chains" value="4" />
        <flag name="dependency_ordering" value="512" />
        <flag name="dimensions" value="8" />
        <flag name="direct" value="1" />
        <flag name="graph" value="64" />
        <flag name="graph_wrap" value="128" />
        <flag name="grouping" value="1024" />
        <flag name="groups" value="32" />
        <flag name="legacy" value="0" />
        <flag name="none" value="0" />
        <flag name="ratio" value="16" />
        <flag name="standard" value="257" />
    </attr>
    <attr name="layout_scrollFlags">
        <flag name="enterAlways" value="0x4" />
        <flag name="enterAlwaysCollapsed" value="0x8" />
        <flag name="exitUntilCollapsed" value="0x2" />
        <flag name="noScroll" value="0x0" />
        <flag name="scroll" value="0x1" />
        <flag name="snap" value="0x10" />
        <flag name="snapMargins" value="0x20" />
    </attr>
    <attr name="layout_scrollInterpolator" format="reference">
    </attr>
    <attr name="layout_wrapBehaviorInParent">
        <enum name="horizontal_only" value="1" />
        <enum name="included" value="0" />
        <enum name="skipped" value="3" />
        <enum name="vertical_only" value="2" />
    </attr>
    <attr name="left_down_text" format="string">
    </attr>
    <attr name="left_icon" format="reference">
    </attr>
    <attr name="legacyLayout" format="reference">
    </attr>
    <attr name="liftOnScroll" format="boolean">
    </attr>
    <attr name="liftOnScrollTargetViewId" format="reference">
    </attr>
    <attr name="limitBoundsTo" format="reference">
    </attr>
    <attr name="lineHeight" format="dimension">
    </attr>
    <attr name="lineSpacing" format="dimension">
    </attr>
    <attr name="listChoiceBackgroundIndicator" format="reference">
    </attr>
    <attr name="listChoiceIndicatorMultipleAnimated" format="reference">
    </attr>
    <attr name="listChoiceIndicatorSingleAnimated" format="reference">
    </attr>
    <attr name="listDividerAlertDialog" format="reference">
    </attr>
    <attr name="listItemLayout" format="reference">
    </attr>
    <attr name="listLayout" format="reference">
    </attr>
    <attr name="listMenuViewStyle" format="reference">
    </attr>
    <attr name="listPopupWindowStyle" format="reference">
    </attr>
    <attr name="listPreferredItemHeight" format="dimension">
    </attr>
    <attr name="listPreferredItemHeightLarge" format="dimension">
    </attr>
    <attr name="listPreferredItemHeightSmall" format="dimension">
    </attr>
    <attr name="listPreferredItemPaddingEnd" format="dimension">
    </attr>
    <attr name="listPreferredItemPaddingLeft" format="dimension">
    </attr>
    <attr name="listPreferredItemPaddingRight" format="dimension">
    </attr>
    <attr name="listPreferredItemPaddingStart" format="dimension">
    </attr>
    <attr name="logo" format="reference">
    </attr>
    <attr name="logoDescription" format="string">
    </attr>
    <attr name="maskColor" format="color">
    </attr>
    <attr name="materialAlertDialogBodyTextStyle" format="reference">
    </attr>
    <attr name="materialAlertDialogTheme" format="reference">
    </attr>
    <attr name="materialAlertDialogTitleIconStyle" format="reference">
    </attr>
    <attr name="materialAlertDialogTitlePanelStyle" format="reference">
    </attr>
    <attr name="materialAlertDialogTitleTextStyle" format="reference">
    </attr>
    <attr name="materialButtonOutlinedStyle" format="reference">
    </attr>
    <attr name="materialButtonStyle" format="reference">
    </attr>
    <attr name="materialButtonToggleGroupStyle" format="reference">
    </attr>
    <attr name="materialCalendarDay" format="reference">
    </attr>
    <attr name="materialCalendarFullscreenTheme" format="reference">
    </attr>
    <attr name="materialCalendarHeaderConfirmButton" format="reference">
    </attr>
    <attr name="materialCalendarHeaderDivider" format="reference">
    </attr>
    <attr name="materialCalendarHeaderLayout" format="reference">
    </attr>
    <attr name="materialCalendarHeaderSelection" format="reference">
    </attr>
    <attr name="materialCalendarHeaderTitle" format="reference">
    </attr>
    <attr name="materialCalendarHeaderToggleButton" format="reference">
    </attr>
    <attr name="materialCalendarStyle" format="reference">
    </attr>
    <attr name="materialCalendarTheme" format="reference">
    </attr>
    <attr name="materialCardViewStyle" format="reference">
    </attr>
    <attr name="materialThemeOverlay" format="reference">
    </attr>
    <attr name="maxAcceleration" format="float">
    </attr>
    <attr name="maxActionInlineWidth" format="dimension">
    </attr>
    <attr name="maxButtonHeight" format="dimension">
    </attr>
    <attr name="maxCharacterCount" format="integer">
    </attr>
    <attr name="maxDate" format="string">
    </attr>
    <attr name="maxHeight" format="dimension">
    </attr>
    <attr name="maxImageSize" format="dimension">
    </attr>
    <attr name="maxLines" format="integer">
    </attr>
    <attr name="maxVelocity" format="float">
    </attr>
    <attr name="maxWidth" format="dimension">
    </attr>
    <attr name="measureWithLargestChild" format="boolean">
    </attr>
    <attr name="menu" format="reference">
    </attr>
    <attr name="methodName" format="string">
    </attr>
    <attr name="minDate" format="string">
    </attr>
    <attr name="minHeight" format="dimension">
    </attr>
    <attr name="minTouchTargetSize" format="dimension">
    </attr>
    <attr name="minWidth" format="dimension">
    </attr>
    <attr name="mock_diagonalsColor" format="color">
    </attr>
    <attr name="mock_label" format="string">
    </attr>
    <attr name="mock_labelBackgroundColor" format="color">
    </attr>
    <attr name="mock_labelColor" format="color">
    </attr>
    <attr name="mock_showDiagonals" format="boolean">
    </attr>
    <attr name="mock_showLabel" format="boolean">
    </attr>
    <attr name="motionDebug">
        <enum name="NO_DEBUG" value="0" />
        <enum name="SHOW_ALL" value="3" />
        <enum name="SHOW_PATH" value="2" />
        <enum name="SHOW_PROGRESS" value="1" />
    </attr>
    <attr name="motionEffect_alpha" format="float">
    </attr>
    <attr name="motionEffect_end" format="integer">
    </attr>
    <attr name="motionEffect_move">
        <enum name="auto" value="-1" />
        <enum name="east" value="2" />
        <enum name="north" value="0" />
        <enum name="south" value="1" />
        <enum name="west" value="3" />
    </attr>
    <attr name="motionEffect_start" format="integer">
    </attr>
    <attr name="motionEffect_strict" format="boolean">
    </attr>
    <attr name="motionEffect_translationX" format="dimension">
    </attr>
    <attr name="motionEffect_translationY" format="dimension">
    </attr>
    <attr name="motionEffect_viewTransition" format="reference">
    </attr>
    <attr name="motionInterpolator" format="reference|string">
        <enum name="anticipate" value="6" />
        <enum name="bounce" value="4" />
        <enum name="easeIn" value="1" />
        <enum name="easeInOut" value="0" />
        <enum name="easeOut" value="2" />
        <enum name="linear" value="3" />
        <enum name="overshoot" value="5" />
    </attr>
    <attr name="motionPathRotate" format="float">
    </attr>
    <attr name="motionProgress" format="float">
    </attr>
    <attr name="motionStagger" format="float">
    </attr>
    <attr name="motionTarget" format="reference|string">
    </attr>
    <attr name="motion_postLayoutCollision" format="boolean">
    </attr>
    <attr name="motion_triggerOnCollision" format="reference">
    </attr>
    <attr name="moveWhenScrollAtTop" format="boolean">
    </attr>
    <attr name="multiChoiceItemLayout" format="reference">
    </attr>
    <attr name="mute_icon" format="reference">
    </attr>
    <attr name="navigationContentDescription" format="string">
    </attr>
    <attr name="navigationIcon" format="reference">
    </attr>
    <attr name="navigationMode">
        <enum name="listMode" value="1" />
        <enum name="normal" value="0" />
        <enum name="tabMode" value="2" />
    </attr>
    <attr name="navigationViewStyle" format="reference">
    </attr>
    <attr name="nestedScrollFlags">
        <flag name="disablePostScroll" value="1" />
        <flag name="disableScroll" value="2" />
        <flag name="none" value="0" />
        <flag name="supportScrollUp" value="4" />
    </attr>
    <attr name="normal_drawable" format="reference">
    </attr>
    <attr name="number" format="integer">
    </attr>
    <attr name="numberPickerStyle" format="reference">
    </attr>
    <attr name="numbersBackgroundColor" format="color">
    </attr>
    <attr name="numbersInnerTextColor" format="color">
    </attr>
    <attr name="numbersSelectorColor" format="color">
    </attr>
    <attr name="numbersTextColor" format="color">
    </attr>
    <attr name="numericModifiers">
        <flag name="ALT" value="0x2" />
        <flag name="CTRL" value="0x1000" />
        <flag name="FUNCTION" value="0x8" />
        <flag name="META" value="0x10000" />
        <flag name="SHIFT" value="0x1" />
        <flag name="SYM" value="0x4" />
    </attr>
    <attr name="onCross" format="string">
    </attr>
    <attr name="onHide" format="boolean">
    </attr>
    <attr name="onNegativeCross" format="string">
    </attr>
    <attr name="onPositiveCross" format="string">
    </attr>
    <attr name="onShow" format="boolean">
    </attr>
    <attr name="onStateTransition">
        <enum name="actionDown" value="1" />
        <enum name="actionDownUp" value="3" />
        <enum name="actionUp" value="2" />
        <enum name="sharedValueSet" value="4" />
        <enum name="sharedValueUnset" value="5" />
    </attr>
    <attr name="onTouchUp">
        <enum name="autoComplete" value="0" />
        <enum name="autoCompleteToEnd" value="2" />
        <enum name="autoCompleteToStart" value="1" />
        <enum name="decelerate" value="4" />
        <enum name="decelerateAndComplete" value="5" />
        <enum name="neverCompleteToEnd" value="7" />
        <enum name="neverCompleteToStart" value="6" />
        <enum name="stop" value="3" />
    </attr>
    <attr name="orientation" format="integer">
        <enum name="horizontal" value="0" />
        <enum name="vertical" value="1" />
    </attr>
    <attr name="overlapAnchor" format="boolean">
    </attr>
    <attr name="overlay" format="boolean">
    </attr>
    <attr name="overlayImage" format="reference">
    </attr>
    <attr name="paddingBottomNoButtons" format="dimension">
    </attr>
    <attr name="paddingBottomSystemWindowInsets" format="boolean">
    </attr>
    <attr name="paddingEnd" format="dimension">
    </attr>
    <attr name="paddingLeftSystemWindowInsets" format="boolean">
    </attr>
    <attr name="paddingRightSystemWindowInsets" format="boolean">
    </attr>
    <attr name="paddingStart" format="dimension">
    </attr>
    <attr name="paddingTopNoTitle" format="dimension">
    </attr>
    <attr name="panelBackground" format="reference">
    </attr>
    <attr name="panelMenuListTheme" format="reference">
    </attr>
    <attr name="panelMenuListWidth" format="dimension">
    </attr>
    <attr name="passwordToggleContentDescription" format="string">
    </attr>
    <attr name="passwordToggleDrawable" format="reference">
    </attr>
    <attr name="passwordToggleEnabled" format="boolean">
    </attr>
    <attr name="passwordToggleTint" format="color">
    </attr>
    <attr name="passwordToggleTintMode">
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="pathMotionArc">
        <enum name="flip" value="3" />
        <enum name="none" value="0" />
        <enum name="startHorizontal" value="2" />
        <enum name="startVertical" value="1" />
    </attr>
    <attr name="path_percent" format="float">
    </attr>
    <attr name="percentHeight" format="float">
    </attr>
    <attr name="percentWidth" format="float">
    </attr>
    <attr name="percentX" format="float">
    </attr>
    <attr name="percentY" format="float">
    </attr>
    <attr name="perpendicularPath_percent" format="float">
    </attr>
    <attr name="pivotAnchor" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="placeholderImage" format="reference">
    </attr>
    <attr name="placeholderImageScaleType">
        <enum name="center" value="4" />
        <enum name="centerCrop" value="6" />
        <enum name="centerInside" value="5" />
        <enum name="fitBottomStart" value="8" />
        <enum name="fitCenter" value="2" />
        <enum name="fitEnd" value="3" />
        <enum name="fitStart" value="1" />
        <enum name="fitXY" value="0" />
        <enum name="focusCrop" value="7" />
        <enum name="none" value="-1" />
    </attr>
    <attr name="placeholderText" format="string">
    </attr>
    <attr name="placeholderTextAppearance" format="reference">
    </attr>
    <attr name="placeholderTextColor" format="color">
    </attr>
    <attr name="placeholder_emptyVisibility">
        <enum name="gone" value="8" />
        <enum name="invisible" value="4" />
        <enum name="visible" value="0" />
    </attr>
    <attr name="polarRelativeTo" format="reference">
    </attr>
    <attr name="popupMenuBackground" format="reference">
    </attr>
    <attr name="popupMenuStyle" format="reference">
    </attr>
    <attr name="popupTheme" format="reference">
    </attr>
    <attr name="popupWindowStyle" format="reference">
    </attr>
    <attr name="prefixText" format="string">
    </attr>
    <attr name="prefixTextAppearance" format="reference">
    </attr>
    <attr name="prefixTextColor" format="color">
    </attr>
    <attr name="preserveIconSpacing" format="boolean">
    </attr>
    <attr name="pressedStateOverlayImage" format="reference">
    </attr>
    <attr name="pressedTranslationZ" format="dimension">
    </attr>
    <attr name="progressBarAutoRotateInterval" format="integer">
    </attr>
    <attr name="progressBarImage" format="reference">
    </attr>
    <attr name="progressBarImageScaleType">
        <enum name="center" value="4" />
        <enum name="centerCrop" value="6" />
        <enum name="centerInside" value="5" />
        <enum name="fitBottomStart" value="8" />
        <enum name="fitCenter" value="2" />
        <enum name="fitEnd" value="3" />
        <enum name="fitStart" value="1" />
        <enum name="fitXY" value="0" />
        <enum name="focusCrop" value="7" />
        <enum name="none" value="-1" />
    </attr>
    <attr name="progressBarPadding" format="dimension">
    </attr>
    <attr name="progressBarStyle" format="reference">
    </attr>
    <attr name="progressTextColor" format="color">
    </attr>
    <attr name="progressTextSize" format="dimension">
    </attr>
    <attr name="quantizeMotionInterpolator" format="reference|string">
        <enum name="bounce" value="4" />
        <enum name="easeIn" value="1" />
        <enum name="easeInOut" value="0" />
        <enum name="easeOut" value="2" />
        <enum name="linear" value="3" />
        <enum name="overshoot" value="5" />
    </attr>
    <attr name="quantizeMotionPhase" format="float">
    </attr>
    <attr name="quantizeMotionSteps" format="integer">
    </attr>
    <attr name="queryBackground" format="reference">
    </attr>
    <attr name="queryHint" format="string">
    </attr>
    <attr name="radioButtonStyle" format="reference">
    </attr>
    <attr name="radius" format="dimension">
    </attr>
    <attr name="rangeFillColor" format="color">
    </attr>
    <attr name="ratingBarStyle" format="reference">
    </attr>
    <attr name="ratingBarStyleIndicator" format="reference">
    </attr>
    <attr name="ratingBarStyleSmall" format="reference">
    </attr>
    <attr name="reactiveGuide_animateChange" format="boolean">
    </attr>
    <attr name="reactiveGuide_applyToAllConstraintSets" format="boolean">
    </attr>
    <attr name="reactiveGuide_applyToConstraintSet" format="reference">
    </attr>
    <attr name="reactiveGuide_valueId" format="reference">
    </attr>
    <attr name="recyclerViewStyle" format="reference">
    </attr>
    <attr name="region_heightLessThan" format="dimension">
    </attr>
    <attr name="region_heightMoreThan" format="dimension">
    </attr>
    <attr name="region_widthLessThan" format="dimension">
    </attr>
    <attr name="region_widthMoreThan" format="dimension">
    </attr>
    <attr name="retryImage" format="reference">
    </attr>
    <attr name="retryImageScaleType">
        <enum name="center" value="4" />
        <enum name="centerCrop" value="6" />
        <enum name="centerInside" value="5" />
        <enum name="fitBottomStart" value="8" />
        <enum name="fitCenter" value="2" />
        <enum name="fitEnd" value="3" />
        <enum name="fitStart" value="1" />
        <enum name="fitXY" value="0" />
        <enum name="focusCrop" value="7" />
        <enum name="none" value="-1" />
    </attr>
    <attr name="reverseLayout" format="boolean">
    </attr>
    <attr name="rightDisplayMode">
        <enum name="button" value="1" />
        <enum name="icon" value="0" />
    </attr>
    <attr name="right_down_text" format="string">
    </attr>
    <attr name="right_icon" format="reference">
    </attr>
    <attr name="ringColor" format="color">
    </attr>
    <attr name="ringWidth" format="float">
    </attr>
    <attr name="rippleColor" format="color">
    </attr>
    <attr name="rotationCenterId" format="reference">
    </attr>
    <attr name="round" format="dimension">
    </attr>
    <attr name="roundAsCircle" format="boolean">
    </attr>
    <attr name="roundBottomEnd" format="boolean">
    </attr>
    <attr name="roundBottomLeft" format="boolean">
    </attr>
    <attr name="roundBottomRight" format="boolean">
    </attr>
    <attr name="roundBottomStart" format="boolean">
    </attr>
    <attr name="roundPercent" format="float">
    </attr>
    <attr name="roundTopEnd" format="boolean">
    </attr>
    <attr name="roundTopLeft" format="boolean">
    </attr>
    <attr name="roundTopRight" format="boolean">
    </attr>
    <attr name="roundTopStart" format="boolean">
    </attr>
    <attr name="roundWithOverlayColor" format="color">
    </attr>
    <attr name="roundedCornerRadius" format="dimension">
    </attr>
    <attr name="roundingBorderColor" format="color">
    </attr>
    <attr name="roundingBorderPadding" format="dimension">
    </attr>
    <attr name="roundingBorderWidth" format="dimension">
    </attr>
    <attr name="saturation" format="float">
    </attr>
    <attr name="scaleFromTextSize" format="dimension">
    </attr>
    <attr name="scrimAnimationDuration" format="integer">
    </attr>
    <attr name="scrimBackground" format="reference|color">
    </attr>
    <attr name="scrimVisibleHeightTrigger" format="dimension">
    </attr>
    <attr name="searchHintIcon" format="reference">
    </attr>
    <attr name="searchIcon" format="reference">
    </attr>
    <attr name="searchViewStyle" format="reference">
    </attr>
    <attr name="second_desc" format="string">
    </attr>
    <attr name="second_icon" format="reference">
    </attr>
    <attr name="second_text" format="string">
    </attr>
    <attr name="seekBarStyle" format="reference">
    </attr>
    <attr name="selectableItemBackground" format="reference">
    </attr>
    <attr name="selectableItemBackgroundBorderless" format="reference">
    </attr>
    <attr name="selectedAdditionalDistance" format="dimension">
    </attr>
    <attr name="selected_drawable" format="reference">
    </attr>
    <attr name="selectionDivider" format="reference">
    </attr>
    <attr name="selectionDividerHeight" format="dimension">
    </attr>
    <attr name="selectionDividersDistance" format="dimension">
    </attr>
    <attr name="selectionRequired" format="boolean">
    </attr>
    <attr name="setsTag" format="reference">
    </attr>
    <attr name="shapeAppearance" format="reference">
    </attr>
    <attr name="shapeAppearanceLargeComponent" format="reference">
    </attr>
    <attr name="shapeAppearanceMediumComponent" format="reference">
    </attr>
    <attr name="shapeAppearanceOverlay" format="reference">
    </attr>
    <attr name="shapeAppearanceSmallComponent" format="reference">
    </attr>
    <attr name="showAsAction">
        <flag name="always" value="2" />
        <flag name="collapseActionView" value="8" />
        <flag name="ifRoom" value="1" />
        <flag name="never" value="0" />
        <flag name="withText" value="4" />
    </attr>
    <attr name="showDay" format="boolean">
    </attr>
    <attr name="showDividers">
        <flag name="beginning" value="1" />
        <flag name="end" value="4" />
        <flag name="middle" value="2" />
        <flag name="none" value="0" />
    </attr>
    <attr name="showHour" format="boolean">
    </attr>
    <attr name="showMinute" format="boolean">
    </attr>
    <attr name="showMonth" format="boolean">
    </attr>
    <attr name="showMotionSpec" format="reference">
    </attr>
    <attr name="showPaths" format="boolean">
    </attr>
    <attr name="showText" format="boolean">
    </attr>
    <attr name="showTitle" format="boolean">
    </attr>
    <attr name="showYear" format="boolean">
    </attr>
    <attr name="shrinkMotionSpec" format="reference">
    </attr>
    <attr name="singleChoiceItemLayout" format="reference">
    </attr>
    <attr name="singleLine" format="boolean">
    </attr>
    <attr name="singleSelection" format="boolean">
    </attr>
    <attr name="six_desc" format="string">
    </attr>
    <attr name="six_text" format="string">
    </attr>
    <attr name="sizePercent" format="float">
    </attr>
    <attr name="sliderStyle" format="reference">
    </attr>
    <attr name="snackbarButtonStyle" format="reference">
    </attr>
    <attr name="snackbarStyle" format="reference">
    </attr>
    <attr name="snackbarTextViewStyle" format="reference">
    </attr>
    <attr name="solidColor" format="reference|color">
    </attr>
    <attr name="spanCount" format="integer">
    </attr>
    <attr name="spinBars" format="boolean">
    </attr>
    <attr name="spinnerDropDownItemStyle" format="reference">
    </attr>
    <attr name="spinnerStyle" format="reference">
    </attr>
    <attr name="spinnersShown" format="boolean">
    </attr>
    <attr name="splitTrack" format="boolean">
    </attr>
    <attr name="springBoundary">
        <flag name="bounceBoth" value="3" />
        <flag name="bounceEnd" value="2" />
        <flag name="bounceStart" value="1" />
        <flag name="overshoot" value="0" />
    </attr>
    <attr name="springDamping" format="float">
    </attr>
    <attr name="springMass" format="float">
    </attr>
    <attr name="springStiffness" format="float">
    </attr>
    <attr name="springStopThreshold" format="float">
    </attr>
    <attr name="srcCompat" format="reference">
    </attr>
    <attr name="srcpath" format="string">
    </attr>
    <attr name="stackFromEnd" format="boolean">
    </attr>
    <attr name="staggered" format="float">
    </attr>
    <attr name="startDay" format="integer">
    </attr>
    <attr name="startHour" format="integer">
    </attr>
    <attr name="startIconCheckable" format="boolean">
    </attr>
    <attr name="startIconContentDescription" format="string">
    </attr>
    <attr name="startIconDrawable" format="reference">
    </attr>
    <attr name="startIconTint" format="color">
    </attr>
    <attr name="startIconTintMode">
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="startMinute" format="integer">
    </attr>
    <attr name="startMonth" format="integer">
    </attr>
    <attr name="startYear" format="integer">
    </attr>
    <attr name="start_icon" format="reference">
    </attr>
    <attr name="state_above_anchor" format="boolean">
    </attr>
    <attr name="state_collapsed" format="boolean">
    </attr>
    <attr name="state_collapsible" format="boolean">
    </attr>
    <attr name="state_dragged" format="boolean">
    </attr>
    <attr name="state_liftable" format="boolean">
    </attr>
    <attr name="state_lifted" format="boolean">
    </attr>
    <attr name="state_switch_icon" format="reference">
    </attr>
    <attr name="state_switch_text" format="string">
    </attr>
    <attr name="statusBarBackground" format="reference|color">
    </attr>
    <attr name="statusBarForeground" format="color">
    </attr>
    <attr name="statusBarScrim" format="color">
    </attr>
    <attr name="strokeColor" format="color">
    </attr>
    <attr name="strokeWidth" format="dimension">
    </attr>
    <attr name="subMenuArrow" format="reference">
    </attr>
    <attr name="submitBackground" format="reference">
    </attr>
    <attr name="subtitle" format="string">
    </attr>
    <attr name="subtitleTextAppearance" format="reference">
    </attr>
    <attr name="subtitleTextColor" format="color">
    </attr>
    <attr name="subtitleTextStyle" format="reference">
    </attr>
    <attr name="suffixText" format="string">
    </attr>
    <attr name="suffixTextAppearance" format="reference">
    </attr>
    <attr name="suffixTextColor" format="color">
    </attr>
    <attr name="suggestionRowLayout" format="reference">
    </attr>
    <attr name="swb_animationDuration" format="integer">
    </attr>
    <attr name="swb_backColor" format="reference|color">
    </attr>
    <attr name="swb_backDrawable" format="reference">
    </attr>
    <attr name="swb_backRadius" format="reference|dimension">
    </attr>
    <attr name="swb_fadeBack" format="boolean">
    </attr>
    <attr name="swb_textAdjust" format="dimension">
    </attr>
    <attr name="swb_textExtra" format="dimension">
    </attr>
    <attr name="swb_textOff" format="string">
    </attr>
    <attr name="swb_textOn" format="string">
    </attr>
    <attr name="swb_textThumbInset" format="dimension">
    </attr>
    <attr name="swb_thumbColor" format="reference|color">
    </attr>
    <attr name="swb_thumbDrawable" format="reference">
    </attr>
    <attr name="swb_thumbHeight" format="reference|dimension">
    </attr>
    <attr name="swb_thumbMargin" format="reference|dimension">
    </attr>
    <attr name="swb_thumbMarginBottom" format="reference|dimension">
    </attr>
    <attr name="swb_thumbMarginLeft" format="reference|dimension">
    </attr>
    <attr name="swb_thumbMarginRight" format="reference|dimension">
    </attr>
    <attr name="swb_thumbMarginTop" format="reference|dimension">
    </attr>
    <attr name="swb_thumbRadius" format="reference|dimension">
    </attr>
    <attr name="swb_thumbRangeRatio" format="float">
    </attr>
    <attr name="swb_thumbWidth" format="reference|dimension">
    </attr>
    <attr name="swb_tintColor" format="reference|color">
    </attr>
    <attr name="switchMinWidth" format="dimension">
    </attr>
    <attr name="switchPadding" format="dimension">
    </attr>
    <attr name="switchStyle" format="reference">
    </attr>
    <attr name="switchTextAppearance" format="reference">
    </attr>
    <attr name="switch_title" format="string">
    </attr>
    <attr name="tabBackground" format="reference">
    </attr>
    <attr name="tabContentStart" format="dimension">
    </attr>
    <attr name="tabGravity">
        <enum name="center" value="1" />
        <enum name="fill" value="0" />
        <enum name="start" value="2" />
    </attr>
    <attr name="tabIconTint" format="color">
    </attr>
    <attr name="tabIconTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="tabIndicator" format="reference">
    </attr>
    <attr name="tabIndicatorAnimationDuration" format="integer">
    </attr>
    <attr name="tabIndicatorColor" format="color">
    </attr>
    <attr name="tabIndicatorFullWidth" format="boolean">
    </attr>
    <attr name="tabIndicatorGravity">
        <enum name="bottom" value="0" />
        <enum name="center" value="1" />
        <enum name="stretch" value="3" />
        <enum name="top" value="2" />
    </attr>
    <attr name="tabIndicatorHeight" format="dimension">
    </attr>
    <attr name="tabInlineLabel" format="boolean">
    </attr>
    <attr name="tabMaxWidth" format="dimension">
    </attr>
    <attr name="tabMinWidth" format="dimension">
    </attr>
    <attr name="tabMode">
        <enum name="auto" value="2" />
        <enum name="fixed" value="1" />
        <enum name="scrollable" value="0" />
    </attr>
    <attr name="tabPadding" format="dimension">
    </attr>
    <attr name="tabPaddingBottom" format="dimension">
    </attr>
    <attr name="tabPaddingEnd" format="dimension">
    </attr>
    <attr name="tabPaddingStart" format="dimension">
    </attr>
    <attr name="tabPaddingTop" format="dimension">
    </attr>
    <attr name="tabRippleColor" format="color">
    </attr>
    <attr name="tabSelectedTextColor" format="color">
    </attr>
    <attr name="tabStyle" format="reference">
    </attr>
    <attr name="tabTextAppearance" format="reference">
    </attr>
    <attr name="tabTextColor" format="color">
    </attr>
    <attr name="tabUnboundedRipple" format="boolean">
    </attr>
    <attr name="targetId" format="reference">
    </attr>
    <attr name="telltales_tailColor" format="color">
    </attr>
    <attr name="telltales_tailScale" format="float">
    </attr>
    <attr name="telltales_velocityMode">
        <enum name="layout" value="0" />
        <enum name="postLayout" value="1" />
        <enum name="staticLayout" value="3" />
        <enum name="staticPostLayout" value="2" />
    </attr>
    <attr name="text" format="string">
    </attr>
    <attr name="textAllCaps" format="reference|boolean">
    </attr>
    <attr name="textAppearanceBody1" format="reference">
    </attr>
    <attr name="textAppearanceBody2" format="reference">
    </attr>
    <attr name="textAppearanceButton" format="reference">
    </attr>
    <attr name="textAppearanceCaption" format="reference">
    </attr>
    <attr name="textAppearanceHeadline1" format="reference">
    </attr>
    <attr name="textAppearanceHeadline2" format="reference">
    </attr>
    <attr name="textAppearanceHeadline3" format="reference">
    </attr>
    <attr name="textAppearanceHeadline4" format="reference">
    </attr>
    <attr name="textAppearanceHeadline5" format="reference">
    </attr>
    <attr name="textAppearanceHeadline6" format="reference">
    </attr>
    <attr name="textAppearanceLargePopupMenu" format="reference">
    </attr>
    <attr name="textAppearanceLineHeightEnabled" format="boolean">
    </attr>
    <attr name="textAppearanceListItem" format="reference">
    </attr>
    <attr name="textAppearanceListItemSecondary" format="reference">
    </attr>
    <attr name="textAppearanceListItemSmall" format="reference">
    </attr>
    <attr name="textAppearanceOverline" format="reference">
    </attr>
    <attr name="textAppearancePopupMenuHeader" format="reference">
    </attr>
    <attr name="textAppearanceSearchResultSubtitle" format="reference">
    </attr>
    <attr name="textAppearanceSearchResultTitle" format="reference">
    </attr>
    <attr name="textAppearanceSmallPopupMenu" format="reference">
    </attr>
    <attr name="textAppearanceSubtitle1" format="reference">
    </attr>
    <attr name="textAppearanceSubtitle2" format="reference">
    </attr>
    <attr name="textBackground" format="reference">
    </attr>
    <attr name="textBackgroundPanX" format="float">
    </attr>
    <attr name="textBackgroundPanY" format="float">
    </attr>
    <attr name="textBackgroundRotate" format="float">
    </attr>
    <attr name="textBackgroundZoom" format="float">
    </attr>
    <attr name="textColorAlertDialogListItem" format="reference|color">
    </attr>
    <attr name="textColorNormal" format="color">
    </attr>
    <attr name="textColorSearchUrl" format="reference|color">
    </attr>
    <attr name="textColorSelected" format="color">
    </attr>
    <attr name="textDefaultWidth" format="dimension">
    </attr>
    <attr name="textEndPadding" format="dimension">
    </attr>
    <attr name="textFillColor" format="color">
    </attr>
    <attr name="textInputLayoutFocusedRectEnabled" format="boolean">
    </attr>
    <attr name="textInputStyle" format="reference">
    </attr>
    <attr name="textLocale" format="string">
    </attr>
    <attr name="textOutlineColor" format="color">
    </attr>
    <attr name="textOutlineThickness" format="dimension">
    </attr>
    <attr name="textPanX" format="float">
    </attr>
    <attr name="textPanY" format="float">
    </attr>
    <attr name="textSizeNormal" format="dimension">
    </attr>
    <attr name="textSizeSelected" format="dimension">
    </attr>
    <attr name="textStartPadding" format="dimension">
    </attr>
    <attr name="text_color" format="color">
    </attr>
    <attr name="text_size" format="dimension">
    </attr>
    <attr name="textureBlurFactor" format="integer">
    </attr>
    <attr name="textureEffect">
        <enum name="frost" value="1" />
        <enum name="none" value="0" />
    </attr>
    <attr name="textureHeight" format="dimension">
    </attr>
    <attr name="textureWidth" format="dimension">
    </attr>
    <attr name="theme" format="reference">
    </attr>
    <attr name="themeLineHeight" format="dimension">
    </attr>
    <attr name="thickness" format="dimension">
    </attr>
    <attr name="three_desc" format="string">
    </attr>
    <attr name="three_icon" format="reference">
    </attr>
    <attr name="three_text" format="string">
    </attr>
    <attr name="thumbColor" format="color">
    </attr>
    <attr name="thumbElevation" format="dimension">
    </attr>
    <attr name="thumbRadius" format="dimension">
    </attr>
    <attr name="thumbTextPadding" format="dimension">
    </attr>
    <attr name="thumbTint" format="color">
    </attr>
    <attr name="thumbTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="tickColor" format="color">
    </attr>
    <attr name="tickColorActive" format="color">
    </attr>
    <attr name="tickColorInactive" format="color">
    </attr>
    <attr name="tickMark" format="reference">
    </attr>
    <attr name="tickMarkTint" format="color">
    </attr>
    <attr name="tickMarkTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="timePickerMode">
        <enum name="clock" value="2" />
        <enum name="spinner" value="1" />
    </attr>
    <attr name="timePickerStyle" format="reference">
    </attr>
    <attr name="tint" format="color">
    </attr>
    <attr name="tintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="title" format="string">
    </attr>
    <attr name="titleEnabled" format="boolean">
    </attr>
    <attr name="titleMargin" format="dimension">
    </attr>
    <attr name="titleMarginBottom" format="dimension">
    </attr>
    <attr name="titleMarginEnd" format="dimension">
    </attr>
    <attr name="titleMarginStart" format="dimension">
    </attr>
    <attr name="titleMarginTop" format="dimension">
    </attr>
    <attr name="titleMargins" format="dimension">
    </attr>
    <attr name="titleTextAppearance" format="reference">
    </attr>
    <attr name="titleTextColor" format="color">
    </attr>
    <attr name="titleTextStyle" format="reference">
    </attr>
    <attr name="title_text" format="string">
    </attr>
    <attr name="toolbarId" format="reference">
    </attr>
    <attr name="toolbarNavigationButtonStyle" format="reference">
    </attr>
    <attr name="toolbarStyle" format="reference">
    </attr>
    <attr name="tooltipForegroundColor" format="reference|color">
    </attr>
    <attr name="tooltipFrameBackground" format="reference">
    </attr>
    <attr name="tooltipStyle" format="reference">
    </attr>
    <attr name="tooltipText" format="string">
    </attr>
    <attr name="topLeftRadius" format="dimension">
    </attr>
    <attr name="topRightRadius" format="dimension">
    </attr>
    <attr name="touchAnchorId" format="reference">
    </attr>
    <attr name="touchAnchorSide">
        <enum name="bottom" value="3" />
        <enum name="end" value="6" />
        <enum name="left" value="1" />
        <enum name="middle" value="4" />
        <enum name="right" value="2" />
        <enum name="start" value="5" />
        <enum name="top" value="0" />
    </attr>
    <attr name="touchRegionId" format="reference">
    </attr>
    <attr name="track" format="reference">
    </attr>
    <attr name="trackColor" format="color">
    </attr>
    <attr name="trackColorActive" format="color">
    </attr>
    <attr name="trackColorInactive" format="color">
    </attr>
    <attr name="trackHeight" format="dimension">
    </attr>
    <attr name="trackTint" format="color">
    </attr>
    <attr name="trackTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="transformPivotTarget" format="reference">
    </attr>
    <attr name="transitionDisable" format="boolean">
    </attr>
    <attr name="transitionEasing" format="string">
        <enum name="accelerate" value="1" />
        <enum name="decelerate" value="2" />
        <enum name="linear" value="3" />
        <enum name="standard" value="0" />
    </attr>
    <attr name="transitionFlags">
        <flag name="beginOnFirstDraw" value="1" />
        <flag name="disableIntraAutoTransition" value="2" />
        <flag name="none" value="0" />
        <flag name="onInterceptTouchReturnSwipe" value="4" />
    </attr>
    <attr name="transitionPathRotate" format="float">
    </attr>
    <attr name="transitionShapeAppearance" format="reference">
    </attr>
    <attr name="triggerId" format="reference">
    </attr>
    <attr name="triggerReceiver" format="reference">
    </attr>
    <attr name="triggerSlack" format="float">
    </attr>
    <attr name="ttcIndex" format="integer">
    </attr>
    <attr name="upDuration" format="integer">
    </attr>
    <attr name="useCompatPadding" format="boolean">
    </attr>
    <attr name="useMaterialThemeColors" format="boolean">
    </attr>
    <attr name="values" format="reference">
    </attr>
    <attr name="verticalOffset" format="dimension">
    </attr>
    <attr name="viewAspectRatio" format="float">
    </attr>
    <attr name="viewInflaterClass" format="string">
    </attr>
    <attr name="viewTransitionMode">
        <enum name="allStates" value="1" />
        <enum name="currentState" value="0" />
        <enum name="noState" value="2" />
    </attr>
    <attr name="viewTransitionOnCross" format="reference">
    </attr>
    <attr name="viewTransitionOnNegativeCross" format="reference">
    </attr>
    <attr name="viewTransitionOnPositiveCross" format="reference">
    </attr>
    <attr name="virtualButtonPressedDrawable" format="reference">
    </attr>
    <attr name="visibilityMode">
        <enum name="ignore" value="1" />
        <enum name="normal" value="0" />
    </attr>
    <attr name="voiceIcon" format="reference">
    </attr>
    <attr name="vpi_orientation">
        <enum name="horizontal" value="0" />
        <enum name="rtl" value="3" />
        <enum name="vertical" value="1" />
    </attr>
    <attr name="vpi_rtl" format="boolean">
    </attr>
    <attr name="vpi_slide_mode">
        <enum name="color" value="5" />
        <enum name="normal" value="0" />
        <enum name="scale" value="4" />
        <enum name="smooth" value="2" />
        <enum name="worm" value="3" />
    </attr>
    <attr name="vpi_slider_checked_color" format="color">
    </attr>
    <attr name="vpi_slider_normal_color" format="color">
    </attr>
    <attr name="vpi_slider_radius" format="dimension">
    </attr>
    <attr name="vpi_style">
        <enum name="circle" value="0" />
        <enum name="dash" value="2" />
        <enum name="round_rect" value="4" />
    </attr>
    <attr name="warmth" format="float">
    </attr>
    <attr name="waveDecay" format="integer">
    </attr>
    <attr name="waveOffset" format="float|dimension">
    </attr>
    <attr name="wavePeriod" format="float">
    </attr>
    <attr name="wavePhase" format="float">
    </attr>
    <attr name="waveShape" format="string">
        <enum name="bounce" value="6" />
        <enum name="cos" value="5" />
        <enum name="reverseSawtooth" value="4" />
        <enum name="sawtooth" value="3" />
        <enum name="sin" value="0" />
        <enum name="square" value="1" />
        <enum name="triangle" value="2" />
    </attr>
    <attr name="waveVariesBy">
        <enum name="path" value="1" />
        <enum name="position" value="0" />
    </attr>
    <attr name="widthSpace" format="dimension">
    </attr>
    <attr name="windowActionBar" format="boolean">
    </attr>
    <attr name="windowActionBarOverlay" format="boolean">
    </attr>
    <attr name="windowActionModeOverlay" format="boolean">
    </attr>
    <attr name="windowFixedHeightMajor" format="dimension|fraction">
    </attr>
    <attr name="windowFixedHeightMinor" format="dimension|fraction">
    </attr>
    <attr name="windowFixedWidthMajor" format="dimension|fraction">
    </attr>
    <attr name="windowFixedWidthMinor" format="dimension|fraction">
    </attr>
    <attr name="windowMinWidthMajor" format="dimension|fraction">
    </attr>
    <attr name="windowMinWidthMinor" format="dimension|fraction">
    </attr>
    <attr name="windowNoTitle" format="boolean">
    </attr>
    <attr name="yearListItemActivatedTextAppearance" format="reference">
    </attr>
    <attr name="yearListItemTextAppearance" format="reference">
    </attr>
    <attr name="yearListSelectorColor" format="color">
    </attr>
    <attr name="yearSelectedStyle" format="reference">
    </attr>
    <attr name="yearStyle" format="reference">
    </attr>
    <attr name="yearTodayStyle" format="reference">
    </attr>
    <attr name="zxing_framing_rect_height" format="dimension">
    </attr>
    <attr name="zxing_framing_rect_width" format="dimension">
    </attr>
    <attr name="zxing_possible_result_points" format="color">
    </attr>
    <attr name="zxing_preview_scaling_strategy">
        <enum name="centerCrop" value="1" />
        <enum name="fitCenter" value="2" />
        <enum name="fitXY" value="3" />
    </attr>
    <attr name="zxing_result_view" format="color">
    </attr>
    <attr name="zxing_scanner_layout" format="reference">
    </attr>
    <attr name="zxing_use_texture_view" format="boolean">
    </attr>
    <attr name="zxing_viewfinder_laser" format="color">
    </attr>
    <attr name="zxing_viewfinder_mask" format="color">
    </attr>
</resources>
