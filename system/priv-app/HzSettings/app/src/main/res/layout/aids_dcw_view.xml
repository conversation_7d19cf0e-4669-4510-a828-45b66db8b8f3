<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/clDowLayout"
    android:paddingBottom="@dimen/dp_10"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.hozon.settings.view.FontScaleTextView
        android:id="@+id/title"
        android:text="@string/adas_dcw_fun_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/FunTitle"/>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clDow"
        android:layout_marginTop="@dimen/px_6"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title"
        style="@style/AidsCard">
        <com.hozonauto.widget.SwitchButton
            android:id="@+id/dowSwitch"
            android:checked="true"
            android:contentDescription="@string/adas_dcw_card_title_switch"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/SwitchButton"/>
        <com.hozon.settings.view.FontScaleTextView
            android:id="@+id/dowTv"
            android:text="@string/adas_dcw_card_title_1"
            android:layout_marginStart="20dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/dowSwitch"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/LightFragmentFirstText"/>
        <skin.support.widget.SkinCompatImageView
            android:id="@+id/dowTipIv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_tip"
            android:contentDescription="@string/aids_012"
            android:layout_marginStart="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/dowTv"
            app:layout_constraintTop_toTopOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
