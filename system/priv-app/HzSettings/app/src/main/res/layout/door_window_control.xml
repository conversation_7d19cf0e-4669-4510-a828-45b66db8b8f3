<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <com.hozonauto.widget.SwitchButton
        android:id="@+id/ss_one_button_ventilate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="一键通风(switch)"
        app:hz_click_interval="500"
        style="@style/SwitchButton"/>
    <com.hozonauto.widget.tablayout.HzFixedTabLayout
        android:id="@+id/item_lockup"
        android:layout_width="576dp"
        android:layout_height="60dp"
        app:hz_mode="tab"
        app:hz_position="0"
        app:tabBackground="@null">
        <view
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="锁车升窗全关"
            app:hz_text="全关"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="锁车升窗通风"
            app:hz_text="通风"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="锁车升窗保持原状"
            app:hz_text="保持原状"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
    </com.hozonauto.widget.tablayout.HzFixedTabLayout>
    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/ll_door_window"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <LinearLayout
            android:orientation="horizontal"
            android:id="@+id/ll_sky_window"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <com.hozon.settings.view.LRSwitch
                android:id="@+id/switch_sky_window"
                android:layout_width="wrap_content"
                android:layout_height="150dp"
                app:switch_title="天窗"/>
            <com.hozon.settings.view.StateSwitchDis
                android:id="@+id/ss_sky_light_open_all"
                android:layout_width="150dp"
                android:layout_height="150dp"
                android:layout_marginLeft="24dp"
                app:state_switch_icon="@drawable/icon_skylight_open_all"
                app:state_switch_text="全开"/>
            <com.hozon.settings.view.StateSwitchDis
                android:id="@+id/ss_skylight_close"
                android:layout_width="150dp"
                android:layout_height="150dp"
                android:layout_marginLeft="24dp"
                app:state_switch_icon="@drawable/icon_skylight_close"
                app:state_switch_text="全关"/>
            <com.hozon.settings.view.StateSwitchDis
                android:id="@+id/ss_skylight_open_ventilate"
                android:layout_width="150dp"
                android:layout_height="150dp"
                android:layout_marginLeft="24dp"
                app:state_switch_icon="@drawable/icon_skylight_open_ventilate"
                app:state_switch_text="通风"/>
            <com.hozon.settings.view.StateSwitchDis
                android:id="@+id/ss_skylight_open_comfort"
                android:layout_width="150dp"
                android:layout_height="150dp"
                android:layout_marginLeft="24dp"
                app:state_switch_icon="@drawable/icon_skylight_open_comfort"
                app:state_switch_text="舒适"/>
        </LinearLayout>
        <LinearLayout
            android:orientation="horizontal"
            android:id="@+id/ll_sunshade"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp">
            <com.hozon.settings.view.LRSwitch
                android:id="@+id/switch_sunshade"
                android:layout_width="wrap_content"
                android:layout_height="150dp"
                app:switch_title="遮阳帘"/>
            <com.hozon.settings.view.StateSwitchDis
                android:id="@+id/ss_one_button_ventilation_open"
                android:layout_width="150dp"
                android:layout_height="150dp"
                android:layout_marginLeft="24dp"
                app:state_switch_icon="@drawable/icon_one_button_ventilation_open"
                app:state_switch_text="全开"/>
            <com.hozon.settings.view.StateSwitchDis
                android:id="@+id/ss_one_button_ventilation_close"
                android:layout_width="150dp"
                android:layout_height="150dp"
                android:layout_marginLeft="24dp"
                app:state_switch_icon="@drawable/icon_one_button_ventilation_close"
                app:state_switch_text="全关"/>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
