<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RelativeLayout
        android:gravity="center"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true">
            <ImageView
                android:layout_gravity="center_horizontal"
                android:id="@+id/iv_icon"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/default_pic_empty_abnormal_ep36"/>
            <TextView
                android:textSize="44dp"
                android:textColor="@color/com_result_text_title_color"
                android:layout_gravity="center_horizontal"
                android:id="@+id/tv_hint"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="40dp"
                android:text="hint"/>
            <TextView
                android:textSize="32sp"
                android:textColor="@color/com_result_text_caption_color"
                android:layout_gravity="center_horizontal"
                android:id="@+id/tv_msg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:text="提示⽂本"/>
            <LinearLayout
                android:layout_gravity="center_horizontal"
                android:orientation="horizontal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/px_8"
                android:dividerPadding="30dp">
                <com.hozonauto.widget.btn.ContainerButton
                    android:id="@+id/tv_ensure2"
                    android:visibility="gone"
                    android:layout_width="@dimen/px_70"
                    android:layout_height="@dimen/px_24"
                    app:hz_btnStyle="primary"
                    app:hz_btnText="行动按钮1"/>
                <com.hozonauto.widget.btn.ContainerButton
                    android:id="@+id/tv_cancel2"
                    android:visibility="gone"
                    android:layout_width="@dimen/px_70"
                    android:layout_height="@dimen/px_24"
                    android:layout_marginLeft="@dimen/px_8"
                    app:hz_btnStyle="secondary"
                    app:hz_btnText="行动按钮2"/>
            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>
</FrameLayout>
