<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/clLdwLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.hozon.settings.view.FontScaleTextView
        android:id="@+id/title"
        android:text="@string/adas_ldw_fun_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/FunTitle"/>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clLdw"
        android:layout_marginTop="@dimen/px_6"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title"
        style="@style/AidsCard">
        <com.hozonauto.widget.SwitchButton
            android:id="@+id/ldwSwitch"
            android:visibility="gone"
            android:checked="true"
            android:contentDescription="车道偏离辅助(switch)"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/SwitchButton"/>
        <com.hozon.settings.view.FontScaleTextView
            android:id="@+id/ldwTv"
            android:text="@string/adas_ldw_card_title_1"
            android:layout_marginStart="20dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/ldwSwitch"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/LightFragmentFirstText"/>
        <skin.support.widget.SkinCompatImageView
            android:id="@+id/ldwTipIv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_tip"
            android:contentDescription="@string/aids_003"
            android:layout_marginStart="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/ldwTv"
            app:layout_constraintTop_toTopOf="parent"/>
        <skin.support.widget.SkinCompatImageView
            android:id="@+id/ldwArrowIv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_arrow_right"
            android:contentDescription="@string/adas_ldw_card_title_enter"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clLca"
        android:layout_marginTop="@dimen/px_6"
        android:layout_marginStart="27dp"
        app:layout_constraintLeft_toRightOf="@+id/clLdw"
        app:layout_constraintTop_toBottomOf="@+id/title"
        app:layout_goneMarginStart="0dp"
        style="@style/AidsCard">
        <com.hozonauto.widget.SwitchButton
            android:id="@+id/lcaSwitch"
            android:checked="true"
            android:contentDescription="@string/adas_ldw_card_title_switch"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/SwitchButton"/>
        <com.hozon.settings.view.FontScaleTextView
            android:id="@+id/lcaTv"
            android:text="@string/adas_ldw_card_title_2"
            android:layout_marginStart="20dp"
            app:hz_contentDescription="@string/adas_ldw_card_title_2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/lcaSwitch"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/LightFragmentFirstText"/>
        <skin.support.widget.SkinCompatImageView
            android:id="@+id/lcaTipIv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_tip"
            android:contentDescription="@string/aids_004"
            android:layout_marginStart="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/lcaTv"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@string/aids_004"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
