<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="center_horizontal"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="vertical"
        android:paddingLeft="@dimen/px_8"
        android:paddingTop="@dimen/px_8"
        android:paddingRight="@dimen/px_8"
        android:paddingBottom="@dimen/px_8"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <TextView
            android:textSize="@dimen/font_size_8"
            android:textColor="@color/com_list_text_title_color"
            android:ellipsize="end"
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:includeFontPadding="false"
            style="@style/FzltBlackSimpleFontStyle"/>
        <LinearLayout
            android:layout_gravity="center_vertical"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_20">
            <ImageView
                android:layout_gravity="center_vertical"
                android:id="@+id/iv_left"
                android:layout_width="@dimen/icon_width_40"
                android:layout_height="@dimen/icon_height_40"
                android:layout_marginRight="@dimen/space_14"
                android:scaleType="fitCenter"/>
            <LinearLayout
                android:orientation="horizontal"
                android:id="@+id/seekBar_container"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_weight="1"/>
            <ImageView
                android:layout_gravity="center_vertical"
                android:id="@+id/iv_right"
                android:layout_width="@dimen/icon_width_40"
                android:layout_height="@dimen/icon_height_40"
                android:layout_marginLeft="@dimen/space_14"
                android:scaleType="fitCenter"/>
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>
