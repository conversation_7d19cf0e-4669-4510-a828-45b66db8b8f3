<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:background="@color/transparent"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <View
        android:id="@+id/left"
        android:visibility="gone"
        android:layout_width="@dimen/px_90"
        android:layout_height="match_parent"/>
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="928dp"
        android:layout_weight="1">
        <RelativeLayout
            android:id="@+id/main_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <RelativeLayout
                android:id="@+id/top_title_view"
                android:layout_width="match_parent"
                android:layout_height="@dimen/px_32"
                android:layout_marginTop="@dimen/px_10">
                <ImageView
                    android:id="@+id/iv_close"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/app_icon_back_ep32"
                    android:layout_centerVertical="true"
                    android:contentDescription="返回"/>
                <TextView
                    android:textColor="@color/neutral_color_text_primary"
                    android:id="@+id/tv_audio_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="音效体验厅"
                    android:layout_centerInParent="true"
                    style="@style/HozonTheme.TextAppearance.Subtitle.Small.Regular"/>
            </RelativeLayout>
            <RelativeLayout
                android:id="@+id/top_view"
                android:layout_width="@dimen/px_304"
                android:layout_height="@dimen/px_140"
                android:layout_below="@+id/top_title_view"
                android:layout_centerHorizontal="true">
                <ImageView
                    android:id="@+id/iv_img"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/px_4"
                    android:src="@mipmap/img_video_nor_32"
                    android:scaleType="fitXY"/>
                <ImageView
                    android:id="@+id/rl_close_sound"
                    android:background="@drawable/selector_ic_sound_effect_bg"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:contentDescription="天空音效(switch)"/>
                <LinearLayout
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:id="@+id/rl_control"
                    android:background="@drawable/bg_music_control"
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/px_36"
                    android:layout_alignParentBottom="true">
                    <ImageView
                        android:id="@+id/iv_play"
                        android:layout_width="@dimen/px_24"
                        android:layout_height="@dimen/px_24"
                        android:layout_marginLeft="@dimen/px_16"
                        android:src="@drawable/selector_iv_play_sound"
                        android:layout_centerVertical="true"/>
                    <TextView
                        android:textColor="#ffffff"
                        android:gravity="center"
                        android:id="@+id/tv_start_time"
                        android:layout_width="@dimen/px_35"
                        android:layout_height="@dimen/px_16"
                        android:text="00:00"
                        style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
                    <SeekBar
                        android:id="@+id/seekbar_music"
                        android:background="@color/transparent"
                        android:paddingTop="11dp"
                        android:paddingBottom="11dp"
                        android:layout_width="@dimen/px_190"
                        android:layout_height="30dp"
                        android:max="100"
                        android:progressDrawable="@drawable/sound_room_seekbar_layer"
                        android:thumb="@null"
                        android:splitTrack="false"
                        android:min="1"/>
                    <TextView
                        android:textColor="#ffffff"
                        android:gravity="center_vertical"
                        android:id="@+id/tv_end_time"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/px_16"
                        android:text="00:00"
                        style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
                </LinearLayout>
            </RelativeLayout>
            <TextView
                android:textSize="28sp"
                android:textColor="@color/color_music_list_remind"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="189dp"
                android:layout_marginTop="@dimen/px_8"
                android:text="歌曲列表"/>
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rcy_music"
                android:layout_width="@dimen/px_304"
                android:layout_height="match_parent"
                android:layout_marginBottom="@dimen/px_2"
                android:layout_below="@+id/top_view"
                android:layout_centerHorizontal="true"
                android:overScrollMode="never"/>
        </RelativeLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>
