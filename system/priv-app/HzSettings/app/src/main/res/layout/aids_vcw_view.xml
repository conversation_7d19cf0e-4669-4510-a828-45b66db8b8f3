<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:id="@+id/clVcw"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="64dp">
        <com.hozon.settings.view.FontScaleTextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="0dp"
            android:text="@string/adas_vcw_fun_title"
            style="@style/FunTitle"/>
        <ImageView
            android:id="@+id/vcwTips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/px_6"
            android:src="@drawable/ic_tip"
            android:contentDescription="播报模式提示"/>
    </LinearLayout>
    <com.hozonauto.widget.tablayout.HzFixedTabLayout
        android:id="@+id/itemVoice"
        android:layout_width="582dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/px_4"
        android:layout_centerVertical="true"
        app:hz_animStyle="promptly"
        app:hz_mode="tab"
        app:hz_position="0"
        app:hz_tab_background="@drawable/neutral_color_background_surface_primary"
        app:tabBackground="@null">
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="播报模式极简"
            app:hz_text="极简"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="播报模式简洁"
            app:hz_text="简洁"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="播报模式标准"
            app:hz_text="标准"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
    </com.hozonauto.widget.tablayout.HzFixedTabLayout>
</LinearLayout>
