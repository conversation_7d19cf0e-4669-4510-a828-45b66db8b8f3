<?xml version="1.0" encoding="utf-8"?>
<skin.support.widgetMy.SkinCompatLinearLayoutMy xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:background="@drawable/neutral_color_background_surface_primary_comfort_night"
    android:layout_width="@dimen/px_106"
    android:layout_height="@dimen/px_42">
    <skin.support.widgetMy.SkinCompatLinearLayoutMy
        android:orientation="vertical"
        android:id="@+id/ll_sunshade_close"
        android:background="@drawable/effect_radio_button_press"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1">
        <skin.support.widgetMy.SkinCompatImageViewMy
            android:layout_gravity="center_horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="18dp"
            android:src="@drawable/selector_sunshade_close_icon_comfort_night"/>
        <skin.support.widgetMy.SkinCompatTextViewMy
            android:textSize="@dimen/caption_mini"
            android:textColor="@color/selector_widget_text_color_comfort_night"
            android:layout_gravity="center_horizontal"
            android:id="@+id/title_all_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="9dp"
            android:text="@string/title_sunshade_close"/>
    </skin.support.widgetMy.SkinCompatLinearLayoutMy>
    <View
        android:layout_width="30dp"
        android:layout_height="10dp"/>
    <skin.support.widgetMy.SkinCompatLinearLayoutMy
        android:orientation="vertical"
        android:id="@+id/ll_sunshade_open"
        android:background="@drawable/effect_radio_button_press"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1">
        <skin.support.widgetMy.SkinCompatImageViewMy
            android:layout_gravity="center_horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="18dp"
            android:src="@drawable/selector_sunshade_open_icon_comfort_night"/>
        <skin.support.widgetMy.SkinCompatTextViewMy
            android:textSize="@dimen/caption_mini"
            android:textColor="@color/selector_widget_text_color_comfort_night"
            android:layout_gravity="center_horizontal"
            android:id="@+id/title_all_open"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="9dp"
            android:text="@string/all_open"/>
    </skin.support.widgetMy.SkinCompatLinearLayoutMy>
</skin.support.widgetMy.SkinCompatLinearLayoutMy>
