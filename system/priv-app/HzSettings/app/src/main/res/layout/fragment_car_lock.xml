<?xml version="1.0" encoding="utf-8"?>
<com.hozon.settings.view.BanAutoSlideScrollView xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/scrollview"
    android:scrollbars="none"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:overScrollMode="never">
    <LinearLayout
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_24"
        android:clickable="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp">
        <com.hozon.settings.view.StateSwitch
            android:id="@+id/ss_central_locking"
            android:layout_width="150dp"
            android:layout_height="150dp"
            app:state_switch_icon="@drawable/icon_central_locking"
            app:state_switch_text="油箱锁"/>
        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp">
            <com.hozonauto.widget.list.MultiContainerListItemView
                android:gravity="center_vertical"
                android:id="@+id/ml_hold_unlock"
                android:background="@drawable/sound_list_item_color_bg_comfort_night"
                android:layout_width="462dp"
                android:layout_height="120dp"
                app:hz_icon="@drawable/ic_im_60"
                app:hz_title="驻车解锁"
                app:hz_titleCDesc="驻车解锁提示"/>
            <com.hozonauto.widget.list.MultiContainerListItemView
                android:gravity="center_vertical"
                android:id="@+id/ml_no_feel_in_out"
                android:background="@drawable/sound_list_item_color_bg_comfort_night"
                android:layout_width="462dp"
                android:layout_height="120dp"
                android:layout_marginLeft="24dp"
                app:hz_icon="@drawable/ic_im_60"
                app:hz_title="无感进出"
                app:hz_titleCDesc="无感进出提示"/>
        </LinearLayout>
        <com.hozonauto.widget.list.MultiContainerListItemView
            android:gravity="center_vertical"
            android:id="@+id/ml_door_auto_recover"
            android:background="@drawable/sound_list_item_color_bg_comfort_night"
            android:layout_width="462dp"
            android:layout_height="120dp"
            android:layout_marginTop="24dp"
            app:hz_icon="@drawable/ic_im_60"
            app:hz_title="门把手自动收回"
            app:hz_titleSingeLine="false"/>
        <TextView
            android:textSize="24sp"
            android:textColor="#fff"
            android:id="@+id/ml_lock_unlock_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="解闭锁提示"/>
        <com.hozonauto.widget.list.MultiContainerListItemView
            android:id="@+id/ml_lock_unlock_tip"
            android:layout_width="625dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="-24dp"
            android:layout_marginTop="-50dp"
            app:hz_titleSingeLine="false"/>
        <com.hozonauto.widget.list.MultiContainerListItemView
            android:id="@+id/ml_lock_unlock_sound"
            android:layout_width="630dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="-24dp"
            app:hz_title="解闭锁音效"
            app:hz_titleSingeLine="false"/>
    </LinearLayout>
</com.hozon.settings.view.BanAutoSlideScrollView>
