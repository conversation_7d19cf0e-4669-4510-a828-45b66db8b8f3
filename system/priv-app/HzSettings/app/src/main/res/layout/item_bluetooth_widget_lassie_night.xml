<?xml version="1.0" encoding="utf-8"?>
<skin.support.widgetMy.SkinCompatRelativeLayoutMy xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:id="@+id/root_layout"
    android:background="@drawable/selector_widget_bg_lassie_night"
    android:layout_width="144dp"
    android:layout_height="126dp">
    <skin.support.widgetMy.SkinCompatImageViewMy
        android:id="@+id/iv_bluetooth"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="18dp"
        android:src="@drawable/selector_bluetooth_icon_lassie_night"
        android:layout_centerHorizontal="true"/>
    <skin.support.widgetMy.SkinCompatTextViewMy
        android:textColor="@color/selector_widget_text_color_lassie_night"
        android:gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="36dp"
        android:layout_marginTop="6dp"
        android:text="@string/bt_bt"
        android:layout_below="@+id/iv_bluetooth"
        android:layout_centerHorizontal="true"
        style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="9dp"
        android:layout_marginBottom="9dp"
        android:src="@drawable/selector_pull_down_more_lassie_night"
        android:layout_alignParentBottom="true"
        android:layout_alignParentEnd="true"/>
</skin.support.widgetMy.SkinCompatRelativeLayoutMy>
