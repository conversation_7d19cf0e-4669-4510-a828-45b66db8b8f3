<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/scrollview"
    android:scrollbars="none"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:overScrollMode="never">
    <LinearLayout
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_24"
        android:clickable="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:textSize="24sp"
            android:textColor="#fff"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="坐姿加载"/>
        <com.hozonauto.widget.list.MultiContainerListItemView
            android:id="@+id/ml_main_seat_load"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="-24dp"
            android:layout_marginTop="16dp"
            app:hz_icon="@drawable/ic_im_60"
            app:hz_title="主驾"
            app:hz_titleCDesc="主驾坐姿加载提示"/>
        <com.hozonauto.widget.list.MultiContainerListItemView
            android:id="@+id/ml_copilot_seat_load"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="-24dp"
            android:layout_marginTop="-32dp"
            app:hz_icon="@drawable/ic_im_60"
            app:hz_title="副驾"
            app:hz_titleCDesc="副驾坐姿加载提示"/>
        <TextView
            android:textSize="24sp"
            android:textColor="#fff"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="坐姿名称"/>
        <com.hozonauto.widget.list.MultiContainerListItemView
            android:id="@+id/ml_main_seat_edit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="-24dp"
            android:layout_marginTop="16dp"
            app:hz_icon="@drawable/ic_im_60"
            app:hz_title="主驾"
            app:hz_titleCDesc="主驾坐姿名称编辑提示"/>
        <com.hozonauto.widget.list.MultiContainerListItemView
            android:id="@+id/ml_copilot_seat_edit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="-24dp"
            android:layout_marginTop="-32dp"
            app:hz_icon="@drawable/ic_im_60"
            app:hz_title="副驾"
            app:hz_titleCDesc="副驾坐姿名称编辑提示"/>
        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <com.hozonauto.widget.list.MultiContainerListItemView
                android:gravity="center_vertical"
                android:id="@+id/ml_main_seat_welcome"
                android:background="@drawable/sound_list_item_color_bg_comfort_night"
                android:layout_width="462dp"
                android:layout_height="120dp"
                app:hz_icon="@drawable/ic_im_60"
                app:hz_title="主驾迎宾"
                app:hz_titleCDesc="主驾迎宾提示"/>
            <com.hozonauto.widget.list.MultiContainerListItemView
                android:gravity="center_vertical"
                android:id="@+id/ml_copilot_seat_welcome"
                android:background="@drawable/sound_list_item_color_bg_comfort_night"
                android:layout_width="462dp"
                android:layout_height="120dp"
                android:layout_marginStart="24dp"
                app:hz_icon="@drawable/ic_im_60"
                app:hz_title="副驾迎宾"
                app:hz_titleCDesc="副驾迎宾提示"/>
        </LinearLayout>
    </LinearLayout>
</ScrollView>
