<?xml version="1.0" encoding="utf-8"?>
<skin.support.widgetMy.SkinCompatLinearLayoutMy xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:id="@+id/root_layout"
    android:background="@drawable/neutral_color_background_surface_primary_lassie"
    android:layout_width="@dimen/px_160"
    android:layout_height="@dimen/px_42"
    android:paddingHorizontal="@dimen/px_4"
    android:paddingVertical="@dimen/px_2">
    <skin.support.widgetMy.SkinCompatTextViewMy
        android:textSize="@dimen/caption_mini"
        android:textColor="@color/selector_widget_text_color"
        android:id="@+id/light_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/px_1"
        android:text="大灯控制"
        android:drawablePadding="10dp"
        android:drawableEnd="@drawable/ic_light_control"/>
    <skin.support.widgetMy.SkinCompatLinearLayoutMy
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:background="@drawable/neutral_color_background_surface_secondary_lassie"
        android:layout_width="match_parent"
        android:layout_height="@dimen/px_24"
        android:layout_marginTop="@dimen/px_1">
        <skin.support.widgetMy.SkinCompatTextViewMy
            android:textSize="@dimen/caption_mini"
            android:textColor="@color/effect_radio_button_text_selector"
            android:gravity="center"
            android:id="@+id/rbLightMode1"
            android:background="@drawable/effect_radio_button_selector_lassie"
            android:layout_width="@dimen/px_38"
            android:layout_height="@dimen/px_24"
            android:text="关闭"
            android:contentDescription="大灯控制关闭"/>
        <skin.support.widgetMy.SkinCompatImageViewMy
            android:gravity="center"
            android:id="@+id/rbLightMode2"
            android:background="@drawable/effect_radio_button_selector_lassie"
            android:layout_width="@dimen/px_38"
            android:layout_height="@dimen/px_24"
            android:src="@drawable/selector_postlamp"
            android:scaleType="center"
            android:contentDescription="大灯控制位置灯"/>
        <skin.support.widgetMy.SkinCompatImageViewMy
            android:gravity="center"
            android:id="@+id/rbLightMode3"
            android:background="@drawable/effect_radio_button_selector_lassie"
            android:layout_width="@dimen/px_38"
            android:layout_height="@dimen/px_24"
            android:src="@drawable/selector_nearlamp"
            android:scaleType="center"
            android:contentDescription="大灯控制近光灯"/>
        <skin.support.widgetMy.SkinCompatTextViewMy
            android:textSize="@dimen/caption_mini"
            android:textColor="@color/effect_radio_button_text_selector"
            android:gravity="center"
            android:id="@+id/rbLightMode4"
            android:background="@drawable/effect_radio_button_selector_lassie"
            android:layout_width="@dimen/px_38"
            android:layout_height="@dimen/px_24"
            android:text="自动"
            android:contentDescription="大灯控制自动"/>
    </skin.support.widgetMy.SkinCompatLinearLayoutMy>
</skin.support.widgetMy.SkinCompatLinearLayoutMy>
