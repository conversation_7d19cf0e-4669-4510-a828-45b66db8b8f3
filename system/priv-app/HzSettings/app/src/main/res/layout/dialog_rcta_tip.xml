<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <skin.support.widget.SkinCompatImageView
        android:id="@+id/desIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/dialog_rcta_icon"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>
    <TextView
        android:textColor="@color/neutral_color_text_primary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="@string/adis_rcta"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/desIv"
        style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
</androidx.constraintlayout.widget.ConstraintLayout>
