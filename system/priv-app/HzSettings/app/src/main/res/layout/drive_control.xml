<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <com.hozonauto.widget.tablayout.HzFixedTabLayout
        android:id="@+id/item_drive_mode"
        android:layout_width="732dp"
        android:layout_height="wrap_content"
        app:hz_mode="tab"
        app:hz_position="0"
        app:tabBackground="@null">
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/desc_drive_mode_eco"
            app:hz_text="@string/item_drive_mode_eco"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/desc_drive_mode_comfort"
            app:hz_text="@string/item_drive_mode_comfort"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/desc_drive_mode_sport"
            app:hz_text="@string/item_drive_mode_sport"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/desc_drive_mode_one_pedal"
            app:hz_text="@string/item_drive_mode_one_pedal"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
    </com.hozonauto.widget.tablayout.HzFixedTabLayout>
    <com.hozonauto.widget.tablayout.HzFixedTabLayout
        android:id="@+id/item_stop_mode"
        android:layout_width="366dp"
        android:layout_height="wrap_content"
        app:hz_mode="tab"
        app:hz_position="0"
        app:tabBackground="@null">
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/desc_stop_mode_creep"
            app:hz_text="@string/item_stop_mode_creep"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/desc_stop_mode_park_stop"
            app:hz_text="@string/item_stop_mode_park_stop"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
    </com.hozonauto.widget.tablayout.HzFixedTabLayout>
    <com.hozonauto.widget.tablayout.HzFixedTabLayout
        android:id="@+id/item_energy_recovery"
        android:layout_width="549dp"
        android:layout_height="wrap_content"
        app:hz_mode="tab"
        app:hz_position="0"
        app:tabBackground="@null">
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/desc_energy_recover_low"
            app:hz_text="@string/item_energy_recover_low"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/desc_energy_recover_mid"
            app:hz_text="@string/item_energy_recover_mid"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/desc_energy_recover_height"
            app:hz_text="@string/item_energy_recover_height"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
    </com.hozonauto.widget.tablayout.HzFixedTabLayout>
    <com.hozonauto.widget.tablayout.HzFixedTabLayout
        android:id="@+id/item_eps_mode"
        android:layout_width="549dp"
        android:layout_height="wrap_content"
        app:hz_mode="tab"
        app:hz_position="0"
        app:tabBackground="@null">
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/desc_eps_comfort"
            app:hz_text="@string/item_eps_comfort"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/desc_eps_normal"
            app:hz_text="@string/item_eps_normal"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/desc_eps_sport"
            app:hz_text="@string/item_eps_sport"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
    </com.hozonauto.widget.tablayout.HzFixedTabLayout>
    <com.hozonauto.widget.tablayout.HzFixedTabLayout
        android:id="@+id/item_energy_mode"
        android:layout_width="732dp"
        android:layout_height="wrap_content"
        app:hz_mode="tab"
        app:hz_position="0"
        app:tabBackground="@null">
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/desc_energy_mode_1"
            app:hz_text="@string/item_energy_mode_1"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/desc_energy_mode_2"
            app:hz_text="@string/item_energy_mode_2"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/desc_energy_mode_3"
            app:hz_text="@string/item_energy_mode_3"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/desc_energy_mode_4"
            app:hz_text="@string/item_energy_mode_4"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
    </com.hozonauto.widget.tablayout.HzFixedTabLayout>
    <com.hozonauto.widget.tablayout.HzFixedTabLayout
        android:id="@+id/item_forced_charging"
        android:layout_width="576dp"
        android:layout_height="wrap_content"
        app:hz_mode="tab"
        app:hz_position="0"
        app:tabBackground="@null">
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/desc_force_charge_low"
            app:hz_text="@string/item_force_charge_low"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/desc_force_charge_mid"
            app:hz_text="@string/item_force_charge_mid"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/desc_force_charge_height"
            app:hz_text="@string/item_force_charge_height"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
    </com.hozonauto.widget.tablayout.HzFixedTabLayout>
    <com.hozonauto.widget.SwitchButton
        android:id="@+id/swRYB"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:contentDescription="@string/desc_ryb"
        app:hz_click_interval="500"
        style="@style/SwitchButton"/>
    <com.hozonauto.widget.SwitchButton
        android:id="@+id/swXCB"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:contentDescription="@string/desc_xcb"
        app:hz_click_interval="500"
        style="@style/SwitchButton"/>
    <com.hozonauto.widget.SwitchButton
        android:id="@+id/swAVH"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:contentDescription="@string/desc_avh"
        app:hz_click_interval="500"
        style="@style/SwitchButton"/>
    <com.hozonauto.widget.SwitchButton
        android:id="@+id/sw_epb"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:contentDescription="@string/desc_epb"
        app:hz_click_interval="500"
        style="@style/SwitchButton"/>
    <com.hozonauto.widget.SwitchButton
        android:id="@+id/sw_stop_car"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:contentDescription="@string/desc_stop_car"
        app:hz_click_interval="500"
        style="@style/SwitchButton"/>
    <com.hozonauto.widget.SwitchButton
        android:id="@+id/sw_hdc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:contentDescription="@string/desc_hdc"
        app:hz_click_interval="500"
        style="@style/SwitchButton"/>
    <com.hozonauto.widget.SwitchButton
        android:id="@+id/sw_esc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:contentDescription="@string/desc_esc"
        app:hz_click_interval="500"
        style="@style/SwitchButton"/>
    <com.hozonauto.widget.btn.ContainerButton
        android:id="@+id/btn_force_change"
        android:tag="false"
        android:layout_width="210dp"
        android:layout_height="72dp"
        app:hz_btnStyle="secondary"
        app:hz_btnText="@string/door_open"/>
    <com.hozonauto.widget.SwitchButton
        android:id="@+id/sw_fatigue_driving"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:contentDescription="@string/desc_fatigue"
        app:hz_click_interval="500"
        style="@style/SwitchButton"/>
    <com.hozonauto.widget.SwitchButton
        android:id="@+id/sw_distracted_driving"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:contentDescription="@string/desc_distracted_driving"
        app:hz_click_interval="500"
        style="@style/SwitchButton"/>
    <com.hozonauto.widget.SwitchButton
        android:id="@+id/sw_play_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:contentDescription="@string/desc_play_hint"
        app:hz_click_interval="500"
        style="@style/SwitchButton"/>
</LinearLayout>
