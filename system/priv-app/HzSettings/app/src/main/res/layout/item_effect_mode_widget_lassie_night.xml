<?xml version="1.0" encoding="utf-8"?>
<skin.support.widgetMy.SkinCompatLinearLayoutMy xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@drawable/neutral_color_background_surface_primary_lassie_night"
    android:layout_width="480dp"
    android:layout_height="126dp"
    android:paddingHorizontal="12dp"
    android:paddingVertical="6dp">
    <skin.support.widgetMy.SkinCompatTextViewMy
        android:textColor="@color/selector_widget_text_color_lassie_night"
        android:id="@+id/tv_head"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:text="@string/sound_mode_title"
        android:includeFontPadding="false"
        style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
    <skin.support.widgetMy.SkinCompatLinearLayoutMy
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:id="@+id/rgSoundMode"
        android:background="@drawable/neutral_color_background_surface_secondary_lassie_night"
        android:layout_width="@dimen/px_152"
        android:layout_height="72dp"
        android:layout_marginTop="6dp">
        <skin.support.widgetMy.SkinCompatTextViewMy
            android:textColor="@color/selector_widget_text_color_lassie_night"
            android:gravity="center"
            android:id="@+id/rbSoundMode1"
            android:background="@drawable/effect_radio_button_selector_lassie_night"
            android:padding="6dp"
            android:layout_width="0dp"
            android:layout_height="72dp"
            android:text="@string/sound_mode1"
            android:layout_weight="1"
            style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
        <skin.support.widgetMy.SkinCompatTextViewMy
            android:textColor="@color/selector_widget_text_color_lassie_night"
            android:gravity="center"
            android:id="@+id/rbSoundMode2"
            android:background="@drawable/effect_radio_button_selector_lassie_night"
            android:padding="6dp"
            android:layout_width="0dp"
            android:layout_height="72dp"
            android:text="@string/sound_mode2"
            android:layout_weight="1"
            style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
        <skin.support.widgetMy.SkinCompatTextViewMy
            android:textColor="@color/selector_widget_text_color_lassie_night"
            android:gravity="center"
            android:id="@+id/rbSoundMode3"
            android:background="@drawable/effect_radio_button_selector_lassie_night"
            android:padding="6dp"
            android:layout_width="0dp"
            android:layout_height="72dp"
            android:text="@string/sound_mode3"
            android:layout_weight="1"
            style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
        <skin.support.widgetMy.SkinCompatTextViewMy
            android:textColor="@color/selector_widget_text_color_lassie_night"
            android:gravity="center"
            android:id="@+id/rbSoundMode4"
            android:background="@drawable/effect_radio_button_selector_lassie_night"
            android:padding="6dp"
            android:layout_width="0dp"
            android:layout_height="72dp"
            android:text="@string/sound_mode4"
            android:layout_weight="1"
            style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
    </skin.support.widgetMy.SkinCompatLinearLayoutMy>
</skin.support.widgetMy.SkinCompatLinearLayoutMy>
