<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <ImageView
        android:id="@+id/backIv"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginTop="42dp"
        android:src="@drawable/ic_back"
        android:contentDescription="@string/back"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textColor="@color/neutral_color_text_primary"
        android:id="@+id/titleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="48dp"
        android:text="@string/maintain"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/HozonTheme.TextAppearance.Subtitle.Small.Regular"/>
    <com.hozon.settings.view.FlexLayout
        android:id="@+id/flexLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="135dp"
        app:heightSpace="24dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/backIv"
        app:widthSpace="24dp">
        <RelativeLayout
            android:id="@+id/carMaintainRl"
            android:background="@drawable/neutral_color_background_surface_primary"
            style="@style/NetaFragmentFirstMaintainRl">
            <TextView
                android:id="@+id/title1"
                android:text="整车保养"
                style="@style/MaintainFragmentStyle"/>
            <TextView
                android:textSize="@dimen/caption_small"
                android:textColor="@color/function_color_success_default"
                android:id="@+id/state1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="健康"
                android:layout_alignParentRight="true"/>
            <TextView
                android:id="@+id/value1"
                android:text="————km"
                style="@style/NetaFragmentMaintainTextValue"/>
        </RelativeLayout>
        <RelativeLayout
            android:id="@+id/brakeFluidRl"
            android:background="@drawable/neutral_color_background_surface_primary"
            style="@style/NetaFragmentMaintainRl">
            <TextView
                android:id="@+id/title6"
                android:text="制动液"
                style="@style/MaintainFragmentStyle"/>
            <TextView
                android:textSize="@dimen/caption_small"
                android:textColor="@color/function_color_success_default"
                android:id="@+id/state6"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="健康"
                android:layout_alignParentRight="true"/>
            <TextView
                android:id="@+id/value6"
                android:text="————km/"
                style="@style/NetaFragmentMaintainTextValue"/>
            <TextView
                android:id="@+id/day6"
                android:text="———天"
                android:layout_toEndOf="@+id/value6"
                style="@style/NetaFragmentMaintainTextValue"/>
        </RelativeLayout>
        <RelativeLayout
            android:id="@+id/sparkRl"
            android:background="@drawable/neutral_color_background_surface_primary"
            style="@style/NetaFragmentMaintainRl">
            <TextView
                android:id="@+id/title3"
                android:text="火花塞"
                style="@style/MaintainFragmentStyle"/>
            <TextView
                android:textSize="@dimen/caption_small"
                android:textColor="@color/function_color_success_default"
                android:id="@+id/state3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="健康"
                android:layout_alignParentRight="true"/>
            <TextView
                android:id="@+id/value3"
                android:text="————km/"
                style="@style/NetaFragmentMaintainTextValue"/>
            <TextView
                android:id="@+id/day3"
                android:text="———天"
                android:layout_toEndOf="@+id/value3"
                style="@style/NetaFragmentMaintainTextValue"/>
        </RelativeLayout>
        <RelativeLayout
            android:id="@+id/airFilterRl"
            android:background="@drawable/neutral_color_background_surface_primary"
            style="@style/NetaFragmentMaintainRl">
            <TextView
                android:id="@+id/title4"
                android:text="空调滤芯"
                style="@style/MaintainFragmentStyle"/>
            <TextView
                android:textSize="@dimen/caption_small"
                android:textColor="@color/function_color_success_default"
                android:id="@+id/state4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="健康"
                android:layout_alignParentRight="true"/>
            <TextView
                android:id="@+id/value4"
                android:text="————km/"
                style="@style/NetaFragmentMaintainTextValue"/>
            <TextView
                android:id="@+id/day4"
                android:text="———天"
                android:layout_toEndOf="@+id/value4"
                style="@style/NetaFragmentMaintainTextValue"/>
        </RelativeLayout>
        <RelativeLayout
            android:id="@+id/frozenRl"
            android:background="@drawable/neutral_color_background_surface_primary"
            style="@style/NetaFragmentMaintainRl">
            <TextView
                android:id="@+id/title5"
                android:text="冷却液"
                style="@style/MaintainFragmentStyle"/>
            <TextView
                android:textSize="@dimen/caption_small"
                android:textColor="@color/function_color_success_default"
                android:id="@+id/state5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="健康"
                android:layout_alignParentRight="true"/>
            <TextView
                android:id="@+id/value5"
                android:text="————km/"
                style="@style/NetaFragmentMaintainTextValue"/>
            <TextView
                android:id="@+id/day5"
                android:text="———天"
                android:layout_toEndOf="@+id/value5"
                style="@style/NetaFragmentMaintainTextValue"/>
        </RelativeLayout>
        <RelativeLayout
            android:id="@+id/extenderRl"
            android:background="@drawable/neutral_color_background_surface_primary"
            style="@style/NetaFragmentMaintainRl">
            <TextView
                android:id="@+id/title2"
                android:text="增程器机油保养"
                style="@style/MaintainFragmentStyle"/>
            <TextView
                android:textSize="@dimen/caption_small"
                android:textColor="@color/function_color_success_default"
                android:id="@+id/state2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="健康"
                android:layout_alignParentRight="true"/>
            <TextView
                android:id="@+id/value2"
                android:text="————km/"
                style="@style/NetaFragmentMaintainTextValue"/>
            <TextView
                android:id="@+id/day2"
                android:text="———天"
                android:layout_toEndOf="@+id/value2"
                style="@style/NetaFragmentMaintainTextValue"/>
        </RelativeLayout>
    </com.hozon.settings.view.FlexLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
