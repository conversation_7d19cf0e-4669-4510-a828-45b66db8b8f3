<?xml version="1.0" encoding="utf-8"?>
<com.hozon.settings.view.SkinConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center"
    android:id="@+id/dialog_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <View
        android:background="@drawable/neutral_color_background_system_mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:alpha="0.9"/>
    <com.hozon.settings.view.SkinConstraintLayout
        android:background="@drawable/dialog_bg_ep32"
        android:clickable="true"
        android:layout_width="1010dp"
        android:layout_height="746dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="54dp"
            android:layout_height="54dp"
            android:layout_marginTop="15dp"
            android:src="@drawable/app_icon_close_ep32"
            android:scaleType="fitXY"
            android:contentDescription="@string/close"
            android:layout_marginStart="15dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <TextView
            android:id="@+id/dialog_tv_title"
            android:text="@string/title_custom_key"
            app:layout_constraintBottom_toBottomOf="@+id/iv_close"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/iv_close"
            style="@style/title_text_style"/>
        <ImageView
            android:id="@+id/iv_img"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="96dp"
            android:layout_marginBottom="24dp"
            android:src="@mipmap/img_steering_wheel"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="48dp"
            android:layout_marginVertical="24dp"
            app:layout_constraintTop_toBottomOf="@+id/iv_img">
            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <com.hozonauto.widget.btn.ContainerButton
                    android:id="@+id/tv_photo"
                    android:background="@drawable/neutral_color_background_surface_primary"
                    android:layout_width="420dp"
                    android:layout_height="72dp"
                    android:layout_marginStart="0dp"
                    android:layout_marginEnd="24dp"
                    app:hz_btnText="@string/title_photo"/>
                <com.hozonauto.widget.btn.ContainerButton
                    android:id="@+id/tv_emergency_recording"
                    android:background="@drawable/neutral_color_background_surface_primary"
                    android:layout_width="420dp"
                    android:layout_height="72dp"
                    android:layout_marginStart="0dp"
                    app:hz_btnText="@string/title_emergency_recording"/>
            </LinearLayout>
            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp">
                <com.hozonauto.widget.btn.ContainerButton
                    android:id="@+id/tv_avm"
                    android:background="@drawable/neutral_color_background_surface_primary"
                    android:layout_width="420dp"
                    android:layout_height="72dp"
                    android:layout_marginStart="0dp"
                    android:layout_marginEnd="24dp"
                    app:hz_btnText="@string/title_avm"/>
                <com.hozonauto.widget.btn.ContainerButton
                    android:id="@+id/tv_apa"
                    android:background="@drawable/neutral_color_background_surface_primary"
                    android:layout_width="420dp"
                    android:layout_height="72dp"
                    android:layout_marginStart="0dp"
                    app:hz_btnText="@string/title_apa"/>
            </LinearLayout>
            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp">
                <com.hozonauto.widget.btn.ContainerButton
                    android:id="@+id/tv_mirror"
                    android:background="@drawable/neutral_color_background_surface_primary"
                    android:layout_width="420dp"
                    android:layout_height="72dp"
                    android:layout_marginStart="0dp"
                    android:layout_marginEnd="24dp"
                    app:hz_btnText="@string/title_mirror"/>
            </LinearLayout>
        </LinearLayout>
    </com.hozon.settings.view.SkinConstraintLayout>
</com.hozon.settings.view.SkinConstraintLayout>
