<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:background="@mipmap/item_gridview_bg"
    android:layout_width="123dp"
    android:layout_height="123dp"
    android:layout_marginLeft="15dp"
    android:layout_marginTop="17dp"
    android:layout_marginRight="15dp">
    <TextView
        android:textSize="48sp"
        android:textColor="#ffffff"
        android:gravity="center"
        android:id="@+id/tv_item"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <TextView
        android:id="@+id/line"
        android:background="#212121"
        android:layout_width="20dp"
        android:layout_height="match_parent"/>
</LinearLayout>
