<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/clAccLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.hozon.settings.view.FontScaleTextView
        android:id="@+id/title"
        android:text="巡航辅助"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/FunTitle"/>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clAcc"
        android:layout_marginTop="@dimen/px_6"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title"
        style="@style/AidsCard">
        <com.hozonauto.widget.SwitchButton
            android:id="@+id/accSwitch"
            android:checked="true"
            android:contentDescription="车道居中巡航辅助(switch)"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/SwitchButton"/>
        <com.hozon.settings.view.FontScaleTextView
            android:id="@+id/accTv"
            android:text="车道居中巡航辅助"
            android:layout_marginStart="20dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/accSwitch"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/LightFragmentFirstText"/>
        <skin.support.widget.SkinCompatImageView
            android:id="@+id/accTipIv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_tip"
            android:contentDescription="@string/aids_002"
            android:layout_marginStart="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/accTv"
            app:layout_constraintTop_toTopOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clAlc"
        android:layout_marginTop="@dimen/px_6"
        android:layout_marginStart="27dp"
        app:layout_constraintLeft_toRightOf="@+id/clAcc"
        app:layout_constraintTop_toBottomOf="@+id/title"
        style="@style/AidsCard">
        <com.hozonauto.widget.SwitchButton
            android:id="@+id/alcListswitch"
            android:checked="true"
            android:contentDescription="拨杆变道(switch)"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/SwitchButton"/>
        <com.hozon.settings.view.FontScaleTextView
            android:id="@+id/alcTv"
            android:text="拨杆变道"
            android:layout_marginStart="20dp"
            app:hz_contentDescription="拨杆变道"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/alcListswitch"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/LightFragmentFirstText"/>
        <skin.support.widget.SkinCompatImageView
            android:id="@+id/alcTipIv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_tip"
            android:contentDescription="@string/aids_001"
            android:layout_marginStart="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/alcTv"
            app:layout_constraintTop_toTopOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
