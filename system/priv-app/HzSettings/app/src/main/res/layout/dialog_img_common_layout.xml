<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/fl_all"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <View
        android:background="@color/dialogOutsideBg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <LinearLayout
        android:gravity="center_horizontal"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:id="@+id/background"
        android:background="@drawable/section_bg_white100"
        android:clickable="true"
        android:layout_width="576dp"
        android:layout_height="wrap_content">
        <TextView
            android:textSize="32sp"
            android:textColor="@color/neutral_black100"
            android:gravity="center"
            android:id="@+id/dialog_tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="45dp"
            android:layout_marginBottom="30dp"/>
        <ImageView
            android:id="@+id/iv_top"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:src="@mipmap/img_biological_detection"
            android:layout_marginStart="61dp"
            android:layout_marginEnd="61dp"/>
        <TextView
            android:textSize="21sp"
            android:textColor="@color/neutral_black100"
            android:gravity="left"
            android:id="@+id/dialog_tv_msg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginStart="61dp"
            android:layout_marginEnd="61dp"/>
        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <LinearLayout
                android:gravity="center"
                android:orientation="vertical"
                android:id="@+id/ll_left"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginRight="20dp"
                android:layout_weight="1">
                <RelativeLayout
                    android:layout_width="220dp"
                    android:layout_height="220dp"
                    android:layout_marginBottom="10dp">
                    <org.libpag.PAGView
                        android:id="@+id/pag_left"
                        android:layout_width="220dp"
                        android:layout_height="220dp"/>
                    <ImageView
                        android:id="@+id/iv_up_down_bg"
                        android:visibility="gone"
                        android:layout_width="220dp"
                        android:layout_height="220dp"
                        android:src="@mipmap/oms_bg_up_down"/>
                </RelativeLayout>
                <TextView
                    android:textSize="@dimen/x_display_28"
                    android:textColor="@color/neutral_black56"
                    android:gravity="center"
                    android:id="@+id/tv_left"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxWidth="250dp"/>
            </LinearLayout>
            <LinearLayout
                android:gravity="center"
                android:orientation="vertical"
                android:id="@+id/ll_content"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">
                <org.libpag.PAGView
                    android:id="@+id/pag_content"
                    android:layout_width="220dp"
                    android:layout_height="220dp"
                    android:layout_marginBottom="10dp"/>
                <TextView
                    android:textSize="@dimen/x_display_28"
                    android:textColor="@color/neutral_black56"
                    android:gravity="center"
                    android:id="@+id/tv_content"
                    android:visibility="visible"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxWidth="250dp"/>
            </LinearLayout>
            <LinearLayout
                android:gravity="center"
                android:orientation="vertical"
                android:id="@+id/ll_right"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_weight="1">
                <RelativeLayout
                    android:layout_width="220dp"
                    android:layout_height="220dp"
                    android:layout_marginBottom="10dp">
                    <org.libpag.PAGView
                        android:id="@+id/pag_right"
                        android:layout_width="220dp"
                        android:layout_height="220dp"
                        android:layout_marginBottom="10dp"/>
                    <ImageView
                        android:id="@+id/iv_left_right_bg"
                        android:visibility="gone"
                        android:layout_width="220dp"
                        android:layout_height="220dp"
                        android:src="@mipmap/oms_bg_left_right"/>
                </RelativeLayout>
                <TextView
                    android:textSize="@dimen/x_display_28"
                    android:textColor="@color/neutral_black56"
                    android:gravity="center"
                    android:id="@+id/tv_right"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxWidth="250dp"
                    android:drawablePadding="@dimen/dp_10"/>
            </LinearLayout>
        </LinearLayout>
        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="24dp">
            <TextView
                android:textSize="24sp"
                android:textColor="#fff"
                android:gravity="center"
                android:id="@+id/dialog_btn_positive"
                android:background="@drawable/shape_006cff_r16"
                android:layout_width="match_parent"
                android:layout_height="72dp"
                android:text="关闭"
                android:layout_marginStart="48dp"
                android:layout_marginEnd="48dp"/>
            <TextView
                android:textSize="32sp"
                android:textColor="@color/neutral_black100"
                android:gravity="center"
                android:id="@+id/dialog_btn_negative"
                android:background="@drawable/dialog_bt_selector"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="取消"
                android:layout_marginStart="20dp"/>
        </LinearLayout>
    </LinearLayout>
</FrameLayout>
