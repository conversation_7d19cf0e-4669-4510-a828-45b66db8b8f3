<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <com.hozonauto.widget.SwitchButton
        android:id="@+id/sw_hold_unlock"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="驻车解锁(switch)"
        app:hz_click_interval="500"
        style="@style/SwitchButton"/>
    <com.hozonauto.widget.SwitchButton
        android:id="@+id/sw_no_feel_in_out"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="无感进出(switch)"
        app:hz_click_interval="500"
        style="@style/SwitchButton"/>
    <com.hozonauto.widget.SwitchButton
        android:id="@+id/sw_door_auto_recover"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="门把手自动收回(switch)"
        app:hz_click_interval="500"
        style="@style/SwitchButton"/>
    <com.hozonauto.widget.tablayout.HzFixedTabLayout
        android:id="@+id/item_lock_unlock_tip"
        android:layout_width="576dp"
        android:layout_height="wrap_content"
        app:hz_mode="tab"
        app:hz_position="0"
        app:tabBackground="@null">
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="解闭锁提示灯光"
            app:hz_text="灯光"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="解闭锁提示灯光加音效"
            app:hz_text="灯光+音效"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="解闭锁提示灯语加音效"
            app:hz_text="灯语+音效"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
    </com.hozonauto.widget.tablayout.HzFixedTabLayout>
    <com.hozonauto.widget.tablayout.HzFixedTabLayout
        android:id="@+id/item_lock_unlock_sound"
        android:layout_width="576dp"
        android:layout_height="wrap_content"
        app:hz_mode="tab"
        app:hz_position="0"
        app:tabBackground="@null">
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="解闭锁音效轻巧"
            app:hz_text="轻巧"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="解闭锁音效欢快"
            app:hz_text="欢快"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="解闭锁音效灵动"
            app:hz_text="灵动"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
    </com.hozonauto.widget.tablayout.HzFixedTabLayout>
    <com.hozonauto.widget.tablayout.HzFixedTabLayout
        android:id="@+id/item_lockup"
        android:layout_width="576dp"
        android:layout_height="wrap_content"
        app:hz_mode="tab"
        app:hz_position="0"
        app:tabBackground="@null">
        <view
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="锁车升窗全关"
            app:hz_text="全关"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="锁车升窗通风"
            app:hz_text="通风"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="锁车升窗保持原状"
            app:hz_text="保持原状"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
    </com.hozonauto.widget.tablayout.HzFixedTabLayout>
</LinearLayout>
