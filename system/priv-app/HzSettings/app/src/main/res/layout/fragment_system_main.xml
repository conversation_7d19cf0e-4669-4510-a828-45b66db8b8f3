<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:overScrollMode="never">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <ImageView
            android:id="@+id/carModelBg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:src="@drawable/system_car_bg"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <com.hozon.settings.view.FontTextView
            android:textSize="72sp"
            android:textColor="@color/neutral_color_text_primary"
            android:gravity="center"
            android:id="@+id/localName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="@string/nezha_x"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <com.hozon.settings.view.FontTextView
            android:textColor="@color/neutral_color_text_primary"
            android:id="@+id/softVersion"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            app:layout_constraintLeft_toLeftOf="@+id/localName"
            app:layout_constraintTop_toBottomOf="@+id/localName"
            style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
        <com.hozon.settings.view.FontTextView
            android:textColor="@color/function_color_link_default"
            android:id="@+id/detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="@string/view_details"
            app:layout_constraintLeft_toLeftOf="@+id/softVersion"
            app:layout_constraintTop_toBottomOf="@+id/softVersion"
            style="@style/HozonTheme.TextAppearance.Button.Medium.Regular"/>
        <com.hozon.settings.view.FontTextView
            android:textColor="@color/neutral_color_text_primary"
            android:id="@+id/title_authority_agreement"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="66dp"
            android:text="@string/authority_and_agreement"
            app:layout_constraintLeft_toLeftOf="@+id/detail"
            app:layout_constraintTop_toBottomOf="@+id/detail"
            style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
        <com.hozonauto.widget.list.MultiContainerListItemView
            android:id="@+id/itemLocation"
            android:background="@drawable/neutral_color_background_surface_primary"
            android:layout_width="444dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:contentDescription="@string/permission_location_des"
            app:hz_assitText="@string/unauthorized"
            app:hz_title="@string/permission_location"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title_authority_agreement"/>
        <com.hozonauto.widget.list.MultiContainerListItemView
            android:id="@+id/itemMic"
            android:background="@drawable/neutral_color_background_surface_primary"
            android:layout_width="444dp"
            android:layout_height="wrap_content"
            android:contentDescription="@string/permission_mic_des"
            android:layout_marginStart="24dp"
            app:hz_assitText="@string/unauthorized"
            app:hz_title="@string/permission_mic"
            app:layout_constraintLeft_toRightOf="@+id/itemLocation"
            app:layout_constraintTop_toTopOf="@+id/itemLocation"/>
        <com.hozonauto.widget.list.MultiContainerListItemView
            android:id="@+id/itemCamera"
            android:background="@drawable/neutral_color_background_surface_primary"
            android:layout_width="444dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:contentDescription="@string/permission_camera_des"
            app:hz_assitText="@string/unauthorized"
            app:hz_title="@string/permission_camera"
            app:layout_constraintLeft_toLeftOf="@+id/itemLocation"
            app:layout_constraintTop_toBottomOf="@+id/itemLocation"/>
        <RelativeLayout
            android:id="@+id/layout_agreement"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/itemCamera">
            <com.hozon.settings.view.FontTextView
                android:textColor="@color/neutral_color_text_primary"
                android:id="@+id/tv_read"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/you_can_read"
                style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
            <com.hozon.settings.view.FontTextView
                android:textColor="@color/function_color_link_default"
                android:id="@+id/serviceAgreement"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/user_service_agreement_mark"
                android:layout_toRightOf="@+id/tv_read"
                style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
            <com.hozon.settings.view.FontTextView
                android:textColor="@color/neutral_color_text_primary"
                android:id="@+id/tv_and"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/with"
                android:layout_toRightOf="@+id/serviceAgreement"
                style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
            <com.hozon.settings.view.FontTextView
                android:textColor="@color/function_color_link_default"
                android:id="@+id/privacyAgreement"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/user_privacy_agreement_mark"
                android:layout_toRightOf="@+id/tv_and"
                style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
        </RelativeLayout>
        <com.hozon.settings.view.FontTextView
            android:textColor="@color/neutral_color_text_primary"
            android:id="@+id/tv_reset"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="42dp"
            android:text="@string/reset"
            app:layout_constraintLeft_toLeftOf="@+id/layout_agreement"
            app:layout_constraintTop_toBottomOf="@+id/layout_agreement"
            style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
        <com.hozonauto.widget.btn.ContainerButton
            android:id="@+id/itemRestoreFactory"
            android:layout_width="444dp"
            android:layout_height="72dp"
            android:layout_marginTop="12dp"
            android:layout_marginBottom="10dp"
            app:hz_btnIcon="@drawable/icon_restore_factory"
            app:hz_btnText="@string/factory_data_reset"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_reset"/>
        <com.hozonauto.widget.btn.ContainerButton
            android:id="@+id/itemNetworkReset"
            android:layout_width="444dp"
            android:layout_height="72dp"
            android:layout_marginLeft="24dp"
            android:layout_marginTop="12dp"
            android:layout_marginBottom="10dp"
            app:hz_btnIcon="@drawable/ic_netreset_refresh"
            app:hz_btnStyle="secondary"
            app:hz_btnText="@string/network_reset"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/itemRestoreFactory"
            app:layout_constraintTop_toBottomOf="@+id/tv_reset"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>
