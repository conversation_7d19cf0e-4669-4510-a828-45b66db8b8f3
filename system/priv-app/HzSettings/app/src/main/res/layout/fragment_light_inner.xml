<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:overScrollMode="never">
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:app="http://schemas.android.com/apk/res-auto"
        android:paddingTop="24dp"
        android:layout_width="912dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp">
        <com.hozonauto.widget.btn.ContainerButton
            android:id="@+id/moodLightSetCb"
            android:layout_width="@dimen/px_86"
            android:layout_height="@dimen/px_24"
            android:layout_marginTop="24dp"
            android:contentDescription="@string/lamp"
            app:hz_btnText="@string/lamp"
            app:layout_constraintLeft_toLeftOf="parent"/>
        <TextView
            android:id="@+id/colorBgShader"
            android:visibility="visible"
            android:layout_width="783dp"
            android:layout_height="87dp"
            android:layout_marginTop="120dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/moodLightSetCb"/>
        <ImageView
            android:id="@+id/carBg"
            android:layout_width="match_parent"
            android:layout_height="300dp"
            android:layout_marginTop="24dp"
            android:src="@drawable/bg_car_mode_light"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/moodLightSetCb"/>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:gravity="center"
            android:id="@+id/rhythmLl"
            android:background="@drawable/neutral_color_background_surface_primary"
            android:paddingTop="18dp"
            android:layout_width="276dp"
            android:layout_height="wrap_content"
            android:minHeight="72dp"
            android:layout_marginStart="24dp"
            android:paddingHorizontal="@dimen/dp_48"
            app:layout_constraintLeft_toRightOf="@+id/moodLightSetCb"
            app:layout_constraintTop_toTopOf="@+id/moodLightSetCb">
            <TextView
                android:textColor="@color/neutral_color_text_primary"
                android:gravity="center_vertical"
                android:id="@+id/rhythmModeTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/normal_mode"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
            <ImageView
                android:id="@+id/arrowIv"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:src="@drawable/ic_arrow_down"
                android:layout_marginStart="28dp"
                app:layout_constraintBottom_toBottomOf="@+id/rhythmModeTv"
                app:layout_constraintLeft_toRightOf="@+id/rhythmModeTv"
                app:layout_constraintTop_toTopOf="@+id/rhythmModeTv"/>
            <View
                android:id="@+id/deLine"
                android:background="@drawable/neutral_color_background_surface_tertiary"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginTop="21dp"
                app:layout_constraintTop_toBottomOf="@+id/rhythmModeTv"/>
            <RadioGroup
                android:orientation="vertical"
                android:id="@+id/rhythmItemRg"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/deLine">
                <RadioButton
                    android:id="@+id/rhyBt_1"
                    android:text="@string/rythem_mode"
                    style="@style/LightRhythmRadioButton"/>
                <RadioButton
                    android:id="@+id/rhyBt_2"
                    android:text="@string/normal_mode"
                    style="@style/LightRhythmRadioButton"/>
            </RadioGroup>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/moodLightCl"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="48dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/carBg">
            <com.hozonauto.widget.tablayout.HzFixedTabLayout
                android:id="@+id/colorModeTab"
                android:layout_width="@dimen/px_148"
                android:layout_height="72dp"
                android:layout_marginStart="24dp"
                app:hz_mode="tab"
                app:hz_position="0"
                app:layout_constraintLeft_toRightOf="@+id/moodLightBrightSk"
                app:layout_constraintTop_toTopOf="@+id/moodLightBrightSk"
                app:tabBackground="@null">
                <view
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:contentDescription="@string/color_mode_single"
                    app:hz_text="@string/color_mode_single"
                    class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
                <view
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:contentDescription="@string/color_mode_mood"
                    app:hz_text="@string/color_mode_mood"
                    class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
                <view
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:contentDescription="@string/color_mode_driving"
                    app:hz_text="@string/color_mode_driving"
                    class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
            </com.hozonauto.widget.tablayout.HzFixedTabLayout>
            <com.hozonauto.widget.HzWidenSeekBar
                android:id="@+id/moodLightBrightSk"
                android:layout_width="@dimen/px_148"
                android:layout_height="@dimen/px_24"
                android:layout_marginTop="12dp"
                android:max="100"
                android:progress="50"
                android:min="1"
                app:hz_hasPop="false"
                app:hz_isEnable="true"
                app:hz_startIcon="@drawable/ic_brightness_light"
                app:hz_startText="@string/string_brigh"
                app:layout_constraintLeft_toLeftOf="parent"/>
            <SeekBar
                android:id="@+id/moodLightColorSk"
                android:background="@drawable/bg_color_sb"
                android:layout_width="912dp"
                android:layout_height="@dimen/px_24"
                android:layout_marginTop="24dp"
                android:max="62"
                android:progressDrawable="@null"
                android:thumb="@drawable/ic_cursor"
                android:contentDescription="@string/seekbar_des"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/moodLightBrightSk"/>
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/mulColorRv"
                android:layout_width="912dp"
                android:layout_height="162dp"
                android:layout_marginTop="24dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/moodLightBrightSk"/>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/drivingModeCl"
                android:visibility="gone"
                android:layout_width="912dp"
                android:layout_height="168dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/moodLightBrightSk">
                <ImageView
                    android:id="@+id/colorIv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_driving_color"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <ImageView
                    android:id="@+id/carIconIv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_driving_car"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="24sp"
                    android:textColor="@color/neutral_color_text_secondary"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/color_mode_tip"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/carIconIv"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <RadioGroup
                android:orientation="horizontal"
                android:id="@+id/musicColorRg"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="48dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/moodLightBrightSk">
                <RadioButton
                    android:id="@+id/colorRadia1"
                    android:background="@drawable/color_radia_b1"
                    style="@style/LightFragmentFirstRadioButton"/>
                <RadioButton
                    android:id="@+id/colorRadia2"
                    android:background="@drawable/color_radia_b2"
                    style="@style/LightFragmentRadioButton"/>
                <RadioButton
                    android:id="@+id/colorRadia3"
                    android:background="@drawable/color_radia_b3"
                    style="@style/LightFragmentRadioButton"/>
                <RadioButton
                    android:id="@+id/colorRadia4"
                    android:background="@drawable/color_radia_b4"
                    style="@style/LightFragmentRadioButton"/>
                <RadioButton
                    android:id="@+id/colorRadia5"
                    android:background="@drawable/color_radia_b5"
                    style="@style/LightFragmentRadioButton"/>
                <RadioButton
                    android:id="@+id/colorRadia6"
                    android:background="@drawable/color_radia_b6"
                    style="@style/LightFragmentRadioButton"/>
                <RadioButton
                    android:id="@+id/colorRadia7"
                    android:background="@drawable/color_radia_b7"
                    style="@style/LightFragmentRadioButton"/>
            </RadioGroup>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <RelativeLayout
            android:gravity="center_vertical"
            android:id="@+id/moodLightRl"
            android:visibility="visible"
            android:layout_width="540dp"
            android:layout_height="123dp"
            android:layout_marginTop="38dp"
            android:layout_marginBottom="28dp"
            android:paddingStart="21dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/moodLightCl">
            <com.hozonauto.widget.SwitchButton
                android:id="@+id/moodLightSw"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:checked="true"
                android:contentDescription="@string/mood_light_des"
                style="@style/SwitchButtonDefaultStyle"/>
            <TextView
                android:id="@+id/moodLightSingleTv"
                android:text="@string/mood_light"
                android:layout_marginStart="20dp"
                android:layout_toEndOf="@+id/moodLightSw"
                style="@style/LightFragmentFirstText"/>
            <TextView
                android:id="@+id/moodLightDes"
                android:layout_marginTop="@dimen/dp_10"
                android:text="@string/mood_light_tip"
                android:layout_below="@+id/moodLightSingleTv"
                android:layout_marginStart="20dp"
                android:layout_toEndOf="@+id/moodLightSw"
                style="@style/LightFragmentSecondText"/>
        </RelativeLayout>
        <TextView
            android:gravity="center_vertical"
            android:id="@+id/readModeTv"
            android:text="@string/read_light"
            android:drawablePadding="10dp"
            android:contentDescription="@string/read_light_tip"
            android:drawableEnd="@drawable/ic_tip"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/moodLightRl"
            style="@style/LightFragmentFirstText"/>
        <com.hozonauto.widget.tablayout.HzFixedTabLayout
            android:id="@+id/readModeTab"
            android:layout_width="@dimen/px_210"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            app:hz_mode="tab"
            app:hz_position="0"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/readModeTv"
            app:tabBackground="@null">
            <view
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:contentDescription="@string/read_light_des_1"
                app:hz_text="@string/close"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
            <view
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:contentDescription="@string/read_light_des_2"
                app:hz_text="@string/door_open"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
            <view
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:contentDescription="@string/read_light_des_3"
                app:hz_text="@string/auto"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        </com.hozonauto.widget.tablayout.HzFixedTabLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
