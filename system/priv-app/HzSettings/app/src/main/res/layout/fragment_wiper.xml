<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <ImageView
        android:id="@+id/backIv"
        android:background="@drawable/ic_back"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginTop="42dp"
        android:contentDescription="@string/back"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textColor="@color/neutral_color_text_primary"
        android:id="@+id/titleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="50dp"
        android:text="@string/protect"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/HozonTheme.TextAppearance.Subtitle.Small.Regular"/>
    <TextView
        android:textColor="@color/neutral_color_text_caption"
        android:id="@+id/carPowerTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="60dp"
        android:text="@string/car_power"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/backIv"
        style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
    <com.hozonauto.widget.btn.ContainerButton
        android:id="@+id/carPowerCb"
        android:layout_width="265dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        app:hz_btnIcon="@drawable/selector_power_icon"
        app:hz_btnStyle="secondary"
        app:hz_btnText="@string/power_down"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/carPowerTv"/>
    <TextView
        android:textColor="@color/neutral_color_text_caption"
        android:id="@+id/contentTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="66dp"
        android:text="@string/wiper_remain"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/carPowerCb"
        style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
    <com.hozonauto.widget.btn.ContainerButton
        android:id="@+id/wiperCb"
        android:layout_width="265dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:contentDescription="@string/wiper_remain_des"
        app:hz_btnIcon="@drawable/selector_wiper_icon"
        app:hz_btnStyle="secondary"
        app:hz_btnText="@string/wiper_remain"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/contentTitleTv"/>
    <TextView
        android:textColor="@color/neutral_color_text_caption"
        android:id="@+id/tractionModelTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="66dp"
        android:text="@string/traction_mode"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/wiperCb"
        style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
    <com.hozonauto.widget.btn.ContainerButton
        android:id="@+id/tractionCb"
        android:layout_width="265dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        app:hz_btnIcon="@drawable/selector_traction_icon"
        app:hz_btnStyle="secondary"
        app:hz_btnText="@string/goto_traction"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tractionModelTv"/>
    <ImageView
        android:id="@+id/wiperDesIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@mipmap/car_bg_wiper"
        android:layout_marginEnd="150dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/carPowerCb"/>
    <TextView
        android:textColor="@color/neutral_color_text_secondary"
        android:id="@+id/wiperDesTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/wiper_tip_4"
        app:layout_constraintLeft_toLeftOf="@+id/wiperDesIv"
        app:layout_constraintTop_toBottomOf="@+id/wiperDesIv"
        style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
</androidx.constraintlayout.widget.ConstraintLayout>
