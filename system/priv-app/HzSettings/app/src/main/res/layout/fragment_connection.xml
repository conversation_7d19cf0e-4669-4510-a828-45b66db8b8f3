<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <com.hozon.settings.view.FontTextView
        android:textColor="@color/neutral_color_text_primary"
        android:id="@+id/tv_local"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="64dp"
        android:text="本机名称"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
    <com.hozonauto.widget.list.ContainerListItemView
        android:id="@+id/itemLocalName"
        android:background="@drawable/neutral_color_background_surface_primary"
        android:layout_width="444dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:contentDescription="编辑"
        app:hz_endIcon="@drawable/ic_edit_64"
        app:hz_title="NETA_FFFFFFFF"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_local"/>
    <com.hozon.settings.view.FontTextView
        android:textColor="@color/neutral_color_text_secondary"
        android:id="@+id/tv_local_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:text="蓝牙、热点将被发现为"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/itemLocalName"
        style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
    <com.hozonauto.widget.list.MultiContainerListItemView
        android:id="@+id/itemBluetooth"
        android:background="@drawable/neutral_color_background_surface_primary"
        android:layout_width="444dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="48dp"
        android:contentDescription="蓝牙展开"
        app:hz_assitText="未开启"
        app:hz_title="蓝牙"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_local_content"/>
    <com.hozonauto.widget.list.MultiContainerListItemView
        android:id="@+id/itemWlan"
        android:background="@drawable/neutral_color_background_surface_primary"
        android:layout_width="444dp"
        android:layout_height="wrap_content"
        android:contentDescription="WLAN展开"
        android:layout_marginStart="24dp"
        app:hz_assitText="未开启"
        app:hz_title="WLAN"
        app:layout_constraintLeft_toRightOf="@+id/itemBluetooth"
        app:layout_constraintTop_toTopOf="@+id/itemBluetooth"/>
    <com.hozonauto.widget.list.MultiContainerListItemView
        android:id="@+id/itemAp"
        android:background="@drawable/neutral_color_background_surface_primary"
        android:layout_width="444dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:contentDescription="热点展开"
        app:hz_assitText="未开启"
        app:hz_title="热点"
        app:layout_constraintLeft_toLeftOf="@+id/itemBluetooth"
        app:layout_constraintTop_toBottomOf="@+id/itemBluetooth"/>
    <RelativeLayout
        android:id="@+id/itemWireCharg"
        android:background="@drawable/neutral_color_background_surface_primary"
        android:layout_width="444dp"
        android:layout_height="0dp"
        android:layout_marginStart="24dp"
        app:layout_constraintBottom_toBottomOf="@+id/itemAp"
        app:layout_constraintLeft_toRightOf="@+id/itemAp"
        app:layout_constraintTop_toTopOf="@+id/itemAp">
        <com.hozonauto.widget.SwitchButton
            android:id="@+id/wireChargSwitch"
            android:contentDescription="无线充电(switch)"
            android:layout_marginStart="24dp"
            android:layout_alignParentEnd="false"
            style="@style/SwitchButton"/>
        <com.hozon.settings.view.FontTextView
            android:textColor="@color/neutral_color_text_primary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="无线充电"
            android:layout_toRightOf="@+id/wireChargSwitch"
            android:layout_centerVertical="true"
            android:layout_marginStart="24dp"
            style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
