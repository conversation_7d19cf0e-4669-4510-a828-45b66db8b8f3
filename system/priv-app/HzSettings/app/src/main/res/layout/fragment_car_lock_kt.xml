<?xml version="1.0" encoding="utf-8"?>
<com.hozon.settings.view.BanAutoSlideScrollView xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/scrollview"
    android:paddingBottom="@dimen/dp_24"
    android:scrollbars="none"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:overScrollMode="never">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <com.hozonauto.widget.btn.ContainerButton
            android:id="@+id/ll_central_locking"
            android:layout_width="444dp"
            android:layout_height="72dp"
            android:layout_marginTop="@dimen/dp_48"
            app:hz_btnIcon="@drawable/icon_central_locking"
            app:hz_btnText="@string/title_central_locking"/>
        <TextView
            android:id="@+id/tv_remind_title"
            android:layout_marginTop="@dimen/dp_48"
            android:text="@string/title_lock_unlock_tip"
            android:layout_marginStart="0dp"
            style="@style/title_text_style"/>
        <com.hozonauto.widget.tablayout.HzFixedTabLayout
            android:id="@+id/item_lock_unlock_tip"
            android:layout_width="576dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            app:hz_mode="tab"
            app:hz_position="0"
            app:hz_tab_background="@drawable/neutral_color_background_surface_primary"/>
        <TextView
            android:id="@+id/tv_lock_unlock_sound"
            android:layout_marginTop="@dimen/dp_48"
            android:text="@string/title_lock_unlock_sound"
            android:layout_marginStart="0dp"
            style="@style/title_text_style"/>
        <com.hozonauto.widget.tablayout.HzFixedTabLayout
            android:id="@+id/item_lock_unlock_sound"
            android:layout_width="576dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            app:hz_mode="tab"
            app:hz_position="0"
            app:hz_tab_background="@drawable/neutral_color_background_surface_primary">
            <view
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:contentDescription="@string/desc_1_lock_unlock_sound"
                app:hz_text="@string/select_1_lock_unlock_sound"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
            <view
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:contentDescription="@string/desc_2_lock_unlock_sound"
                app:hz_text="@string/select_2_lock_unlock_sound"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
            <view
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:contentDescription="@string/desc_3_lock_unlock_sound"
                app:hz_text="@string/select_3_lock_unlock_sound"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        </com.hozonauto.widget.tablayout.HzFixedTabLayout>
        <TextView
            android:id="@+id/tv_rise_window_title"
            android:layout_marginTop="@dimen/dp_48"
            android:text="@string/title_lockup"
            android:layout_marginStart="0dp"
            style="@style/title_text_style"/>
        <com.hozonauto.widget.tablayout.HzFixedTabLayout
            android:id="@+id/item_lockup"
            android:layout_width="576dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            app:hz_mode="tab"
            app:hz_position="0"
            app:hz_tab_background="@drawable/neutral_color_background_surface_primary">
            <view
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:contentDescription="@string/desc_1_lockup"
                app:hz_text="@string/all_close"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
            <view
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:contentDescription="@string/desc_2_lockup"
                app:hz_text="@string/ventilate"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
            <view
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:contentDescription="@string/desc_3_lockup"
                app:hz_text="@string/keeping"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        </com.hozonauto.widget.tablayout.HzFixedTabLayout>
        <TextView
            android:textColor="@color/neutral_color_text_secondary"
            android:id="@+id/tv_lock_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="@string/msg_lock_up"
            style="@style/HozonTheme.TextAppearance.Caption.Small.Regular"/>
        <LinearLayout
            android:id="@+id/ll_wrap_flex"
            android:layout_width="match_parent"
            android:layout_height="240dp">
            <com.hozon.settings.view.FlexLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                app:heightSpace="48dp"
                app:widthSpace="24dp">
                <LinearLayout
                    android:id="@+id/ll_hold_unlock"
                    android:tag="@string/title_hold_unlock"
                    android:layout_marginEnd="24dp"
                    style="@style/switch_bg_style">
                    <com.hozonauto.widget.SwitchButton
                        android:id="@+id/sw_hold_unlock"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="@string/desc_hold_unlock"
                        app:hz_click_interval="500"
                        style="@style/SwitchButton"/>
                    <TextView
                        android:id="@+id/tv_hold_unlock"
                        android:text="@string/title_hold_unlock"
                        style="@style/title_text_style"/>
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/ll_no_feel_in_out"
                    android:tag="@string/title_no_feel_in_out"
                    style="@style/switch_bg_style">
                    <com.hozonauto.widget.SwitchButton
                        android:id="@+id/sw_no_feel_in_out"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="@string/desc_no_feel_in_out"
                        app:hz_click_interval="500"
                        style="@style/SwitchButton"/>
                    <TextView
                        android:id="@+id/tv_no_feel_in_out"
                        android:text="@string/title_no_feel_in_out"
                        style="@style/title_text_style"/>
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/ll_door_auto_recover"
                    android:tag="@string/title_door_auto_recover"
                    android:layout_marginTop="@dimen/dp_48"
                    style="@style/switch_bg_style">
                    <com.hozonauto.widget.SwitchButton
                        android:id="@+id/sw_door_auto_recover"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="@string/sw_door_auto_recover"
                        app:hz_click_interval="500"
                        style="@style/SwitchButton"/>
                    <TextView
                        android:id="@+id/tv_door_auto_recover"
                        android:text="@string/title_door_auto_recover"
                        style="@style/title_text_style"/>
                </LinearLayout>
            </com.hozon.settings.view.FlexLayout>
        </LinearLayout>
    </LinearLayout>
</com.hozon.settings.view.BanAutoSlideScrollView>
