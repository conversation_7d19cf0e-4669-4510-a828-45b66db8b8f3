<?xml version="1.0" encoding="utf-8"?>
<com.hozon.settings.view.BanAutoSlideScrollView xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/scrollLy"
    android:scrollbars="none"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:overScrollMode="never">
    <LinearLayout
        android:orientation="vertical"
        android:paddingTop="64dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:textColor="@color/neutral_color_text_primary"
            android:id="@+id/brightnessTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/display_brightness"
            style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_4">
            <com.hozonauto.widget.HzWidenSeekBar
                android:id="@+id/sbHuBrightness"
                android:layout_width="@dimen/px_164"
                android:layout_height="@dimen/px_24"
                android:max="50"
                android:min="1"
                app:hz_hasPop="false"
                app:hz_isEnable="true"
                app:hz_startIcon="@drawable/ic_brightness_light"
                app:hz_startText="@string/central_control"/>
            <com.hozon.settings.widge.SkinBrightCompatCheckBox
                android:gravity="center"
                android:id="@+id/cbHuBrightness"
                android:background="@drawable/neutral_color_background_surface_primary"
                android:layout_width="@dimen/px_40"
                android:layout_height="@dimen/px_24"
                android:layout_marginLeft="@dimen/px_8"
                android:button="@null"
                android:layout_toRightOf="@+id/sbHuBrightness"
                android:contentDescription="@string/central_control_switch"
                style="@style/HozonTheme.TextAppearance.Button.Medium.Regular"/>
        </RelativeLayout>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_8">
            <com.hozonauto.widget.HzWidenSeekBar
                android:id="@+id/sbMeterBrightness"
                android:layout_width="@dimen/px_164"
                android:layout_height="@dimen/px_24"
                android:max="10"
                android:min="1"
                app:hz_hasPop="false"
                app:hz_isEnable="true"
                app:hz_startIcon="@drawable/ic_brightness_light"
                app:hz_startText="@string/meter_brightness_str"/>
            <com.hozon.settings.widge.SkinBrightCompatCheckBox
                android:gravity="center"
                android:layout_gravity="bottom|right"
                android:id="@+id/cbMeterBrightness"
                android:background="@drawable/neutral_color_background_surface_primary"
                android:layout_width="@dimen/px_40"
                android:layout_height="@dimen/px_24"
                android:layout_marginLeft="@dimen/px_8"
                android:button="@null"
                android:layout_toRightOf="@+id/sbMeterBrightness"
                android:contentDescription="@string/meter_brightness_switch"
                style="@style/HozonTheme.TextAppearance.Button.Medium.Regular"/>
        </RelativeLayout>
        <RelativeLayout
            android:id="@+id/SecondaryHuView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_8">
            <com.hozonauto.widget.HzWidenSeekBar
                android:id="@+id/sbSecondaryHuBrightness"
                android:layout_width="@dimen/px_164"
                android:layout_height="@dimen/px_24"
                android:max="50"
                android:min="1"
                app:hz_hasPop="false"
                app:hz_isEnable="true"
                app:hz_startIcon="@drawable/ic_brightness_light"
                app:hz_startText="@string/secondary_brightness_str"/>
            <com.hozon.settings.widge.SkinBrightCompatCheckBox
                android:gravity="center"
                android:layout_gravity="bottom|right"
                android:id="@+id/cbSecondaryBrightness"
                android:background="@drawable/neutral_color_background_surface_primary"
                android:layout_width="@dimen/px_40"
                android:layout_height="@dimen/px_24"
                android:layout_marginLeft="@dimen/px_8"
                android:button="@null"
                android:layout_toRightOf="@+id/sbSecondaryHuBrightness"
                android:contentDescription="@string/secondary_brightness_switch"
                style="@style/HozonTheme.TextAppearance.Button.Medium.Regular"/>
        </RelativeLayout>
        <RelativeLayout
            android:orientation="horizontal"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_16">
            <TextView
                android:textColor="@color/neutral_color_text_primary"
                android:id="@+id/title_theme"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/theme_str"
                style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
            <com.hozon.settings.widge.FlowRadioGroup
                android:id="@+id/rgThemeMode"
                android:layout_width="@dimen/px_180"
                android:layout_height="@dimen/px_24"
                android:layout_marginTop="@dimen/px_4"
                android:layout_below="@+id/title_theme">
                <com.hozon.settings.widge.SkinRadioButton
                    android:textColor="@color/neutral_color_text_primary"
                    android:gravity="center"
                    android:id="@+id/rbThemeModeModern"
                    android:background="@drawable/neutral_color_background_surface_primary"
                    android:layout_width="@dimen/px_86"
                    android:layout_height="@dimen/px_24"
                    android:button="@null"
                    android:text="@string/modern_str"
                    android:contentDescription="@string/modern_str_desc"
                    style="@style/HozonTheme.TextAppearance.Button.Medium.Regular"/>
                <com.hozon.settings.widge.SkinRadioButton
                    android:textColor="@color/neutral_color_text_primary"
                    android:gravity="center"
                    android:id="@+id/rbThemeModeDream"
                    android:background="@drawable/neutral_color_background_surface_primary"
                    android:layout_width="@dimen/px_86"
                    android:layout_height="@dimen/px_24"
                    android:layout_marginLeft="@dimen/px_8"
                    android:button="@null"
                    android:text="@string/dream_str"
                    android:contentDescription="@string/dream_str_desc"
                    style="@style/HozonTheme.TextAppearance.Button.Medium.Regular"/>
            </com.hozon.settings.widge.FlowRadioGroup>
            <LinearLayout
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:visibility="gone"
                android:layout_width="@dimen/px_100"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/px_24"
                android:layout_toRightOf="@+id/rgThemeMode">
                <TextView
                    android:textSize="@dimen/px_8"
                    android:textColor="@color/neutral_color_text_secondary"
                    android:id="@+id/themeSubTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/theme_sub_title"/>
                <com.zhpan.bannerview.BannerViewPager
                    android:id="@+id/bannerView"
                    android:layout_width="@dimen/px_100"
                    android:layout_height="@dimen/px_56"
                    android:layout_margin="@dimen/px_8"/>
            </LinearLayout>
        </RelativeLayout>
        <TextView
            android:textColor="@color/neutral_color_text_primary"
            android:id="@+id/displayModeTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_16"
            android:text="@string/display_mode_str"
            style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
        <com.hozonauto.widget.tablayout.HzFixedTabLayout
            android:id="@+id/displayModeHzFixedTabLayout"
            android:layout_width="@dimen/px_210"
            android:layout_height="@dimen/px_24"
            android:layout_marginTop="@dimen/px_4"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            app:hz_animStyle="promptly"
            app:hz_mode="tab"
            app:hz_position="0"
            app:hz_tab_background="@drawable/neutral_color_background_surface_primary"
            app:tabBackground="@null">
            <view
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:contentDescription="@string/deep_colour_str_desc"
                app:hz_text="@string/deep_colour_str"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
            <view
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:contentDescription="@string/light_colour_str_desc"
                app:hz_text="@string/light_colour_str"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
            <view
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:contentDescription="@string/light_auto_str_desc"
                app:hz_text="@string/auto_str"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        </com.hozonauto.widget.tablayout.HzFixedTabLayout>
        <TextView
            android:textColor="@color/neutral_color_text_primary"
            android:id="@+id/fontScaleTitle"
            android:visibility="visible"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_16"
            android:text="@string/title_font_scale"
            style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
        <com.hozonauto.widget.tablayout.HzFixedTabLayout
            android:id="@+id/rlFontScale"
            android:visibility="visible"
            android:layout_width="@dimen/px_210"
            android:layout_height="@dimen/px_24"
            android:layout_marginTop="@dimen/px_4"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            app:hz_animStyle="promptly"
            app:hz_mode="tab"
            app:hz_position="0"
            app:hz_tab_background="@drawable/neutral_color_background_surface_primary"
            app:tabBackground="@null">
            <view
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:contentDescription="@string/font_scale_small_str_desc"
                app:hz_text="@string/font_scale_small_str"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
            <view
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:contentDescription="@string/font_scale_middle_str_desc"
                app:hz_text="@string/font_scale_middle_str"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
            <view
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:contentDescription="@string/font_scale_big_str_desc"
                app:hz_text="@string/font_scale_big_str"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        </com.hozonauto.widget.tablayout.HzFixedTabLayout>
        <TextView
            android:textColor="@color/neutral_color_text_primary"
            android:id="@+id/instrumentTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_16"
            android:text="@string/title_instrument_display"
            style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
        <com.hozonauto.widget.tablayout.HzFixedTabLayout
            android:id="@+id/rlGaugeTheme"
            android:layout_width="@dimen/px_140"
            android:layout_height="@dimen/px_24"
            android:layout_marginTop="@dimen/px_4"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            app:hz_animStyle="promptly"
            app:hz_mode="tab"
            app:hz_position="0"
            app:hz_tab_background="@drawable/neutral_color_background_surface_primary"
            app:tabBackground="@null">
            <view
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:contentDescription="@string/instrument_light_standard_str_desc"
                app:hz_text="@string/light_standard_str"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
            <view
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:contentDescription="@string/instrument_light_concise_st_desc"
                app:hz_text="@string/light_concise_str"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        </com.hozonauto.widget.tablayout.HzFixedTabLayout>
        <TextView
            android:textColor="@color/neutral_color_text_primary"
            android:id="@+id/desktopThemeTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_16"
            android:text="@string/title_desktop_theme"
            style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
        <com.hozonauto.widget.tablayout.HzFixedTabLayout
            android:id="@+id/rlDeskTopTheme"
            android:layout_width="@dimen/px_140"
            android:layout_height="@dimen/px_24"
            android:layout_marginTop="@dimen/px_4"
            android:layout_marginBottom="@dimen/px_8"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            app:hz_animStyle="promptly"
            app:hz_mode="tab"
            app:hz_position="0"
            app:hz_tab_background="@drawable/neutral_color_background_surface_primary"
            app:tabBackground="@null">
            <view
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:contentDescription="@string/title_desktop_theme_map_desc"
                app:hz_text="@string/title_desktop_theme_map"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
            <view
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:contentDescription="@string/title_desktop_theme_wallpaper_desc"
                app:hz_text="@string/title_desktop_theme_wallpaper"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        </com.hozonauto.widget.tablayout.HzFixedTabLayout>
        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/PsdView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <com.hozonauto.widget.list.MultiContainerListItemView
                android:gravity="center_vertical"
                android:id="@+id/autoWallpaperFollow"
                android:layout_width="@dimen/px_148"
                android:layout_height="@dimen/px_30"
                android:layout_marginLeft="-24dp"
                android:layout_marginTop="@dimen/px_4"
                app:hz_icon="@drawable/ic_im_60"
                app:hz_title="@string/psd_wallpaper_follow_str"
                app:hz_titleCDesc="@string/psd_wallpaper_follow_str_desc"/>
            <com.hozonauto.widget.list.MultiContainerListItemView
                android:gravity="center_vertical"
                android:id="@+id/autoCohuView"
                android:layout_width="@dimen/px_148"
                android:layout_height="@dimen/px_30"
                android:layout_marginLeft="-24dp"
                android:layout_marginTop="@dimen/px_4"
                app:hz_icon="@drawable/ic_im_60"
                app:hz_title="@string/auto_cohu_str"
                app:hz_titleCDesc="副驾屏自动进入屏保提示"/>
            <com.hozonauto.widget.list.MultiContainerListItemView
                android:gravity="center_vertical"
                android:id="@+id/cbCopilotScreenView"
                android:layout_width="@dimen/px_148"
                android:layout_height="@dimen/px_30"
                android:layout_marginLeft="-24dp"
                android:layout_marginTop="@dimen/px_4"
                app:hz_icon="@drawable/ic_im_60"
                app:hz_title="@string/copilot_screen_str"
                app:hz_titleCDesc="副驾息屏提示"/>
        </LinearLayout>
        <skin.support.widget.SkinCompatTextView
            android:textColor="@color/neutral_color_text_primary"
            android:gravity="center_vertical"
            android:id="@+id/switch_clean_screen"
            android:background="@drawable/neutral_color_background_surface_primary"
            android:paddingLeft="@dimen/px_16"
            android:paddingTop="@dimen/px_6"
            android:paddingRight="@dimen/px_15"
            android:paddingBottom="@dimen/px_6"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_8"
            android:layout_marginBottom="@dimen/px_6"
            android:checked="false"
            android:button="@null"
            android:text="@string/clean_screen_str"
            android:drawableLeft="@drawable/clean_screen_icon"
            android:drawablePadding="@dimen/px_4"
            android:contentDescription="@string/clean_screen_switch_str"
            style="@style/HozonTheme.TextAppearance.Button.Medium.Regular"/>
    </LinearLayout>
</com.hozon.settings.view.BanAutoSlideScrollView>
