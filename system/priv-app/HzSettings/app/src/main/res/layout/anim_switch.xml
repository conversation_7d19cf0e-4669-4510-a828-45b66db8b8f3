<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:gravity="center"
        android:orientation="vertical"
        android:layout_width="108dp"
        android:layout_height="156dp">
        <FrameLayout
            android:gravity="center"
            android:layout_gravity="center"
            android:id="@+id/ll_back"
            android:background="@drawable/neutral_color_background_surface_primary"
            android:layout_width="108dp"
            android:layout_height="108dp">
            <View
                android:layout_gravity="center"
                android:id="@+id/view"
                android:layout_width="60dp"
                android:layout_height="60dp"/>
        </FrameLayout>
        <TextView
            android:textColor="@color/neutral_color_text_primary"
            android:id="@+id/tv_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="一键通风"
            style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
    </LinearLayout>
</merge>
