<?xml version="1.0" encoding="utf-8"?>
<com.hozon.settings.view.BanAutoSlideScrollView xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/scrollview"
    android:scrollbars="none"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:overScrollMode="never">
    <LinearLayout
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_24"
        android:clickable="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <com.hozon.settings.view.SkinConstraintLayout
            android:layout_width="912dp"
            android:layout_height="234dp"
            android:layout_marginTop="@dimen/dp_48">
            <TextView
                android:textColor="@color/neutral_color_text_primary"
                android:id="@+id/tv_car_window"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/car_window"
                style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
            <com.hozon.settings.view.AnimSwitch
                android:id="@+id/ss_car_window_close_all"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:contentDescription="@string/all_close"
                android:layout_marginStart="24dp"
                app:layout_constraintTop_toBottomOf="@+id/tv_car_window"
                app:state_switch_icon="@drawable/car_window_close"
                app:state_switch_text="@string/all_close"/>
            <com.hozon.settings.view.AnimSwitch
                android:id="@+id/ss_car_window_ventilate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:contentDescription="@string/ventilate"
                android:layout_marginStart="24dp"
                app:layout_constraintStart_toEndOf="@+id/ss_car_window_close_all"
                app:layout_constraintTop_toTopOf="@+id/ss_car_window_close_all"
                app:state_switch_icon="@drawable/car_window_ventilate"
                app:state_switch_text="@string/ventilate"/>
            <com.hozon.settings.view.AnimSwitch
                android:id="@+id/ss_car_window_open_all"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:contentDescription="@string/all_open"
                android:layout_marginStart="24dp"
                app:layout_constraintStart_toEndOf="@+id/ss_car_window_ventilate"
                app:layout_constraintTop_toTopOf="@+id/ss_car_window_close_all"
                app:state_switch_icon="@drawable/car_window_open"
                app:state_switch_text="@string/all_open"/>
            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/img_car_window"/>
                <ImageView
                    android:id="@+id/ivCarWindowAllClose"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/img_car_window_all_close"
                    android:alpha="0"/>
                <ImageView
                    android:id="@+id/ivCarWindowVentilate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/img_car_window_ventilate"
                    android:alpha="0"/>
                <ImageView
                    android:id="@+id/ivCarWindowAllOpen"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/img_car_window_all_open"
                    android:alpha="0"/>
            </FrameLayout>
        </com.hozon.settings.view.SkinConstraintLayout>
        <com.hozon.settings.view.SkinConstraintLayout
            android:id="@+id/cl_sky_window"
            android:layout_width="912dp"
            android:layout_height="234dp"
            android:layout_marginTop="24dp">
            <TextView
                android:textColor="@color/neutral_color_text_primary"
                android:id="@+id/tv_sky_window"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="18dp"
                android:text="@string/sky_window"
                style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
            <com.hozon.settings.view.AnimSwitch
                android:id="@+id/ss_sky_window_close_all"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_marginStart="24dp"
                app:layout_constraintTop_toBottomOf="@+id/tv_sky_window"
                app:state_switch_icon="@drawable/icon_skylight_close"
                app:state_switch_text="@string/all_close"/>
            <com.hozon.settings.view.AnimSwitch
                android:id="@+id/ss_sky_window_ventilate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                app:layout_constraintStart_toEndOf="@+id/ss_sky_window_close_all"
                app:layout_constraintTop_toTopOf="@+id/ss_sky_window_close_all"
                app:state_switch_icon="@drawable/icon_skylight_open_ventilate"
                app:state_switch_text="@string/ventilate"/>
            <com.hozon.settings.view.AnimSwitch
                android:id="@+id/ss_sky_window_open_all"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                app:layout_constraintStart_toEndOf="@+id/ss_sky_window_ventilate"
                app:layout_constraintTop_toTopOf="@+id/ss_sky_window_close_all"
                app:state_switch_icon="@drawable/ic_skylight_open_all"
                app:state_switch_text="@string/all_open"/>
            <com.hozon.settings.view.StateSwitch
                android:id="@+id/ss_sky_window_pause"
                android:visibility="invisible"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                app:layout_constraintStart_toEndOf="@+id/ss_sky_window_open_all"
                app:layout_constraintTop_toTopOf="@+id/ss_sky_window_close_all"
                app:state_switch_icon="@drawable/icon_pause"
                app:state_switch_text="@string/pause"/>
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/img_sunshade"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
            <TextView
                android:textColor="@color/neutral_color_text_primary"
                android:id="@+id/tv_progress_value_sky_window"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_36"
                android:layout_marginTop="45dp"
                app:layout_constraintLeft_toLeftOf="@+id/seek_bar_sky_window"
                app:layout_constraintRight_toRightOf="@+id/seek_bar_sky_window"
                app:layout_constraintTop_toTopOf="parent"
                style="@style/HozonTheme.TextAppearance.Body.Small.Regular.EN"/>
            <com.hozon.settings.view.ReversedSeekBar
                android:id="@+id/seek_bar_sky_window"
                android:background="#00ffffff"
                android:paddingLeft="0dp"
                android:paddingRight="0dp"
                android:duplicateParentState="true"
                android:layout_width="230dp"
                android:layout_height="61dp"
                android:layout_marginRight="87dp"
                android:max="20"
                android:progressDrawable="@drawable/seek_bar_bg"
                android:thumb="@null"
                android:paddingStart="0dp"
                android:paddingEnd="0dp"
                android:min="0"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_progress_value_sky_window"/>
            <TextView
                android:textColor="@color/neutral_color_text_caption"
                android:id="@+id/tv_sky_window_location"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_36"
                android:layout_marginTop="21dp"
                android:text="@string/sky_window_position"
                app:layout_constraintLeft_toLeftOf="@+id/seek_bar_sky_window"
                app:layout_constraintRight_toRightOf="@+id/seek_bar_sky_window"
                app:layout_constraintTop_toBottomOf="@+id/seek_bar_sky_window"
                style="@style/HozonTheme.TextAppearance.Caption.Mini.Regular"/>
        </com.hozon.settings.view.SkinConstraintLayout>
        <com.hozon.settings.view.SkinConstraintLayout
            android:id="@+id/cl_sunshade"
            android:layout_width="912dp"
            android:layout_height="234dp"
            android:layout_marginTop="24dp">
            <TextView
                android:textColor="@color/neutral_color_text_primary"
                android:id="@+id/tv_sunshade"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@string/sunshade"
                style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
            <com.hozon.settings.view.AnimSwitch
                android:id="@+id/ss_sunshade_close_all"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="18dp"
                android:layout_marginStart="24dp"
                app:layout_constraintTop_toBottomOf="@+id/tv_sunshade"
                app:state_switch_icon="@drawable/icon_one_button_ventilation_close"
                app:state_switch_text="@string/all_close"/>
            <com.hozon.settings.view.AnimSwitch
                android:id="@+id/ss_sunshade_open_all"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                app:layout_constraintStart_toEndOf="@+id/ss_sunshade_close_all"
                app:layout_constraintTop_toTopOf="@+id/ss_sunshade_close_all"
                app:state_switch_icon="@drawable/icon_one_button_ventilation_open"
                app:state_switch_text="@string/all_open"/>
            <com.hozon.settings.view.StateSwitch
                android:id="@+id/ss_sun_shade_pause"
                android:visibility="invisible"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                app:layout_constraintStart_toEndOf="@+id/ss_sunshade_open_all"
                app:layout_constraintTop_toTopOf="@+id/ss_sunshade_close_all"
                app:state_switch_icon="@drawable/icon_pause"
                app:state_switch_text="@string/pause"/>
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/img_sunshade"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
            <TextView
                android:textColor="@color/neutral_color_text_primary"
                android:id="@+id/tv_progress_value_sunshade"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_36"
                android:layout_marginTop="45dp"
                app:layout_constraintLeft_toLeftOf="@+id/seek_bar_sunshade"
                app:layout_constraintRight_toRightOf="@+id/seek_bar_sunshade"
                app:layout_constraintTop_toTopOf="parent"
                style="@style/HozonTheme.TextAppearance.Body.Small.Regular.EN"/>
            <com.hozon.settings.view.ReversedSeekBar
                android:id="@+id/seek_bar_sunshade"
                android:background="#00ffffff"
                android:paddingLeft="0dp"
                android:paddingRight="0dp"
                android:duplicateParentState="true"
                android:layout_width="230dp"
                android:layout_height="61dp"
                android:layout_marginRight="87dp"
                android:max="20"
                android:progressDrawable="@drawable/seek_bar_bg"
                android:thumb="@null"
                android:paddingStart="0dp"
                android:paddingEnd="0dp"
                android:min="0"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_progress_value_sunshade"/>
            <TextView
                android:textColor="@color/neutral_color_text_caption"
                android:id="@+id/tv_sun_shade_location"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_36"
                android:layout_marginTop="21dp"
                android:text="@string/sunshade_position"
                app:layout_constraintLeft_toLeftOf="@+id/seek_bar_sunshade"
                app:layout_constraintRight_toRightOf="@+id/seek_bar_sunshade"
                app:layout_constraintTop_toBottomOf="@+id/seek_bar_sunshade"
                style="@style/HozonTheme.TextAppearance.Caption.Mini.Regular"/>
        </com.hozon.settings.view.SkinConstraintLayout>
        <com.hozon.settings.view.SkinConstraintLayout
            android:layout_width="912dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="-110dp">
            <TextView
                android:textColor="@color/neutral_color_text_primary"
                android:id="@+id/tv_rear_door"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@string/trunk"
                style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
            <com.hozon.settings.view.AnimSwitch
                android:id="@+id/ss_rear_Door"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="18dp"
                android:contentDescription="@string/desc_rear_door"
                app:layout_constraintTop_toBottomOf="@+id/tv_rear_door"
                app:state_switch_icon="@drawable/icon_trunk_off"
                app:state_switch_text="@string/door_open"/>
            <FrameLayout
                android:id="@+id/flLoading"
                android:background="@drawable/brand_color_brand_default"
                android:clickable="true"
                android:layout_width="108dp"
                android:layout_height="108dp"
                app:layout_constraintLeft_toLeftOf="@+id/ss_rear_Door"
                app:layout_constraintTop_toTopOf="@+id/ss_rear_Door">
                <ImageView
                    android:layout_gravity="center"
                    android:id="@+id/ivLoading"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/icon_pause"/>
            </FrameLayout>
            <com.hozon.settings.view.FontTextView
                android:textColor="@color/neutral_color_text_primary"
                android:gravity="center"
                android:id="@+id/iv_trunk"
                android:background="@drawable/icon_trunk_click"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="50dp"
                android:layout_marginStart="10dp"
                app:layout_constraintBottom_toBottomOf="@+id/ss_rear_Door"
                app:layout_constraintStart_toEndOf="@+id/ss_rear_Door"
                app:layout_constraintTop_toTopOf="@+id/ss_rear_Door"
                style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
            <com.hozon.settings.view.SkinConstraintLayout
                android:id="@+id/cl_plg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <ImageView
                    android:id="@+id/iv_rear_door"
                    android:layout_width="361dp"
                    android:layout_height="361dp"
                    android:layout_marginRight="92dp"
                    android:src="@drawable/rear_door"
                    android:translationY="-87dp"
                    app:layout_constraintRight_toRightOf="parent"/>
                <RelativeLayout
                    android:id="@+id/rl_plg_car"
                    android:layout_width="match_parent"
                    android:layout_height="276dp"
                    android:translationX="80dp">
                    <ImageView
                        android:id="@+id/iv_rear_door2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="190dp"
                        android:src="@drawable/rear_door_car_bg"
                        android:layout_alignParentRight="true"/>
                    <TextView
                        android:textColor="@color/neutral_color_text_caption"
                        android:id="@+id/tv_rear_door_dec"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="255dp"
                        android:layout_marginBottom="30dp"
                        android:text="@string/rear_door_height"
                        android:layout_alignParentRight="true"
                        android:layout_alignParentBottom="true"
                        style="@style/HozonTheme.TextAppearance.Caption.Mini.Regular"/>
                </RelativeLayout>
                <com.hozon.settings.view.VerticalSeekBar
                    android:id="@+id/verticalSeekBar"
                    android:background="@color/transparent"
                    android:paddingLeft="0dp"
                    android:paddingRight="0dp"
                    android:duplicateParentState="true"
                    android:layout_width="263dp"
                    android:layout_height="36dp"
                    android:max="100"
                    android:progressDrawable="@drawable/vertical_seek_bar_bg"
                    android:thumb="@null"
                    android:rotation="-90"
                    android:paddingStart="132dp"
                    android:paddingEnd="0dp"
                    android:min="30"
                    app:layout_constraintBottom_toTopOf="@+id/tv_progress_value_rear_door"
                    app:layout_constraintLeft_toLeftOf="@+id/tv_progress_value_rear_door"
                    app:layout_constraintRight_toRightOf="@+id/tv_progress_value_rear_door"/>
                <TextView
                    android:textColor="@color/neutral_color_text_primary"
                    android:gravity="center"
                    android:id="@+id/tv_progress_value_rear_door"
                    android:layout_width="70dp"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="30dp"
                    android:layout_marginBottom="110dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    style="@style/HozonTheme.TextAppearance.Body.Small.Regular.EN"/>
            </com.hozon.settings.view.SkinConstraintLayout>
        </com.hozon.settings.view.SkinConstraintLayout>
    </LinearLayout>
</com.hozon.settings.view.BanAutoSlideScrollView>
