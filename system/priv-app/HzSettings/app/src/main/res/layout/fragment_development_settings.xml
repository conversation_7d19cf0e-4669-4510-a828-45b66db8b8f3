<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_gravity="center"
    android:orientation="vertical"
    android:background="@color/black"
    android:paddingTop="50dp"
    android:paddingBottom="50dp"
    android:layout_width="1380dp"
    android:layout_height="888dp"
    android:layout_marginStart="28dp"
    android:layout_marginEnd="28dp">
    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="80dp">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:textSize="@dimen/caption_20"
                android:textColor="@color/white"
                android:layout_width="400dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="50dp"
                android:text="USB调试"
                android:layout_alignParentLeft="true"/>
            <TextView
                android:textSize="@dimen/caption_20"
                android:textColor="@color/white"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="50dp"
                android:text="连接USB后启用调试模式"
                android:layout_alignParentLeft="true"/>
        </LinearLayout>
        <com.hozonauto.widget.SwitchButton
            android:id="@+id/switch_usb_debug"
            android:visibility="gone"
            android:contentDescription="USB调试(switch)"
            style="@style/SwitchButton"/>
    </LinearLayout>
    <View
        android:background="@color/neutral_white16"
        android:layout_width="match_parent"
        android:layout_height="1dp"/>
    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="80dp">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:textSize="@dimen/caption_20"
                android:textColor="@color/white"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="无线调试"
                android:layout_marginStart="50dp"/>
            <TextView
                android:textSize="@dimen/caption_20"
                android:textColor="@color/white"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="50dp"
                android:text="连接IP后启用调试模式"/>
        </LinearLayout>
        <com.hozonauto.widget.SwitchButton
            android:id="@+id/switch_wiress_adb"
            android:contentDescription="无线调试(switch)"
            style="@style/SwitchButton"/>
        <TextView
            android:textSize="24sp"
            android:textColor="@color/white"
            android:layout_gravity="center"
            android:id="@+id/textview_wiress_adb_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="25dp"/>
    </LinearLayout>
    <View
        android:background="@color/neutral_white16"
        android:layout_width="match_parent"
        android:layout_height="1dp"/>
    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="80dp">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:textSize="@dimen/caption_20"
                android:textColor="@color/white"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="车辆设置中启用“四驱模式”选项"
                android:layout_marginStart="50dp"/>
            <TextView
                android:textSize="18sp"
                android:textColor="@color/white"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="非量产功能，仅供展车试用"
                android:layout_marginStart="50dp"/>
        </LinearLayout>
        <com.hozonauto.widget.SwitchButton
            android:id="@+id/switch_enable_4wd_ui"
            android:contentDescription="非量产功能(switch)"
            style="@style/SwitchButton"/>
    </LinearLayout>
    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="80dp">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:textSize="@dimen/caption_20"
                android:textColor="@color/white"
                android:layout_width="400dp"
                android:layout_height="wrap_content"
                android:text="显示工程版本水印"
                android:layout_marginStart="50dp"/>
            <TextView
                android:textSize="18sp"
                android:textColor="@color/white"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="仅在APA/AVM/SR应用内显示"
                android:layout_marginStart="50dp"/>
        </LinearLayout>
        <com.hozonauto.widget.SwitchButton
            android:id="@+id/engineVersionSB"
            android:contentDescription="APA/AVM(switch)"
            style="@style/SwitchButton"/>
    </LinearLayout>
    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="80dp">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:textSize="@dimen/caption_20"
                android:textColor="@color/white"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="显示工程版本水印(全局显示)"
                android:layout_marginStart="50dp"/>
            <TextView
                android:textSize="18sp"
                android:textColor="@color/white"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="QNX端实现"
                android:layout_marginStart="50dp"/>
        </LinearLayout>
        <com.hozonauto.widget.SwitchButton
            android:id="@+id/qnxEngineVersionSB"
            android:contentDescription="QNX端实现(switch)"
            style="@style/SwitchButton"/>
    </LinearLayout>
    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="80dp">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:textSize="@dimen/caption_20"
                android:textColor="@color/white"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="SR Demo数据开关"
                android:layout_marginStart="50dp"/>
            <TextView
                android:textSize="18sp"
                android:textColor="@color/white"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="开关打开时显示模拟数据"
                android:layout_marginStart="50dp"/>
        </LinearLayout>
        <com.hozonauto.widget.SwitchButton
            android:id="@+id/srDemoSB"
            android:contentDescription="开关打开时显示模拟数据(switch)"
            style="@style/SwitchButton"/>
    </LinearLayout>
    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="80dp">
        <TextView
            android:textSize="@dimen/caption_20"
            android:textColor="@color/white"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="智驾总开关"
            android:layout_marginStart="50dp"
            android:layout_marginEnd="50dp"/>
        <com.hozonauto.widget.SwitchButton
            android:id="@+id/support_master_switch"
            android:contentDescription="智驾总开关(switch)"
            style="@style/SwitchButton"/>
    </LinearLayout>
    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="80dp">
        <TextView
            android:textSize="@dimen/caption_20"
            android:textColor="@color/white"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="展厅模式开关"
            android:layout_marginStart="50dp"
            android:layout_marginEnd="50dp"/>
        <com.hozonauto.widget.SwitchButton
            android:id="@+id/exhibition_mode_switch"
            android:contentDescription="展厅模式开关(switch)"
            style="@style/SwitchButton"/>
    </LinearLayout>
    <View
        android:background="@color/neutral_white16"
        android:layout_width="match_parent"
        android:layout_height="1dp"/>
    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="80dp">
        <TextView
            android:textSize="@dimen/caption_20"
            android:textColor="@color/white"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="合众MCU版本:"
            android:layout_marginStart="50dp"/>
        <TextView
            android:textSize="@dimen/caption_20"
            android:textColor="@color/white"
            android:id="@+id/dev_hozonauto_mcu_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text=""
            android:layout_marginStart="25dp"/>
    </LinearLayout>
    <View
        android:background="@color/neutral_white16"
        android:layout_width="match_parent"
        android:layout_height="1dp"/>
    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="80dp">
        <TextView
            android:textSize="@dimen/caption_20"
            android:textColor="@color/white"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="供应商MCU版本:"
            android:layout_marginStart="50dp"/>
        <TextView
            android:textSize="@dimen/caption_20"
            android:textColor="@color/white"
            android:id="@+id/dev_vehicle_mcu_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text=""
            android:layout_marginStart="25dp"/>
    </LinearLayout>
    <View
        android:background="@color/neutral_white16"
        android:layout_width="match_parent"
        android:layout_height="1dp"/>
    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">
        <TextView
            android:textSize="@dimen/caption_20"
            android:textColor="@color/white"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="安卓版本"
            android:layout_marginStart="50dp"/>
        <TextView
            android:textSize="@dimen/caption_20"
            android:textColor="@color/white"
            android:id="@+id/version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="9.0"
            android:layout_marginStart="25dp"/>
    </LinearLayout>
    <View
        android:background="@color/neutral_white16"
        android:layout_width="match_parent"
        android:layout_height="1dp"/>
    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">
        <TextView
            android:textSize="@dimen/caption_20"
            android:textColor="@color/white"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="蓝牙地址:"
            android:layout_marginStart="50dp"/>
        <TextView
            android:textSize="@dimen/caption_20"
            android:textColor="@color/white"
            android:id="@+id/bluetooth_mac"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="--"
            android:layout_marginStart="25dp"/>
    </LinearLayout>
    <View
        android:background="@color/neutral_white16"
        android:layout_width="match_parent"
        android:layout_height="1dp"/>
    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">
        <TextView
            android:textSize="@dimen/caption_20"
            android:textColor="@color/white"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="WiFi地址:"
            android:layout_marginStart="50dp"/>
        <TextView
            android:textSize="@dimen/caption_20"
            android:textColor="@color/white"
            android:id="@+id/wifi_mac"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="--"
            android:layout_marginStart="25dp"/>
    </LinearLayout>
    <View
        android:background="@color/neutral_white16"
        android:layout_width="match_parent"
        android:layout_height="1dp"/>
    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">
        <TextView
            android:textSize="@dimen/caption_20"
            android:textColor="@color/white"
            android:id="@+id/openMoreAppTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="打开更多应用"
            android:layout_marginStart="50dp"/>
    </LinearLayout>
    <View
        android:background="@color/neutral_white16"
        android:layout_width="match_parent"
        android:layout_height="1dp"/>
    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">
        <TextView
            android:textSize="@dimen/caption_20"
            android:textColor="@color/white"
            android:id="@+id/close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="关闭"
            android:layout_marginStart="50dp"/>
    </LinearLayout>
    <View
        android:background="@color/neutral_white16"
        android:layout_width="match_parent"
        android:layout_height="1dp"/>
    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1">
            <CheckBox
                android:layout_gravity="center"
                android:id="@+id/cbTbox"
                android:background="@color/white"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_marginStart="50dp"/>
            <TextView
                android:textSize="@dimen/caption_20"
                android:textColor="@color/white"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="TBOX"
                android:layout_marginStart="50dp"/>
            <TextView
                android:textSize="@dimen/caption_20"
                android:textColor="@color/white"
                android:id="@+id/tvTboxStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="状态"
                android:layout_marginStart="25dp"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1">
            <CheckBox
                android:id="@+id/cbAdas"
                android:background="@color/white"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_marginStart="50dp"/>
            <TextView
                android:textSize="@dimen/caption_20"
                android:textColor="@color/white"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="ADAS"
                android:layout_marginStart="50dp"/>
            <TextView
                android:textSize="@dimen/caption_20"
                android:textColor="@color/white"
                android:id="@+id/tvAdasStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="状态"
                android:layout_marginStart="25dp"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1">
            <CheckBox
                android:id="@+id/cbAics"
                android:background="@color/white"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_marginStart="50dp"/>
            <TextView
                android:textSize="@dimen/caption_20"
                android:textColor="@color/white"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="AICS"
                android:layout_marginStart="50dp"/>
            <TextView
                android:textSize="@dimen/caption_20"
                android:textColor="@color/white"
                android:id="@+id/tvAicsStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="状态"
                android:layout_marginStart="25dp"/>
        </LinearLayout>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="25dp">
        <Button
            android:textSize="@dimen/caption_20"
            android:textColor="@color/white"
            android:id="@+id/btnSend"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="发送"
            android:layout_marginStart="50dp"/>
        <Button
            android:textSize="@dimen/caption_20"
            android:textColor="@color/white"
            android:id="@+id/btnClear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="清除日志"
            android:layout_marginStart="50dp"/>
        <Button
            android:textSize="@dimen/caption_20"
            android:textColor="@color/white"
            android:id="@+id/btnTieCarActivation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="绑车激活"
            android:layout_marginStart="50dp"/>
    </LinearLayout>
</LinearLayout>
