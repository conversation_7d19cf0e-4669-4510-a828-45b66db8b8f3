<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/clRcwLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.hozon.settings.view.FontScaleTextView
        android:id="@+id/title"
        android:text="@string/adas_rcw_fun_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/FunTitle"/>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clRcw"
        android:visibility="gone"
        android:layout_marginTop="@dimen/px_6"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title"
        style="@style/AidsCard">
        <com.hozonauto.widget.SwitchButton
            android:id="@+id/rcwSwitch"
            android:checked="true"
            android:contentDescription="后向碰撞预警(switch)"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/SwitchButton"/>
        <com.hozon.settings.view.FontScaleTextView
            android:id="@+id/rcwTv"
            android:text="@string/adas_rcw_card_title_1"
            android:layout_marginStart="20dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/rcwSwitch"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/LightFragmentFirstText"/>
        <skin.support.widget.SkinCompatImageView
            android:id="@+id/rcwTipIv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_tip"
            android:contentDescription="@string/aids_009"
            android:layout_marginStart="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/rcwTv"
            app:layout_constraintTop_toTopOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clRcta"
        android:layout_marginTop="@dimen/px_6"
        android:layout_marginStart="27dp"
        app:layout_constraintLeft_toRightOf="@+id/clRcw"
        app:layout_constraintTop_toBottomOf="@+id/title"
        app:layout_goneMarginStart="0dp"
        style="@style/AidsCard">
        <com.hozonauto.widget.SwitchButton
            android:id="@+id/rctaSwitch"
            android:checked="true"
            android:contentDescription="后向横穿预警(switch)"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/SwitchButton"/>
        <com.hozon.settings.view.FontScaleTextView
            android:id="@+id/rctaTv"
            android:text="@string/adas_rcw_card_title_2"
            android:layout_marginStart="20dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/rctaSwitch"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/LightFragmentFirstText"/>
        <skin.support.widget.SkinCompatImageView
            android:id="@+id/rctaTipIv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_tip"
            android:contentDescription="@string/aids_010"
            android:layout_marginStart="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/rctaTv"
            app:layout_constraintTop_toTopOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
