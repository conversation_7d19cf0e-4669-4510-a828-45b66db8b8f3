<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/scrollview"
    android:scrollbars="none"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:overScrollMode="never">
    <LinearLayout
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_24"
        android:clickable="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <com.hozon.settings.view.FlexLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_48"
            app:heightSpace="48dp"
            app:widthSpace="24dp">
            <com.hozonauto.widget.btn.ContainerButton
                android:id="@+id/ll_checkMirror"
                android:layout_width="444dp"
                android:layout_height="72dp"
                android:layout_marginTop="@dimen/dp_48"
                app:hz_btnIcon="@drawable/selector_exterior_mirror_unfold_icon"
                app:hz_btnText="@string/title_fold_open"/>
            <LinearLayout
                android:gravity="center"
                android:orientation="horizontal"
                android:id="@+id/ll_manual_adjustment"
                android:background="@drawable/neutral_color_background_surface_primary"
                android:layout_width="444dp"
                android:layout_height="72dp">
                <ImageView
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:src="@drawable/ic_manual_adjustment"/>
                <TextView
                    android:id="@+id/tv_manual_adjust"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/title_manual_adjustment"
                    android:layout_marginStart="12dp"
                    style="@style/title_text_style"/>
            </LinearLayout>
            <LinearLayout
                android:id="@+id/ll_lock_auto_in"
                style="@style/switch_bg_style">
                <com.hozonauto.widget.SwitchButton
                    android:id="@+id/sw_lock_auto_in"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:contentDescription="@string/desc_lock_auto_in"
                    app:hz_click_interval="500"
                    style="@style/SwitchButton"/>
                <TextView
                    android:id="@+id/tv_door_auto_recover"
                    android:text="@string/title_lock_auto_in"
                    style="@style/title_text_style"/>
            </LinearLayout>
        </com.hozon.settings.view.FlexLayout>
        <TextView
            android:id="@+id/tv_back_car_auto_down_title"
            android:layout_marginTop="@dimen/dp_48"
            android:text="@string/title_back_car_auto_down"
            android:layout_marginStart="0dp"
            style="@style/title_text_style"/>
        <LinearLayout
            android:id="@+id/ll_back_car_auto_down"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp">
            <LinearLayout
                android:gravity="center_vertical"
                android:background="@drawable/neutral_color_background_surface_primary"
                android:layout_width="210dp"
                android:layout_height="66dp">
                <com.hozonauto.widget.CheckBox
                    android:id="@+id/cb_left"
                    android:tag="false"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="24dp"
                    android:contentDescription="倒车自动下翻左侧"/>
                <TextView
                    android:textColor="@color/neutral_color_text_primary"
                    android:id="@+id/tv_left"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/title_left"
                    android:layout_marginStart="12dp"
                    style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
            </LinearLayout>
            <LinearLayout
                android:gravity="center_vertical"
                android:background="@drawable/neutral_color_background_surface_primary"
                android:layout_width="210dp"
                android:layout_height="66dp"
                android:layout_marginStart="24dp">
                <com.hozonauto.widget.CheckBox
                    android:id="@+id/cb_right"
                    android:tag="false"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:contentDescription="倒车自动下翻右侧"
                    android:layout_marginStart="24dp"/>
                <TextView
                    android:textColor="@color/neutral_color_text_primary"
                    android:id="@+id/tv_right"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/title_right"
                    android:layout_marginStart="12dp"
                    style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</ScrollView>
