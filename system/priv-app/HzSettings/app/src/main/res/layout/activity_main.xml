<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="horizontal"
    android:background="@drawable/neutral_color_background_system_secondary"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="vertical"
        android:background="@drawable/bg_f4f5f7_12_left"
        android:layout_width="@dimen/px_102"
        android:layout_height="match_parent">
        <ImageView
            android:id="@+id/backIv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_2"
            android:src="@drawable/app_icon_close_ep32"
            android:contentDescription="@string/close"
            android:layout_marginStart="@dimen/dp_4"/>
        <com.hozonauto.widget.tablayout.HzVerticalTabLayout
            android:id="@+id/leftTabLayout"
            android:background="@color/black"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="30dp"
            app:hz_animStyle="promptly"
            app:hz_position="0"/>
    </LinearLayout>
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:paddingTop="48dp"
        android:paddingBottom="6dp"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:paddingHorizontal="48dp"/>
    <FrameLayout
        android:id="@+id/mainFragmentContainer"
        android:paddingTop="60dp"
        android:paddingBottom="6dp"
        android:visibility="gone"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:paddingHorizontal="48dp"/>
</LinearLayout>
