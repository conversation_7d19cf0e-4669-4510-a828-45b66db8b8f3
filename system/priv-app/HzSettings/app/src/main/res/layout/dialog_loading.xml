<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="center"
    android:background="@color/transparent"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RelativeLayout
        android:id="@+id/root_loading"
        android:background="@drawable/neutral_color_background_system_popup"
        android:visibility="gone"
        android:layout_width="@dimen/px_192"
        android:layout_height="@dimen/px_94"
        android:layout_centerInParent="true">
        <skin.support.widget.SkinCompatProgressBar
            android:gravity="center_horizontal"
            android:id="@+id/version_reset_progress"
            android:layout_width="@dimen/px_24"
            android:layout_height="@dimen/px_24"
            android:layout_marginTop="@dimen/px_24"
            android:indeterminate="false"
            android:indeterminateDrawable="@drawable/anim_dialog_loading"
            android:layout_centerHorizontal="true"/>
        <TextView
            android:textColor="@color/neutral_color_text_primary"
            android:gravity="center"
            android:id="@+id/tipTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_8"
            android:layout_below="@+id/version_reset_progress"
            android:layout_centerHorizontal="true"
            style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
    </RelativeLayout>
</RelativeLayout>
