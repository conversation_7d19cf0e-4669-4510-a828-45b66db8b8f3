<?xml version="1.0" encoding="utf-8"?>
<skin.support.widgetMy.SkinCompatLinearLayoutMy xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:background="@drawable/neutral_color_background_surface_primary"
    android:layout_width="@dimen/px_160"
    android:layout_height="@dimen/px_42"
    android:paddingHorizontal="@dimen/px_4"
    android:paddingVertical="@dimen/px_2">
    <skin.support.widgetMy.SkinCompatTextViewMy
        android:textSize="@dimen/caption_mini"
        android:textColor="@color/selector_widget_text_color"
        android:id="@+id/tv_title_energy_mode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/px_2"
        android:text="能源模式"/>
    <skin.support.widgetMy.SkinCompatLinearLayoutMy
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:id="@+id/rgEnergyMode"
        android:background="@drawable/neutral_color_background_surface_secondary"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/px_24"
        android:layout_marginTop="@dimen/px_1"
        android:paddingStart="@dimen/px_2"
        android:paddingEnd="@dimen/px_2">
        <skin.support.widgetMy.SkinCompatTextViewMy
            android:textSize="@dimen/caption_mini"
            android:textColor="@color/effect_radio_button_text_selector"
            android:gravity="center"
            android:id="@+id/rbEnergyMode1"
            android:background="@drawable/effect_radio_button_selector"
            android:layout_width="@dimen/px_37"
            android:layout_height="@dimen/px_20"
            android:text="极致纯电"/>
        <skin.support.widgetMy.SkinCompatTextViewMy
            android:textSize="@dimen/caption_mini"
            android:textColor="@color/effect_radio_button_text_selector"
            android:gravity="center"
            android:id="@+id/rbEnergyMode2"
            android:background="@drawable/effect_radio_button_selector"
            android:layout_width="@dimen/px_37"
            android:layout_height="@dimen/px_20"
            android:text="纯电优先"/>
        <skin.support.widgetMy.SkinCompatTextViewMy
            android:textSize="@dimen/caption_mini"
            android:textColor="@color/effect_radio_button_text_selector"
            android:gravity="center"
            android:id="@+id/rbEnergyMode3"
            android:background="@drawable/effect_radio_button_selector"
            android:layout_width="@dimen/px_37"
            android:layout_height="@dimen/px_20"
            android:text="增程优先"/>
        <skin.support.widgetMy.SkinCompatTextViewMy
            android:textSize="@dimen/caption_mini"
            android:textColor="@color/effect_radio_button_text_selector"
            android:gravity="center"
            android:id="@+id/rbEnergyMode4"
            android:background="@drawable/effect_radio_button_selector"
            android:layout_width="@dimen/px_37"
            android:layout_height="@dimen/px_20"
            android:text="前哨节能"/>
    </skin.support.widgetMy.SkinCompatLinearLayoutMy>
</skin.support.widgetMy.SkinCompatLinearLayoutMy>
