<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/child_item_rl"
    android:background="@drawable/list_item_color_bg"
    android:layout_width="match_parent"
    android:layout_height="84dp"
    android:layout_marginStart="24dp"
    android:layout_marginEnd="20dp">
    <com.hozon.settings.view.FontTextView
        android:textColor="@color/neutral_color_text_primary"
        android:ellipsize="end"
        android:id="@+id/btName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="264dp"
        android:singleLine="true"
        android:layout_centerVertical="true"
        style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
    <ImageView
        android:id="@+id/imLoading"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_loading"
        android:scaleType="fitXY"
        android:layout_centerVertical="true"
        android:layout_alignParentEnd="true"/>
</RelativeLayout>
