<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <ImageView
            android:id="@+id/ivBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="54dp"
            android:src="@drawable/ic_back"
            android:contentDescription="@string/back"/>
        <TextView
            android:textColor="@color/neutral_color_text_primary"
            android:gravity="center"
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="57dp"
            android:text="用户服务协议"
            android:layout_marginStart="285dp"
            style="@style/HozonTheme.TextAppearance.Headline.Large.Regular"/>
    </LinearLayout>
    <LinearLayout
        android:gravity="center"
        android:orientation="horizontal"
        android:id="@+id/ll_container"
        android:background="@color/transparent"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="32dp"
        android:layout_weight="1">
        <ScrollView
            android:id="@+id/sv_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
    </LinearLayout>
    <com.hozonauto.widget.EmptyAbnormalView
        android:layout_gravity="center_horizontal"
        android:id="@+id/loadErrorContainer"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="183dp"/>
    <com.hozonauto.widget.EmptyAbnormalView
        android:layout_gravity="center_horizontal"
        android:id="@+id/rlRefreshLoading"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="231dp"/>
</LinearLayout>
