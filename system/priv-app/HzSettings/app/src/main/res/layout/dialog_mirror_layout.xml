<?xml version="1.0" encoding="utf-8"?>
<com.hozon.settings.view.SkinConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center"
    android:id="@+id/cl_dialog_layout"
    android:background="@color/dialogOutsideBg"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <com.hozon.settings.view.SkinConstraintLayout
        android:id="@+id/mirror_dialog_layout"
        android:background="@drawable/neutral_color_background_system_popup"
        android:paddingBottom="@dimen/dp_64"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:clickable="true"
        android:layout_width="1024dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <ImageView
            android:id="@+id/dialog_iv_close"
            android:background="@mipmap/ic_close"
            android:visibility="gone"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginTop="24dp"
            android:contentDescription="关闭"
            android:layout_marginStart="24dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <TextView
            android:textSize="24dp"
            android:id="@+id/dialog_tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:text="外后视镜调节"
            android:layout_marginStart="48dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/title_text_style"/>
        <ImageView
            android:layout_width="700dp"
            android:layout_height="400dp"
            android:layout_marginTop="120dp"
            android:src="@mipmap/seat_bg_two"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <TextView
            android:textSize="24dp"
            android:id="@+id/tvMirrorHint"
            android:visibility="visible"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:layout_marginTop="226dp"
            android:text="上下左右键调节外后视镜角度"
            android:layout_marginStart="48dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/title_text_style"/>
        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/rg_save_mirror"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:splitMotionEvents="false"
            android:layout_marginStart="84dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvMirrorHint">
            <com.hozonauto.widget.btn.ContainerButton
                android:id="@+id/rb_mirror1"
                android:layout_width="240dp"
                android:layout_height="wrap_content"
                app:hz_btnStyle="primary"
                app:hz_btnText="主驾坐姿1"/>
            <com.hozonauto.widget.btn.ContainerButton
                android:id="@+id/rb_mirror2"
                android:layout_width="240dp"
                android:layout_height="48dp"
                android:layout_marginTop="24dp"
                app:hz_btnStyle="primary"
                app:hz_btnText="主驾坐姿2"/>
        </LinearLayout>
    </com.hozon.settings.view.SkinConstraintLayout>
</com.hozon.settings.view.SkinConstraintLayout>
