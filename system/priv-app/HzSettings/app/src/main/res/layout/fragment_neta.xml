<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:overScrollMode="never">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <ImageView
            android:id="@+id/carBg"
            android:visibility="visible"
            android:layout_width="520dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:src="@drawable/car_bg"
            android:scaleType="center"
            android:layout_marginStart="365dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <org.libpag.PAGView
            android:id="@+id/agsPagView"
            android:layout_width="700dp"
            android:layout_height="260dp"
            android:layout_marginTop="-20dp"
            android:layout_marginStart="340dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <TextView
            android:textColor="@color/neutral_color_text_caption"
            android:id="@+id/agsValueTv"
            android:visibility="visible"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="80dp"
            android:text="AGS开度 50%"
            android:layout_marginStart="285dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/HozonTheme.TextAppearance.Caption.Mini.Regular"/>
        <LinearLayout
            android:orientation="vertical"
            android:background="@drawable/ic_total_mileage"
            android:paddingTop="12dp"
            android:layout_width="213dp"
            android:layout_height="102dp"
            android:paddingStart="24dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <TextView
                android:textColor="@color/neutral_color_text_caption"
                android:id="@+id/tv_all_mileage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/all_mileage"
                style="@style/NetaFragmentSecondText"/>
            <TextView
                android:textColor="@color/neutral_color_text_primary"
                android:id="@+id/totalMileageValueTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="————km"
                style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
        </LinearLayout>
        <com.hozonauto.widget.tablayout.HzFixedTabLayout
            android:id="@+id/tabModel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_8"
            app:hz_mode="tab"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/carBg"/>
        <TextView
            android:textColor="@color/neutral_color_text_secondary"
            android:id="@+id/timeRecord"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/reset_launch"
            android:layout_marginEnd="24dp"
            app:layout_constraintBottom_toBottomOf="@+id/tabModel"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tabModel"
            style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
        <TextView
            android:textColor="@color/function_color_link_default"
            android:id="@+id/restoreTv"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/restore"
            android:layout_marginEnd="58dp"
            app:layout_constraintBottom_toBottomOf="@+id/tabModel"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tabModel"
            style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
        <FrameLayout
            android:id="@+id/fl_values"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_4"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tabModel">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:id="@+id/sinceLastBootLl"
                android:background="@drawable/neutral_color_background_surface_primary"
                android:padding="@dimen/px_4"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:id="@+id/ll_driving_time"
                    style="@style/NetaFragmentValuesItem">
                    <TextView
                        android:id="@+id/drivingTime"
                        android:text="@string/driving_time"
                        style="@style/NetaFragmentSecondText"/>
                    <TextView
                        android:id="@+id/drivingTimeValue"
                        android:layout_marginTop="10dp"
                        android:text="--h--min"
                        style="@style/NetaFragmentPremDataValue"/>
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/ll_driving_mileage"
                    style="@style/NetaFragmentValuesItem">
                    <TextView
                        android:id="@+id/drivingMileage"
                        android:text="@string/mileage"
                        style="@style/NetaFragmentSecondText"/>
                    <TextView
                        android:id="@+id/drivingMileageValue"
                        android:layout_marginTop="10dp"
                        android:text="----.-km"
                        style="@style/NetaFragmentPremDataValue"/>
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/ll_average_speed"
                    style="@style/NetaFragmentValuesItem">
                    <TextView
                        android:id="@+id/averageSpeed"
                        android:text="@string/average_speed"
                        style="@style/NetaFragmentSecondText"/>
                    <TextView
                        android:id="@+id/averageSpeedValue"
                        android:layout_marginTop="10dp"
                        android:text="----km/h"
                        style="@style/NetaFragmentPremDataValue"/>
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/averageEnergyAECLl"
                    style="@style/NetaFragmentValuesItem">
                    <TextView
                        android:id="@+id/averageEnergyAEC"
                        android:text="@string/average_energy_aec"
                        style="@style/NetaFragmentSecondText"/>
                    <TextView
                        android:id="@+id/averageEnergyAECValue"
                        android:layout_marginTop="10dp"
                        android:text="--.-kWh/100km"
                        style="@style/NetaFragmentPremDataValue"/>
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/averageElectricLl"
                    style="@style/NetaFragmentValuesItem">
                    <TextView
                        android:id="@+id/averageElectric"
                        android:text="@string/average_power"
                        style="@style/NetaFragmentSecondText"/>
                    <TextView
                        android:id="@+id/averageElectricValue"
                        android:layout_marginTop="10dp"
                        android:text="--.-kWh/100km"
                        style="@style/NetaFragmentPremDataValue"/>
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/averageOilLl"
                    style="@style/NetaFragmentValuesItem">
                    <TextView
                        android:id="@+id/averageFuel"
                        android:text="@string/average_fuel"
                        style="@style/NetaFragmentSecondText"/>
                    <TextView
                        android:id="@+id/averageFuelValue"
                        android:layout_marginTop="10dp"
                        android:text="--.-L/100km"
                        style="@style/NetaFragmentPremDataValue"/>
                </LinearLayout>
                <androidx.constraintlayout.helper.widget.Flow
                    android:id="@+id/cfl_sinceLast"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:constraint_referenced_ids="ll_driving_time,ll_driving_mileage,ll_average_speed,averageEnergyAECLl,averageElectricLl,averageOilLl"
                    app:flow_horizontalStyle="spread_inside"
                    app:flow_maxElementsWrap="4"
                    app:flow_verticalGap="@dimen/px_4"
                    app:flow_wrapMode="chain"
                    app:layout_constraintTop_toTopOf="parent"/>
                <androidx.constraintlayout.widget.Group
                    android:id="@+id/g_sinceLast"
                    android:visibility="gone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:constraint_referenced_ids="averageElectricLl,averageOilLl,v_divider_sinceLast"/>
                <View
                    android:id="@+id/v_divider_sinceLast"
                    android:background="@drawable/ic_divider_line"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_0.4"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:id="@+id/sinceLastResetLl"
                android:background="@drawable/neutral_color_background_surface_primary"
                android:padding="@dimen/px_4"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:id="@+id/ll_sinceLast_time"
                    app:layout_constraintHorizontal_chainStyle="spread_inside"
                    style="@style/NetaFragmentValuesItem">
                    <TextView
                        android:id="@+id/resetDrivingTime"
                        android:text="@string/driving_time"
                        style="@style/NetaFragmentSecondText"/>
                    <TextView
                        android:id="@+id/resetDrivingTimeValue"
                        android:layout_marginTop="10dp"
                        android:text="--h--min"
                        style="@style/NetaFragmentPremDataValue"/>
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/ll_sincelast_mileage"
                    style="@style/NetaFragmentValuesItem">
                    <TextView
                        android:id="@+id/resetDrivingMileage"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/mileage"
                        style="@style/NetaFragmentSecondText"/>
                    <TextView
                        android:id="@+id/resetDrivingMileageValue"
                        android:layout_marginTop="10dp"
                        android:text="----.-km"
                        style="@style/NetaFragmentPremDataValue"/>
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/ll_sinceLast_speed"
                    style="@style/NetaFragmentValuesItem">
                    <TextView
                        android:id="@+id/resetAverageSpeed"
                        android:text="@string/average_speed"
                        style="@style/NetaFragmentSecondText"/>
                    <TextView
                        android:id="@+id/resetAverageSpeedValue"
                        android:layout_marginTop="10dp"
                        android:text="----km/h"
                        style="@style/NetaFragmentPremDataValue"/>
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/resetAverageEnergyAECLl"
                    style="@style/NetaFragmentValuesItem">
                    <TextView
                        android:id="@+id/resetAverageEnergyAEC"
                        android:text="@string/average_energy_aec"
                        style="@style/NetaFragmentSecondText"/>
                    <TextView
                        android:id="@+id/resetAverageEnergyAECValue"
                        android:layout_marginTop="10dp"
                        android:text="--.-kWh/100km"
                        style="@style/NetaFragmentPremDataValue"/>
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/resetAverageElectricLl"
                    style="@style/NetaFragmentValuesItem">
                    <TextView
                        android:id="@+id/resetAverageElectric"
                        android:text="@string/average_power"
                        style="@style/NetaFragmentSecondText"/>
                    <TextView
                        android:id="@+id/resetAverageElectricValue"
                        android:layout_marginTop="10dp"
                        android:text="--.-kWh/100km"
                        style="@style/NetaFragmentPremDataValue"/>
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/resetAverageOilLl"
                    style="@style/NetaFragmentValuesItem">
                    <TextView
                        android:id="@+id/resetAverageFuel"
                        android:text="@string/average_fuel"
                        style="@style/NetaFragmentSecondText"/>
                    <TextView
                        android:id="@+id/resetAverageFuelValue"
                        android:layout_marginTop="10dp"
                        android:text="--.-L/100km"
                        style="@style/NetaFragmentPremDataValue"/>
                </LinearLayout>
                <androidx.constraintlayout.helper.widget.Flow
                    android:id="@+id/cfl_reset"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:constraint_referenced_ids="ll_sinceLast_time,ll_sincelast_mileage,ll_sinceLast_speed,resetAverageEnergyAECLl,resetAverageElectricLl,resetAverageOilLl"
                    app:flow_horizontalStyle="spread_inside"
                    app:flow_maxElementsWrap="4"
                    app:flow_verticalGap="@dimen/px_4"
                    app:flow_wrapMode="chain"
                    app:layout_constraintTop_toTopOf="parent"/>
                <androidx.constraintlayout.widget.Group
                    android:id="@+id/g_resetAverage"
                    android:visibility="gone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:constraint_referenced_ids="resetAverageOilLl,resetAverageElectricLl,v_divider_reset"/>
                <View
                    android:id="@+id/v_divider_reset"
                    android:background="@drawable/ic_divider_line"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_0.4"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:id="@+id/outpostEnergyLl"
                android:background="@drawable/neutral_color_background_surface_primary"
                android:paddingLeft="@dimen/px_4"
                android:paddingTop="@dimen/px_6"
                android:paddingRight="@dimen/px_4"
                android:paddingBottom="@dimen/px_6"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="@dimen/px_66">
                <LinearLayout
                    android:gravity="center"
                    android:orientation="vertical"
                    android:id="@+id/ll_outpost1"
                    android:padding="@dimen/px_4"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/px_34"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent">
                    <TextView
                        android:id="@+id/addBatteryLife"
                        android:text="@string/increase_life"
                        style="@style/NetaFragmentSecondText"/>
                    <TextView
                        android:id="@+id/premAddMileageTv"
                        android:layout_marginTop="@dimen/px_4"
                        android:text="————km"
                        style="@style/NetaFragmentPremDataValue"/>
                </LinearLayout>
                <LinearLayout
                    android:gravity="center"
                    android:orientation="vertical"
                    android:id="@+id/ll_outpost2"
                    android:padding="@dimen/px_4"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/px_34"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/ll_outpost1">
                    <TextView
                        android:id="@+id/totalMileageTv"
                        android:text="@string/perMileage"
                        style="@style/NetaFragmentSecondText"/>
                    <TextView
                        android:id="@+id/premMileageTv"
                        android:layout_marginTop="10dp"
                        android:text="————.—km"
                        style="@style/NetaFragmentPremDataValue"/>
                </LinearLayout>
                <LinearLayout
                    android:gravity="center"
                    android:orientation="vertical"
                    android:id="@+id/ll_outpost3"
                    android:padding="@dimen/px_4"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/px_34"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/ll_outpost1">
                    <TextView
                        android:id="@+id/totalSaveOil"
                        android:text="@string/total_fuel_saving"
                        style="@style/NetaFragmentSecondText"/>
                    <TextView
                        android:id="@+id/premFuelTv"
                        android:layout_marginTop="10dp"
                        android:text="——.—L"
                        style="@style/NetaFragmentPremDataValue"/>
                </LinearLayout>
                <View
                    android:id="@+id/v_divider_outpost"
                    android:background="@drawable/ic_divider_line"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_0.4"
                    android:layout_marginTop="@dimen/px_4"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/ll_outpost1"/>
                <TextView
                    android:textColor="@color/neutral_color_text_secondary"
                    android:gravity="center"
                    android:id="@+id/outpostEnergyDes"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/px_4"
                    android:text="@string/outpostEnergyDes"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/v_divider_outpost"
                    style="@style/HozonTheme.TextAppearance.Body.Small.Regular.EN"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </FrameLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/maintainRl"
            android:background="@drawable/neutral_color_background_surface_primary"
            android:layout_width="444dp"
            android:layout_height="126dp"
            android:layout_marginTop="24dp"
            android:contentDescription="@string/expand_maintenance"
            android:paddingStart="24dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/fl_values">
            <TextView
                android:textColor="@color/neutral_color_text_primary"
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/maintain"
                app:layout_constraintBottom_toTopOf="@+id/resRemainTipTv"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed"
                style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
            <TextView
                android:textColor="@color/neutral_color_text_secondary"
                android:id="@+id/resRemainTipTv"
                android:layout_marginTop="@dimen/px_4"
                android:text="@string/maintain_mil"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="@+id/title"
                app:layout_constraintTop_toBottomOf="@+id/title"
                style="@style/NetaFragmentSecondText"/>
            <ImageView
                android:id="@+id/maintainIv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_arrow_right"
                android:layout_marginEnd="25dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/wipersRl"
            android:background="@drawable/neutral_color_background_surface_primary"
            android:layout_width="444dp"
            android:layout_height="126dp"
            android:contentDescription="@string/expand_defend"
            android:paddingStart="24dp"
            android:layout_marginStart="24dp"
            app:layout_constraintStart_toEndOf="@+id/maintainRl"
            app:layout_constraintTop_toTopOf="@+id/maintainRl">
            <TextView
                android:textColor="@color/neutral_color_text_primary"
                android:id="@+id/title3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/protect"
                app:layout_constraintBottom_toTopOf="@+id/wipersDesTv"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed"
                style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
            <TextView
                android:id="@+id/wipersDesTv"
                android:layout_marginTop="@dimen/px_4"
                android:text="@string/wiper_tip_1"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="@+id/title3"
                app:layout_constraintTop_toBottomOf="@+id/title3"
                style="@style/NetaFragmentSecondText"/>
            <ImageView
                android:id="@+id/wipersIv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_arrow_right"
                android:layout_marginEnd="25dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/connectRl"
            android:background="@drawable/neutral_color_background_surface_primary"
            android:layout_width="444dp"
            android:layout_height="126dp"
            android:layout_marginTop="20dp"
            android:paddingStart="24dp"
            app:layout_constraintStart_toStartOf="@+id/wipersRl"
            app:layout_constraintTop_toBottomOf="@+id/wipersRl">
            <TextView
                android:textColor="@color/neutral_color_text_primary"
                android:id="@+id/title2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/connect_us"
                app:layout_constraintBottom_toTopOf="@+id/num"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed"
                style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
            <TextView
                android:id="@+id/num"
                android:layout_marginTop="@dimen/px_4"
                android:text="@string/version_call"
                android:layout_below="@+id/title2"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="@+id/title2"
                app:layout_constraintTop_toBottomOf="@+id/title2"
                style="@style/NetaFragmentSecondENText"/>
            <ImageView
                android:id="@+id/phoneIv"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/ic_outgoing"
                android:layout_marginEnd="30dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <LinearLayout
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:id="@+id/speechLl"
            android:background="@drawable/neutral_color_background_surface_primary"
            android:layout_width="444dp"
            android:layout_height="126dp"
            android:layout_marginTop="20dp"
            android:contentDescription="@string/expand_neta_speak"
            android:paddingStart="24dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/carNumRl">
            <TextView
                android:textColor="@color/neutral_color_text_primary"
                android:id="@+id/tvNetaSay"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/neta_say"
                android:layout_weight="1"
                style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
            <ImageView
                android:id="@+id/connectIv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_arrow_right"
                android:layout_marginEnd="25dp"/>
        </LinearLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/carNumRl"
            android:background="@drawable/neutral_color_background_surface_primary"
            android:layout_width="444dp"
            android:layout_height="126dp"
            android:layout_marginTop="20dp"
            android:paddingStart="24dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/maintainRl">
            <TextView
                android:textColor="@color/neutral_color_text_primary"
                android:id="@+id/inputNumTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/please_input_num"
                android:contentDescription="@string/version_license_plate"
                app:layout_constraintBottom_toTopOf="@+id/vinNum"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed"
                style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
            <TextView
                android:id="@+id/vinNum"
                android:layout_marginTop="@dimen/px_4"
                android:text="@string/vin_asdhsadjkhaj"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="@+id/inputNumTv"
                app:layout_constraintTop_toBottomOf="@+id/inputNumTv"
                style="@style/NetaFragmentSecondENText"/>
            <ImageView
                android:id="@+id/inputNumIv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_edit"
                android:contentDescription="@string/desc_edit_num"
                android:layout_marginEnd="25dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
