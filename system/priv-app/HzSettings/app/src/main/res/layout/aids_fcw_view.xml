<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/clFcwLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.hozon.settings.view.FontScaleTextView
        android:id="@+id/title"
        android:text="@string/adas_fcw_fun_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/FunTitle"/>
    <com.hozon.settings.view.FlexLayout
        android:id="@+id/ll_Layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/px_6"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title"
        app:widthSpace="24dp">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clFcw"
            android:layout_marginTop="@dimen/px_6"
            android:layout_marginEnd="27dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title"
            style="@style/AidsCard">
            <com.hozonauto.widget.SwitchButton
                android:id="@+id/fcwSwitch"
                android:checked="true"
                android:contentDescription="前向碰撞预警(switch)"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                style="@style/SwitchButton"/>
            <com.hozon.settings.view.FontScaleTextView
                android:id="@+id/fcwTv"
                android:text="前向碰撞预警"
                android:layout_marginStart="20dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/fcwSwitch"
                app:layout_constraintTop_toTopOf="parent"
                style="@style/LightFragmentFirstText"/>
            <skin.support.widget.SkinCompatImageView
                android:id="@+id/fcwTipIv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_tip"
                android:contentDescription="@string/aids_005"
                android:layout_marginStart="10dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/fcwTv"
                app:layout_constraintTop_toTopOf="parent"/>
            <skin.support.widget.SkinCompatImageView
                android:id="@+id/fcwArrowIv"
                android:visibility="invisible"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_arrow_right"
                android:contentDescription="@string/aids_013"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clFcta"
            android:layout_marginTop="@dimen/px_6"
            app:layout_constraintLeft_toRightOf="@+id/clFcw"
            app:layout_constraintTop_toBottomOf="@+id/title"
            style="@style/AidsCard">
            <com.hozonauto.widget.SwitchButton
                android:id="@+id/fctaSwitch"
                android:checked="true"
                android:contentDescription="前向横穿预警(switch)"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                style="@style/SwitchButton"/>
            <com.hozon.settings.view.FontScaleTextView
                android:id="@+id/fctaTv"
                android:text="@string/adas_fcw_card_title_2"
                android:layout_marginStart="20dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/fctaSwitch"
                app:layout_constraintTop_toTopOf="parent"
                style="@style/LightFragmentFirstText"/>
            <skin.support.widget.SkinCompatImageView
                android:id="@+id/fctaTipIv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_tip"
                android:contentDescription="@string/aids_006"
                android:layout_marginStart="10dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/fctaTv"
                app:layout_constraintTop_toTopOf="parent"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clAeb"
            android:layout_marginEnd="27dp"
            style="@style/AidsCard">
            <com.hozonauto.widget.SwitchButton
                android:id="@+id/aebSwitch"
                android:checked="true"
                android:contentDescription="自动紧急制动(switch)"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                style="@style/SwitchButton"/>
            <com.hozon.settings.view.FontScaleTextView
                android:id="@+id/aebTv"
                android:text="@string/adas_fcw_card_title_3"
                android:contentDescription="@string/adas_fcw_card_title_3"
                android:layout_marginStart="20dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/aebSwitch"
                app:layout_constraintTop_toTopOf="parent"
                style="@style/LightFragmentFirstText"/>
            <skin.support.widget.SkinCompatImageView
                android:id="@+id/aebTipIv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_tip"
                android:contentDescription="@string/aids_007"
                android:layout_marginStart="10dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/aebTv"
                app:layout_constraintTop_toTopOf="parent"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clDrvOff"
            app:layout_constraintLeft_toRightOf="@+id/clAeb"
            app:layout_constraintTop_toBottomOf="@+id/clFcw"
            style="@style/AidsCard">
            <com.hozonauto.widget.SwitchButton
                android:id="@+id/drivePossSwitch"
                android:checked="true"
                android:contentDescription="起步提醒(switch)"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                style="@style/SwitchButton"/>
            <com.hozon.settings.view.FontScaleTextView
                android:id="@+id/drvOffTv"
                android:text="@string/adas_fcw_card_title_4"
                android:layout_marginStart="20dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/drivePossSwitch"
                app:layout_constraintTop_toTopOf="parent"
                style="@style/LightFragmentFirstText"/>
            <skin.support.widget.SkinCompatImageView
                android:id="@+id/drvOffTipIv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_tip"
                android:contentDescription="@string/aids_008"
                android:layout_marginStart="10dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/drvOffTv"
                app:layout_constraintTop_toTopOf="parent"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.hozon.settings.view.FlexLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
