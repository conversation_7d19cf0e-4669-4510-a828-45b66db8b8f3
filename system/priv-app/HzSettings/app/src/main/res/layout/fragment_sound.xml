<?xml version="1.0" encoding="utf-8"?>
<com.hozon.settings.view.BanAutoSlideScrollView xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/scrollLy"
    android:scrollbars="none"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:overScrollMode="never">
    <LinearLayout
        android:orientation="vertical"
        android:paddingTop="64dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:textColor="@color/neutral_color_text_primary"
            android:id="@+id/titleSound"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="音量"
            style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_4">
            <com.hozonauto.widget.HzWidenSeekBar
                android:id="@+id/soundVolumeMedia"
                android:layout_width="@dimen/px_164"
                android:layout_height="@dimen/px_24"
                android:max="30"
                android:min="0"
                app:hz_hasPop="false"
                app:hz_isEnable="true"
                app:hz_startText="@string/sound_media"/>
            <com.hozon.settings.widge.SkinAppCompatCheckBox
                android:gravity="center"
                android:id="@+id/cbSoundVolumeMedia"
                android:background="@drawable/selector_media_vol"
                android:layout_width="120dp"
                android:layout_height="72dp"
                android:layout_marginLeft="@dimen/px_8"
                android:button="@null"
                android:layout_toRightOf="@+id/soundVolumeMedia"
                style="@style/HozonTheme.TextAppearance.Button.Medium.Regular"/>
        </RelativeLayout>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_8">
            <com.hozonauto.widget.HzWidenSeekBar
                android:id="@+id/soundVolumeNavigation"
                android:layout_width="@dimen/px_164"
                android:layout_height="@dimen/px_24"
                android:max="30"
                android:min="0"
                app:hz_hasPop="false"
                app:hz_isEnable="true"
                app:hz_startText="@string/sound_navigation"/>
            <com.hozon.settings.widge.SkinAppCompatCheckBox
                android:gravity="center"
                android:id="@+id/cbSoundVolumeNavigation"
                android:background="@drawable/selector_media_vol"
                android:layout_width="120dp"
                android:layout_height="72dp"
                android:layout_marginLeft="@dimen/px_8"
                android:button="@null"
                android:layout_toRightOf="@+id/soundVolumeNavigation"
                style="@style/HozonTheme.TextAppearance.Button.Medium.Regular"/>
        </RelativeLayout>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_8">
            <com.hozonauto.widget.HzWidenSeekBar
                android:id="@+id/soundVolumeVoice"
                android:layout_width="@dimen/px_164"
                android:layout_height="@dimen/px_24"
                android:max="30"
                android:min="0"
                app:hz_hasPop="false"
                app:hz_isEnable="true"
                app:hz_startText="@string/sound_voice"/>
            <com.hozon.settings.widge.SkinAppCompatCheckBox
                android:gravity="center"
                android:id="@+id/cbSoundVolumeVoice"
                android:background="@drawable/selector_media_vol"
                android:layout_width="120dp"
                android:layout_height="72dp"
                android:layout_marginLeft="@dimen/px_8"
                android:button="@null"
                android:layout_toRightOf="@+id/soundVolumeVoice"
                style="@style/HozonTheme.TextAppearance.Button.Medium.Regular"/>
        </RelativeLayout>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_8">
            <com.hozonauto.widget.HzWidenSeekBar
                android:id="@+id/soundVolumeCall"
                android:layout_width="@dimen/px_164"
                android:layout_height="@dimen/px_24"
                android:max="30"
                android:min="0"
                app:hz_hasPop="false"
                app:hz_isEnable="true"
                app:hz_minControl="5"
                app:hz_startText="@string/sound_call"/>
            <com.hozon.settings.widge.SkinCallCompatCheckBox
                android:gravity="center"
                android:id="@+id/cbSoundVolumeCall"
                android:background="@drawable/neutral_color_background_surface_primary"
                android:layout_width="120dp"
                android:layout_height="72dp"
                android:layout_marginLeft="@dimen/px_8"
                android:button="@null"
                android:layout_toRightOf="@+id/soundVolumeCall"
                style="@style/HozonTheme.TextAppearance.Button.Medium.Regular"/>
        </RelativeLayout>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_8">
            <com.hozonauto.widget.HzWidenSeekBar
                android:id="@+id/soundVolumeSys"
                android:layout_width="@dimen/px_164"
                android:layout_height="@dimen/px_24"
                android:max="30"
                android:min="0"
                app:hz_hasPop="false"
                app:hz_isEnable="true"
                app:hz_startText="@string/sound_sys"/>
            <com.hozon.settings.widge.SkinAppCompatCheckBox
                android:gravity="center"
                android:id="@+id/cbSoundVolumeSys"
                android:background="@drawable/neutral_color_background_surface_primary"
                android:layout_width="120dp"
                android:layout_height="72dp"
                android:layout_marginLeft="@dimen/px_8"
                android:button="@null"
                android:layout_toRightOf="@+id/soundVolumeSys"
                style="@style/HozonTheme.TextAppearance.Button.Medium.Regular"/>
        </RelativeLayout>
        <RelativeLayout
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_8">
            <com.hozonauto.widget.HzWidenSeekBar
                android:id="@+id/soundVolumeWarn"
                android:layout_width="@dimen/px_160"
                android:layout_height="@dimen/px_24"
                android:max="20"
                android:min="0"
                app:hz_hasPop="false"
                app:hz_isEnable="true"
                app:hz_minControl="8"
                app:hz_startText="@string/sound_warn"/>
            <com.hozon.settings.widge.SkinCallCompatCheckBox
                android:gravity="center"
                android:id="@+id/cbSoundVolumeWarn"
                android:background="@drawable/neutral_color_background_surface_primary"
                android:layout_width="@dimen/px_44"
                android:layout_height="@dimen/px_24"
                android:layout_marginLeft="@dimen/px_8"
                android:button="@null"
                android:layout_toRightOf="@+id/soundVolumeWarn"
                style="@style/HozonTheme.TextAppearance.Button.Medium.Regular"/>
        </RelativeLayout>
        <LinearLayout
            android:orientation="horizontal"
            android:id="@+id/rlSoundModeContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_16"
            android:layout_marginBottom="@dimen/px_8">
            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <TextView
                    android:textColor="@color/neutral_color_text_primary"
                    android:id="@+id/tvoundModeTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/sound_mode_title"
                    style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
                <com.hozon.settings.widge.FlowRadioGroup
                    android:id="@+id/rgSoundMode"
                    android:layout_width="@dimen/px_180"
                    android:layout_height="@dimen/px_56"
                    android:layout_marginTop="@dimen/px_4"
                    android:layout_below="@+id/tvoundModeTitle">
                    <com.hozon.settings.widge.SkinRadioButton
                        android:textColor="@color/neutral_color_text_primary"
                        android:gravity="center"
                        android:id="@+id/rbSoundMode1"
                        android:background="@drawable/neutral_color_background_surface_primary"
                        android:layout_width="@dimen/px_86"
                        android:layout_height="@dimen/px_24"
                        android:button="@null"
                        android:text="@string/sound_mode1"
                        style="@style/HozonTheme.TextAppearance.Button.Medium.Regular"/>
                    <com.hozon.settings.widge.SkinRadioButton
                        android:textColor="@color/neutral_color_text_primary"
                        android:gravity="center"
                        android:id="@+id/rbSoundMode2"
                        android:background="@drawable/neutral_color_background_surface_primary"
                        android:layout_width="@dimen/px_86"
                        android:layout_height="@dimen/px_24"
                        android:layout_marginLeft="@dimen/px_8"
                        android:button="@null"
                        android:text="@string/sound_mode2"
                        style="@style/HozonTheme.TextAppearance.Button.Medium.Regular"/>
                    <com.hozon.settings.widge.SkinRadioButton
                        android:textColor="@color/neutral_color_text_primary"
                        android:gravity="center"
                        android:id="@+id/rbSoundMode3"
                        android:background="@drawable/neutral_color_background_surface_primary"
                        android:layout_width="@dimen/px_86"
                        android:layout_height="@dimen/px_24"
                        android:layout_marginTop="@dimen/px_8"
                        android:button="@null"
                        android:text="@string/sound_mode3"
                        style="@style/HozonTheme.TextAppearance.Button.Medium.Regular"/>
                    <com.hozon.settings.widge.SkinRadioButton
                        android:textColor="@color/neutral_color_text_primary"
                        android:gravity="center"
                        android:id="@+id/rbSoundMode4"
                        android:background="@drawable/neutral_color_background_surface_primary"
                        android:layout_width="@dimen/px_86"
                        android:layout_height="@dimen/px_24"
                        android:layout_marginLeft="@dimen/px_8"
                        android:layout_marginTop="@dimen/px_8"
                        android:button="@null"
                        android:text="@string/sound_mode4"
                        style="@style/HozonTheme.TextAppearance.Button.Medium.Regular"/>
                </com.hozon.settings.widge.FlowRadioGroup>
                <TextView
                    android:textColor="@color/neutral_color_text_secondary"
                    android:id="@+id/tvSoundModeHint"
                    android:layout_width="@dimen/px_180"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/px_4"
                    android:text="@string/sound_mode1_hint"
                    android:layout_below="@+id/rgSoundMode"
                    style="@style/HozonTheme.TextAppearance.Caption.Small.Regular"/>
            </RelativeLayout>
            <ImageView
                android:id="@+id/bestListenAreaIv"
                android:layout_width="@dimen/px_100"
                android:layout_height="@dimen/px_60"
                android:layout_marginLeft="@dimen/px_17"
                android:layout_marginTop="@dimen/px_12"
                android:src="@mipmap/img_voice_driving_enjoyment"/>
        </LinearLayout>
        <LinearLayout
            android:orientation="horizontal"
            android:id="@+id/rlSoundEffectContent"
            android:layout_width="match_parent"
            android:layout_height="@dimen/px_98"
            android:layout_marginTop="@dimen/px_8">
            <RelativeLayout
                android:id="@+id/tvSoundEffectRemind"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:id="@+id/llSoundEffectTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <TextView
                        android:textColor="@color/neutral_color_text_primary"
                        android:id="@+id/tvSoundEffectTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/sound_effect_title"
                        style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
                    <LinearLayout
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:id="@+id/tvAudioRoom"
                        android:background="@drawable/shape_audio_room_entry"
                        android:paddingLeft="@dimen/px_4"
                        android:paddingTop="@dimen/px_2_5"
                        android:paddingRight="@dimen/px_4"
                        android:paddingBottom="@dimen/px_2_5"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/px_8"
                        android:contentDescription="@string/audio_room_str">
                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="@dimen/px_2"
                            android:src="@mipmap/img_audio_room"/>
                        <TextView
                            android:textColor="@color/color_audio_room_entry"
                            android:id="@+id/tvAudioRoomEntry"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/sound_audio_room_entry"
                            style="@style/HozonTheme.TextAppearance.Caption.Mini.Regular"/>
                    </LinearLayout>
                </LinearLayout>
                <com.hozon.settings.widge.FlowRadioGroup
                    android:id="@+id/rgSoundEffect"
                    android:layout_width="@dimen/px_180"
                    android:layout_height="@dimen/px_56"
                    android:layout_marginTop="@dimen/px_4"
                    android:layout_below="@+id/llSoundEffectTitle">
                    <com.hozon.settings.widge.SkinRadioButton
                        android:textColor="@color/neutral_color_text_primary"
                        android:gravity="center"
                        android:id="@+id/rbSoundEffect1"
                        android:background="@drawable/neutral_color_background_surface_primary"
                        android:layout_width="@dimen/px_86"
                        android:layout_height="@dimen/px_24"
                        android:button="@null"
                        android:text="@string/sound_effect_mode1"
                        style="@style/HozonTheme.TextAppearance.Button.Medium.Regular"/>
                    <com.hozon.settings.widge.SkinRadioButton
                        android:textColor="@color/neutral_color_text_primary"
                        android:gravity="center"
                        android:id="@+id/rbSoundEffect2"
                        android:background="@drawable/neutral_color_background_surface_primary"
                        android:layout_width="@dimen/px_86"
                        android:layout_height="@dimen/px_24"
                        android:layout_marginLeft="@dimen/px_8"
                        android:button="@null"
                        android:text="@string/sound_effect_mode2"
                        style="@style/HozonTheme.TextAppearance.Button.Medium.Regular"/>
                    <com.hozon.settings.widge.SkinRadioButton
                        android:textColor="@color/neutral_color_text_primary"
                        android:gravity="center"
                        android:id="@+id/rbSoundEffect3"
                        android:background="@drawable/neutral_color_background_surface_primary"
                        android:layout_width="@dimen/px_86"
                        android:layout_height="@dimen/px_24"
                        android:layout_marginTop="@dimen/px_8"
                        android:button="@null"
                        android:text="@string/sound_effect_mode3"
                        style="@style/HozonTheme.TextAppearance.Button.Medium.Regular"/>
                    <com.hozon.settings.widge.SkinRadioButton
                        android:textColor="@color/neutral_color_text_primary"
                        android:gravity="center"
                        android:id="@+id/rbSoundEffect4"
                        android:background="@drawable/neutral_color_background_surface_primary"
                        android:layout_width="@dimen/px_86"
                        android:layout_height="@dimen/px_24"
                        android:layout_marginLeft="@dimen/px_8"
                        android:layout_marginTop="@dimen/px_8"
                        android:button="@null"
                        android:text="@string/sound_effect_mode4"
                        style="@style/HozonTheme.TextAppearance.Button.Medium.Regular"/>
                </com.hozon.settings.widge.FlowRadioGroup>
                <TextView
                    android:textColor="@color/neutral_color_text_secondary"
                    android:id="@+id/tvSoundEffectSubTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/px_4"
                    android:text="@string/sound_effect_sub_title"
                    android:layout_below="@+id/rgSoundEffect"
                    style="@style/HozonTheme.TextAppearance.Caption.Small.Regular"/>
            </RelativeLayout>
            <RelativeLayout
                android:id="@+id/rl_hand_move"
                android:layout_width="0dp"
                android:layout_height="@dimen/px_98"
                android:layout_marginLeft="@dimen/px_17"
                android:layout_weight="1">
                <com.hozon.settings.view.HzSkinCompatImageView
                    android:id="@+id/ivCarBodyBg"
                    android:layout_width="@dimen/px_92"
                    android:layout_height="@dimen/px_90"
                    android:layout_marginLeft="@dimen/px_4"
                    android:layout_alignParentLeft="true"/>
                <com.hozon.settings.view.HandMoveImageView
                    android:id="@+id/hand_move_iv"
                    android:background="@android:color/transparent"
                    android:layout_width="@dimen/px_100"
                    android:layout_height="match_parent"
                    android:layout_alignParentLeft="true"/>
            </RelativeLayout>
        </LinearLayout>
        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/voiceView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_8">
            <TextView
                android:textColor="@color/neutral_color_text_primary"
                android:id="@+id/tv_sound_voice_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/sound_voice_title"
                style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
            <com.hozonauto.widget.tablayout.HzFixedTabLayout
                android:id="@+id/subSoundVoiceHzFixedTabLayout"
                android:layout_width="@dimen/px_210"
                android:layout_height="@dimen/px_24"
                android:layout_marginTop="@dimen/px_4"
                android:layout_centerVertical="true"
                android:layout_alignParentEnd="true"
                app:hz_animStyle="promptly"
                app:hz_mode="tab"
                app:hz_position="0"
                app:hz_tab_background="@drawable/neutral_color_background_surface_primary"
                app:tabBackground="@null">
                <view
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:contentDescription="@string/sound_voice_close_desc"
                    app:hz_text="@string/close"
                    class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
                <view
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:contentDescription="@string/sound_voice_sports_car_desc"
                    app:hz_text="@string/sound_voice_sports_car"
                    class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
                <view
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:contentDescription="@string/sound_voice_ship_desc"
                    app:hz_text="@string/sound_voice_ship"
                    class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
            </com.hozonauto.widget.tablayout.HzFixedTabLayout>
        </LinearLayout>
        <TextView
            android:textColor="@color/neutral_color_text_primary"
            android:id="@+id/beepTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_16"
            android:text="@string/sound_speed_warning"
            style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
        <com.hozonauto.widget.tablayout.HzFixedTabLayout
            android:id="@+id/pedestrianHzFixedTabLayout"
            android:layout_width="@dimen/px_280"
            android:layout_height="@dimen/px_24"
            android:layout_marginTop="@dimen/px_4"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            app:hz_animStyle="promptly"
            app:hz_mode="tab"
            app:hz_position="0"
            app:hz_tab_background="@drawable/neutral_color_background_surface_primary"
            app:tabBackground="@null">
            <view
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:contentDescription="@string/sound_speed_warning_close_desc"
                app:hz_text="@string/close"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
            <view
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:contentDescription="@string/sound_speed_warning_science_desc"
                app:hz_text="@string/sound_speed_warning_science"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
            <view
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:contentDescription="@string/sound_speed_warning_sport_desc"
                app:hz_text="@string/sound_speed_warning_sport"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
            <view
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:contentDescription="@string/sound_speed_warning_engine_desc"
                app:hz_text="@string/sound_speed_warning_engine"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        </com.hozonauto.widget.tablayout.HzFixedTabLayout>
        <LinearLayout
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_16">
            <TextView
                android:textColor="@color/neutral_color_text_primary"
                android:id="@+id/tv_sound_wheel_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/sound_str03"
                style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
            <ImageView
                android:id="@+id/img_sound_wheel_des"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/px_6"
                android:src="@drawable/ic_tip"
                android:contentDescription="@string/sound_str06"/>
        </LinearLayout>
        <com.hozonauto.widget.tablayout.HzFixedTabLayout
            android:id="@+id/wheelSoundSetCl"
            android:layout_width="@dimen/px_140"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_4"
            android:layout_centerVertical="true"
            app:hz_animStyle="promptly"
            app:hz_mode="tab"
            app:hz_position="0"
            app:hz_tab_background="@drawable/neutral_color_background_surface_primary"
            app:tabBackground="@null">
            <view
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:contentDescription="@string/sound_str05"
                app:hz_text="@string/sound_str01"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
            <view
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:contentDescription="@string/sound_str04"
                app:hz_text="@string/sound_str02"
                class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        </com.hozonauto.widget.tablayout.HzFixedTabLayout>
        <com.hozon.settings.widge.FlowRadioGroup
            android:id="@+id/mFlowlayout"
            android:layout_width="@dimen/px_312"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/px_6">
            <LinearLayout
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:background="@drawable/neutral_color_background_surface_primary"
                android:paddingLeft="@dimen/px_8"
                android:layout_width="@dimen/px_148"
                android:layout_height="@dimen/px_24"
                android:layout_marginTop="@dimen/px_16"
                android:layout_marginRight="@dimen/px_8">
                <com.hozonauto.widget.SwitchButton
                    android:id="@+id/soundProfileKeyToneSwitch"
                    android:checked="true"
                    android:contentDescription="@string/sound_key_code_switch"
                    android:layout_marginEnd="0dp"
                    style="@style/SwitchButton"/>
                <TextView
                    android:textColor="@color/neutral_color_text_primary"
                    android:id="@+id/tv_sound_key"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/px_7"
                    android:text="@string/sound_key_code"
                    style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
            </LinearLayout>
            <LinearLayout
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:id="@+id/tvSeatAlarm"
                android:background="@drawable/neutral_color_background_surface_primary"
                android:paddingLeft="@dimen/px_8"
                android:layout_width="@dimen/px_148"
                android:layout_height="@dimen/px_24"
                android:layout_marginTop="@dimen/px_16"
                android:layout_marginRight="@dimen/px_8">
                <com.hozonauto.widget.SwitchButton
                    android:id="@+id/swSeatAlarm"
                    android:checked="true"
                    android:contentDescription="@string/sound_seat_belt_switch"
                    android:layout_marginEnd="0dp"
                    style="@style/SwitchButton"/>
                <TextView
                    android:textColor="@color/neutral_color_text_primary"
                    android:id="@+id/tv_sound_rear_belt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/px_7"
                    android:text="@string/sound_seat_belt"
                    style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
            </LinearLayout>
            <LinearLayout
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:background="@drawable/neutral_color_background_surface_primary"
                android:paddingLeft="@dimen/px_8"
                android:layout_width="@dimen/px_148"
                android:layout_height="@dimen/px_24"
                android:layout_marginTop="@dimen/px_16"
                android:layout_marginRight="@dimen/px_8">
                <com.hozonauto.widget.SwitchButton
                    android:id="@+id/swDuckNavi"
                    android:checked="true"
                    android:contentDescription="@string/sound_str058"
                    android:layout_marginEnd="0dp"
                    style="@style/SwitchButton"/>
                <TextView
                    android:textColor="@color/neutral_color_text_primary"
                    android:id="@+id/tv_sound_nav"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/px_7"
                    android:text="@string/sound_str057"
                    style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
            </LinearLayout>
            <LinearLayout
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:background="@drawable/neutral_color_background_surface_primary"
                android:paddingLeft="@dimen/px_8"
                android:layout_width="@dimen/px_148"
                android:layout_height="@dimen/px_24"
                android:layout_marginTop="@dimen/px_16"
                android:layout_marginRight="@dimen/px_8">
                <com.hozonauto.widget.SwitchButton
                    android:id="@+id/swDuckVoice"
                    android:checked="true"
                    android:contentDescription="@string/sound_str060"
                    android:layout_marginEnd="0dp"
                    style="@style/SwitchButton"/>
                <TextView
                    android:textColor="@color/neutral_color_text_primary"
                    android:id="@+id/tv_sound_voice_media"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/px_7"
                    android:text="@string/sound_str059"
                    style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
            </LinearLayout>
            <LinearLayout
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:background="@drawable/neutral_color_background_surface_primary"
                android:paddingLeft="@dimen/px_8"
                android:layout_width="@dimen/px_148"
                android:layout_height="@dimen/px_24"
                android:layout_marginTop="@dimen/px_16">
                <com.hozonauto.widget.SwitchButton
                    android:id="@+id/swDuckGear"
                    android:checked="true"
                    android:contentDescription="@string/sound_str062"
                    android:layout_marginEnd="0dp"
                    style="@style/SwitchButton"/>
                <TextView
                    android:textColor="@color/neutral_color_text_primary"
                    android:id="@+id/tv_sound_gear_media"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/px_7"
                    android:text="@string/sound_str061"
                    style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
            </LinearLayout>
        </com.hozon.settings.widge.FlowRadioGroup>
        <LinearLayout
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:background="@drawable/sound_list_item_color_bg_comfort_night"
            android:paddingLeft="@dimen/px_8"
            android:visibility="gone"
            android:layout_width="@dimen/px_148"
            android:layout_height="@dimen/px_30"
            android:layout_marginTop="@dimen/px_16">
            <com.hozonauto.widget.SwitchButton
                android:id="@+id/swRadarAlarm"
                android:checked="true"
                android:contentDescription="雷达报警音(switch)"
                android:layout_marginEnd="0dp"
                style="@style/SwitchButton"/>
            <TextView
                android:id="@+id/tv_sound_radar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/px_7"
                android:text="雷达报警音"
                style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
        </LinearLayout>
    </LinearLayout>
</com.hozon.settings.view.BanAutoSlideScrollView>
