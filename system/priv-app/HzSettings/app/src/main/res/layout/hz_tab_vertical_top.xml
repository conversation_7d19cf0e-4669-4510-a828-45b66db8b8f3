<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="top"
    android:layout_width="match_parent"
    android:layout_height="@dimen/px_28">
    <ImageView
        android:id="@+id/tabCloseIcon"
        android:layout_width="@dimen/px_16"
        android:layout_height="@dimen/px_16"
        android:src="@drawable/app_icon_back_ep32"
        android:contentDescription="返回"
        android:layout_marginStart="@dimen/px_8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tabCloseDec"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textColor="@color/neutral_color_text_primary"
        android:id="@+id/tabCloseDec"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="返回"
        android:layout_marginStart="@dimen/px_8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tabCloseIcon"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/HozonTheme.TextAppearance.Headline.Small.Regular"/>
</androidx.constraintlayout.widget.ConstraintLayout>
