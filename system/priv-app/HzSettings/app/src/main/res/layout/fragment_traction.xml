<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/neutral_color_background_system_mask"
    android:padding="20dp"
    android:layout_width="1391dp"
    android:layout_height="936dp">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:background="@drawable/neutral_color_background_system_popup"
        android:layout_width="576dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <TextView
            android:id="@+id/titleTv"
            android:layout_marginTop="24dp"
            android:text="开启拖车模式"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/TractionDialogTitleStyle"/>
        <TextView
            android:id="@+id/secTitleTv"
            android:layout_marginTop="24dp"
            android:text="当以下条件满足时可开启拖车模式："
            android:layout_marginStart="48dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/titleTv"
            style="@style/TractionDialogTitleStyle"/>
        <ImageView
            android:id="@+id/mergeIv"
            android:visibility="gone"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_marginTop="60dp"
            android:src="@mipmap/ic_arrow_yellow"
            android:layout_marginStart="60dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/secTitleTv"/>
        <TextView
            android:id="@+id/mergeTv"
            android:layout_marginTop="36dp"
            android:text="(1) 充电枪已拔出"
            android:layout_marginStart="65dp"
            app:layout_constraintLeft_toRightOf="@+id/mergeIv"
            app:layout_constraintTop_toBottomOf="@+id/secTitleTv"
            style="@style/TractionDialogTextStyle"/>
        <ImageView
            android:id="@+id/brakeIv"
            android:visibility="gone"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_marginTop="60dp"
            android:src="@mipmap/ic_arrow_yellow"
            android:layout_marginStart="60dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/mergeTv"/>
        <TextView
            android:id="@+id/brakeTv"
            android:layout_marginTop="36dp"
            android:text="(2) 刹车踏板已踩下"
            android:layout_marginStart="65dp"
            app:layout_constraintLeft_toRightOf="@+id/brakeIv"
            app:layout_constraintTop_toBottomOf="@+id/mergeTv"
            style="@style/TractionDialogTextStyle"/>
        <ImageView
            android:id="@+id/stateIv"
            android:visibility="gone"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_marginTop="60dp"
            android:src="@mipmap/ic_arrow_yellow"
            android:layout_marginStart="60dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/brakeTv"/>
        <TextView
            android:id="@+id/stateTv"
            android:layout_marginTop="36dp"
            android:text="(3) 挡位已切换到P挡"
            android:layout_marginStart="65dp"
            app:layout_constraintLeft_toRightOf="@+id/stateIv"
            app:layout_constraintTop_toBottomOf="@+id/brakeTv"
            style="@style/TractionDialogTextStyle"/>
        <TextView
            android:textColor="@color/function_color_error_text_default"
            android:id="@+id/failTv"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="36dp"
            android:layout_marginBottom="42dp"
            android:text="未知原因，牵引失败"
            app:layout_constraintBottom_toTopOf="@+id/cancelBt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/stateTv"
            style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
        <com.hozonauto.widget.btn.ContainerButton
            android:textSize="24sp"
            android:textColor="@drawable/selector_traction_btn_text"
            android:id="@+id/openBt"
            android:background="@drawable/selector_traction_open"
            android:visibility="visible"
            android:layout_width="228dp"
            android:layout_height="72dp"
            android:layout_marginTop="36dp"
            android:layout_marginBottom="24dp"
            android:layout_marginStart="48dp"
            app:hz_btnStyle="primary"
            app:hz_btnText="开启"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/stateTv"/>
        <com.hozonauto.widget.btn.ContainerButton
            android:textSize="24sp"
            android:id="@+id/closeBt"
            android:visibility="visible"
            android:layout_width="228dp"
            android:layout_height="72dp"
            android:layout_marginBottom="24dp"
            android:layout_marginStart="24dp"
            app:hz_btnStyle="secondary"
            app:hz_btnText="取消"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/openBt"/>
        <com.hozonauto.widget.btn.ContainerButton
            android:textSize="24sp"
            android:textColor="@drawable/selector_traction_btn_text"
            android:id="@+id/cancelBt"
            android:background="@drawable/selector_traction_open"
            android:visibility="gone"
            android:layout_width="480dp"
            android:layout_height="72dp"
            android:layout_marginBottom="24dp"
            app:hz_btnStyle="primary"
            app:hz_btnText="取消"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
