<?xml version="1.0" encoding="utf-8"?>
<com.hozon.settings.view.BanAutoSlideScrollView xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/scrollview"
    android:scrollbars="none"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:overScrollMode="never">
    <LinearLayout
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_24"
        android:clickable="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <com.hozonauto.widget.list.MultiContainerListItemView
            android:id="@+id/ml_drive_mode"
            android:layout_width="780dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="36dp"
            android:layout_marginStart="-24dp"
            app:hz_title="驾驶模式"
            app:hz_titleSingeLine="false"/>
        <com.hozonauto.widget.list.MultiContainerListItemView
            android:id="@+id/ml_stop_mode"
            android:layout_width="630dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="-24dp"
            app:hz_icon="@drawable/ic_im_60"
            app:hz_title="停车模式"
            app:hz_titleCDesc="停车模式提示"
            app:hz_titleSingeLine="false"/>
        <com.hozonauto.widget.list.MultiContainerListItemView
            android:id="@+id/ml_energy_recovery"
            android:layout_width="600dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="-24dp"
            app:hz_icon="@drawable/ic_im_60"
            app:hz_title="能量回收"/>
        <com.hozonauto.widget.list.MultiContainerListItemView
            android:id="@+id/ml_energy_mode"
            android:layout_width="800dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="-24dp"
            app:hz_icon="@drawable/ic_im_60"
            app:hz_title="能源模式"
            app:hz_titleCDesc="能源模式提示"/>
        <LinearLayout
            android:orientation="horizontal"
            android:id="@+id/ll_extend_range_priority"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="36dp">
            <com.hozonauto.widget.list.MultiContainerListItemView
                android:id="@+id/ml_fuel_recharge"
                android:background="@drawable/neutral_color_background_surface_primary"
                android:layout_width="438dp"
                android:layout_height="wrap_content"
                android:layout_marginRight="24dp"
                app:hz_icon="@drawable/ic_im_60"
                app:hz_title="@string/title_fuel_recharge"
                app:hz_titleCDesc="驻车补电提示"/>
            <com.hozonauto.widget.list.MultiContainerListItemView
                android:id="@+id/ml_vehicle_electric_protection"
                android:background="@drawable/neutral_color_background_surface_primary"
                android:layout_width="438dp"
                android:layout_height="wrap_content"
                android:layout_marginRight="24dp"
                app:hz_icon="@drawable/ic_im_60"
                app:hz_title="@string/title_vehicle_electric_protection"
                app:hz_titleCDesc="行车保电提示"/>
        </LinearLayout>
        <com.hozonauto.widget.list.MultiContainerListItemView
            android:id="@+id/ml_forced_charging_power"
            android:layout_width="800dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginStart="-24dp"
            app:hz_title="@string/title_fuel_recharge_gear"
            app:hz_titleCDesc="驻车补电挡位提示"/>
        <com.hozonauto.widget.list.MultiContainerListItemView
            android:id="@+id/ml_eps"
            android:layout_width="610dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="-24dp"
            app:hz_icon="@drawable/ic_im_60"
            app:hz_title="转向助力"
            app:hz_titleCDesc="转向助力提示"/>
        <LinearLayout
            android:id="@+id/ll_wrap_flex"
            android:layout_width="match_parent"
            android:layout_height="480dp">
            <com.hozon.settings.view.FlexLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="24dp"
                app:heightSpace="48dp"
                app:widthSpace="24dp">
                <com.hozonauto.widget.list.MultiContainerListItemView
                    android:id="@+id/ml_AVH"
                    android:background="@drawable/neutral_color_background_surface_primary"
                    android:layout_width="438dp"
                    android:layout_height="@dimen/px_24"
                    android:layout_marginRight="24dp"
                    app:hz_icon="@drawable/ic_im_60"
                    app:hz_title="自动驻车"
                    app:hz_titleCDesc="自动驻车提示"/>
                <com.hozonauto.widget.list.MultiContainerListItemView
                    android:id="@+id/ml_epb"
                    android:background="@drawable/neutral_color_background_surface_primary"
                    android:layout_width="438dp"
                    android:layout_height="@dimen/px_24"
                    app:hz_icon="@drawable/ic_im_60"
                    app:hz_title="电子驻车制动"
                    app:hz_titleCDesc="电子驻车制动提示"/>
                <com.hozonauto.widget.list.MultiContainerListItemView
                    android:id="@+id/ml_stop_car"
                    android:background="@drawable/neutral_color_background_surface_primary"
                    android:layout_width="438dp"
                    android:layout_height="@dimen/px_24"
                    android:layout_marginRight="24dp"
                    app:hz_icon="@drawable/ic_im_60"
                    app:hz_title="舒适停车"
                    app:hz_titleCDesc="舒适停车提示"/>
                <com.hozonauto.widget.list.MultiContainerListItemView
                    android:id="@+id/ml_hdc"
                    android:background="@drawable/neutral_color_background_surface_primary"
                    android:layout_width="438dp"
                    android:layout_height="@dimen/px_24"
                    app:hz_icon="@drawable/ic_im_60"
                    app:hz_title="陡坡缓降"
                    app:hz_titleCDesc="陡坡缓降提示"/>
                <com.hozonauto.widget.list.MultiContainerListItemView
                    android:id="@+id/ml_esc"
                    android:background="@drawable/neutral_color_background_surface_primary"
                    android:layout_width="438dp"
                    android:layout_height="@dimen/px_24"
                    android:layout_marginTop="48dp"
                    app:hz_icon="@drawable/ic_im_60"
                    app:hz_title="电子稳定系统"
                    app:hz_titleCDesc="电子稳定系统提示"/>
                <com.hozonauto.widget.list.MultiContainerListItemView
                    android:id="@+id/ml_fatigue_driving"
                    android:background="@drawable/neutral_color_background_surface_primary"
                    android:layout_width="438dp"
                    android:layout_height="@dimen/px_24"
                    android:layout_marginTop="48dp"
                    app:hz_icon="@drawable/ic_im_60"
                    app:hz_title="疲劳驾驶提醒"
                    app:hz_titleCDesc="疲劳驾驶提醒提示"/>
                <com.hozonauto.widget.list.MultiContainerListItemView
                    android:id="@+id/ml_distracted_driving"
                    android:background="@drawable/neutral_color_background_surface_primary"
                    android:layout_width="438dp"
                    android:layout_height="@dimen/px_24"
                    android:layout_marginLeft="24dp"
                    android:layout_marginTop="48dp"
                    app:hz_icon="@drawable/ic_im_60"
                    app:hz_title="分心驾驶提醒"
                    app:hz_titleCDesc="分心驾驶提示"/>
                <com.hozonauto.widget.list.MultiContainerListItemView
                    android:id="@+id/ml_play_hint"
                    android:background="@drawable/neutral_color_background_surface_primary"
                    android:layout_width="438dp"
                    android:layout_height="@dimen/px_24"
                    android:layout_marginTop="48dp"
                    app:hz_icon="@drawable/ic_im_60"
                    app:hz_title="行车播放视频提醒"
                    app:hz_titleCDesc="行车播放视频提醒提示"/>
            </com.hozon.settings.view.FlexLayout>
        </LinearLayout>
    </LinearLayout>
</com.hozon.settings.view.BanAutoSlideScrollView>
