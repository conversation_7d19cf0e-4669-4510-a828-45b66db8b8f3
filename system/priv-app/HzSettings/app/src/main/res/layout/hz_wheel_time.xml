<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/wheel"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <View
        android:id="@+id/vDivider"
        android:background="@drawable/wheel_divider_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.hozonauto.widget.wheel.HzLoopView
        android:id="@+id/loopYear"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/loopMonth"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.hozonauto.widget.wheel.HzLoopView
        android:id="@+id/loopMonth"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/loopDay"
        app:layout_constraintStart_toEndOf="@+id/loopYear"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.hozonauto.widget.wheel.HzLoopView
        android:id="@+id/loopDay"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/loopHour"
        app:layout_constraintStart_toEndOf="@+id/loopMonth"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.hozonauto.widget.wheel.HzLoopView
        android:id="@+id/loopHour"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/loopMinute"
        app:layout_constraintStart_toEndOf="@+id/loopDay"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.hozonauto.widget.wheel.HzLoopView
        android:id="@+id/loopMinute"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/loopHour"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
