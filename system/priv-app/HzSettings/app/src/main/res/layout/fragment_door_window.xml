<?xml version="1.0" encoding="utf-8"?>
<com.hozon.settings.view.BanAutoSlideScrollView xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/scrollview"
    android:scrollbars="none"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:overScrollMode="never">
    <LinearLayout
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_24"
        android:clickable="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/ll_one_button_ventilation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:textSize="24sp"
                android:textColor="#7c7e89"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="车窗"/>
            <com.hozonauto.widget.list.MultiContainerListItemView
                android:gravity="center_vertical"
                android:id="@+id/ss_one_button_ventilation"
                android:background="@drawable/sound_list_item_color_bg_comfort_night"
                android:layout_width="450dp"
                android:layout_height="120dp"
                android:layout_marginTop="24dp"
                app:hz_title="一键通风"/>
        </LinearLayout>
        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/ll_lockup"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <com.hozonauto.widget.list.MultiContainerListItemView
                android:id="@+id/ml_lockup"
                android:layout_width="630dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="-24dp"
                android:layout_marginTop="28dp"
                app:hz_title="锁车升窗"
                app:hz_titleSingeLine="false"/>
            <TextView
                android:textSize="18sp"
                android:textColor="#7c7e89"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="28dp"
                android:text="设置锁车时车窗的位置"/>
        </LinearLayout>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <ImageView
                android:id="@+id/iv_rear_door2"
                android:layout_width="600dp"
                android:layout_height="342dp"
                android:layout_marginTop="80dp"
                android:layout_marginRight="100dp"
                android:src="@drawable/rear_door_car_bg"
                android:layout_alignParentRight="true"/>
            <ImageView
                android:layout_gravity="end"
                android:id="@+id/iv_rear_door"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="31dp"
                android:layout_marginRight="159dp"
                android:src="@drawable/rear_door"
                android:layout_alignParentRight="true"/>
            <LinearLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:orientation="vertical"
                    android:id="@+id/ll_rear_Door"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <TextView
                        android:textSize="24sp"
                        android:textColor="#7c7e89"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="24dp"
                        android:text="后备箱"/>
                    <com.hozon.settings.view.StateSwitch
                        android:id="@+id/ss_rear_Door"
                        android:layout_width="150dp"
                        android:layout_height="150dp"
                        android:layout_marginTop="48dp"
                        android:contentDescription="后备箱(switch)"
                        app:state_switch_icon="@drawable/icon_trunk_off"
                        app:state_switch_text="后备箱"/>
                    <com.hozonauto.widget.HzWidenSeekBar
                        android:id="@+id/sb_rear_door_height_set"
                        android:layout_width="@dimen/px_205"
                        android:layout_height="@dimen/px_24"
                        android:layout_marginLeft="-36dp"
                        android:layout_marginTop="68dp"
                        android:max="100"
                        android:progress="100"
                        android:min="30"
                        app:hz_hasPop="false"
                        app:hz_isEnable="true"
                        app:hz_startText="开启高度预设"/>
                </LinearLayout>
                <RelativeLayout
                    android:id="@+id/rl_sunroof_blind"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="-24dp"
                    android:layout_marginTop="48dp">
                    <TextView
                        android:textSize="24sp"
                        android:textColor="#7c7e89"
                        android:id="@+id/ml_lock_unlock_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="24dp"
                        android:text="天窗遮阳帘"
                        android:layout_alignTop="@+id/ml_sky_window"/>
                    <com.hozonauto.widget.list.MultiContainerListItemView
                        android:id="@+id/ml_sky_window"
                        android:layout_width="990dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="24dp"
                        app:hz_textColor="#7c7e89"
                        app:hz_titleSingeLine="false"/>
                    <com.hozonauto.widget.list.MultiContainerListItemView
                        android:id="@+id/ml_sunshade"
                        android:layout_width="640dp"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/ml_sky_window"
                        app:hz_titleSingeLine="false"/>
                </RelativeLayout>
            </LinearLayout>
        </RelativeLayout>
    </LinearLayout>
</com.hozon.settings.view.BanAutoSlideScrollView>
