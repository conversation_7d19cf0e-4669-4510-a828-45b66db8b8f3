<?xml version="1.0" encoding="utf-8"?>
<skin.support.widgetMy.SkinCompatLinearLayoutMy xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:background="@drawable/neutral_color_background_surface_primary_lassie"
    android:layout_width="@dimen/px_160"
    android:layout_height="@dimen/px_42">
    <skin.support.widgetMy.SkinCompatLinearLayoutMy
        android:orientation="vertical"
        android:id="@+id/ll_sky_window_open"
        android:background="@drawable/effect_radio_button_press_lassie"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1">
        <skin.support.widgetMy.SkinCompatImageViewMy
            android:layout_gravity="center_horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="18dp"
            android:src="@drawable/selector_sky_window_close_icon"/>
        <skin.support.widgetMy.SkinCompatTextViewMy
            android:textSize="@dimen/caption_mini"
            android:textColor="@color/selector_widget_text_color_press"
            android:layout_gravity="center_horizontal"
            android:id="@+id/title_sky_window"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="9dp"
            android:text="@string/title_widget_sky_window_close"/>
    </skin.support.widgetMy.SkinCompatLinearLayoutMy>
    <View
        android:layout_width="30dp"
        android:layout_height="10dp"/>
    <skin.support.widgetMy.SkinCompatLinearLayoutMy
        android:orientation="vertical"
        android:id="@+id/ll_sky_window_ventilate"
        android:background="@drawable/effect_radio_button_press_lassie"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1">
        <skin.support.widgetMy.SkinCompatImageViewMy
            android:layout_gravity="center_horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="18dp"
            android:src="@drawable/selector_sky_window_ventilate_icon"/>
        <skin.support.widgetMy.SkinCompatTextViewMy
            android:textSize="@dimen/caption_mini"
            android:textColor="@color/selector_widget_text_color_press"
            android:layout_gravity="center_horizontal"
            android:id="@+id/title_ventilate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="9dp"
            android:text="@string/ventilate"/>
    </skin.support.widgetMy.SkinCompatLinearLayoutMy>
    <View
        android:layout_width="30dp"
        android:layout_height="10dp"/>
    <skin.support.widgetMy.SkinCompatLinearLayoutMy
        android:orientation="vertical"
        android:id="@+id/ll_sky_window_close"
        android:background="@drawable/effect_radio_button_press_lassie"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1">
        <skin.support.widgetMy.SkinCompatImageViewMy
            android:layout_gravity="center_horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="18dp"
            android:src="@drawable/selector_sky_window_open_icon"/>
        <skin.support.widgetMy.SkinCompatTextViewMy
            android:textSize="@dimen/caption_mini"
            android:textColor="@color/selector_widget_text_color_press"
            android:layout_gravity="center_horizontal"
            android:id="@+id/title_all_open"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="9dp"
            android:text="@string/all_open"/>
    </skin.support.widgetMy.SkinCompatLinearLayoutMy>
</skin.support.widgetMy.SkinCompatLinearLayoutMy>
