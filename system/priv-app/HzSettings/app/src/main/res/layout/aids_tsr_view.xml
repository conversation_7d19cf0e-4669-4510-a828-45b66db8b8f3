<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/clTsrLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.hozon.settings.view.FontScaleTextView
        android:id="@+id/title"
        android:text="@string/adas_tsr_fun_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/FunTitle"/>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clTsr"
        android:layout_marginTop="@dimen/px_6"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title"
        style="@style/AidsCard">
        <com.hozonauto.widget.SwitchButton
            android:id="@+id/tsrSwitch"
            android:checked="true"
            android:contentDescription="@string/adas_tsr_card_title_switch"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/SwitchButton"/>
        <com.hozon.settings.view.FontScaleTextView
            android:id="@+id/tsrTv"
            android:text="@string/adas_tsr_card_title_1"
            android:layout_marginStart="20dp"
            app:hz_contentDescription="@string/adas_tsr_card_title_1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/tsrSwitch"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/LightFragmentFirstText"/>
        <skin.support.widget.SkinCompatImageView
            android:id="@+id/tsrTipIv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_tip"
            android:contentDescription="@string/aids_011"
            android:layout_marginStart="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/tsrTv"
            app:layout_constraintTop_toTopOf="parent"/>
        <skin.support.widget.SkinCompatImageView
            android:id="@+id/tsrArrowIv"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_arrow_right"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
