<?xml version="1.0" encoding="utf-8"?>
<com.hozon.settings.view.SkinConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center"
    android:id="@+id/dialog_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <View
        android:background="@drawable/bg_dialog_shade_rectangle_shape_no_corners"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:alpha="0.9"/>
    <com.hozon.settings.view.SkinConstraintLayout
        android:background="@drawable/bg_dialog_rectangle_shape"
        android:clickable="true"
        android:layout_width="960dp"
        android:layout_height="408dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="54dp"
            android:layout_height="54dp"
            android:layout_marginTop="15dp"
            android:src="@drawable/icon_close"
            android:scaleType="fitXY"
            android:contentDescription="@string/close"
            android:layout_marginStart="15dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <TextView
            android:id="@+id/dialog_tv_title"
            android:text="@string/title_fast_key"
            app:layout_constraintBottom_toBottomOf="@+id/iv_close"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/iv_close"
            style="@style/title_text_style"/>
        <ImageView
            android:id="@+id/iv_img"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="96dp"
            android:layout_marginBottom="24dp"
            android:src="@mipmap/img_fast_key"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
    </com.hozon.settings.view.SkinConstraintLayout>
</com.hozon.settings.view.SkinConstraintLayout>
