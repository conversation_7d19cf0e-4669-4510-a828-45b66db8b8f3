<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginBottom="20dp">
    <TextView
        android:gravity="center_vertical"
        android:id="@+id/lightControlTv"
        android:layout_marginTop="24dp"
        android:text="@string/light_control"
        android:drawablePadding="10dp"
        android:drawableEnd="@drawable/ic_light_control"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/LightFragmentFirstText"/>
    <com.hozonauto.widget.tablayout.HzFixedTabLayout
        android:id="@+id/rgOutLamp"
        android:layout_width="@dimen/px_280"
        android:layout_height="@dimen/px_24"
        android:layout_marginTop="12dp"
        app:hz_mode="tab"
        app:hz_position="0"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lightControlTv"
        app:tabBackground="@null">
        <view
            android:id="@+id/TabTextItem"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/light_control_des_close"
            app:hz_text="@string/close"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/light_control_des_pos"
            app:hz_selectedImage="@drawable/ic_poslamp_select_64"
            app:hz_unselectedImage="@drawable/ic_poslamp_64"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabImageItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/light_control_des_near"
            app:hz_selectedImage="@drawable/ic_diplight_select_64"
            app:hz_unselectedImage="@drawable/ic_diplight_64"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabImageItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/light_control_des_auto"
            app:hz_text="@string/auto"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
    </com.hozonauto.widget.tablayout.HzFixedTabLayout>
    <com.hozonauto.widget.btn.ContainerButton
        android:id="@+id/fogLightCb"
        android:layout_width="258dp"
        android:layout_height="80dp"
        android:layout_marginTop="24dp"
        android:contentDescription="@string/fog_light"
        app:hz_btnIcon="@drawable/ic_rear_fog_lamps"
        app:hz_btnText="@string/fog_light"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rgOutLamp"/>
    <TextView
        android:gravity="center_vertical"
        android:id="@+id/lightHightTv"
        android:layout_marginTop="30dp"
        android:text="@string/light_height"
        android:drawablePadding="10dp"
        android:drawableEnd="@drawable/ic_light_hight"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/fogLightCb"
        style="@style/LightFragmentFirstText"/>
    <com.hozonauto.widget.tablayout.HzFixedTabLayout
        android:id="@+id/rgHeadLamp"
        android:layout_width="@dimen/px_280"
        android:layout_height="20dp"
        android:layout_marginTop="12dp"
        app:hz_mode="tab"
        app:hz_position="0"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lightHightTv"
        app:tabBackground="@null">
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="大灯高度低"
            app:hz_text="低"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="大灯高度中"
            app:hz_text="中"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="大灯高度较高"
            app:hz_text="较高"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="大灯高度高"
            app:hz_text="高"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
    </com.hozonauto.widget.tablayout.HzFixedTabLayout>
    <TextView
        android:textColor="@color/neutral_color_text_secondary"
        android:id="@+id/title6"
        android:layout_marginTop="12dp"
        android:text="请将大灯调至自动或近光灯挡位，并确认大灯已亮起"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rgHeadLamp"
        style="@style/LightFragmentSecondText"/>
    <com.hozon.settings.view.FlexLayout
        android:id="@+id/flexLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="70dp"
        app:heightSpace="24dp"
        app:layout_constraintTop_toBottomOf="@+id/rgHeadLamp"
        app:widthSpace="24dp">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/lightDelayCl"
            android:background="@drawable/neutral_color_background_surface_primary"
            android:layout_width="444dp"
            android:layout_height="72dp"
            android:paddingStart="24dp">
            <com.hozonauto.widget.SwitchButton
                android:id="@+id/lightDelaySw"
                android:checked="true"
                android:contentDescription="大灯延时照明(switch)"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                style="@style/SwitchButton"/>
            <TextView
                android:id="@+id/lightDelayTv"
                android:text="大灯延时照明"
                android:layout_marginStart="20dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/lightDelaySw"
                app:layout_constraintTop_toTopOf="parent"
                style="@style/LightFragmentFirstText"/>
            <ImageView
                android:id="@+id/lightDelayTipIv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_tip"
                android:contentDescription="大灯延时照明提示"
                android:layout_marginStart="10dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/lightDelayTv"
                app:layout_constraintTop_toTopOf="parent"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/nearLightCl"
            android:background="@drawable/neutral_color_background_surface_primary"
            android:layout_width="444dp"
            android:layout_height="72dp"
            android:paddingStart="24dp"
            android:layout_marginStart="20dp">
            <com.hozonauto.widget.SwitchButton
                android:id="@+id/nearAndFarSw"
                android:checked="true"
                android:contentDescription="智能远近光灯(switch)"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                style="@style/SwitchButton"/>
            <TextView
                android:textColor="@color/neutral_color_text_primary"
                android:id="@+id/nearAndFarTv"
                android:text="智能远近光灯"
                android:layout_marginStart="20dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/nearAndFarSw"
                app:layout_constraintTop_toTopOf="parent"
                style="@style/LightFragmentFirstText"/>
            <ImageView
                android:id="@+id/nearAndFarTipIv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_tip"
                android:contentDescription="智能远近光灯提示"
                android:layout_marginStart="10dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/nearAndFarTv"
                app:layout_constraintTop_toTopOf="parent"/>
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_near_light"
                android:layout_marginStart="50dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/nearAndFarTipIv"
                app:layout_constraintTop_toTopOf="parent"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/welcomeLightCl"
            android:background="@drawable/neutral_color_background_surface_primary"
            android:layout_width="444dp"
            android:layout_height="72dp"
            android:layout_marginTop="20dp"
            android:paddingStart="24dp">
            <com.hozonauto.widget.SwitchButton
                android:id="@+id/welcomeLightSw"
                android:checked="true"
                android:contentDescription="迎宾灯(switch)"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                style="@style/SwitchButton"/>
            <TextView
                android:id="@+id/welcomeLightTv"
                android:text="迎宾灯"
                android:layout_marginStart="20dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/welcomeLightSw"
                app:layout_constraintTop_toTopOf="parent"
                style="@style/LightFragmentFirstText"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.hozon.settings.view.FlexLayout>
    <TextView
        android:gravity="center_vertical"
        android:id="@+id/findCarTipTv"
        android:text="寻车提醒"
        android:drawablePadding="10dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/flexLayout"
        style="@style/LightFragmentFirstText"/>
    <com.hozonauto.widget.tablayout.HzFixedTabLayout
        android:id="@+id/findCarTipTab"
        android:layout_width="420dp"
        android:layout_height="@dimen/px_34"
        android:layout_marginTop="12dp"
        app:hz_mode="tab"
        app:hz_position="0"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/findCarTipTv"
        app:tabBackground="@null">
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="寻车提醒闪灯"
            app:hz_text="闪灯"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="寻车提醒闪灯加鸣笛"
            app:hz_text="闪灯+鸣笛"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
    </com.hozonauto.widget.tablayout.HzFixedTabLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
