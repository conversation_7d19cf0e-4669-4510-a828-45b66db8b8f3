<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/neutral_color_background_system_secondary"
    android:paddingTop="230dp"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingHorizontal="96dp">
    <TextView
        android:id="@+id/explain1Tv"
        android:text="@string/traction_des_1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/NetaFragmentSecondTextValue"/>
    <TextView
        android:id="@+id/explain2Tv"
        android:text="@string/traction_des_2"
        app:layout_constraintLeft_toLeftOf="@+id/carBg2Iv"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/NetaFragmentSecondTextValue"/>
    <ImageView
        android:id="@+id/carBg1Iv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:src="@drawable/bg_traction_1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/explain1Tv"/>
    <ImageView
        android:id="@+id/carBg2Iv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/bg_traction_2"
        android:layout_marginStart="30dp"
        app:layout_constraintLeft_toRightOf="@+id/carBg1Iv"
        app:layout_constraintTop_toTopOf="@+id/carBg1Iv"/>
    <com.hozonauto.widget.btn.ContainerButton
        android:id="@+id/quitTracCb"
        android:layout_width="260dp"
        android:layout_height="72dp"
        android:layout_marginTop="96dp"
        app:hz_btnStyle="primary"
        app:hz_btnText="@string/quit_traction"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/carBg2Iv"/>
    <TextView
        android:id="@+id/tv_traction_des_3"
        android:layout_marginBottom="72dp"
        android:text="@string/traction_des_3"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        style="@style/NetaFragmentSecondTextValue"/>
    <TextView
        android:id="@+id/tv_taction_call"
        android:layout_marginBottom="24dp"
        android:text="@string/taction_call"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        style="@style/NetaFragmentSecondText"/>
</androidx.constraintlayout.widget.ConstraintLayout>
