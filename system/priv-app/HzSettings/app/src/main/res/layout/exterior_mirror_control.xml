<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <com.hozonauto.widget.tablayout.HzFixedTabLayout
        android:id="@+id/item_back_auto_down"
        android:layout_width="776dp"
        android:layout_height="wrap_content"
        app:hz_mode="tab"
        app:hz_position="0"
        app:tabBackground="@null">
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="倒车自动下翻关闭"
            app:hz_text="关闭"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="倒车自动下翻左侧"
            app:hz_text="左侧"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="倒车自动下翻左侧右侧"
            app:hz_text="右侧"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="倒车自动下翻左侧双侧"
            app:hz_text="双侧"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
    </com.hozonauto.widget.tablayout.HzFixedTabLayout>
    <com.hozonauto.widget.SwitchButton
        android:id="@+id/sw_lock_auto_in"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="锁车自动折叠(switch)"
        app:hz_click_interval="500"
        style="@style/SwitchButton"/>
</LinearLayout>
