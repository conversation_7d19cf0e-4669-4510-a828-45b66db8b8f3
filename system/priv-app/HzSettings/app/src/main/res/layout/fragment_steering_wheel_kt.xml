<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RelativeLayout
        android:gravity="center"
        android:id="@+id/rl_custom_key"
        android:background="@drawable/neutral_color_background_surface_primary"
        android:padding="24dp"
        android:layout_width="444dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_48">
        <TextView
            android:id="@+id/tv_title"
            android:layout_margin="0dp"
            android:text="@string/title_custom_key"
            style="@style/title_text_style"/>
        <TextView
            android:textColor="@color/neutral_color_text_secondary"
            android:id="@+id/tv_select"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_below="@+id/tv_title"
            android:layout_alignLeft="@+id/tv_title"
            style="@style/HozonTheme.TextAppearance.Caption.Small.Regular"/>
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_forward"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:contentDescription="方向盘自定义按键展开"/>
    </RelativeLayout>
    <RelativeLayout
        android:gravity="center"
        android:id="@+id/rl_fast_key"
        android:background="@drawable/neutral_color_background_surface_primary"
        android:padding="24dp"
        android:layout_width="444dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_48">
        <TextView
            android:id="@+id/tv_fast_title"
            android:layout_margin="0dp"
            android:text="@string/title_fast_key"
            style="@style/title_text_style"/>
        <TextView
            android:textColor="@color/neutral_color_text_secondary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="@string/title_avm"
            android:layout_below="@+id/tv_fast_title"
            android:layout_alignLeft="@+id/tv_fast_title"
            style="@style/HozonTheme.TextAppearance.Caption.Small.Regular"/>
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_forward"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"/>
    </RelativeLayout>
</LinearLayout>
