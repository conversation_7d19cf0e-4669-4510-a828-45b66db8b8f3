<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <com.hozonauto.widget.SwitchButton
        android:id="@+id/switch_fcta"
        android:contentDescription="副驾屏自动进入屏保(switch)"
        app:hz_click_interval="500"
        style="@style/SwitchButton"/>
    <com.hozonauto.widget.SwitchButton
        android:id="@+id/cbCopilotScreen"
        android:contentDescription="副驾息屏(switch)"
        style="@style/SwitchButton"/>
    <com.hozonauto.widget.SwitchButton
        android:id="@+id/cbWallpaperFollow"
        android:contentDescription="副驾屏壁纸跟随(switch)"
        style="@style/SwitchButton"/>
    <ImageView
        android:id="@+id/fctaTips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_im"
        android:contentDescription="副驾屏自动进入屏保提示"/>
    <ImageView
        android:id="@+id/close_cohu_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_im"
        android:contentDescription="副驾息屏提示"/>
</LinearLayout>
