<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rl_item_music"
    android:background="@drawable/neutral_color_background_surface_quaternary"
    android:layout_width="match_parent"
    android:layout_height="@dimen/px_44"
    android:layout_marginTop="@dimen/px_8">
    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:id="@+id/ll_sound_container"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/px_16"
        android:layout_marginTop="@dimen/px_6">
        <TextView
            android:textColor="@color/neutral_color_text_primary"
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/px_8"
            android:text="歌曲名称"
            android:lines="15"
            android:singleLine="true"
            style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
        <skin.support.widget.SkinCompatTextView
            android:textColor="#fff"
            android:gravity="center"
            android:id="@+id/tv_type"
            android:background="@mipmap/music_room_tag_1_"
            android:paddingLeft="16dp"
            android:paddingTop="4dp"
            android:paddingRight="16dp"
            android:paddingBottom="4dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/px_8"
            android:text="天空音效"
            android:layout_toRightOf="@+id/tv_name"
            style="@style/HozonTheme.TextAppearance.Caption.Mini.Regular"/>
        <ImageView
            android:id="@+id/iv_playing"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_marginLeft="24dp"
            android:scaleType="centerInside"/>
    </LinearLayout>
    <TextView
        android:textColor="@color/neutral_color_text_secondary"
        android:id="@+id/tv_author"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/px_12"
        android:layout_marginLeft="@dimen/px_8"
        android:layout_marginTop="@dimen/px_4"
        android:text="作者"
        android:lines="26"
        android:singleLine="true"
        android:layout_below="@+id/ll_sound_container"
        style="@style/HozonTheme.TextAppearance.Caption.Small.Regular"/>
    <TextView
        android:textColor="@color/neutral_color_text_primary"
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/px_8"
        android:text="00:00"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:alpha="0.6"
        style="@style/HozonTheme.TextAppearance.Caption.Small.Regular"/>
</RelativeLayout>
