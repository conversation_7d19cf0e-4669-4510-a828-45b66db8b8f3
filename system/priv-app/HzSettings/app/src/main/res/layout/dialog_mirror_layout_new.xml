<?xml version="1.0" encoding="utf-8"?>
<com.hozon.settings.view.SkinConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center"
    android:id="@+id/cl_dialog_layout"
    android:background="@drawable/neutral_color_background_system_mask"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <com.hozon.settings.view.SkinConstraintLayout
        android:background="@drawable/dialog_bg_ep32"
        android:clickable="true"
        android:layout_width="1010dp"
        android:layout_height="593dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <ImageView
            android:id="@+id/dialog_iv_close"
            android:layout_width="54dp"
            android:layout_height="54dp"
            android:layout_marginTop="15dp"
            android:src="@drawable/app_icon_close_ep32"
            android:contentDescription="@string/close"
            android:layout_marginStart="15dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <com.hozon.settings.view.FontScaleTextView
            android:textColor="@color/neutral_color_text_primary"
            android:id="@+id/dialog_tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@+id/dialog_iv_close"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/dialog_iv_close"
            style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
        <ImageView
            android:id="@+id/mirror_bg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="90dp"
            android:layout_marginRight="48dp"
            android:src="@mipmap/seat_bg_two"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <com.hozonauto.widget.btn.ContainerButton
            android:id="@+id/rb_mirror3"
            android:layout_width="300dp"
            android:layout_height="72dp"
            app:hz_btnIcon="@drawable/selector_exterior_mirror_unfold_icon"
            app:hz_btnStyle="secondary"
            app:hz_btnText="折叠/展开"
            app:layout_constraintLeft_toLeftOf="@+id/mirror_bg"
            app:layout_constraintRight_toRightOf="@+id/mirror_bg"
            app:layout_constraintTop_toBottomOf="@+id/mirror_bg"/>
        <com.hozon.settings.view.FontScaleTextView
            android:textColor="@color/neutral_color_text_primary"
            android:gravity="center"
            android:id="@+id/tvMirrorHint"
            android:visibility="invisible"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="48dp"
            android:layout_marginTop="99dp"
            android:text="位置已调节，选择保存为"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/HozonTheme.TextAppearance.Button.Medium.Regular"/>
        <com.hozonauto.widget.btn.ContainerButton
            android:id="@+id/rb_mirror1"
            android:layout_width="330dp"
            android:layout_height="72dp"
            android:layout_marginTop="24dp"
            android:layout_marginStart="48dp"
            app:hz_btnStyle="secondary"
            app:hz_btnText="主驾坐姿1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvMirrorHint"/>
        <com.hozonauto.widget.btn.ContainerButton
            android:id="@+id/rb_mirror2"
            android:layout_width="330dp"
            android:layout_height="72dp"
            android:layout_marginTop="24dp"
            android:layout_marginStart="48dp"
            app:hz_btnStyle="secondary"
            app:hz_btnText="主驾坐姿2"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/rb_mirror1"/>
        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/ll_auto_recover"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="48dp"
            android:layout_marginTop="48dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/rb_mirror2">
            <com.hozon.settings.view.FontScaleTextView
                android:id="@+id/tv_back_car_auto_down_title"
                android:text="@string/title_back_car_auto_down"
                android:layout_marginStart="0dp"
                style="@style/title_text_style"/>
            <LinearLayout
                android:id="@+id/ll_back_car_auto_down"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp">
                <LinearLayout
                    android:gravity="center_vertical"
                    android:background="@drawable/neutral_color_background_surface_secondary"
                    android:layout_width="153dp"
                    android:layout_height="72dp">
                    <com.hozonauto.widget.CheckBox
                        android:id="@+id/cb_left"
                        android:tag="false"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="24dp"
                        android:contentDescription="@string/title_left"/>
                    <com.hozon.settings.view.FontScaleTextView
                        android:textColor="@color/neutral_color_text_primary"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/title_left"
                        android:layout_marginStart="6dp"
                        style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
                </LinearLayout>
                <LinearLayout
                    android:gravity="center_vertical"
                    android:background="@drawable/neutral_color_background_surface_secondary"
                    android:layout_width="153dp"
                    android:layout_height="72dp"
                    android:layout_marginStart="24dp">
                    <com.hozonauto.widget.CheckBox
                        android:id="@+id/cb_right"
                        android:tag="false"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="@string/title_right"
                        android:layout_marginStart="24dp"/>
                    <com.hozon.settings.view.FontScaleTextView
                        android:textColor="@color/neutral_color_text_primary"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/title_right"
                        android:layout_marginStart="6dp"
                        style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </com.hozon.settings.view.SkinConstraintLayout>
</com.hozon.settings.view.SkinConstraintLayout>
