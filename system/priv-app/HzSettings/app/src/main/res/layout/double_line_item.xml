<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:paddingLeft="@dimen/space_60"
    android:paddingTop="@dimen/space_32"
    android:paddingRight="@dimen/space_60"
    android:paddingBottom="@dimen/space_32"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <ImageView
        android:id="@+id/iv_left_big_icon"
        android:clickable="false"
        android:layout_width="@dimen/icon_width_120"
        android:layout_height="@dimen/icon_height_120"
        android:layout_marginTop="@dimen/space_2"
        android:scaleType="fitCenter"/>
    <LinearLayout
        android:layout_gravity="center_vertical"
        android:orientation="vertical"
        android:id="@+id/layout_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/space_40"
        android:layout_weight="1"
        android:layout_toRightOf="@+id/iv_left_big_icon">
        <TextView
            android:textSize="@dimen/font_36"
            android:textColor="@color/primary_grey_dark"
            android:ellipsize="end"
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:includeFontPadding="false"
            style="@style/FzltBlackSimpleFontStyle"/>
        <LinearLayout
            android:orientation="horizontal"
            android:id="@+id/layout_assit"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_36">
            <ImageView
                android:layout_gravity="center_vertical"
                android:id="@+id/iv_assist"
                android:visibility="gone"
                android:clickable="false"
                android:layout_width="@dimen/icon_width_76"
                android:layout_height="@dimen/icon_height_38"
                android:layout_marginRight="@dimen/space_16"
                android:scaleType="fitCenter"/>
            <TextView
                android:textSize="@dimen/font_24"
                android:textColor="@color/primary_grey_dark"
                android:ellipsize="end"
                android:layout_gravity="center_vertical"
                android:id="@+id/tv_assist"
                android:visibility="gone"
                android:clickable="false"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:alpha="0.36"
                style="@style/FzltBlackSimpleFontStyle"/>
        </LinearLayout>
    </LinearLayout>
    <LinearLayout
        android:orientation="horizontal"
        android:id="@+id/right_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/space_40"
        android:layout_marginTop="@dimen/space_28">
        <TextView
            android:textSize="@dimen/font_28"
            android:textColor="@color/primary_grey_dark"
            android:ellipsize="end"
            android:layout_gravity="center"
            android:id="@+id/tv_end_left"
            android:visibility="gone"
            android:clickable="false"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            style="@style/FzltBlackSimpleFontStyle"/>
        <ImageView
            android:layout_gravity="center"
            android:id="@+id/iv_end_left"
            android:visibility="gone"
            android:clickable="false"
            android:layout_width="@dimen/icon_width_64"
            android:layout_height="@dimen/icon_width_64"
            android:layout_marginLeft="@dimen/space_16"
            android:layout_marginRight="@dimen/space_16"
            android:scaleType="fitCenter"/>
        <TextView
            android:textSize="@dimen/font_28"
            android:textColor="@color/primary_grey_dark"
            android:ellipsize="end"
            android:layout_gravity="center"
            android:id="@+id/tv_end"
            android:visibility="gone"
            android:clickable="false"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            style="@style/FzltBlackSimpleFontStyle"/>
        <ImageView
            android:layout_gravity="center"
            android:id="@+id/iv_icon_end"
            android:visibility="gone"
            android:clickable="false"
            android:layout_width="@dimen/icon_width_64"
            android:layout_height="@dimen/icon_width_64"
            android:layout_marginLeft="@dimen/space_16"
            android:scaleType="fitCenter"/>
    </LinearLayout>
</LinearLayout>
