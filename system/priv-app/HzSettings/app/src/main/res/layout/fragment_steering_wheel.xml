<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/scrollview"
    android:scrollbars="none"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:overScrollMode="never">
    <LinearLayout
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_24"
        android:clickable="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:textSize="24sp"
            android:textColor="#fff"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="方向盘自定义按键"/>
        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="13dp">
            <GridLayout
                android:orientation="horizontal"
                android:id="@+id/grid_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:rowCount="2"
                android:columnCount="2"/>
            <ImageView
                android:id="@+id/iv_steering_wheel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/img_steering_wheel"
                android:layout_toRightOf="@+id/grid_layout"/>
            <TextView
                android:textSize="21sp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="长按方向盘左侧滚轮，可实现自定义功能"
                android:layout_below="@+id/iv_steering_wheel"
                android:layout_alignParentRight="true"
                android:layout_marginEnd="36dp"/>
        </RelativeLayout>
    </LinearLayout>
</ScrollView>
