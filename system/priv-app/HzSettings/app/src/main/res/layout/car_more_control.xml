<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <com.hozonauto.widget.tablayout.HzFixedTabLayout
        android:id="@+id/item_find_car_tip"
        android:layout_width="576dp"
        android:layout_height="wrap_content"
        app:hz_mode="slider"
        app:hz_position="0"
        app:tabBackground="@null">
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="寻车提醒闪灯"
            app:hz_text="闪灯"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
        <view
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="寻车提醒闪灯+鸣笛"
            app:hz_text="闪灯+鸣笛"
            class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
    </com.hozonauto.widget.tablayout.HzFixedTabLayout>
    <com.hozonauto.widget.SwitchButton
        android:id="@+id/sw_face_login"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="人脸识别(switch)"
        style="@style/SwitchButton"/>
    <com.hozonauto.widget.SwitchButton
        android:id="@+id/sw_people_check"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="车内生物遗留检测(switch)"
        style="@style/SwitchButton"/>
    <com.hozonauto.widget.SwitchButton
        android:id="@+id/sw_gesture_check"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="手势识别(switch)"
        style="@style/SwitchButton"/>
    <com.hozonauto.widget.SwitchButton
        android:id="@+id/sw_call_control"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="来电控制(switch)"
        style="@style/SwitchButton"/>
    <TextView
        android:textSize="18sp"
        android:textColor="#006cff"
        android:id="@+id/tv_teach"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="试一试"/>
</LinearLayout>
