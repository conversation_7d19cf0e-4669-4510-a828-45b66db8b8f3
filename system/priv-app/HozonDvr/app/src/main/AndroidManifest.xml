<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    android:sharedUserId="android.uid.system"
    android:versionCode="11032"
    android:versionName="1.1.32"
    android:compileSdkVersion="31"
    android:compileSdkVersionCodename="12"
    package="com.hozonauto.dvr"
    platformBuildVersionCode="31"
    platformBuildVersionName="12">
    <uses-sdk
        android:minSdkVersion="28"
        android:targetSdkVersion="30"/>
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false"/>
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>
    <uses-permission android:name="android.permission.CAPTURE_AUDIO_OUTPUT"/>
    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/>
    <protected-broadcast android:name="com.desaysv.svdvr.action.file.update"/>
    <protected-broadcast android:name="com.desaysv.svdvr.action.dvr.emergency.state"/>
    <protected-broadcast android:name="com.desaysv.svdvr.action.dvr.device.state"/>
    <protected-broadcast android:name="com.desaysv.svdvr.action.dvr.delete.user.files.state"/>
    <uses-permission android:name="android.permission.REORDER_TASKS"/>
    <application
        android:theme="@style/Theme.PreStudyDemos"
        android:label="@string/app_name"
        android:icon="@drawable/ic_launcher_dvr"
        android:name="com.hozonauto.dvr.DVRApplication"
        android:allowBackup="true"
        android:hardwareAccelerated="true"
        android:largeHeap="true"
        android:supportsRtl="true"
        android:extractNativeLibs="false"
        android:roundIcon="@drawable/ic_launcher_round"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory">
        <service
            android:name="com.hozonauto.dvr.services.DVRRunningService"
            android:enabled="true"
            android:exported="true"
            android:foregroundServiceType="camera">
            <intent-filter>
                <action android:name="com.hozonauto.dvr.services.DVRRunningService"/>
            </intent-filter>
        </service>
        <activity
            android:name="com.hozonauto.dvr.MainActivity"
            android:exported="true"/>
        <activity
            android:theme="@style/BgTransparent"
            android:name="com.hozonauto.dvr.PermissionsActivity"/>
        <receiver
            android:name="com.hozonauto.dvr.receivers.DVRUsbStatesReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MEDIA_MOUNTED"/>
                <action android:name="android.intent.action.MEDIA_EJECT"/>
                <action android:name="android.intent.action.MEDIA_NOFS"/>
                <action android:name="android.intent.action.MEDIA_UNMOUNTABLE"/>
                <data android:scheme="file"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.hozonauto.dvr.receivers.NotificationBroadcastReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="DVR_DEVICE_STATE_ACTION"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.hozonauto.dvr.receivers.EmergencyReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.desaysv.svdvr.action.record.emergency"/>
            </intent-filter>
        </receiver>
        <service
            android:name="com.hozonauto.dvr.MainPageService"
            android:enabled="true"
            android:exported="true"/>
        <service
            android:name="com.hozonauto.dvr.DvrTaskService"
            android:process=".dvr_remote"/>
        <receiver
            android:name="com.hozonauto.dvr.MainPageReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
                <action android:name="com.hozonauto.dvr.open_dvr"/>
                <action android:name="com.hozonauto.dvr.action.RECORD_EMERGENCY_FROM"/>
            </intent-filter>
        </receiver>
        <provider
            android:name="com.hozonauto.widget.WidgetProvider"
            android:enabled="true"
            android:exported="false"
            android:authorities="com.hozonauto.dvr"/>
    </application>
</manifest>
