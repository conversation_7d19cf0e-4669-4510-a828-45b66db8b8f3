<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="abc_action_bar_home_description">Navigate home</string>
    <string name="abc_action_bar_up_description">Navigate up</string>
    <string name="abc_action_menu_overflow_description">More options</string>
    <string name="abc_action_mode_done">Done</string>
    <string name="abc_activity_chooser_view_see_all">See all</string>
    <string name="abc_activitychooserview_choose_application">Choose an app</string>
    <string name="abc_capital_off">OFF</string>
    <string name="abc_capital_on">ON</string>
    <string name="abc_menu_alt_shortcut_label">Alt+</string>
    <string name="abc_menu_ctrl_shortcut_label">Ctrl+</string>
    <string name="abc_menu_delete_shortcut_label">delete</string>
    <string name="abc_menu_enter_shortcut_label">enter</string>
    <string name="abc_menu_function_shortcut_label">Function+</string>
    <string name="abc_menu_meta_shortcut_label">Meta+</string>
    <string name="abc_menu_shift_shortcut_label">Shift+</string>
    <string name="abc_menu_space_shortcut_label">space</string>
    <string name="abc_menu_sym_shortcut_label">Sym+</string>
    <string name="abc_prepend_shortcut_label">Menu+</string>
    <string name="abc_search_hint">Search…</string>
    <string name="abc_searchview_description_clear">Clear query</string>
    <string name="abc_searchview_description_query">Search query</string>
    <string name="abc_searchview_description_search">Search</string>
    <string name="abc_searchview_description_submit">Submit query</string>
    <string name="abc_searchview_description_voice">Voice search</string>
    <string name="abc_shareactionprovider_share_with">Share with</string>
    <string name="abc_shareactionprovider_share_with_application">Share with %s</string>
    <string name="abc_toolbar_collapse_description">Collapse</string>
    <string name="am">上午</string>
    <string name="app_name">SVDVR</string>
    <string name="app_name_en">androidx-skin-support-appcompat</string>
    <string name="appbar_scrolling_view_behavior">com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior</string>
    <string name="back">Go back</string>
    <string name="bottom_sheet_behavior">com.google.android.material.bottomsheet.BottomSheetBehavior</string>
    <string name="bottomsheet_action_expand_halfway">Expand halfway</string>
    <string name="camera_open_soon">相机开启中，请稍等</string>
    <string name="cancel">取消</string>
    <string name="character_counter_content_description">Characters entered %1$d of %2$d</string>
    <string name="character_counter_overflowed_content_description">Character limit exceeded %1$d of %2$d</string>
    <string name="character_counter_pattern">%1$d/%2$d</string>
    <string name="check_usb">U盘加载中，请稍后</string>
    <string name="chip_text">Chip text</string>
    <string name="clear">清除</string>
    <string name="clear_text_end_icon_content_description">Clear text</string>
    <string name="close">关闭</string>
    <string name="close_dvr_record">关闭行车记录仪可能造成安全风险，请确认是否关闭行车记录仪</string>
    <string name="confirm">确定</string>
    <string name="container_btn_count_down">(%ds)</string>
    <string name="count_down_send_again">重新发送(%ds)</string>
    <string name="current_flc_mode_not_supported">行车记录仪已暂停录制，行车即可恢复</string>
    <string name="current_flc_mode_not_supported_massage">检测到您较久没有行车行为， 行车记录仪已暂停录制，行车即可恢复录制</string>
    <string name="current_flc_mode_not_supported_title">行车记录仪暂停录制</string>
    <string name="current_mode_not_supported">行车记录仪当前暂不可用</string>
    <string name="date_picker_decrement_day_button">Decrease day</string>
    <string name="date_picker_decrement_month_button">Decrease month</string>
    <string name="date_picker_decrement_year_button">Decrease year</string>
    <string name="date_picker_increment_day_button">Increase day</string>
    <string name="date_picker_increment_month_button">Increase month</string>
    <string name="date_picker_increment_year_button">Increase year</string>
    <string name="date_picker_next_month_button">Next month</string>
    <string name="date_picker_prev_month_button">Previous month</string>
    <string name="day">日</string>
    <string name="dialog_format_usb_content">U盘可用存储空间不足，为确保行车记录仪正常使用，请求清除您的用户文件</string>
    <string name="dvr">行车记录仪</string>
    <string name="dvr_camera_error_massage">行车记录仪设备损坏无法录制，请至服务点维修</string>
    <string name="dvr_camera_error_title">DVR故障提醒</string>
    <string name="dvr_device">U盘设备</string>
    <string name="dvr_devices">行车记录存储设备</string>
    <string name="dvr_error">行车记录仪故障，请至服务点维修</string>
    <string name="dvr_filesystem_massage">当前U盘的文件系统格式不支持，请格式化后使用</string>
    <string name="dvr_filesystem_title">U盘格式化请求</string>
    <string name="dvr_not_turned_on">行车记录仪未开启</string>
    <string name="dvr_settings">行车录制设置</string>
    <string name="dvr_settings_cd">设置</string>
    <string name="dvr_usb_error_massage">U盘异常，行车记录仪暂不可用，请尝试格式化处理。若仍无法解决，建议您更换U盘。</string>
    <string name="dvr_usb_error_title">U盘异常</string>
    <string name="dvr_usb_write_slow_massage">检测到U盘存在视频丢失或播放卡顿的风险，可尝试格式化以获得更佳性能或更换U盘。</string>
    <string name="dvr_usb_write_slow_title">U盘性能风险提示</string>
    <string name="eme_record_no_size_massage">紧急录制剩余空间为 0M，请及时清理，以免影响正常使用</string>
    <string name="eme_record_no_size_title">紧急录制储存空间不足，无法正常开启</string>
    <string name="eme_record_size_small_massage">紧急录制剩余空间小于 100M，请及时清理，以免影响正常使用</string>
    <string name="eme_record_size_small_title">紧急录制储存空间不足</string>
    <string name="eme_record_success">紧急录制视频录制完成</string>
    <string name="eme_record_tx">紧急录像</string>
    <string name="emergency_record">紧急录像</string>
    <string name="emergency_recording">紧急录像正在录制中</string>
    <string name="emergency_recording_triggered">已为您触发紧急视频录制</string>
    <string name="emergency_save">录像保存中</string>
    <string name="emergency_time">紧急录像(%1ds)</string>
    <string name="error_icon_content_description">Error</string>
    <string name="exposed_dropdown_menu_content_description">Show dropdown menu</string>
    <string name="fab_transformation_scrim_behavior">com.google.android.material.transformation.FabTransformationScrimBehavior</string>
    <string name="fab_transformation_sheet_behavior">com.google.android.material.transformation.FabTransformationSheetBehavior</string>
    <string name="font_en">@string/font_family_number_en</string>
    <string name="font_family_heavy">fzlth_gb18030l2_r.ttf</string>
    <string name="font_family_number_en">plusjakartsans_regular.ttf</string>
    <string name="font_family_regular">fzlth_gb18030l2_r.ttf</string>
    <string name="font_num">@string/font_family_number_en</string>
    <string name="font_source_han_sans_cn">@string/font_family_regular</string>
    <string name="fontfamilies_fzlantinghei_db_gb18030">FZLanTingHei-DB-GB18030</string>
    <string name="fontfamilies_fzlantinghei_r_gb18030">FZLanTingHei-R-GB18030</string>
    <string name="fontfamilies_plus_jakarta_sans">Plus Jakarta Sans</string>
    <string name="fontweights_fzlantinghei_db_gb18030_0">Regular</string>
    <string name="fontweights_fzlantinghei_r_gb18030_1">Regular</string>
    <string name="fontweights_plus_jakarta_sans_2">Regular</string>
    <string name="format">格式化</string>
    <string name="hide_bottom_view_on_scroll_behavior">com.google.android.material.behavior.HideBottomViewOnScrollBehavior</string>
    <string name="hidenPassword">隐藏密码</string>
    <string name="hour">时</string>
    <string name="i_know">我知道了</string>
    <string name="icon_content_description">Dialog Icon</string>
    <string name="iconfont_SRS"></string>
    <string name="iconfont_abs"></string>
    <string name="iconfont_acc"></string>
    <string name="iconfont_acc_failure"></string>
    <string name="iconfont_add_brake_fluid_normal"></string>
    <string name="iconfont_authentication_failure"></string>
    <string name="iconfont_autohold"></string>
    <string name="iconfont_automatic_high_beam"></string>
    <string name="iconfont_battery_charge_status"></string>
    <string name="iconfont_battery_overheating"></string>
    <string name="iconfont_brake_failure"></string>
    <string name="iconfont_brake_pedal"></string>
    <string name="iconfont_brake_shift_d"></string>
    <string name="iconfont_brake_shift_p"></string>
    <string name="iconfont_brake_shift_r"></string>
    <string name="iconfont_cancel_appointment_for_charging"></string>
    <string name="iconfont_car_window_close"></string>
    <string name="iconfont_cc_fault"></string>
    <string name="iconfont_ccs"></string>
    <string name="iconfont_child_seats_failure"></string>
    <string name="iconfont_coolant"></string>
    <string name="iconfont_corner_light_malfunction_l"></string>
    <string name="iconfont_corner_light_malfunction_r"></string>
    <string name="iconfont_daytime_running_light_fault"></string>
    <string name="iconfont_dont_drive_tired"></string>
    <string name="iconfont_door_unclosed"></string>
    <string name="iconfont_downshift_d"></string>
    <string name="iconfont_downshift_p"></string>
    <string name="iconfont_downshift_r"></string>
    <string name="iconfont_driving"></string>
    <string name="iconfont_electric_motor_failure"></string>
    <string name="iconfont_engine_failure"></string>
    <string name="iconfont_engine_oil"></string>
    <string name="iconfont_eps_fault"></string>
    <string name="iconfont_esp"></string>
    <string name="iconfont_esp_off"></string>
    <string name="iconfont_excessive_driving_time"></string>
    <string name="iconfont_exit_refueling"></string>
    <string name="iconfont_external_cable_connect"></string>
    <string name="iconfont_far_light_failure"></string>
    <string name="iconfont_front_fog_lamp"></string>
    <string name="iconfont_gear_malfunction"></string>
    <string name="iconfont_high_beam"></string>
    <string name="iconfont_high_voltage_alarm"></string>
    <string name="iconfont_hill_descent_control"></string>
    <string name="iconfont_hill_descent_malfunction"></string>
    <string name="iconfont_hint"></string>
    <string name="iconfont_intelligent_high_beam_malfunction"></string>
    <string name="iconfont_key_undetected"></string>
    <string name="iconfont_limited_performance_mode"></string>
    <string name="iconfont_low_beam"></string>
    <string name="iconfont_low_fuel"></string>
    <string name="iconfont_low_key_battery"></string>
    <string name="iconfont_low_power_battery_level"></string>
    <string name="iconfont_motor_overheating"></string>
    <string name="iconfont_near_light_failure"></string>
    <string name="iconfont_parking_brake"></string>
    <string name="iconfont_position_light_malfunction"></string>
    <string name="iconfont_position_lights"></string>
    <string name="iconfont_power_battery_fault"></string>
    <string name="iconfont_power_battery_low"></string>
    <string name="iconfont_power_battery_maintenance"></string>
    <string name="iconfont_power_battery_temperature"></string>
    <string name="iconfont_powertrain_malfunction_car"></string>
    <string name="iconfont_powertrain_malfunction_suv"></string>
    <string name="iconfont_put_into_n_file"></string>
    <string name="iconfont_rear_fog_lamp"></string>
    <string name="iconfont_rear_fog_lamp_fault"></string>
    <string name="iconfont_safe_belt"></string>
    <string name="iconfont_steering_wheel_angle_not_initialized"></string>
    <string name="iconfont_the_tire_leakage"></string>
    <string name="iconfont_tire_pressure"></string>
    <string name="iconfont_tire_pressure_sensor_malfunction"></string>
    <string name="iconfont_turn_lamp_failure_l"></string>
    <string name="iconfont_turn_lamp_failure_r"></string>
    <string name="iconfont_turn_left"></string>
    <string name="iconfont_turn_right"></string>
    <string name="iconfont_tyre_pressure_sensor_low_power"></string>
    <string name="input_content_suggest">请输入内容</string>
    <string name="input_text_num_200">/200</string>
    <string name="input_text_num_zero">0</string>
    <string name="input_verification_code_suggest">请输入验证码</string>
    <string name="item_view_role_description">Tab</string>
    <string name="loop_record_tx">循环录像</string>
    <string name="loop_set_time">循环录制时长</string>
    <string name="luminance">Brightness</string>
    <string name="material_clock_display_divider">:</string>
    <string name="material_clock_toggle_content_description">Select AM or PM</string>
    <string name="material_hour_selection">Select hour</string>
    <string name="material_hour_suffix">%1$s o\'clock</string>
    <string name="material_minute_selection">Select minutes</string>
    <string name="material_minute_suffix">%1$s minutes</string>
    <string name="material_motion_easing_accelerated">cubic-bezier(0.4, 0.0, 1.0, 1.0)</string>
    <string name="material_motion_easing_decelerated">cubic-bezier(0.0, 0.0, 0.2, 1.0)</string>
    <string name="material_motion_easing_emphasized">path(M 0,0 C 0.05, 0, 0.133333, 0.06, 0.166666, 0.4 C 0.208333, 0.82, 0.25, 1, 1, 1)</string>
    <string name="material_motion_easing_linear">cubic-bezier(0.0, 0.0, 1.0, 1.0)</string>
    <string name="material_motion_easing_standard">cubic-bezier(0.4, 0.0, 0.2, 1.0)</string>
    <string name="material_slider_range_end">Range end,</string>
    <string name="material_slider_range_start">Range start,</string>
    <string name="material_timepicker_am">AM</string>
    <string name="material_timepicker_clock_mode_description">Switch to clock mode for the time input.</string>
    <string name="material_timepicker_hour">Hour</string>
    <string name="material_timepicker_minute">Minute</string>
    <string name="material_timepicker_pm">PM</string>
    <string name="material_timepicker_select_time">Select time</string>
    <string name="material_timepicker_text_input_mode_description">Switch to text input mode for the time input.</string>
    <string name="method_call_order_exception">MethodCallOrderException setWindowType(int windowType) must call before show()</string>
    <string name="minute">分</string>
    <string name="month">月</string>
    <string name="mtrl_badge_numberless_content_description">New notification</string>
    <string name="mtrl_chip_close_icon_content_description">Remove %1$s</string>
    <string name="mtrl_exceed_max_badge_number_content_description">More than %1$d new notifications</string>
    <string name="mtrl_exceed_max_badge_number_suffix">%1$d%2$s</string>
    <string name="mtrl_picker_a11y_next_month">Change to next month</string>
    <string name="mtrl_picker_a11y_prev_month">Change to previous month</string>
    <string name="mtrl_picker_announce_current_selection">Current selection: %1$s</string>
    <string name="mtrl_picker_cancel">@android:string/cancel</string>
    <string name="mtrl_picker_confirm">@android:string/ok</string>
    <string name="mtrl_picker_date_header_selected">%1$s</string>
    <string name="mtrl_picker_date_header_title">Select Date</string>
    <string name="mtrl_picker_date_header_unselected">Selected date</string>
    <string name="mtrl_picker_day_of_week_column_header">Column of days: %1$s</string>
    <string name="mtrl_picker_invalid_format">Invalid format.</string>
    <string name="mtrl_picker_invalid_format_example">Example: %1$s</string>
    <string name="mtrl_picker_invalid_format_use">Use: %1$s</string>
    <string name="mtrl_picker_invalid_range">Invalid range.</string>
    <string name="mtrl_picker_navigate_to_year_description">Navigate to year %1$s</string>
    <string name="mtrl_picker_out_of_range">Out of range: %1$s</string>
    <string name="mtrl_picker_range_header_only_end_selected">Start date – %1$s</string>
    <string name="mtrl_picker_range_header_only_start_selected">%1$s – End date</string>
    <string name="mtrl_picker_range_header_selected">%1$s – %2$s</string>
    <string name="mtrl_picker_range_header_title">Select Range</string>
    <string name="mtrl_picker_range_header_unselected">Start date – End date</string>
    <string name="mtrl_picker_save">Save</string>
    <string name="mtrl_picker_text_input_date_hint">Date</string>
    <string name="mtrl_picker_text_input_date_range_end_hint">End date</string>
    <string name="mtrl_picker_text_input_date_range_start_hint">Start date</string>
    <string name="mtrl_picker_text_input_day_abbr">d</string>
    <string name="mtrl_picker_text_input_month_abbr">m</string>
    <string name="mtrl_picker_text_input_year_abbr">y</string>
    <string name="mtrl_picker_toggle_to_calendar_input_mode">Switch to calendar input mode</string>
    <string name="mtrl_picker_toggle_to_day_selection">Tap to switch to selecting a day</string>
    <string name="mtrl_picker_toggle_to_text_input_mode">Switch to text input mode</string>
    <string name="mtrl_picker_toggle_to_year_selection">Tap to switch to selecting a year</string>
    <string name="no_share_target">No screen to share</string>
    <string name="notification_dvr_device_state_content">未检测到有效行车记录仪U盘，点击查看使用教程</string>
    <string name="notification_dvr_device_state_title">行车记录仪存储设备缺失</string>
    <string name="notification_dvr_emergency_state_content">紧急录制存储空间不足，无法使用该功能，请及时清理</string>
    <string name="notification_dvr_emergency_state_title">紧急录制</string>
    <string name="notification_dvr_loop_state_title">行车记录仪</string>
    <string name="notification_dvr_loop_state_title_cd">行车记录仪(switch)</string>
    <string name="number_picker_decrement_button">Decrease</string>
    <string name="number_picker_increment_button">Increase</string>
    <string name="number_picker_increment_scroll_action">Slide up to increase and down to decrease.</string>
    <string name="open">打开</string>
    <string name="password_dialog_title">输入密码</string>
    <string name="password_reminder">请输入密码</string>
    <string name="password_toggle_content_description">Show password</string>
    <string name="path_password_eye">M12,4.5C7,4.5 2.73,7.61 1,12c1.73,4.39 6,7.5 11,7.5s9.27,-3.11 11,-7.5c-1.73,-4.39 -6,-7.5 -11,-7.5zM12,17c-2.76,0 -5,-2.24 -5,-5s2.24,-5 5,-5 5,2.24 5,5 -2.24,5 -5,5zM12,9c-1.66,0 -3,1.34 -3,3s1.34,3 3,3 3,-1.34 3,-3 -1.34,-3 -3,-3z</string>
    <string name="path_password_eye_mask_strike_through">M2,4.27 L19.73,22 L22.27,19.46 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_eye_mask_visible">M2,4.27 L2,4.27 L4.54,1.73 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_strike_through">M3.27,4.27 L19.74,20.74</string>
    <string name="percent">%1$s%%</string>
    <string name="pm">下午</string>
    <string name="record_audio">录制声音</string>
    <string name="record_audio_cd">录制声音(switch)</string>
    <string name="remove_fail">U盘移除失败，请稍后再试</string>
    <string name="remove_success">U盘已成功移除</string>
    <string name="remove_usb">移除U盘</string>
    <string name="remove_usb_confirm">移除U盘会将录制中的视频中断，请确认是否移除</string>
    <string name="remove_usb_warn">格式化过程中行车视频不可录制，U盘中储存的所有视频文件将被清空，请确认是否格式化</string>
    <string name="reserve_charging_time_str">日期%1$s%2$s%3$s</string>
    <string name="return_page">返回</string>
    <string name="search">搜索</string>
    <string name="search_menu_title">Search</string>
    <string name="searchview_description_delete">Delete</string>
    <string name="searchview_description_submit">Submit query</string>
    <string name="searchview_description_voice">Voice search</string>
    <string name="send_again">重新发送</string>
    <string name="send_verification_code">发送验证码</string>
    <string name="showPassword">显示密码</string>
    <string name="status_bar_notification_info_overflow">999+</string>
    <string name="textcase_none">none</string>
    <string name="textdecoration_none">none</string>
    <string name="time_1min">1分钟</string>
    <string name="time_1min_cd">循环录像时长一分钟</string>
    <string name="time_3min">3分钟</string>
    <string name="time_3min_cd">循环录像时长三分钟</string>
    <string name="time_5min">5分钟</string>
    <string name="time_5min_cd">循环录像时长五分钟</string>
    <string name="time_picker_decrement_hour_button">Decrease hour</string>
    <string name="time_picker_decrement_minute_button">Decrease minute</string>
    <string name="time_picker_decrement_set_am_button">Set AM</string>
    <string name="time_picker_increment_hour_button">Increase hour</string>
    <string name="time_picker_increment_minute_button">Increase minute</string>
    <string name="time_picker_increment_set_pm_button">Set PM</string>
    <string name="to_gallery">查看相册</string>
    <string name="un_mount_usb">移除U盘</string>
    <string name="usb_guide_text">需支持USB2.0及以上的单分区U盘，推荐容量为128GB，格式为exFAT。</string>
    <string name="usb_has_self_file">当前行车录制专用U盘内存在其他文件，可能影响录制性能，请格式化后使用</string>
    <string name="usb_mount_fail_title">格式化失败，请更换U盘</string>
    <string name="usb_mount_success_title">U盘格式化成功</string>
    <string name="usb_mounting_title">U盘格式化中，请稍后</string>
    <string name="usb_not_available">U盘不满足条件</string>
    <string name="usb_size_no_use">USB设备容量过小，无法使用行车记录仪。U盘支持：USB2.0以上，容量不小于16GB</string>
    <string name="usb_size_no_use_title">U盘设备内存不足</string>
    <string name="use_size" formatted="false">已用%1s/%2s</string>
    <string name="verification_code_dialog_title">输入验证码</string>
    <string name="view_eme_in_gallery">可在相册查看录制视频</string>
    <string name="volume">Volume</string>
    <string name="year">年</string>
    <string name="zero_percent">0%</string>
</resources>
