<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.button.MaterialButtonToggleGroup xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="horizontal"
    android:id="@+id/material_clock_period_toggle"
    android:background="?attr/colorSurface"
    android:visibility="gone"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:checkedButton="@+id/material_clock_period_am_button"
    app:selectionRequired="true"
    app:singleSelection="true">
    <Button
        android:id="@+id/material_clock_period_am_button"
        android:layout_width="0dp"
        android:text="@string/material_timepicker_am"
        android:layout_weight="1"
        android:insetTop="4dp"
        android:insetBottom="4dp"
        style="?attr/materialButtonOutlinedStyle"/>
    <Button
        android:id="@+id/material_clock_period_pm_button"
        android:layout_width="0dp"
        android:text="@string/material_timepicker_pm"
        android:layout_weight="1"
        android:insetTop="4dp"
        android:insetBottom="4dp"
        style="?attr/materialButtonOutlinedStyle"/>
</com.google.android.material.button.MaterialButtonToggleGroup>
