<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageButton
        android:id="@+id/increment"
        android:background="@color/color_transparent"
        android:paddingTop="@dimen/dp_5"
        android:paddingBottom="@dimen/dp_5"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
    <EditText
        android:textAppearance="@style/HozonTheme.TextAppearance.Headline.Medium.Regular.EN"
        android:textSize="@dimen/body_medium"
        android:textColor="@color/color_number_picker_text_selector"
        android:gravity="center"
        android:id="@+id/numberpicker_input"
        android:background="@color/color_transparent"
        android:visibility="invisible"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:fontFamily="@string/font_family_number_en"/>
    <ImageButton
        android:id="@+id/decrement"
        android:background="@color/color_transparent"
        android:paddingTop="@dimen/dp_5"
        android:paddingBottom="@dimen/dp_5"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
</merge>
