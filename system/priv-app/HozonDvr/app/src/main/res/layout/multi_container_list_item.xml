<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="horizontal"
    android:paddingLeft="@dimen/px_8"
    android:paddingRight="@dimen/px_8"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/layout_left_container"
        android:paddingRight="@dimen/px_8"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"/>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_toRightOf="@+id/layout_left_container">
        <LinearLayout
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:id="@+id/title_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <LinearLayout
                android:gravity="center_vertical"
                android:layout_gravity="center_vertical"
                android:orientation="horizontal"
                android:id="@+id/layout_title_left"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">
                <LinearLayout
                    android:gravity="center_vertical"
                    android:orientation="vertical"
                    android:id="@+id/layout_title_left_container"
                    android:visibility="gone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="@dimen/px_4"/>
                <TextView
                    android:textColor="@color/neutral_color_text_primary"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:id="@+id/tv_title"
                    android:clickable="false"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
                <LinearLayout
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical"
                    android:id="@+id/layout_title_right1_container"
                    android:visibility="gone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/px_4"/>
            </LinearLayout>
            <LinearLayout
                android:layout_gravity="center"
                android:orientation="vertical"
                android:id="@+id/layout_title_right2_container"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/px_8"
                android:layout_toRightOf="@+id/layout_title_left"/>
        </LinearLayout>
        <LinearLayout
            android:orientation="horizontal"
            android:id="@+id/layout_below_title_container"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_4"
            app:layout_constraintLeft_toLeftOf="@+id/title_layout"
            app:layout_constraintTop_toBottomOf="@+id/title_layout"/>
        <TextView
            android:textColor="@color/neutral_color_text_secondary"
            android:ellipsize="end"
            android:id="@+id/tv_assist"
            android:clickable="false"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_4"
            android:maxWidth="@dimen/px_180"
            android:includeFontPadding="false"
            app:layout_constraintLeft_toLeftOf="@+id/title_layout"
            app:layout_constraintTop_toBottomOf="@+id/layout_below_title_container"
            style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
        <LinearLayout
            android:orientation="horizontal"
            android:id="@+id/layout_below_assit_text_container"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_4"
            app:layout_constraintLeft_toLeftOf="@+id/title_layout"
            app:layout_constraintTop_toBottomOf="@+id/tv_assist"/>
        <LinearLayout
            android:orientation="horizontal"
            android:id="@+id/layout_right_container"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/px_4"
            android:layout_alignParentRight="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</RelativeLayout>
