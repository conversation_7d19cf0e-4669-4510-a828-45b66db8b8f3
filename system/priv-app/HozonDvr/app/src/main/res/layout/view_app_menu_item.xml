<?xml version="1.0" encoding="utf-8"?>
<TextView xmlns:android="http://schemas.android.com/apk/res/android"
    android:textSize="@dimen/body_medium"
    android:textColor="@color/color_secondary_menu_item"
    android:ellipsize="end"
    android:gravity="center_vertical"
    android:background="@drawable/bg_view_app_menu_item"
    android:visibility="gone"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_28"
    android:layout_marginLeft="@dimen/dp_4"
    android:layout_marginRight="@dimen/dp_4"
    android:layout_marginBottom="@dimen/app_menu_item_distance"
    android:maxLines="2"
    android:drawablePadding="@dimen/dp_6"
    android:paddingStart="@dimen/dp_12"
    android:paddingEnd="@dimen/dp_4"/>
