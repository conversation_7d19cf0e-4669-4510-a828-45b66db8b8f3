<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <TextView
        android:textSize="36sp"
        android:textColor="#ff000000"
        android:ellipsize="end"
        android:gravity="center"
        android:id="@+id/tvTabText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:maxLines="1"
        android:includeFontPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/vSpace"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_default="wrap"/>
    <View
        android:id="@+id/vSpace"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tvTabMsg"
        app:layout_constraintStart_toEndOf="@+id/tvTabText"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_max="24dp"
        app:layout_constraintWidth_min="8dp"/>
    <TextView
        android:textSize="14sp"
        android:textColor="#ffffffff"
        android:gravity="center"
        android:id="@+id/tvTabMsg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="40dp"
        android:minHeight="24dp"
        android:includeFontPadding="false"
        android:paddingHorizontal="9dp"
        android:paddingVertical="2dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/vSpace"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
