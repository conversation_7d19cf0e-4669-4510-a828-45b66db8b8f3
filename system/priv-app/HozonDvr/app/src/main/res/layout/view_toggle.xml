<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/fl_toggle_root"
    android:background="@drawable/bg_toggle"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:minHeight="@dimen/toggle_height_default">
    <FrameLayout
        android:id="@+id/fl_toggle"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <TextView
        android:id="@+id/tv_indicator"
        android:background="@drawable/bg_toggle_indicator_selector"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"/>
    <LinearLayout
        android:orientation="horizontal"
        android:id="@+id/ll_toggle"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
</FrameLayout>
