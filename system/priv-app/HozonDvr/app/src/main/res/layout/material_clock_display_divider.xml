<?xml version="1.0" encoding="utf-8"?>
<TextView xmlns:android="http://schemas.android.com/apk/res/android"
    android:textAppearance="?attr/textAppearanceHeadline3"
    android:textSize="56dp"
    android:textColor="?attr/colorOnSurface"
    android:gravity="center"
    android:layout_width="@dimen/material_clock_display_padding"
    android:layout_height="wrap_content"
    android:layout_marginTop="4dp"
    android:text="@string/material_clock_display_divider"
    android:maxEms="1"
    android:includeFontPadding="false"
    android:lineSpacingExtra="0dp"
    android:importantForAccessibility="no"/>
