<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center_horizontal"
    android:orientation="horizontal"
    android:id="@+id/timePickerLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.hozon.widget.NumberPicker
        android:gravity="center"
        android:layout_gravity="center_vertical"
        android:orientation="vertical"
        android:id="@+id/amPm"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:layout_width="@dimen/dp_36"
        android:layout_height="@dimen/dp_56"
        android:layout_marginEnd="@dimen/dp_2"
        app:itemCount="2"
        app:textSizeNormal="@dimen/body_medium"
        app:textSizeSelected="@dimen/headline_small"/>
    <FrameLayout
        android:layout_gravity="center_vertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <com.hozon.widget.NumberPicker
            android:gravity="center"
            android:orientation="vertical"
            android:id="@+id/hour"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_56"
            app:itemCount="3"
            app:selectionDivider="@drawable/divider_line_numberpicker"
            app:selectionDividerHeight="@dimen/dp_1"
            app:selectionDividersDistance="@dimen/dp_20"
            app:text="@string/hour"/>
    </FrameLayout>
    <FrameLayout
        android:layout_gravity="center_vertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_6">
        <com.hozon.widget.NumberPicker
            android:gravity="center"
            android:orientation="vertical"
            android:id="@+id/minute"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_56"
            app:itemCount="3"
            app:selectionDivider="@drawable/divider_line_numberpicker"
            app:selectionDividerHeight="@dimen/dp_1"
            app:selectionDividersDistance="@dimen/dp_20"
            app:text="@string/minute"/>
    </FrameLayout>
    <TextView
        android:textSize="@dimen/font_size_8"
        android:textColor="@color/brand_color_brand6"
        android:layout_gravity="center"
        android:id="@+id/divider"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_2.5"
        android:layout_marginBottom="@dimen/dp_2.5"
        android:importantForAccessibility="no"/>
</LinearLayout>
