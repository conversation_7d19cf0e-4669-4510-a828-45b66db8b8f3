<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:id="@+id/fl_hud_base"
    android:background="@drawable/bg_hud"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:minWidth="@dimen/hud_width"
    android:minHeight="@dimen/hud_height">
    <TextView
        android:textSize="@dimen/font_size_6"
        android:textColor="@color/neutral_color_text_text5"
        android:gravity="center"
        android:id="@+id/tv_hud_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:text="@string/luminance"
        android:maxLines="1"/>
    <com.hozon.widget.LightView
        android:id="@+id/iv_hud_icon"
        android:layout_width="@dimen/hud_icon_size"
        android:layout_height="@dimen/hud_icon_size"
        android:layout_marginTop="@dimen/dp_8"
        android:src="@drawable/ic_hud_brightness_brightest"
        android:scaleType="centerInside"/>
    <TextView
        android:textSize="@dimen/font_size_7"
        android:textColor="@color/neutral_color_text_text5"
        android:layout_gravity="center_horizontal"
        android:id="@+id/tv_hud_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:text="@string/zero_percent"
        android:fontFamily="@string/font_num"/>
    <com.hozon.widget.ProgressBar
        android:id="@+id/pb_hud_progress"
        android:focusable="false"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:layout_marginBottom="@dimen/dp_8"
        android:progress="0"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_12"
        app:draggable="false"
        app:thumb="@drawable/ic_transparent_thumb"
        style="?android:attr/progressBarStyleHorizontal"/>
</LinearLayout>
