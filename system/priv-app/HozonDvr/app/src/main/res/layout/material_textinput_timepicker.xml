<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="horizontal"
    android:background="?attr/colorSurface"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:baselineAligned="false"
    android:layoutDirection="ltr"
    app:layout_constraintLeft_toLeftOf="parent"
    app:layout_constraintRight_toRightOf="parent"
    app:layout_constraintTop_toTopOf="parent">
    <include layout="@layout/material_timepicker_textinput_display"/>
    <include
        android:id="@+id/material_clock_period_toggle"
        android:layout_width="@dimen/material_clock_period_toggle_width"
        android:layout_height="@dimen/material_clock_period_toggle_height"
        android:layout_marginStart="@dimen/material_clock_period_toggle_margin_left"
        layout="@layout/material_clock_period_toggle"/>
</LinearLayout>
