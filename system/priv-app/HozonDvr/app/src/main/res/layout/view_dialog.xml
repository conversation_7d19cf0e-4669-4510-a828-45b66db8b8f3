<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:layout_gravity="center"
        android:orientation="vertical"
        android:id="@+id/fl_dialog"
        android:background="@drawable/bg_dialog_background"
        android:paddingTop="@dimen/dp_8"
        android:paddingBottom="@dimen/dp_8"
        android:focusable="true"
        android:clickable="true"
        android:layout_width="@dimen/dialog_default_width"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/dialog_default_height"
        android:paddingStart="@dimen/dp_8"
        android:paddingEnd="@dimen/dp_8">
        <TextView
            android:textSize="@dimen/body_medium"
            android:textColor="@color/neutral_color_text_text5"
            android:ellipsize="end"
            android:gravity="center_horizontal"
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_8"
            android:maxLines="1"
            android:paddingStart="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_16"/>
        <LinearLayout
            android:gravity="center"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_marginBottom="@dimen/dp_8"
            android:layout_weight="1">
            <TextView
                android:scrollbarThumbVertical="@drawable/list_scrollbar"
                android:scrollbarStyle="outsideOverlay"
                android:textSize="@dimen/body_small"
                android:textColor="@color/neutral_color_text_text5"
                android:layout_gravity="center"
                android:id="@+id/tv_message"
                android:scrollbars="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:maxWidth="@dimen/dp_160"
                android:maxHeight="@dimen/dp_88"
                android:paddingStart="@dimen/dp_12"
                android:paddingEnd="@dimen/dp_12"
                android:layout_marginStart="@dimen/dp_4"
                android:layout_marginEnd="@dimen/dp_4"
                android:lineHeight="@dimen/dp_12"/>
            <LinearLayout
                android:gravity="center"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:id="@+id/fl_include"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_weight="1"/>
        </LinearLayout>
        <LinearLayout
            android:layout_gravity="bottom"
            android:orientation="horizontal"
            android:id="@+id/buttonPanel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_8"
            android:divider="@drawable/div_h_dialog_button"
            android:showDividers="middle"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16">
            <Button
                android:ellipsize="end"
                android:layout_gravity="center_vertical"
                android:id="@+id/button_positive"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:maxWidth="@dimen/dp_160"
                android:maxLines="1"
                android:layout_weight="1"
                style="@style/HozonTheme.Widget.Button.WeakLevel"/>
            <Button
                android:ellipsize="end"
                android:layout_gravity="center_vertical"
                android:id="@+id/button_negative"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:maxWidth="@dimen/dp_160"
                android:maxLines="1"
                android:layout_weight="1"
                style="@style/HozonTheme.Widget.Button.WeakLevel"/>
            <Button
                android:ellipsize="end"
                android:layout_gravity="center_vertical"
                android:id="@+id/button_neutral"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:maxWidth="@dimen/dp_160"
                android:maxLines="1"
                android:layout_weight="1"
                style="@style/HozonTheme.Widget.Button.WeakLevel"/>
        </LinearLayout>
    </LinearLayout>
</FrameLayout>
