<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:id="@+id/search_bar"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:minHeight="@dimen/dp_24">
    <LinearLayout
        android:orientation="horizontal"
        android:id="@+id/search_edit_frame"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:layoutDirection="locale"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp">
        <ImageView
            android:layout_gravity="center_vertical"
            android:id="@+id/search_back_button"
            android:visibility="visible"
            android:layout_width="@dimen/dp_20"
            android:layout_height="@dimen/dp_20"
            android:src="@drawable/ic_menu_back"
            android:scaleType="centerCrop"
            android:contentDescription="@string/back"
            android:layout_marginEnd="@dimen/dp_8"/>
        <LinearLayout
            android:layout_gravity="center_vertical"
            android:orientation="horizontal"
            android:id="@+id/search_plate"
            android:background="@drawable/bg_input_selector"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:minHeight="@dimen/dp_24"
            android:layout_weight="1">
            <ImageView
                android:layout_gravity="center_vertical"
                android:id="@+id/search_img"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:focusable="true"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:src="@drawable/ic_common_search"
                android:paddingStart="@dimen/dp_8"/>
            <view
                android:ellipsize="end"
                android:layout_gravity="center_vertical"
                android:id="@+id/search_src_text"
                android:background="@null"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:singleLine="true"
                android:layout_weight="1"
                android:inputType="textNoSuggestions"
                android:dropDownAnchor="@+id/search_edit_frame"
                android:imeOptions="actionSearch"
                android:dropDownHeight="wrap_content"
                android:dropDownHorizontalOffset="0dp"
                android:dropDownVerticalOffset="0dp"
                android:layout_marginStart="@dimen/dp_4"
                class="com.hozon.widget.SearchView$SearchAutoComplete"
                style="@style/HozonTheme.Widget.EditText"/>
            <ImageView
                android:layout_gravity="center_vertical"
                android:id="@+id/search_close_btn"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:focusable="true"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:src="@drawable/ic_edit_input_wrong"
                android:contentDescription="@string/searchview_description_delete"
                android:paddingStart="@dimen/dp_8"
                android:paddingEnd="@dimen/dp_10"/>
            <ImageView
                android:layout_gravity="center_vertical"
                android:id="@+id/search_voice_btn"
                android:focusable="true"
                android:visibility="visible"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:src="@drawable/ic_voice"
                android:contentDescription="@string/searchview_description_voice"
                android:paddingStart="16dp"
                android:paddingEnd="@dimen/dp_8"/>
        </LinearLayout>
        <LinearLayout
            android:orientation="horizontal"
            android:id="@+id/submit_area"
            android:layout_width="wrap_content"
            android:layout_height="match_parent">
            <ImageView
                android:layout_gravity="center_vertical"
                android:id="@+id/search_go_btn"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:focusable="true"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"/>
        </LinearLayout>
    </LinearLayout>
    <Button
        android:layout_gravity="center_vertical"
        android:id="@+id/search_button"
        android:focusable="true"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/search"
        android:contentDescription="@string/search"
        style="@style/HozonTheme.Widget.Button.Dark"/>
</LinearLayout>
