<?xml version="1.0" encoding="utf-8"?>
<com.hozon.theme.view.ThemeFrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_dvr_settings"
    android:background="@drawable/neutral_color_background_system_popup"
    android:layout_width="match_parent"
    android:layout_height="@dimen/px_226">
    <ScrollView
        android:scrollbars="none"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <com.hozon.theme.view.ThemeImageView
                android:id="@+id/imv_return"
                android:background="@drawable/app_icon_back"
                android:layout_width="@dimen/px_16"
                android:layout_height="@dimen/px_16"
                android:layout_marginTop="@dimen/px_12"
                android:contentDescription="@string/return_page"
                android:layout_marginStart="@dimen/px_12"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
            <com.hozon.theme.view.ThemeTextView
                android:textColor="@color/neutral_color_text_primary"
                android:ellipsize="end"
                android:gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/px_16"
                android:layout_marginTop="@dimen/px_12"
                android:text="@string/dvr_settings"
                android:maxLines="2"
                android:paddingHorizontal="@dimen/px_36"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                style="@style/HozonTheme.TextAppearance.Large.Heavy"/>
            <com.hozon.theme.view.ThemeTextView
                android:textColor="@color/neutral_color_text_primary"
                android:ellipsize="end"
                android:id="@+id/tv_loop_time_tx"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/px_12"
                android:text="@string/loop_set_time"
                android:maxLines="2"
                android:paddingEnd="@dimen/px_12"
                android:layout_marginStart="@dimen/px_12"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/imv_return"
                style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
            <com.hozonauto.widget.tablayout.HzFixedTabLayout
                android:id="@+id/hft_loop_time"
                android:layout_width="@dimen/px_192"
                android:layout_height="@dimen/px_24"
                android:layout_marginTop="@dimen/px_4"
                android:layout_marginStart="@dimen/px_12"
                app:hz_mode="tab"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_loop_time_tx"
                app:layout_constraintWidth_percent="0.5">
                <view
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:contentDescription="@string/time_1min_cd"
                    app:hz_text="@string/time_1min"
                    class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
                <view
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:contentDescription="@string/time_3min_cd"
                    app:hz_text="@string/time_3min"
                    class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
                <view
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:contentDescription="@string/time_5min_cd"
                    app:hz_text="@string/time_5min"
                    class="com.hozonauto.widget.tablayout.HzFixedTabLayout$TabTextItem"/>
            </com.hozonauto.widget.tablayout.HzFixedTabLayout>
            <com.hozonauto.widget.SwitchButton
                android:layout_gravity="center_vertical"
                android:id="@+id/btn_open_audio"
                android:layout_width="@dimen/px_22"
                android:layout_height="@dimen/px_12"
                android:layout_marginTop="@dimen/px_12"
                android:contentDescription="@string/record_audio_cd"
                android:layout_marginStart="@dimen/px_12"
                app:hz_loading_timeout="3000"
                app:hz_show_loading="false"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/hft_loop_time"
                style="@style/SwitchButtonDefaultStyle"/>
            <com.hozon.theme.view.ThemeTextView
                android:textColor="@color/neutral_color_text_primary"
                android:ellipsize="end"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/record_audio"
                android:maxLines="2"
                android:paddingEnd="@dimen/px_12"
                android:layout_marginStart="@dimen/px_8"
                app:layout_constraintStart_toEndOf="@+id/btn_open_audio"
                app:layout_constraintTop_toTopOf="@+id/btn_open_audio"
                style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
            <LinearLayout
                android:orientation="vertical"
                android:id="@+id/ll_loop_record"
                android:layout_width="@dimen/px_88"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/px_12"
                android:layout_marginStart="@dimen/px_12"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/btn_open_audio">
                <com.hozon.theme.view.ThemeTextView
                    android:textColor="@color/neutral_color_text_primary"
                    android:ellipsize="end"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/loop_record_tx"
                    android:maxLines="2"
                    android:drawablePadding="@dimen/px_8"
                    android:drawableStart="@drawable/ic_loop_record_dot"
                    style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
                <com.hozonauto.widget.HorizontalProgress
                    android:id="@+id/hp_loop_progress"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="@dimen/px_4"
                    app:hz_maxProgress="100"
                    app:hz_progress="0"
                    app:hz_progress_mode="mode_long"/>
                <com.hozon.theme.view.ThemeTextView
                    android:textColor="@color/neutral_color_text_secondary"
                    android:ellipsize="end"
                    android:id="@+id/tv_loop_size"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/use_size"
                    android:maxLines="2"
                    style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
            </LinearLayout>
            <LinearLayout
                android:orientation="vertical"
                android:layout_width="@dimen/px_88"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/px_12"
                android:layout_marginEnd="@dimen/px_12"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/btn_open_audio">
                <com.hozon.theme.view.ThemeTextView
                    android:textColor="@color/neutral_color_text_primary"
                    android:ellipsize="end"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/eme_record_tx"
                    android:maxLines="2"
                    android:drawablePadding="@dimen/px_8"
                    android:drawableStart="@drawable/ic_eme_record_dot"
                    style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
                <com.hozonauto.widget.HorizontalProgress
                    android:id="@+id/hp_eme_progress"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="@dimen/px_4"
                    app:hz_maxProgress="100"
                    app:hz_progress="0"
                    app:hz_progress_mode="mode_long"/>
                <com.hozon.theme.view.ThemeTextView
                    android:textColor="@color/neutral_color_text_secondary"
                    android:ellipsize="end"
                    android:id="@+id/tv_eme_size"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/use_size"
                    android:maxLines="2"
                    style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
            </LinearLayout>
            <com.hozon.theme.view.ThemeTextView
                android:textColor="@color/neutral_color_text_primary"
                android:ellipsize="end"
                android:id="@+id/tv_format_tip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/px_12"
                android:text="@string/dvr_devices"
                android:maxLines="2"
                android:paddingEnd="@dimen/px_12"
                android:layout_marginStart="@dimen/px_12"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ll_loop_record"
                style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
            <com.hozon.theme.view.ThemeButton
                android:id="@+id/tv_format_usb"
                android:layout_width="@dimen/px_80"
                android:layout_height="@dimen/px_24"
                android:layout_marginTop="@dimen/px_4"
                android:text="@string/format"
                android:maxLines="2"
                android:contentDescription="@string/format"
                android:layout_marginStart="@dimen/px_12"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_format_tip"
                style="@style/HozonTheme.Widget.Button.Shadow"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>
</com.hozon.theme.view.ThemeFrameLayout>
