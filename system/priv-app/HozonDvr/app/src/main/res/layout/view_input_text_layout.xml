<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="@dimen/input_text_width"
    android:layout_height="wrap_content">
    <EditText
        android:gravity="center_vertical"
        android:id="@+id/et_input"
        android:background="@null"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxLines="1"
        android:paddingStart="@dimen/dp_6"
        android:paddingEnd="@dimen/dp_6"/>
    <ImageView
        android:layout_gravity="end|center_vertical"
        android:id="@+id/iv_input_visible"
        android:visibility="gone"
        android:layout_width="@dimen/right_icon_size"
        android:layout_height="@dimen/right_icon_size"
        android:src="@drawable/ic_visible_selector"
        android:scaleType="center"
        android:layout_marginEnd="@dimen/dp_21"/>
    <ImageView
        android:layout_gravity="end|center_vertical"
        android:id="@+id/iv_input_clear"
        android:visibility="gone"
        android:layout_width="@dimen/right_icon_size"
        android:layout_height="@dimen/right_icon_size"
        android:src="@drawable/ic_clean_up"
        android:scaleType="center"
        android:layout_marginEnd="@dimen/dp_6"/>
</LinearLayout>
