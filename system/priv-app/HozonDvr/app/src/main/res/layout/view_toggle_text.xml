<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_weight="1"
    android:stateListAnimator="@animator/anim_toggle_item_state_list">
    <TextView
        android:textSize="@dimen/body_medium"
        android:textColor="@color/color_toggle_text"
        android:ellipsize="end"
        android:gravity="center"
        android:layout_gravity="center"
        android:id="@+id/tv_toggle"
        android:paddingTop="@dimen/dp_4"
        android:paddingBottom="@dimen/dp_4"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:singleLine="true"
        android:drawablePadding="@dimen/dp_4"
        android:drawableTint="@color/color_toggle_text"
        android:drawableTintMode="src_in"/>
    <ImageView
        android:layout_gravity="center"
        android:id="@+id/iv_toggle"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:scaleType="fitCenter"
        android:tintMode="src_in"
        app:tint="@color/color_toggle_text"/>
</FrameLayout>
