<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="center"
    android:background="@drawable/bg_toast"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:maxWidth="@dimen/dp_328"
    android:minWidth="@dimen/dp_32">
    <TextView
        android:textSize="@dimen/button_small"
        android:textColor="@color/color_toast_text"
        android:ellipsize="end"
        android:gravity="center"
        android:layout_gravity="center"
        android:id="@+id/message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxLines="2"/>
</LinearLayout>
