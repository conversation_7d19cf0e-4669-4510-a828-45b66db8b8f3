<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.view.menu.ListMenuItemView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="?attr/listPreferredItemHeightSmall">
    <RelativeLayout
        android:layout_gravity="center_vertical"
        android:duplicateParentState="true"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="?attr/listPreferredItemPaddingLeft"
        android:layout_marginRight="?attr/listPreferredItemPaddingRight"
        android:layout_weight="1">
        <TextView
            android:textAppearance="?attr/textAppearanceListItemSmall"
            android:ellipsize="marquee"
            android:id="@+id/title"
            android:fadingEdge="horizontal"
            android:duplicateParentState="true"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:layout_alignParentLeft="true"
            android:layout_alignParentTop="true"/>
        <TextView
            android:textAppearance="?android:attr/textAppearanceSmall"
            android:id="@+id/shortcut"
            android:duplicateParentState="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:layout_below="@+id/title"
            android:layout_alignParentLeft="true"/>
    </RelativeLayout>
</androidx.appcompat.view.menu.ListMenuItemView>
