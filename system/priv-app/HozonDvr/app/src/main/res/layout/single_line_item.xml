<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:paddingLeft="@dimen/px_8"
    android:paddingTop="@dimen/px_8"
    android:paddingRight="@dimen/px_8"
    android:paddingBottom="@dimen/px_8"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <LinearLayout
                android:orientation="horizontal"
                android:id="@+id/left_container"
                android:paddingRight="@dimen/px_12"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true">
                <LinearLayout
                    android:layout_gravity="center_vertical"
                    android:orientation="horizontal"
                    android:id="@+id/title_inner_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <TextView
                        android:textColor="@color/neutral_color_text_primary"
                        android:ellipsize="end"
                        android:layout_gravity="center_vertical"
                        android:id="@+id/title"
                        android:clickable="false"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:singleLine="true"
                        style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
                    <ImageView
                        android:layout_gravity="center_vertical"
                        android:id="@+id/iv_middle_icon"
                        android:visibility="gone"
                        android:layout_width="@dimen/px_12"
                        android:layout_height="@dimen/px_12"
                        android:layout_marginLeft="@dimen/px_4"
                        android:scaleType="fitCenter"/>
                </LinearLayout>
                <TextView
                    android:textColor="@color/neutral_color_text_secondary"
                    android:ellipsize="end"
                    android:layout_gravity="center_vertical"
                    android:id="@+id/content"
                    android:visibility="gone"
                    android:clickable="false"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/px_30"
                    android:singleLine="true"
                    style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
            </LinearLayout>
            <LinearLayout
                android:gravity="end|center_vertical"
                android:orientation="horizontal"
                android:id="@+id/right_container"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@+id/left_container"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true">
                <TextView
                    android:textColor="@color/neutral_color_text_secondary"
                    android:id="@+id/tv_end"
                    android:visibility="gone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    style="@style/HozonTheme.TextAppearance.Body.Small.Regular.EN"/>
                <ImageView
                    android:layout_gravity="center"
                    android:id="@+id/iv_icon"
                    android:visibility="gone"
                    android:clickable="false"
                    android:layout_width="@dimen/px_16"
                    android:layout_height="@dimen/px_16"
                    android:layout_marginLeft="@dimen/px_4"
                    android:scaleType="fitCenter"/>
            </LinearLayout>
        </RelativeLayout>
        <TextView
            android:textColor="@color/neutral_color_text_secondary"
            android:ellipsize="end"
            android:id="@+id/tv_assist"
            android:visibility="gone"
            android:clickable="false"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_4"
            android:maxWidth="@dimen/px_180"
            android:includeFontPadding="false"
            style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
    </LinearLayout>
</LinearLayout>
