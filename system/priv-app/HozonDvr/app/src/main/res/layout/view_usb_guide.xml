<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.hozon.theme.view.ThemeImageView
        android:id="@+id/imv_guide"
        android:background="@drawable/usb_guide_icon_32"
        android:layout_width="@dimen/px_160"
        android:layout_height="@dimen/px_64"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.hozon.theme.view.ThemeTextView
        android:textColor="@color/neutral_color_text_primary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/px_8"
        android:text="@string/usb_guide_text"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/imv_guide"
        style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
</androidx.constraintlayout.widget.ConstraintLayout>
