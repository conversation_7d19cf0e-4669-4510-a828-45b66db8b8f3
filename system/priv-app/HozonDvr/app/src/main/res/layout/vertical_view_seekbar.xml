<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_img"
        android:layout_width="match_parent"
        android:layout_height="@dimen/px_24"
        android:src="@drawable/enable_button"/>
    <com.hozonauto.widget.verticalseekbar.LimitVerticalSeekBar
        android:id="@+id/vertical_seekbar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/px_138"
        android:layout_marginTop="@dimen/px_8"
        android:progressDrawable="@drawable/vertical_seekbar_drawable"
        android:thumb="@null"
        android:paddingStart="0dp"
        android:paddingEnd="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_img"/>
    <androidx.appcompat.widget.AppCompatTextView
        android:textSize="@dimen/font_32"
        android:textColor="@color/brand_color_on_brand_on_slider"
        android:gravity="center"
        android:id="@+id/tv_value"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/px_4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/vertical_seekbar"
        style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_mute"
        android:layout_width="@dimen/px_16"
        android:layout_height="@dimen/px_16"
        android:layout_marginBottom="@dimen/px_6"
        android:src="@drawable/vertical_volume"
        android:layout_centerHorizontal="true"
        app:layout_constraintBottom_toTopOf="@+id/tv_group_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>
    <androidx.appcompat.widget.AppCompatTextView
        android:textSize="@dimen/font_32"
        android:textColor="@color/brand_color_on_brand_on_slider"
        android:gravity="center"
        android:id="@+id/tv_group_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/px_4"
        android:layout_centerHorizontal="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
</merge>
