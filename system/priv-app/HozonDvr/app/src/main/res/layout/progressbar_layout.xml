<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <RelativeLayout
        android:id="@+id/rel_pop"
        android:visibility="invisible"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/px_4"
        android:alpha="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_pop"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_icon_seekbar_pop"/>
        <androidx.appcompat.widget.AppCompatTextView
            android:textSize="@dimen/px_8"
            android:textColor="@color/com_popup_text_color"
            android:id="@+id/tv_popprogress"
            android:paddingBottom="@dimen/px_4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"/>
    </RelativeLayout>
    <com.hozonauto.widget.progressbarlayout.HzWidenSeekBar
        android:id="@+id/hzSeekBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/rel_pop"/>
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_starticon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/px_4"
        android:layout_marginStart="@dimen/px_18"
        app:layout_constraintBottom_toBottomOf="@+id/hzSeekBar"
        app:layout_constraintStart_toStartOf="@+id/hzSeekBar"
        app:layout_constraintTop_toTopOf="@+id/hzSeekBar"/>
    <androidx.appcompat.widget.AppCompatTextView
        android:textSize="@dimen/px_7"
        android:textColor="@color/com_slider_normal_text_color"
        android:id="@+id/tv_start_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/px_4"
        android:layout_marginStart="@dimen/px_5"
        app:layout_constraintBottom_toBottomOf="@+id/hzSeekBar"
        app:layout_constraintStart_toEndOf="@+id/iv_starticon"
        app:layout_constraintTop_toTopOf="@+id/hzSeekBar"/>
    <androidx.appcompat.widget.AppCompatTextView
        android:textSize="@dimen/px_7"
        android:textColor="@color/com_slider_normal_text_color"
        android:id="@+id/tv_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/px_4"
        android:layout_marginEnd="@dimen/px_18"
        app:layout_constraintBottom_toBottomOf="@+id/hzSeekBar"
        app:layout_constraintEnd_toEndOf="@+id/hzSeekBar"
        app:layout_constraintTop_toTopOf="@+id/hzSeekBar"/>
</androidx.constraintlayout.widget.ConstraintLayout>
