<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:background="@drawable/neutral_color_background_surface_primary"
    android:paddingTop="@dimen/px_2"
    android:paddingBottom="@dimen/px_2"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingStart="@dimen/px_2"
    android:paddingEnd="@dimen/px_2">
    <androidx.appcompat.widget.AppCompatImageButton
        android:id="@+id/stepper_minus"
        android:background="@drawable/stepper_minus_selector"
        android:layout_width="@dimen/px_20"
        android:layout_height="@dimen/px_20"/>
    <RelativeLayout
        android:layout_width="@dimen/px_50"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="@dimen/px_4">
        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true">
            <skin.support.widget.SkinCompatTextView
                android:textColor="@color/neutral_color_text_primary"
                android:layout_gravity="center_vertical"
                android:id="@+id/stepper_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="4档"
                style="@style/HozonTheme.TextAppearance.Body.Medium.Regular.EN"/>
            <skin.support.widget.SkinCompatTextView
                android:textColor="@color/neutral_color_text_secondary"
                android:layout_gravity="center_vertical"
                android:id="@+id/stepper_text_unit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="km/h"
                style="@style/HozonTheme.TextAppearance.Caption.Small.Regular"/>
        </LinearLayout>
    </RelativeLayout>
    <androidx.appcompat.widget.AppCompatImageButton
        android:id="@+id/stepper_plus"
        android:background="@drawable/stepper_plus_selector"
        android:layout_width="@dimen/px_20"
        android:layout_height="@dimen/px_20"/>
</LinearLayout>
