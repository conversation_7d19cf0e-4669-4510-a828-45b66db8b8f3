<?xml version="1.0" encoding="utf-8"?>
<com.hozon.theme.view.ThemeConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_main"
    android:background="@drawable/neutral_color_background_system_popup"
    android:layout_width="match_parent"
    android:layout_height="@dimen/px_196">
    <com.hozonauto.widget.SwitchButton
        android:layout_gravity="center_vertical"
        android:id="@+id/sw_open"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/px_18"
        android:contentDescription="@string/notification_dvr_loop_state_title_cd"
        android:layout_marginStart="@dimen/px_12"
        app:hz_loading_timeout="3000"
        app:hz_show_loading="false"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/SwitchButtonDefaultStyle"/>
    <com.hozon.theme.view.ThemeTextView
        android:textColor="@color/neutral_color_text_primary"
        android:ellipsize="end"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="@dimen/px_58"
        android:text="@string/notification_dvr_loop_state_title"
        android:maxLines="2"
        android:layout_marginStart="@dimen/px_8"
        app:layout_constraintBottom_toBottomOf="@+id/sw_open"
        app:layout_constraintStart_toEndOf="@+id/sw_open"
        app:layout_constraintTop_toTopOf="@+id/sw_open"
        style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
    <com.hozon.theme.view.ThemeButton
        android:id="@+id/cb_un_mount_usb"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/px_24"
        android:layout_marginTop="@dimen/px_12"
        android:text="@string/un_mount_usb"
        android:maxLines="2"
        android:contentDescription="@string/un_mount_usb"
        android:drawableStart="@drawable/ic_usb"
        android:layout_marginStart="@dimen/px_8"
        android:layout_marginEnd="@dimen/px_44"
        android:drawableTint="@null"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/HozonTheme.Widget.Button.WeakLevel"/>
    <com.hozon.theme.view.ThemeButton
        android:id="@+id/btn_dvr_setting"
        android:background="@drawable/icon_dvr_setting"
        android:layout_width="@dimen/px_24"
        android:layout_height="@dimen/px_24"
        android:layout_marginTop="@dimen/px_12"
        android:contentDescription="@string/dvr_settings_cd"
        android:layout_marginEnd="@dimen/px_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <RelativeLayout
        android:id="@+id/rl_preview_bg"
        android:background="#000000"
        android:layout_width="@dimen/px_192"
        android:layout_height="@dimen/px_108"
        android:layout_marginTop="@dimen/px_44"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <TextureView
            android:id="@+id/camera_preview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
        <ImageView
            android:background="@drawable/record_mask"
            android:layout_width="match_parent"
            android:layout_height="@dimen/px_22"/>
        <ImageView
            android:id="@+id/imv_dot"
            android:background="@drawable/ic_eme_record_dot"
            android:visibility="gone"
            android:layout_width="@dimen/px_4"
            android:layout_height="@dimen/px_4"
            android:layout_marginTop="@dimen/px_11"
            android:layout_marginStart="@dimen/px_8"/>
        <com.hozon.theme.view.ThemeTextView
            android:textColor="@color/white_common"
            android:ellipsize="end"
            android:id="@+id/tv_record_type"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_8"
            android:maxWidth="@dimen/px_80"
            android:text="@string/emergency_record"
            android:maxLines="2"
            android:layout_marginStart="@dimen/px_16"
            style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
        <com.hozon.theme.view.ThemeTextView
            android:textColor="@color/white_common"
            android:gravity="center"
            android:id="@+id/tv_no_record_type"
            android:background="#8f070a17"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:text="@string/dvr_not_turned_on"
            android:paddingHorizontal="@dimen/px_24"
            style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
    </RelativeLayout>
    <com.hozon.theme.view.ThemeButton
        android:id="@+id/cb_eme"
        android:layout_width="@dimen/px_92"
        android:layout_height="@dimen/px_24"
        android:layout_marginTop="@dimen/px_8"
        android:text="@string/emergency_record"
        android:maxLines="2"
        android:contentDescription="@string/emergency_record"
        android:layout_marginStart="@dimen/px_12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rl_preview_bg"
        style="@style/HozonTheme.Widget.Button.Warning"/>
    <com.hozon.theme.view.ThemeButton
        android:id="@+id/cb_to_gallery"
        android:layout_width="@dimen/px_92"
        android:layout_height="@dimen/px_24"
        android:layout_marginTop="@dimen/px_8"
        android:text="@string/to_gallery"
        android:maxLines="2"
        android:contentDescription="@string/to_gallery"
        android:layout_marginStart="@dimen/px_8"
        app:layout_constraintStart_toEndOf="@+id/cb_eme"
        app:layout_constraintTop_toBottomOf="@+id/rl_preview_bg"
        style="@style/HozonTheme.Widget.Button.WeakLevel"/>
</com.hozon.theme.view.ThemeConstraintLayout>
