<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rootLayout"
    android:paddingBottom="@dimen/px_8"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/layout_top"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/px_8"
        app:layout_constraintBottom_toTopOf="@+id/scrollView"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <RelativeLayout
            android:orientation="horizontal"
            android:id="@+id/layout_top_1"
            android:paddingLeft="@dimen/px_8"
            android:paddingRight="@dimen/px_8"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <LinearLayout
                android:orientation="horizontal"
                android:id="@+id/layout_title_left_container"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"/>
            <RelativeLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/px_8"
                android:layout_marginRight="@dimen/px_8"
                android:layout_toLeftOf="@+id/layout_title_right_container"
                android:layout_toRightOf="@+id/layout_title_left_container">
                <skin.support.widget.SkinCompatTextView
                    android:textColor="@color/neutral_color_text_primary"
                    android:ellipsize="end"
                    android:id="@+id/tv_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:singleLine="true"
                    android:includeFontPadding="false"
                    android:layout_centerHorizontal="true"
                    style="@style/HozonTheme.TextAppearance.Body.Medium.Regular"/>
                <skin.support.widget.SkinCompatTextView
                    android:textColor="@color/neutral_color_text_secondary"
                    android:ellipsize="end"
                    android:id="@+id/tv_subTitle"
                    android:visibility="gone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/px_8"
                    android:maxLines="2"
                    android:includeFontPadding="false"
                    android:layout_below="@+id/tv_title"
                    android:layout_centerHorizontal="true"
                    style="@style/HozonTheme.TextAppearance.Body.Small.Regular"/>
            </RelativeLayout>
            <LinearLayout
                android:orientation="horizontal"
                android:id="@+id/layout_title_right_container"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"/>
        </RelativeLayout>
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:id="@+id/layout_title_below_container"
            android:paddingLeft="@dimen/px_16"
            android:paddingRight="@dimen/px_16"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_8"/>
    </LinearLayout>
    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:paddingLeft="@dimen/px_16"
        android:paddingRight="@dimen/px_16"
        android:scrollbars="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/px_8"
        android:layout_centerVertical="true"
        android:fadeScrollbars="true"
        app:layout_constraintBottom_toTopOf="@+id/layout_bottom_container"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layout_top">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <LinearLayout
                android:orientation="vertical"
                android:id="@+id/layout_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/layout_bottom_container"
        android:paddingLeft="@dimen/px_16"
        android:paddingRight="@dimen/px_16"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/px_8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/scrollView"/>
    <ImageView
        android:layout_gravity="top|left"
        android:id="@+id/iv_back"
        android:visibility="gone"
        android:layout_width="@dimen/px_16"
        android:layout_height="@dimen/px_16"
        android:layout_marginLeft="@dimen/px_4"
        android:layout_marginTop="@dimen/px_4"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
