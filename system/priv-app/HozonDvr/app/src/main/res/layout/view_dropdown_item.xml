<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minWidth="@dimen/dp_96"
    android:minHeight="@dimen/dp_24">
    <com.hozon.widget.MarqueeTextView
        android:textSize="@dimen/dropdown_text_size"
        android:textColor="@color/color_dropdown_item_text"
        android:ellipsize="end"
        android:gravity="center"
        android:layout_gravity="center"
        android:id="@+id/tv_dropdown_item"
        android:background="@drawable/bg_dropdown_list_item"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:adjustViewBounds="true"
        android:minWidth="@dimen/dp_96"
        android:singleLine="true"
        android:drawablePadding="@dimen/dp_4"
        android:paddingStart="@dimen/dp_8"
        android:paddingEnd="@dimen/dp_8"/>
</FrameLayout>
