<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="center"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:gravity="center"
        android:orientation="horizontal"
        android:id="@+id/fl_hud_base"
        android:background="@drawable/bg_hud"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_44"
        android:minHeight="@dimen/dp_52"
        android:paddingStart="@dimen/dp_20"
        android:paddingEnd="@dimen/dp_20">
        <ImageView
            android:layout_gravity="center_vertical"
            android:id="@+id/iv_hud_icon"
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:src="@drawable/ic_loading"/>
        <TextView
            android:textSize="@dimen/body_medium"
            android:textColor="@color/neutral_color_text_text5"
            android:gravity="center_vertical"
            android:layout_gravity="center_vertical"
            android:id="@+id/tv_hud_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="2"
            android:layout_marginStart="@dimen/dp_8"/>
    </LinearLayout>
</LinearLayout>
