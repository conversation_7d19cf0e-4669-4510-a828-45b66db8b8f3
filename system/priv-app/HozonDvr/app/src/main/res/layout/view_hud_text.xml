<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="center"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:gravity="center"
        android:orientation="vertical"
        android:id="@+id/fl_hud_base"
        android:background="@drawable/bg_hud"
        android:layout_width="@dimen/hud_width"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/hud_height">
        <TextView
            android:textSize="@dimen/font_size_6"
            android:textColor="@color/text_color_gray_100"
            android:ellipsize="end"
            android:gravity="center_horizontal"
            android:layout_gravity="center_horizontal"
            android:id="@+id/tv_hud_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_8"
            android:paddingStart="@dimen/dp_12"
            android:paddingEnd="@dimen/dp_12"/>
        <TextView
            android:textSize="@dimen/font_size_5"
            android:textColor="@color/text_color_gray_60"
            android:ellipsize="end"
            android:gravity="center_horizontal"
            android:layout_gravity="center_horizontal"
            android:id="@+id/tv_hud_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_6"
            android:layout_marginBottom="@dimen/dp_8"
            android:paddingStart="@dimen/dp_12"
            android:paddingEnd="@dimen/dp_12"/>
    </LinearLayout>
</LinearLayout>
