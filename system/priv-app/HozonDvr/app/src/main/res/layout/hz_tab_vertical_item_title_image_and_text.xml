<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <ImageView
        android:id="@+id/ivTabImage"
        android:layout_width="@dimen/px_14"
        android:layout_height="@dimen/px_14"
        android:scaleType="centerInside"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="@dimen/px_8"
        android:textColor="#ff000000"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:id="@+id/tvTabText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:maxLines="1"
        android:includeFontPadding="false"
        android:layout_marginStart="@dimen/px_6"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/ivTabImage"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
