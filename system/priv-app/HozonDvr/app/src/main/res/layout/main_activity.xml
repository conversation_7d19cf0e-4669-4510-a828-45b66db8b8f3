<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:background="#aaaaaa"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <TextureView
        android:id="@+id/textureView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <Button
        android:textSize="36sp"
        android:id="@+id/start"
        android:background="@color/purple_500"
        android:layout_width="200dp"
        android:layout_height="80dp"
        android:layout_marginTop="20dp"
        android:text="START"
        android:layout_alignStart="@+id/stop"/>
    <Button
        android:textSize="36sp"
        android:id="@+id/stop"
        android:background="@color/purple_500"
        android:layout_width="200dp"
        android:layout_height="80dp"
        android:layout_marginTop="20dp"
        android:text="STOP"
        android:layout_below="@+id/start"/>
    <Button
        android:textSize="36sp"
        android:id="@+id/test"
        android:background="@color/purple_500"
        android:layout_width="200dp"
        android:layout_height="80dp"
        android:layout_marginTop="20dp"
        android:text="紧急触发"
        android:layout_below="@+id/stop"
        android:layout_alignEnd="@+id/stop"/>
    <androidx.appcompat.widget.SwitchCompat
        android:textSize="36sp"
        android:textColor="@color/white"
        android:id="@+id/loop_record"
        android:background="@color/purple_500"
        android:layout_width="200dp"
        android:layout_height="80dp"
        android:layout_marginTop="20dp"
        android:text="循环开关"
        android:layout_marginStart="20dp"
        android:layout_toEndOf="@+id/start"/>
    <androidx.appcompat.widget.SwitchCompat
        android:textSize="36sp"
        android:textColor="@color/white"
        android:id="@+id/emergency_record"
        android:background="@color/purple_500"
        android:layout_width="200dp"
        android:layout_height="80dp"
        android:layout_marginTop="20dp"
        android:text="紧急开关"
        android:layout_below="@+id/loop_record"
        android:layout_marginStart="20dp"
        android:layout_toEndOf="@+id/start"/>
    <androidx.appcompat.widget.SwitchCompat
        android:textSize="36sp"
        android:textColor="@color/white"
        android:id="@+id/audio_record"
        android:background="@color/purple_500"
        android:layout_width="200dp"
        android:layout_height="80dp"
        android:layout_marginTop="20dp"
        android:text="声音开关"
        android:layout_below="@+id/emergency_record"
        android:layout_marginStart="20dp"
        android:layout_toEndOf="@+id/start"/>
    <androidx.appcompat.widget.SwitchCompat
        android:textSize="36sp"
        android:textColor="@color/white"
        android:id="@+id/location_record"
        android:background="@color/purple_500"
        android:layout_width="200dp"
        android:layout_height="80dp"
        android:layout_marginTop="20dp"
        android:text="定位开关"
        android:layout_below="@+id/audio_record"
        android:layout_marginStart="20dp"
        android:layout_toEndOf="@+id/start"/>
    <androidx.appcompat.widget.SwitchCompat
        android:textSize="36sp"
        android:textColor="@color/white"
        android:id="@+id/active_state"
        android:background="@color/purple_500"
        android:layout_width="200dp"
        android:layout_height="80dp"
        android:layout_marginTop="20dp"
        android:text="绕过激活"
        android:layout_below="@+id/location_record"
        android:layout_marginStart="20dp"
        android:layout_toEndOf="@+id/start"/>
    <androidx.appcompat.widget.SwitchCompat
        android:textSize="36sp"
        android:textColor="@color/white"
        android:id="@+id/power_state"
        android:background="@color/purple_500"
        android:layout_width="200dp"
        android:layout_height="80dp"
        android:layout_marginTop="20dp"
        android:text="电源状态"
        android:layout_below="@+id/active_state"
        android:layout_marginStart="20dp"
        android:layout_toEndOf="@+id/start"/>
    <RadioGroup
        android:id="@+id/loop_time"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginStart="20dp"
        android:layout_toEndOf="@+id/loop_record">
        <RadioButton
            android:textSize="36sp"
            android:textColor="@color/white"
            android:id="@+id/min1"
            android:background="@color/purple_500"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:text="时长1分钟"/>
        <RadioButton
            android:textSize="36sp"
            android:textColor="@color/white"
            android:id="@+id/min3"
            android:background="@color/purple_500"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_marginTop="20dp"
            android:text="时长3分钟"/>
        <RadioButton
            android:textSize="36sp"
            android:textColor="@color/white"
            android:id="@+id/min5"
            android:background="@color/purple_500"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_marginTop="20dp"
            android:text="时长5分钟"/>
    </RadioGroup>
    <TextView
        android:textSize="46sp"
        android:textColor="@color/teal_700"
        android:id="@+id/dvr_state_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/app_name"
        android:layout_centerInParent="true"/>
</RelativeLayout>
