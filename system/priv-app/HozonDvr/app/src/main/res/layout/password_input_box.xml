<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/px_24">
        <LinearLayout
            android:orientation="horizontal"
            android:id="@+id/password_edit_bg"
            android:background="@drawable/bg_edittext_has_line_normal"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/px_24">
            <RelativeLayout
                android:orientation="horizontal"
                android:id="@+id/layout_inter"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginLeft="@dimen/px_8"
                android:layout_marginRight="@dimen/px_2"
                android:weightSum="1"
                android:layout_toLeftOf="@+id/iv_mask">
                <EditText
                    android:textColor="@color/neutral_color_text_primary"
                    android:textColorHint="@color/neutral_color_text_tertiary"
                    android:gravity="center_vertical"
                    android:id="@+id/et_password"
                    android:background="@color/transparent"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginRight="@dimen/px_8"
                    android:hint="请输入密码"
                    android:singleLine="true"
                    android:layout_toLeftOf="@+id/iv_clear"
                    android:layout_centerVertical="true"
                    android:textCursorDrawable="@drawable/cursor_selector"
                    style="@style/HozonTheme.TextAppearance.Button.Medium.Regular"/>
                <ImageView
                    android:id="@+id/iv_clear"
                    android:visibility="invisible"
                    android:layout_width="@dimen/px_16"
                    android:layout_height="@dimen/px_16"
                    android:src="@drawable/app_icon_close"
                    android:scaleType="fitCenter"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"/>
            </RelativeLayout>
            <skin.support.widget.SkinCompatCheckBox
                android:id="@+id/iv_mask"
                android:background="@drawable/dialog_password_checkbox_selector"
                android:layout_width="@dimen/px_18"
                android:layout_height="@dimen/px_18"
                android:layout_marginRight="@dimen/px_7"
                android:checked="true"
                android:button="@null"
                android:scaleType="fitCenter"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"/>
        </RelativeLayout>
    </FrameLayout>
    <TextView
        android:textSize="@dimen/font_size_8"
        android:textColor="@color/function_color_error_text_default"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:layout_gravity="center_horizontal"
        android:id="@+id/tv_warn_ocuppy"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/px_8"
        android:text=" "
        android:maxLines="1"/>
    <TextView
        android:textSize="@dimen/font_size_8"
        android:textColor="@color/function_color_error_text_default"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:layout_gravity="center_horizontal"
        android:id="@+id/tv_warn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/px_8"
        android:maxLines="2"/>
</LinearLayout>
