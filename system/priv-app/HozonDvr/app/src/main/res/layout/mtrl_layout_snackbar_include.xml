<?xml version="1.0" encoding="utf-8"?>
<view xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_gravity="bottom"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    class="com.google.android.material.snackbar.SnackbarContentLayout">
    <TextView
        android:layout_gravity="start|center_vertical"
        android:id="@+id/snackbar_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        style="?attr/snackbarTextViewStyle"/>
    <Button
        android:layout_gravity="end|center_vertical"
        android:id="@+id/snackbar_action"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="48dp"
        style="?attr/snackbarButtonStyle"/>
</view>
