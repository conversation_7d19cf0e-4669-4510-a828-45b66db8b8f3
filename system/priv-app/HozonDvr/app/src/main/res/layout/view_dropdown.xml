<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="center"
    android:background="@drawable/bg_hud"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:minHeight="@dimen/dp_72"
    android:animateLayoutChanges="true">
    <LinearLayout
        android:gravity="center_horizontal"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:id="@+id/ll_root"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dialog_background_transparent_border_vertical"
        android:layout_marginBottom="@dimen/dialog_background_transparent_border_vertical"
        android:maxWidth="@dimen/dp_240"
        android:minWidth="@dimen/dp_96">
        <TextView
            android:textSize="@dimen/body_medium"
            android:textColor="@color/neutral_color_text_text5"
            android:ellipsize="end"
            android:gravity="center"
            android:layout_gravity="center_horizontal"
            android:id="@+id/tv_dropdown_title"
            android:clickable="false"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dropdown_item_height"
            android:singleLine="true"
            android:soundEffectsEnabled="false"
            android:paddingStart="@dimen/dp_8"
            android:paddingEnd="@dimen/dp_8"/>
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:animateLayoutChanges="true">
            <androidx.recyclerview.widget.RecyclerView
                android:layout_gravity="center"
                android:id="@+id/ls_dropdown"
                android:scrollbars="none"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
            <LinearLayout
                android:layout_gravity="center"
                android:orientation="vertical"
                android:id="@+id/ll_no_item"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:animateLayoutChanges="true">
                <ImageView
                    android:layout_gravity="center_horizontal"
                    android:layout_width="@dimen/dp_16"
                    android:layout_height="@dimen/dp_16"
                    android:src="@drawable/ic_popup_airconditioner"/>
                <TextView
                    android:textSize="@dimen/dropdown_text_size"
                    android:textColor="@color/text_color_gray_60"
                    android:layout_gravity="center_horizontal"
                    android:id="@+id/tv_no_item"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_8"
                    android:text="@string/no_share_target"/>
            </LinearLayout>
        </FrameLayout>
    </LinearLayout>
</FrameLayout>
