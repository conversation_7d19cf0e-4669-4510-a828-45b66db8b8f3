<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.textfield.TextInputLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:errorIconDrawable="@null"
    app:hintEnabled="false"
    app:shapeAppearance="?attr/shapeAppearanceMediumComponent"
    app:shapeAppearanceOverlay="@null"
    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
    <EditText
        android:id="@+id/material_timepicker_edit_text"
        android:paddingBottom="6dp"
        android:layout_width="96dp"
        android:layout_height="80dp"
        android:textCursorDrawable="@drawable/material_cursor_drawable"
        style="@style/Widget.MaterialComponents.TimePicker.Display.TextInputEditText"/>
    <TextView
        android:textAppearance="?attr/textAppearanceCaption"
        android:id="@+id/material_label"
        android:focusable="false"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:labelFor="@+id/material_timepicker_edit_text"/>
</com.google.android.material.textfield.TextInputLayout>
