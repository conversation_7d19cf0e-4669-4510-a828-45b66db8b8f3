<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:id="@+id/fl_hud_base"
    android:background="@drawable/bg_hud"
    android:layout_width="@dimen/hud_width"
    android:layout_height="@dimen/hud_height"
    android:minWidth="@dimen/hud_width">
    <TextView
        android:textSize="@dimen/body_medium"
        android:textColor="@color/neutral_color_text_text5"
        android:gravity="center"
        android:id="@+id/tv_hud_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:text="@string/volume"
        android:maxLines="1"/>
    <ImageView
        android:id="@+id/iv_hud_icon"
        android:layout_width="@dimen/hud_icon_size"
        android:layout_height="@dimen/hud_icon_size"
        android:layout_marginTop="@dimen/dp_4"
        android:src="@drawable/ic_hud_sound_mute"
        android:scaleType="centerInside"/>
    <TextView
        android:textAppearance="@style/HozonTheme.TextAppearance.Body.Small.Regular.EN"
        android:textColor="@color/neutral_color_text_text5"
        android:layout_gravity="center_horizontal"
        android:id="@+id/tv_hud_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_2"
        android:text="@string/zero_percent"/>
    <com.hozon.widget.ProgressBar
        android:layout_gravity="bottom"
        android:id="@+id/pb_hud_progress"
        android:focusable="false"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        android:layout_marginBottom="@dimen/dp_14"
        android:progress="0"
        android:progressDrawable="@drawable/bg_progress_bar"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_10"
        app:draggable="false"
        app:thumb="@drawable/ic_transparent_thumb"
        style="?android:attr/progressBarStyleHorizontal"/>
</LinearLayout>
