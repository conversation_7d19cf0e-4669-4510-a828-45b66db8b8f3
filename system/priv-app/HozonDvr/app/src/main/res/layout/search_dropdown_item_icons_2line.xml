<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/list_item_height"
    android:paddingStart="@dimen/dropdown_item_text_padding_left"
    android:paddingEnd="4dp">
    <ImageView
        android:id="@+id/icon1"
        android:visibility="invisible"
        android:layout_width="@dimen/dropdown_item_icon_width"
        android:layout_height="48dp"
        android:scaleType="centerInside"
        android:layout_alignParentTop="true"
        android:layout_alignParentBottom="true"
        android:layout_alignParentStart="true"/>
    <ImageView
        android:id="@+id/edit_query"
        android:background="?attr/selectableItemBackground"
        android:visibility="gone"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:scaleType="centerInside"
        android:layout_alignParentTop="true"
        android:layout_alignParentBottom="true"
        android:layout_alignParentEnd="true"/>
    <ImageView
        android:id="@+id/icon2"
        android:visibility="gone"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:scaleType="centerInside"
        android:layout_alignParentTop="true"
        android:layout_alignParentBottom="true"
        android:layout_alignWithParentIfMissing="true"
        android:layout_toStartOf="@+id/edit_query"/>
    <TextView
        android:textAppearance="?attr/textAppearanceSearchResultSubtitle"
        android:gravity="top"
        android:id="@+id/text2"
        android:paddingBottom="4dp"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="29dp"
        android:singleLine="true"
        android:layout_alignParentBottom="true"
        android:layout_alignWithParentIfMissing="true"
        android:layout_toStartOf="@+id/icon2"
        android:layout_toEndOf="@+id/icon1"
        style="?android:attr/dropDownItemStyle"/>
    <TextView
        android:textAppearance="?attr/textAppearanceSearchResultTitle"
        android:id="@+id/text1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:layout_above="@+id/text2"
        android:layout_centerVertical="true"
        android:layout_toStartOf="@+id/icon2"
        android:layout_toEndOf="@+id/icon1"
        style="?android:attr/dropDownItemStyle"/>
</RelativeLayout>
