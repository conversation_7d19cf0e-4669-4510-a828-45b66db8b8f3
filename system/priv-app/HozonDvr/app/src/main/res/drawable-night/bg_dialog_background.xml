<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android"
    android:paddingLeft="@dimen/dp_8"
    android:paddingTop="@dimen/dp_8"
    android:paddingRight="@dimen/dp_8"
    android:paddingBottom="@dimen/dp_8">
    <item
        android:left="@dimen/dialog_background_transparent_border_horizontal"
        android:top="@dimen/dialog_background_transparent_border_vertical"
        android:right="@dimen/dialog_background_transparent_border_horizontal"
        android:bottom="@dimen/dialog_background_transparent_border_vertical">
        <shape android:shape="rectangle">
            <solid android:color="@color/neutral_color_background_bg4"/>
            <corners android:radius="@dimen/radius_4"/>
            <padding
                android:left="@dimen/dialog_background_transparent_border_horizontal"
                android:top="@dimen/dialog_background_transparent_border_vertical"
                android:right="@dimen/dialog_background_transparent_border_horizontal"
                android:bottom="@dimen/dialog_background_transparent_border_vertical"/>
        </shape>
    </item>
</layer-list>
