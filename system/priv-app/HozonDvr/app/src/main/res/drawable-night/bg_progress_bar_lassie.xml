<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item
        android:gravity="center_vertical"
        android:id="@android:id/background"
        android:height="@dimen/progress_bar_drawable_line_height">
        <shape android:shape="rectangle">
            <size android:height="@dimen/progress_bar_drawable_line_height"/>
            <solid android:color="@color/neutral_color_background_surface_secondary"/>
            <corners android:radius="@dimen/dp_3"/>
        </shape>
    </item>
    <item
        android:gravity="center_vertical"
        android:id="@android:id/secondaryProgress"
        android:height="@dimen/progress_bar_drawable_line_height">
        <clip>
            <shape android:shape="rectangle">
                <size android:height="@dimen/progress_bar_drawable_line_height"/>
                <solid android:color="@color/color_transparent"/>
                <corners android:radius="@dimen/dp_3"/>
            </shape>
        </clip>
    </item>
    <item
        android:gravity="center_vertical"
        android:id="@android:id/progress"
        android:height="@dimen/progress_bar_drawable_line_height">
        <clip>
            <shape>
                <gradient
                    android:startColor="#6567fb"
                    android:endColor="#7854fb"
                    android:angle="315"
                    android:type="linear"/>
                <corners android:radius="@dimen/dp_3"/>
                <size android:height="@dimen/progress_bar_drawable_line_height"/>
            </shape>
        </clip>
    </item>
</layer-list>
