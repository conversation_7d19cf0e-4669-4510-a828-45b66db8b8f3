<?xml version="1.0" encoding="utf-8"?>
<animated-rotate xmlns:android="http://schemas.android.com/apk/res/android"
    android:pivotX="50%"
    android:pivotY="50%">
    <vector xmlns:aapt="http://schemas.android.com/aapt"
        android:height="@dimen/dp_16"
        android:width="@dimen/dp_16"
        android:viewportWidth="8"
        android:viewportHeight="8">
        <path
            android:fillColor="#00000000"
            android:pathData="M6.12609,1.21953C5.53672,0.76819 4.79968,0.5 4,0.5C2.067,0.5 0.5,2.067 0.5,4C0.5,5.933 2.067,7.5 4,7.5C5.933,7.5 7.5,5.933 7.5,4"
            android:strokeColor="@drawable/_ic_loading_lassie__0_res_0x7f07000b"
            android:strokeWidth="1"
            android:strokeLineCap="round"/>
    </vector>
</animated-rotate>
