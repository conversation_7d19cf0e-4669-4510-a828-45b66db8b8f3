<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item
        android:gravity="fill_vertical|center_horizontal"
        android:id="@android:id/background"
        android:height="@dimen/dp_1080"
        android:width="@dimen/dp_4">
        <shape android:shape="rectangle">
            <solid android:color="@color/neutral_color_background_surface_secondary"/>
            <corners android:radius="@dimen/dp_3"/>
        </shape>
    </item>
    <item
        android:gravity="fill_vertical|center_horizontal"
        android:id="@android:id/progress"
        android:width="@dimen/dp_4">
        <layer-list>
            <item>
                <clip
                    android:gravity="bottom"
                    android:clipOrientation="vertical">
                    <shape>
                        <gradient
                            android:startColor="#6567fb"
                            android:endColor="#7854fb"
                            android:angle="315"
                            android:type="linear"/>
                        <corners android:radius="@dimen/dp_3"/>
                    </shape>
                </clip>
            </item>
        </layer-list>
    </item>
</layer-list>
