<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item
        android:height="@dimen/input_text_height"
        android:drawable="@drawable/neutral_color_background_surface_primary_lassie"/>
    <item android:height="@dimen/input_text_height">
        <shape android:shape="rectangle">
            <stroke
                android:width="@dimen/dp_0.4"
                android:color="@color/function_color_error_text_default_lassie"/>
            <corners android:radius="@dimen/edittext_background_radius"/>
        </shape>
    </item>
</layer-list>
