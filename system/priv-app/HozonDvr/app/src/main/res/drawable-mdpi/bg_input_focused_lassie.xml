<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:drawable="@drawable/neutral_color_background_surface_primary_lassie"/>
    <item>
        <shape
            android:layout_height="match_parent"
            android:shape="rectangle">
            <stroke
                android:width="@dimen/dp_0.4"
                android:color="@color/function_color_link_default_lassie"/>
            <corners android:radius="@dimen/edittext_background_radius"/>
        </shape>
    </item>
</layer-list>
