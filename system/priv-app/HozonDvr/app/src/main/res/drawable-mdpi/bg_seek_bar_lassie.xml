<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item
        android:id="@android:id/background"
        android:height="@dimen/dp_28"
        android:drawable="@drawable/neutral_color_background_surface_secondary_lassie"/>
    <item
        android:gravity="center_vertical"
        android:id="@android:id/progress"
        android:height="@dimen/dp_26"
        android:top="@dimen/dp_1"
        android:bottom="@dimen/dp_1"
        android:start="@dimen/dp_1"
        android:end="@dimen/dp_1">
        <layer-list>
            <item>
                <clip>
                    <shape android:shape="rectangle">
                        <solid android:color="@color/neutral_color_background_surface_slider_lassie"/>
                        <corners android:radius="@dimen/dp_3"/>
                        <size android:height="@dimen/dp_26"/>
                    </shape>
                </clip>
            </item>
        </layer-list>
    </item>
</layer-list>
