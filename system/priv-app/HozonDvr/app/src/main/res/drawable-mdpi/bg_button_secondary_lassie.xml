<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item
        android:left="@dimen/dp_0.4"
        android:top="@dimen/dp_0.4"
        android:right="@dimen/dp_0.4"
        android:bottom="@dimen/dp_0.4">
        <selector>
            <item
                android:state_enabled="true"
                android:state_pressed="false"
                android:drawable="@drawable/neutral_color_background_surface_secondary_lassie"/>
            <item
                android:state_enabled="true"
                android:state_pressed="true"
                android:drawable="@drawable/neutral_color_background_surface_primary_lassie"/>
            <item
                android:state_enabled="false"
                android:drawable="@drawable/neutral_color_background_surface_secondary_lassie"/>
        </selector>
    </item>
    <item>
        <shape android:shape="rectangle">
            <stroke
                android:width="@dimen/dp_0.4"
                android:color="@color/neutral_color_stoke_primary_lassie"/>
            <corners android:radius="@dimen/dp_4"/>
        </shape>
    </item>
</layer-list>
