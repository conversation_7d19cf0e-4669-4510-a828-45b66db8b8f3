<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:id="@+id/ll_menu"
    android:background="@drawable/bg_app_menu"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:maxWidth="@dimen/dp_98"
    android:minWidth="@dimen/dp_72">
    <ImageView
        android:textAppearance="@style/HozonTheme.TextAppearance.Headline.Small.Heavy"
        android:textColor="@color/neutral_color_text_text5"
        android:id="@+id/tv_back"
        android:visibility="invisible"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_12"
        android:src="@drawable/ic_common_close"
        android:layout_marginStart="@dimen/dp_12"/>
    <ScrollView
        android:id="@+id/app_scroll_view"
        android:scrollbars="none"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:orientation="vertical"
            android:paddingTop="@dimen/dp_10"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <include
                android:id="@+id/appmenu_item0"
                layout="@layout/view_app_menu_item"/>
            <include
                android:id="@+id/appmenu_item1"
                layout="@layout/view_app_menu_item"/>
            <include
                android:id="@+id/appmenu_item2"
                layout="@layout/view_app_menu_item"/>
            <include
                android:id="@+id/appmenu_item3"
                layout="@layout/view_app_menu_item"/>
            <include
                android:id="@+id/appmenu_item4"
                layout="@layout/view_app_menu_item"/>
            <include
                android:id="@+id/appmenu_item5"
                layout="@layout/view_app_menu_item"/>
            <include
                android:id="@+id/appmenu_item6"
                layout="@layout/view_app_menu_item"/>
            <include
                android:id="@+id/appmenu_item7"
                layout="@layout/view_app_menu_item"/>
            <include
                android:id="@+id/appmenu_item8"
                layout="@layout/view_app_menu_item"/>
            <include
                android:id="@+id/appmenu_item9"
                layout="@layout/view_app_menu_item"/>
            <include
                android:id="@+id/appmenu_item10"
                layout="@layout/view_app_menu_item"/>
            <include
                android:id="@+id/appmenu_item11"
                layout="@layout/view_app_menu_item"/>
            <include
                android:id="@+id/appmenu_item12"
                layout="@layout/view_app_menu_item"/>
        </LinearLayout>
    </ScrollView>
</LinearLayout>
