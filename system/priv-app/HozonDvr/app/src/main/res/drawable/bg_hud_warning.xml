<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android"
    android:paddingLeft="@dimen/dp_12"
    android:paddingTop="@dimen/dp_6"
    android:paddingRight="@dimen/dp_12"
    android:paddingBottom="@dimen/dp_6">
    <item>
        <layer-list>
            <item
                android:left="@dimen/dialog_background_transparent_border_horizontal"
                android:top="@dimen/dialog_background_transparent_border_vertical"
                android:right="@dimen/dialog_background_transparent_border_horizontal"
                android:bottom="@dimen/dialog_background_transparent_border_vertical">
                <shape android:shape="rectangle">
                    <solid android:color="@color/function_color_error_error7"/>
                    <corners android:radius="@dimen/radius_6"/>
                    <padding
                        android:left="@dimen/dialog_background_transparent_border_horizontal"
                        android:top="@dimen/dialog_background_transparent_border_vertical"
                        android:right="@dimen/dialog_background_transparent_border_horizontal"
                        android:bottom="@dimen/dialog_background_transparent_border_vertical"/>
                </shape>
            </item>
        </layer-list>
    </item>
</layer-list>
