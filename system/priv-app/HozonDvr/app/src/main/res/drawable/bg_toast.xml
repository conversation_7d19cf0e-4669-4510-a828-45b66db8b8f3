<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android"
    android:paddingLeft="@dimen/dp_12"
    android:paddingTop="@dimen/dp_11"
    android:paddingRight="@dimen/dp_12"
    android:paddingBottom="@dimen/dp_11">
    <item android:drawable="@drawable/bg_toast_shadow"/>
    <item
        android:gravity="center"
        android:left="@dimen/dp_4"
        android:top="@dimen/dp_4"
        android:right="@dimen/dp_4"
        android:bottom="@dimen/dp_4">
        <shape android:shape="rectangle">
            <solid android:color="@color/color_toast_background"/>
            <corners android:radius="@dimen/radius_6"/>
        </shape>
    </item>
</layer-list>
