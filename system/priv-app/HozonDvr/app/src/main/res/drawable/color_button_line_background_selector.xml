<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item
        android:state_enabled="true"
        android:state_pressed="false">
        <shape android:shape="rectangle">
            <solid android:color="#00ffffff"/>
            <corners android:radius="@dimen/px_4"/>
        </shape>
    </item>
    <item
        android:state_enabled="true"
        android:state_pressed="true"
        android:drawable="@drawable/neutral_color_background_surface_tertiary"/>
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="#00ffffff"/>
            <corners android:radius="@dimen/px_4"/>
        </shape>
    </item>
</selector>
