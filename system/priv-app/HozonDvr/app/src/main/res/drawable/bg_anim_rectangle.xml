<?xml version="1.0" encoding="utf-8"?>
<animated-selector xmlns:android="http://schemas.android.com/apk/res/android" android:constantSize="true">
    <item
        android:state_enabled="true"
        android:state_pressed="true"
        android:id="@+id/press">
        <shape android:shape="rectangle">
            <solid android:color="@color/color_bg_press"/>
            <padding
                android:left="16dp"
                android:top="16dp"
                android:right="16dp"
                android:bottom="16dp"/>
        </shape>
    </item>
    <item
        android:state_enabled="true"
        android:id="@+id/un_press">
        <shape android:shape="rectangle">
            <solid android:color="@color/color_bg_press_0"/>
            <padding
                android:left="16dp"
                android:top="16dp"
                android:right="16dp"
                android:bottom="16dp"/>
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/color_bg_press_0"/>
            <padding
                android:left="16dp"
                android:top="16dp"
                android:right="16dp"
                android:bottom="16dp"/>
        </shape>
    </item>
    <transition
        android:toId="@+id/press"
        android:fromId="@+id/un_press">
        <animation-list>
            <item android:duration="@integer/view_animation_short">
                <shape android:shape="rectangle">
                    <solid android:color="@color/color_bg_press"/>
                    <padding
                        android:left="16dp"
                        android:top="16dp"
                        android:right="16dp"
                        android:bottom="16dp"/>
                </shape>
            </item>
        </animation-list>
    </transition>
    <transition
        android:toId="@+id/un_press"
        android:fromId="@+id/press">
        <animation-list>
            <item android:duration="@integer/view_animation_short">
                <shape android:shape="rectangle">
                    <solid android:color="@color/color_bg_press_0"/>
                    <padding
                        android:left="16dp"
                        android:top="16dp"
                        android:right="16dp"
                        android:bottom="16dp"/>
                </shape>
            </item>
        </animation-list>
    </transition>
</animated-selector>
