<?xml version="1.0" encoding="utf-8"?>
<animated-selector xmlns:android="http://schemas.android.com/apk/res/android" android:constantSize="true">
    <item
        android:state_enabled="true"
        android:state_checked="false"
        android:id="@+id/un_checked"
        android:drawable="@drawable/ic_checkbox_off"/>
    <item
        android:state_enabled="false"
        android:state_checked="false"
        android:drawable="@drawable/ic_checkbox_off"/>
    <item
        android:state_enabled="true"
        android:state_checked="true"
        android:id="@+id/checked"
        android:drawable="@drawable/ic_checkbox_checked"/>
    <item
        android:state_enabled="false"
        android:state_checked="true"
        android:drawable="@drawable/ic_checkbox_checked"/>
    <transition
        android:toId="@+id/checked"
        android:fromId="@+id/un_checked">
        <animation-list>
            <item
                android:duration="0"
                android:drawable="@drawable/ic_checkbox_off"/>
            <item
                android:duration="20"
                android:drawable="@drawable/ic_checkbox_checked"/>
        </animation-list>
    </transition>
    <transition
        android:drawable="@drawable/ic_checkbox_off"
        android:toId="@+id/un_checked"
        android:fromId="@+id/checked"/>
</animated-selector>
