<?xml version="1.0" encoding="utf-8"?>
<animated-selector xmlns:android="http://schemas.android.com/apk/res/android" android:constantSize="true">
    <item
        android:state_enabled="true"
        android:state_checked="false"
        android:id="@+id/un_checked"
        android:drawable="@drawable/bg_check_button"/>
    <item
        android:state_enabled="false"
        android:state_checked="false"
        android:drawable="@drawable/bg_check_button"/>
    <item
        android:state_enabled="true"
        android:state_checked="true"
        android:id="@+id/checked"
        android:drawable="@drawable/bg_check_button"/>
    <item
        android:state_enabled="false"
        android:state_checked="true"
        android:drawable="@drawable/bg_check_button"/>
    <transition
        android:toId="@+id/checked"
        android:fromId="@+id/un_checked">
        <animation-list>
            <item
                android:duration="0"
                android:drawable="@drawable/bg_check_button"/>
            <item
                android:duration="20"
                android:drawable="@drawable/bg_check_button"/>
        </animation-list>
    </transition>
    <transition
        android:drawable="@drawable/bg_check_button"
        android:toId="@+id/un_checked"
        android:fromId="@+id/checked"/>
</animated-selector>
