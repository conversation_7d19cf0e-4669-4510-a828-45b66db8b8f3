package a.o;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.graphics.Path;
import android.graphics.PointF;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.util.Property;
import android.view.View;
import android.view.ViewGroup;
import java.util.Map;

/* renamed from: a.o.c, reason: case insensitive filesystem */
/* loaded from: classes.dex */
public class C0095c extends a.o.i {
    private int[] x = new int[2];
    private static final String[] y = {"android:changeBounds:bounds", "android:changeBounds:clip", "android:changeBounds:parent", "android:changeBounds:windowX", "android:changeBounds:windowY"};
    private static final Property<Drawable, PointF> z = new a(PointF.class, "boundsOrigin");
    private static final Property<i, PointF> A = new b(PointF.class, "topLeft");
    private static final Property<i, PointF> B = new C0022c(PointF.class, "bottomRight");
    private static final Property<View, PointF> C = new d(PointF.class, "bottomRight");
    private static final Property<View, PointF> D = new e(PointF.class, "topLeft");
    private static final Property<View, PointF> E = new f(PointF.class, "position");
    private static a.o.g F = new a.o.g();

    /* renamed from: a.o.c$a */
    static class a extends Property<Drawable, PointF> {

        /* renamed from: a, reason: collision with root package name */
        private Rect f475a;

        a(Class cls, String str) {
            super(cls, str);
            this.f475a = new Rect();
        }

        @Override // android.util.Property
        public PointF get(Drawable drawable) {
            drawable.copyBounds(this.f475a);
            Rect rect = this.f475a;
            return new PointF(rect.left, rect.top);
        }

        @Override // android.util.Property
        public void set(Drawable drawable, PointF pointF) {
            Drawable drawable2 = drawable;
            PointF pointF2 = pointF;
            drawable2.copyBounds(this.f475a);
            this.f475a.offsetTo(Math.round(pointF2.x), Math.round(pointF2.y));
            drawable2.setBounds(this.f475a);
        }
    }

    /* renamed from: a.o.c$b */
    static class b extends Property<i, PointF> {
        b(Class cls, String str) {
            super(cls, str);
        }

        @Override // android.util.Property
        public /* bridge */ /* synthetic */ PointF get(i iVar) {
            return null;
        }

        @Override // android.util.Property
        public void set(i iVar, PointF pointF) {
            iVar.c(pointF);
        }
    }

    /* renamed from: a.o.c$c, reason: collision with other inner class name */
    static class C0022c extends Property<i, PointF> {
        C0022c(Class cls, String str) {
            super(cls, str);
        }

        @Override // android.util.Property
        public /* bridge */ /* synthetic */ PointF get(i iVar) {
            return null;
        }

        @Override // android.util.Property
        public void set(i iVar, PointF pointF) {
            iVar.a(pointF);
        }
    }

    /* renamed from: a.o.c$d */
    static class d extends Property<View, PointF> {
        d(Class cls, String str) {
            super(cls, str);
        }

        @Override // android.util.Property
        public /* bridge */ /* synthetic */ PointF get(View view) {
            return null;
        }

        @Override // android.util.Property
        public void set(View view, PointF pointF) {
            View view2 = view;
            PointF pointF2 = pointF;
            int left = view2.getLeft();
            int top = view2.getTop();
            int round = Math.round(pointF2.x);
            int round2 = Math.round(pointF2.y);
            Property<View, Float> property = s.f521b;
            view2.setLeftTopRightBottom(left, top, round, round2);
        }
    }

    /* renamed from: a.o.c$e */
    static class e extends Property<View, PointF> {
        e(Class cls, String str) {
            super(cls, str);
        }

        @Override // android.util.Property
        public /* bridge */ /* synthetic */ PointF get(View view) {
            return null;
        }

        @Override // android.util.Property
        public void set(View view, PointF pointF) {
            View view2 = view;
            PointF pointF2 = pointF;
            int round = Math.round(pointF2.x);
            int round2 = Math.round(pointF2.y);
            int right = view2.getRight();
            int bottom = view2.getBottom();
            Property<View, Float> property = s.f521b;
            view2.setLeftTopRightBottom(round, round2, right, bottom);
        }
    }

    /* renamed from: a.o.c$f */
    static class f extends Property<View, PointF> {
        f(Class cls, String str) {
            super(cls, str);
        }

        @Override // android.util.Property
        public /* bridge */ /* synthetic */ PointF get(View view) {
            return null;
        }

        @Override // android.util.Property
        public void set(View view, PointF pointF) {
            View view2 = view;
            PointF pointF2 = pointF;
            int round = Math.round(pointF2.x);
            int round2 = Math.round(pointF2.y);
            int width = view2.getWidth() + round;
            int height = view2.getHeight() + round2;
            Property<View, Float> property = s.f521b;
            view2.setLeftTopRightBottom(round, round2, width, height);
        }
    }

    /* renamed from: a.o.c$g */
    class g extends AnimatorListenerAdapter {

        /* renamed from: a, reason: collision with root package name */
        final /* synthetic */ i f476a;
        private i mViewBounds;

        g(C0095c c0095c, i iVar) {
            this.f476a = iVar;
            this.mViewBounds = iVar;
        }
    }

    /* renamed from: a.o.c$h */
    class h extends l {

        /* renamed from: a, reason: collision with root package name */
        boolean f477a = false;

        /* renamed from: b, reason: collision with root package name */
        final /* synthetic */ ViewGroup f478b;

        h(C0095c c0095c, ViewGroup viewGroup) {
            this.f478b = viewGroup;
        }

        @Override // a.o.l, a.o.i.d
        public void a(a.o.i iVar) {
            this.f478b.suppressLayout(false);
        }

        @Override // a.o.l, a.o.i.d
        public void b(a.o.i iVar) {
            this.f478b.suppressLayout(true);
        }

        @Override // a.o.l, a.o.i.d
        public void d(a.o.i iVar) {
            this.f478b.suppressLayout(false);
            this.f477a = true;
        }

        @Override // a.o.i.d
        public void e(a.o.i iVar) {
            if (!this.f477a) {
                this.f478b.suppressLayout(false);
            }
            iVar.B(this);
        }
    }

    /* renamed from: a.o.c$i */
    private static class i {

        /* renamed from: a, reason: collision with root package name */
        private int f479a;

        /* renamed from: b, reason: collision with root package name */
        private int f480b;

        /* renamed from: c, reason: collision with root package name */
        private int f481c;

        /* renamed from: d, reason: collision with root package name */
        private int f482d;
        private View e;
        private int f;
        private int g;

        i(View view) {
            this.e = view;
        }

        private void b() {
            View view = this.e;
            int i = this.f479a;
            int i2 = this.f480b;
            int i3 = this.f481c;
            int i4 = this.f482d;
            Property<View, Float> property = s.f521b;
            view.setLeftTopRightBottom(i, i2, i3, i4);
            this.f = 0;
            this.g = 0;
        }

        void a(PointF pointF) {
            this.f481c = Math.round(pointF.x);
            this.f482d = Math.round(pointF.y);
            int i = this.g + 1;
            this.g = i;
            if (this.f == i) {
                b();
            }
        }

        void c(PointF pointF) {
            this.f479a = Math.round(pointF.x);
            this.f480b = Math.round(pointF.y);
            int i = this.f + 1;
            this.f = i;
            if (i == this.g) {
                b();
            }
        }
    }

    private void N(p pVar) {
        View view = pVar.f513b;
        int i2 = a.h.h.q.e;
        if (!view.isLaidOut() && view.getWidth() == 0 && view.getHeight() == 0) {
            return;
        }
        pVar.f512a.put("android:changeBounds:bounds", new Rect(view.getLeft(), view.getTop(), view.getRight(), view.getBottom()));
        pVar.f512a.put("android:changeBounds:parent", pVar.f513b.getParent());
    }

    @Override // a.o.i
    public void e(p pVar) {
        N(pVar);
    }

    @Override // a.o.i
    public void h(p pVar) {
        N(pVar);
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // a.o.i
    public Animator l(ViewGroup viewGroup, p pVar, p pVar2) {
        int i2;
        C0095c c0095c;
        Path a2;
        Property<View, PointF> property;
        ObjectAnimator a3;
        if (pVar == null || pVar2 == null) {
            return null;
        }
        Map<String, Object> map = pVar.f512a;
        Map<String, Object> map2 = pVar2.f512a;
        ViewGroup viewGroup2 = (ViewGroup) map.get("android:changeBounds:parent");
        ViewGroup viewGroup3 = (ViewGroup) map2.get("android:changeBounds:parent");
        if (viewGroup2 == null || viewGroup3 == null) {
            return null;
        }
        View view = pVar2.f513b;
        Rect rect = (Rect) pVar.f512a.get("android:changeBounds:bounds");
        Rect rect2 = (Rect) pVar2.f512a.get("android:changeBounds:bounds");
        int i3 = rect.left;
        int i4 = rect2.left;
        int i5 = rect.top;
        int i6 = rect2.top;
        int i7 = rect.right;
        int i8 = rect2.right;
        int i9 = rect.bottom;
        int i10 = rect2.bottom;
        int i11 = i7 - i3;
        int i12 = i9 - i5;
        int i13 = i8 - i4;
        int i14 = i10 - i6;
        Rect rect3 = (Rect) pVar.f512a.get("android:changeBounds:clip");
        Rect rect4 = (Rect) pVar2.f512a.get("android:changeBounds:clip");
        if ((i11 == 0 || i12 == 0) && (i13 == 0 || i14 == 0)) {
            i2 = 0;
        } else {
            i2 = (i3 == i4 && i5 == i6) ? 0 : 1;
            if (i7 != i8 || i9 != i10) {
                i2++;
            }
        }
        if ((rect3 != null && !rect3.equals(rect4)) || (rect3 == null && rect4 != null)) {
            i2++;
        }
        int i15 = i2;
        if (i15 <= 0) {
            return null;
        }
        Property<View, Float> property2 = s.f521b;
        view.setLeftTopRightBottom(i3, i5, i7, i9);
        if (i15 != 2) {
            c0095c = this;
            if (i3 == i4 && i5 == i6) {
                a2 = r().a(i7, i9, i8, i10);
                property = C;
            } else {
                a2 = r().a(i3, i5, i4, i6);
                property = D;
            }
            a3 = C0093a.a(view, property, a2);
        } else if (i11 == i13 && i12 == i14) {
            a3 = C0093a.a(view, E, r().a(i3, i5, i4, i6));
            c0095c = this;
        } else {
            i iVar = new i(view);
            ObjectAnimator a4 = C0093a.a(iVar, A, r().a(i3, i5, i4, i6));
            ObjectAnimator a5 = C0093a.a(iVar, B, r().a(i7, i9, i8, i10));
            AnimatorSet animatorSet = new AnimatorSet();
            animatorSet.playTogether(a4, a5);
            c0095c = this;
            animatorSet.addListener(new g(c0095c, iVar));
            a3 = animatorSet;
        }
        if (view.getParent() instanceof ViewGroup) {
            ViewGroup viewGroup4 = (ViewGroup) view.getParent();
            viewGroup4.suppressLayout(true);
            c0095c.a(new h(c0095c, viewGroup4));
        }
        return a3;
    }

    @Override // a.o.i
    public String[] u() {
        return y;
    }
}
