package a.h.c;

import a.e.f;
import a.h.e.g;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Typeface;
import android.os.CancellationSignal;
import android.os.Handler;

@SuppressLint({"NewApi"})
/* loaded from: classes.dex */
public class c {

    /* renamed from: a, reason: collision with root package name */
    private static final e f283a = new d();

    /* renamed from: b, reason: collision with root package name */
    private static final f<String, Typeface> f284b = new f<>(16);

    /* renamed from: c, reason: collision with root package name */
    public static final /* synthetic */ int f285c = 0;

    public static class a extends g.c {

        /* renamed from: a, reason: collision with root package name */
        private a.h.b.b.f f286a;

        public a(a.h.b.b.f fVar) {
            this.f286a = fVar;
        }

        @Override // a.h.e.g.c
        public void a(int i) {
            a.h.b.b.f fVar = this.f286a;
            if (fVar != null) {
                fVar.d(i);
            }
        }

        @Override // a.h.e.g.c
        public void b(Typeface typeface) {
            a.h.b.b.f fVar = this.f286a;
            if (fVar != null) {
                fVar.e(typeface);
            }
        }
    }

    public static Typeface a(Context context, CancellationSignal cancellationSignal, g.b[] bVarArr, int i) {
        return f283a.b(context, null, bVarArr, i);
    }

    public static Typeface b(Context context, a.h.b.b.b bVar, Resources resources, int i, int i2, a.h.b.b.f fVar, Handler handler, boolean z) {
        Typeface a2;
        if (bVar instanceof a.h.b.b.e) {
            a.h.b.b.e eVar = (a.h.b.b.e) bVar;
            String c2 = eVar.c();
            Typeface typeface = null;
            if (c2 != null && !c2.isEmpty()) {
                Typeface create = Typeface.create(c2, 0);
                Typeface create2 = Typeface.create(Typeface.DEFAULT, 0);
                if (create != null && !create.equals(create2)) {
                    typeface = create;
                }
            }
            if (typeface != null) {
                if (fVar != null) {
                    fVar.b(typeface, handler);
                }
                return typeface;
            }
            boolean z2 = !z ? fVar != null : eVar.a() != 0;
            int d2 = z ? eVar.d() : -1;
            a2 = g.a(context, eVar.b(), i2, z2, d2, a.h.b.b.f.c(handler), new a(fVar));
        } else {
            a2 = f283a.a(context, (a.h.b.b.c) bVar, resources, i2);
            if (fVar != null) {
                if (a2 != null) {
                    fVar.b(a2, handler);
                } else {
                    fVar.a(-3, handler);
                }
            }
        }
        if (a2 != null) {
            f284b.b(d(resources, i, i2), a2);
        }
        return a2;
    }

    public static Typeface c(Context context, Resources resources, int i, String str, int i2) {
        Typeface c2 = f283a.c(context, resources, i, str, i2);
        if (c2 != null) {
            f284b.b(d(resources, i, i2), c2);
        }
        return c2;
    }

    private static String d(Resources resources, int i, int i2) {
        return resources.getResourcePackageName(i) + "-" + i + "-" + i2;
    }

    public static Typeface e(Resources resources, int i, int i2) {
        return f284b.a(d(resources, i, i2));
    }
}
