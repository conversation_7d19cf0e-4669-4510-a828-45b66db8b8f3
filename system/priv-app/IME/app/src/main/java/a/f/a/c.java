package a.f.a;

/* loaded from: classes.dex */
public class c {

    /* renamed from: a, reason: collision with root package name */
    f<b> f111a = new f<>(256);

    /* renamed from: b, reason: collision with root package name */
    f<b> f112b = new f<>(256);

    /* renamed from: c, reason: collision with root package name */
    f<h> f113c = new f<>(256);

    /* renamed from: d, reason: collision with root package name */
    h[] f114d = new h[32];
}
