package a.o;

import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Iterator;
import org.libpag.R;

/* loaded from: classes.dex */
public class m {

    /* renamed from: a, reason: collision with root package name */
    private static i f503a = new C0094b();

    /* renamed from: b, reason: collision with root package name */
    private static ThreadLocal<WeakReference<a.e.a<ViewGroup, ArrayList<i>>>> f504b = new ThreadLocal<>();

    /* renamed from: c, reason: collision with root package name */
    static ArrayList<ViewGroup> f505c = new ArrayList<>();

    private static class a implements ViewTreeObserver.OnPreDrawListener, View.OnAttachStateChangeListener {

        /* renamed from: a, reason: collision with root package name */
        i f506a;

        /* renamed from: b, reason: collision with root package name */
        ViewGroup f507b;

        /* renamed from: a.o.m$a$a, reason: collision with other inner class name */
        class C0023a extends l {

            /* renamed from: a, reason: collision with root package name */
            final /* synthetic */ a.e.a f508a;

            C0023a(a.e.a aVar) {
                this.f508a = aVar;
            }

            /* JADX WARN: Multi-variable type inference failed */
            @Override // a.o.i.d
            public void e(i iVar) {
                ((ArrayList) this.f508a.get(a.this.f507b)).remove(iVar);
                iVar.B(this);
            }
        }

        a(i iVar, ViewGroup viewGroup) {
            this.f506a = iVar;
            this.f507b = viewGroup;
        }

        @Override // android.view.ViewTreeObserver.OnPreDrawListener
        public boolean onPreDraw() {
            this.f507b.getViewTreeObserver().removeOnPreDrawListener(this);
            this.f507b.removeOnAttachStateChangeListener(this);
            if (!m.f505c.remove(this.f507b)) {
                return true;
            }
            a.e.a<ViewGroup, ArrayList<i>> b2 = m.b();
            ArrayList<i> arrayList = b2.get(this.f507b);
            ArrayList arrayList2 = null;
            if (arrayList == null) {
                arrayList = new ArrayList<>();
                b2.put(this.f507b, arrayList);
            } else if (arrayList.size() > 0) {
                arrayList2 = new ArrayList(arrayList);
            }
            arrayList.add(this.f506a);
            this.f506a.a(new C0023a(b2));
            this.f506a.i(this.f507b, false);
            if (arrayList2 != null) {
                Iterator it = arrayList2.iterator();
                while (it.hasNext()) {
                    ((i) it.next()).D(this.f507b);
                }
            }
            this.f506a.A(this.f507b);
            return true;
        }

        @Override // android.view.View.OnAttachStateChangeListener
        public void onViewAttachedToWindow(View view) {
        }

        @Override // android.view.View.OnAttachStateChangeListener
        public void onViewDetachedFromWindow(View view) {
            this.f507b.getViewTreeObserver().removeOnPreDrawListener(this);
            this.f507b.removeOnAttachStateChangeListener(this);
            m.f505c.remove(this.f507b);
            ArrayList<i> arrayList = m.b().get(this.f507b);
            if (arrayList != null && arrayList.size() > 0) {
                Iterator<i> it = arrayList.iterator();
                while (it.hasNext()) {
                    it.next().D(this.f507b);
                }
            }
            this.f506a.j(true);
        }
    }

    public static void a(ViewGroup viewGroup, i iVar) {
        if (f505c.contains(viewGroup)) {
            return;
        }
        int i = a.h.h.q.e;
        if (viewGroup.isLaidOut()) {
            f505c.add(viewGroup);
            if (iVar == null) {
                iVar = f503a;
            }
            i clone = iVar.clone();
            ArrayList<i> orDefault = b().getOrDefault(viewGroup, null);
            if (orDefault != null && orDefault.size() > 0) {
                Iterator<i> it = orDefault.iterator();
                while (it.hasNext()) {
                    it.next().z(viewGroup);
                }
            }
            if (clone != null) {
                clone.i(viewGroup, true);
            }
            if (((h) viewGroup.getTag(R.id.transition_current_scene)) != null) {
                throw null;
            }
            viewGroup.setTag(R.id.transition_current_scene, null);
            if (clone != null) {
                a aVar = new a(clone, viewGroup);
                viewGroup.addOnAttachStateChangeListener(aVar);
                viewGroup.getViewTreeObserver().addOnPreDrawListener(aVar);
            }
        }
    }

    static a.e.a<ViewGroup, ArrayList<i>> b() {
        a.e.a<ViewGroup, ArrayList<i>> aVar;
        WeakReference<a.e.a<ViewGroup, ArrayList<i>>> weakReference = f504b.get();
        if (weakReference != null && (aVar = weakReference.get()) != null) {
            return aVar;
        }
        a.e.a<ViewGroup, ArrayList<i>> aVar2 = new a.e.a<>();
        f504b.set(new WeakReference<>(aVar2));
        return aVar2;
    }
}
