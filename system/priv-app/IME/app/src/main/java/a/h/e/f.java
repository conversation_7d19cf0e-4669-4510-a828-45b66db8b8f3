package a.h.e;

import a.h.e.g;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.Typeface;
import android.os.Handler;
import android.os.Looper;
import java.util.ArrayList;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/* loaded from: classes.dex */
class f {

    /* renamed from: a, reason: collision with root package name */
    static final a.e.f<String, Typeface> f299a = new a.e.f<>(16);

    /* renamed from: b, reason: collision with root package name */
    private static final ExecutorService f300b;

    /* renamed from: c, reason: collision with root package name */
    static final Object f301c;

    /* renamed from: d, reason: collision with root package name */
    static final a.e.h<String, ArrayList<a.h.g.a<e>>> f302d;

    class a implements Callable<e> {

        /* renamed from: a, reason: collision with root package name */
        final /* synthetic */ String f303a;

        /* renamed from: b, reason: collision with root package name */
        final /* synthetic */ Context f304b;

        /* renamed from: c, reason: collision with root package name */
        final /* synthetic */ a.h.e.e f305c;

        /* renamed from: d, reason: collision with root package name */
        final /* synthetic */ int f306d;

        a(String str, Context context, a.h.e.e eVar, int i) {
            this.f303a = str;
            this.f304b = context;
            this.f305c = eVar;
            this.f306d = i;
        }

        @Override // java.util.concurrent.Callable
        public e call() {
            return f.b(this.f303a, this.f304b, this.f305c, this.f306d);
        }
    }

    class b implements a.h.g.a<e> {

        /* renamed from: a, reason: collision with root package name */
        final /* synthetic */ a.h.e.c f307a;

        b(a.h.e.c cVar) {
            this.f307a = cVar;
        }

        @Override // a.h.g.a
        public void a(e eVar) {
            this.f307a.a(eVar);
        }
    }

    class c implements Callable<e> {

        /* renamed from: a, reason: collision with root package name */
        final /* synthetic */ String f308a;

        /* renamed from: b, reason: collision with root package name */
        final /* synthetic */ Context f309b;

        /* renamed from: c, reason: collision with root package name */
        final /* synthetic */ a.h.e.e f310c;

        /* renamed from: d, reason: collision with root package name */
        final /* synthetic */ int f311d;

        c(String str, Context context, a.h.e.e eVar, int i) {
            this.f308a = str;
            this.f309b = context;
            this.f310c = eVar;
            this.f311d = i;
        }

        @Override // java.util.concurrent.Callable
        public e call() {
            return f.b(this.f308a, this.f309b, this.f310c, this.f311d);
        }
    }

    class d implements a.h.g.a<e> {

        /* renamed from: a, reason: collision with root package name */
        final /* synthetic */ String f312a;

        d(String str) {
            this.f312a = str;
        }

        @Override // a.h.g.a
        /* renamed from: b, reason: merged with bridge method [inline-methods] */
        public void a(e eVar) {
            synchronized (f.f301c) {
                a.e.h<String, ArrayList<a.h.g.a<e>>> hVar = f.f302d;
                ArrayList<a.h.g.a<e>> arrayList = hVar.get(this.f312a);
                if (arrayList == null) {
                    return;
                }
                hVar.remove(this.f312a);
                for (int i = 0; i < arrayList.size(); i++) {
                    arrayList.get(i).a(eVar);
                }
            }
        }
    }

    static final class e {

        /* renamed from: a, reason: collision with root package name */
        final Typeface f313a;

        /* renamed from: b, reason: collision with root package name */
        final int f314b;

        e(int i) {
            this.f313a = null;
            this.f314b = i;
        }

        @SuppressLint({"WrongConstant"})
        e(Typeface typeface) {
            this.f313a = typeface;
            this.f314b = 0;
        }
    }

    static {
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(0, 1, 10000, TimeUnit.MILLISECONDS, new LinkedBlockingDeque(), new h("fonts-androidx", 10));
        threadPoolExecutor.allowCoreThreadTimeOut(true);
        f300b = threadPoolExecutor;
        f301c = new Object();
        f302d = new a.e.h<>();
    }

    private static String a(a.h.e.e eVar, int i) {
        return eVar.b() + "-" + i;
    }

    static e b(String str, Context context, a.h.e.e eVar, int i) {
        int i2;
        Typeface a2 = f299a.a(str);
        if (a2 != null) {
            return new e(a2);
        }
        try {
            g.a a3 = a.h.e.d.a(context, eVar, null);
            int i3 = 1;
            if (a3.b() != 0) {
                if (a3.b() == 1) {
                    i2 = -2;
                }
                i2 = -3;
            } else {
                g.b[] a4 = a3.a();
                if (a4 != null && a4.length != 0) {
                    for (g.b bVar : a4) {
                        int a5 = bVar.a();
                        if (a5 != 0) {
                            if (a5 >= 0) {
                                i2 = a5;
                            }
                            i2 = -3;
                        }
                    }
                    i3 = 0;
                }
                i2 = i3;
            }
            if (i2 != 0) {
                return new e(i2);
            }
            Typeface a6 = a.h.c.c.a(context, null, a3.a(), i);
            if (a6 == null) {
                return new e(-3);
            }
            f299a.b(str, a6);
            return new e(a6);
        } catch (PackageManager.NameNotFoundException unused) {
            return new e(-1);
        }
    }

    static Typeface c(Context context, a.h.e.e eVar, int i, Executor executor, a.h.e.c cVar) {
        String a2 = a(eVar, i);
        Typeface a3 = f299a.a(a2);
        if (a3 != null) {
            cVar.a(new e(a3));
            return a3;
        }
        b bVar = new b(cVar);
        synchronized (f301c) {
            a.e.h<String, ArrayList<a.h.g.a<e>>> hVar = f302d;
            ArrayList<a.h.g.a<e>> orDefault = hVar.getOrDefault(a2, null);
            if (orDefault != null) {
                orDefault.add(bVar);
                return null;
            }
            ArrayList<a.h.g.a<e>> arrayList = new ArrayList<>();
            arrayList.add(bVar);
            hVar.put(a2, arrayList);
            c cVar2 = new c(a2, context, eVar, i);
            f300b.execute(new i(Looper.myLooper() == null ? new Handler(Looper.getMainLooper()) : new Handler(), cVar2, new d(a2)));
            return null;
        }
    }

    static Typeface d(Context context, a.h.e.e eVar, a.h.e.c cVar, int i, int i2) {
        String a2 = a(eVar, i);
        Typeface a3 = f299a.a(a2);
        if (a3 != null) {
            cVar.a(new e(a3));
            return a3;
        }
        if (i2 == -1) {
            e b2 = b(a2, context, eVar, i);
            cVar.a(b2);
            return b2.f313a;
        }
        try {
            try {
                try {
                    e eVar2 = (e) f300b.submit(new a(a2, context, eVar, i)).get(i2, TimeUnit.MILLISECONDS);
                    cVar.a(eVar2);
                    return eVar2.f313a;
                } catch (TimeoutException unused) {
                    throw new InterruptedException("timeout");
                }
            } catch (InterruptedException e2) {
                throw e2;
            } catch (ExecutionException e3) {
                throw new RuntimeException(e3);
            }
        } catch (InterruptedException unused2) {
            cVar.a(new e(-3));
            return null;
        }
    }
}
