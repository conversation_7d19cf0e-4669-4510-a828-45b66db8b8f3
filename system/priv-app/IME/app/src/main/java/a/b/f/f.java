package a.b.f;

import a.b.f.b;
import android.content.Context;
import android.view.ActionMode;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import androidx.appcompat.view.menu.j;
import androidx.appcompat.view.menu.o;
import java.util.ArrayList;

/* loaded from: classes.dex */
public class f extends ActionMode {

    /* renamed from: a, reason: collision with root package name */
    final Context f19a;

    /* renamed from: b, reason: collision with root package name */
    final b f20b;

    public static class a implements b.a {

        /* renamed from: a, reason: collision with root package name */
        final ActionMode.Callback f21a;

        /* renamed from: b, reason: collision with root package name */
        final Context f22b;

        /* renamed from: c, reason: collision with root package name */
        final ArrayList<f> f23c = new ArrayList<>();

        /* renamed from: d, reason: collision with root package name */
        final a.e.h<Menu, Menu> f24d = new a.e.h<>();

        public a(Context context, ActionMode.Callback callback) {
            this.f22b = context;
            this.f21a = callback;
        }

        private Menu f(Menu menu) {
            Menu orDefault = this.f24d.getOrDefault(menu, null);
            if (orDefault != null) {
                return orDefault;
            }
            o oVar = new o(this.f22b, (a.h.d.a.a) menu);
            this.f24d.put(menu, oVar);
            return oVar;
        }

        @Override // a.b.f.b.a
        public boolean a(b bVar, Menu menu) {
            return this.f21a.onPrepareActionMode(e(bVar), f(menu));
        }

        @Override // a.b.f.b.a
        public boolean b(b bVar, MenuItem menuItem) {
            return this.f21a.onActionItemClicked(e(bVar), new j(this.f22b, (a.h.d.a.b) menuItem));
        }

        @Override // a.b.f.b.a
        public boolean c(b bVar, Menu menu) {
            return this.f21a.onCreateActionMode(e(bVar), f(menu));
        }

        @Override // a.b.f.b.a
        public void d(b bVar) {
            this.f21a.onDestroyActionMode(e(bVar));
        }

        public ActionMode e(b bVar) {
            int size = this.f23c.size();
            for (int i = 0; i < size; i++) {
                f fVar = this.f23c.get(i);
                if (fVar != null && fVar.f20b == bVar) {
                    return fVar;
                }
            }
            f fVar2 = new f(this.f22b, bVar);
            this.f23c.add(fVar2);
            return fVar2;
        }
    }

    public f(Context context, b bVar) {
        this.f19a = context;
        this.f20b = bVar;
    }

    @Override // android.view.ActionMode
    public void finish() {
        this.f20b.c();
    }

    @Override // android.view.ActionMode
    public View getCustomView() {
        return this.f20b.d();
    }

    @Override // android.view.ActionMode
    public Menu getMenu() {
        return new o(this.f19a, (a.h.d.a.a) this.f20b.e());
    }

    @Override // android.view.ActionMode
    public MenuInflater getMenuInflater() {
        return this.f20b.f();
    }

    @Override // android.view.ActionMode
    public CharSequence getSubtitle() {
        return this.f20b.g();
    }

    @Override // android.view.ActionMode
    public Object getTag() {
        return this.f20b.h();
    }

    @Override // android.view.ActionMode
    public CharSequence getTitle() {
        return this.f20b.i();
    }

    @Override // android.view.ActionMode
    public boolean getTitleOptionalHint() {
        return this.f20b.j();
    }

    @Override // android.view.ActionMode
    public void invalidate() {
        this.f20b.k();
    }

    @Override // android.view.ActionMode
    public boolean isTitleOptional() {
        return this.f20b.l();
    }

    @Override // android.view.ActionMode
    public void setCustomView(View view) {
        this.f20b.m(view);
    }

    @Override // android.view.ActionMode
    public void setSubtitle(int i) {
        this.f20b.n(i);
    }

    @Override // android.view.ActionMode
    public void setSubtitle(CharSequence charSequence) {
        this.f20b.o(charSequence);
    }

    @Override // android.view.ActionMode
    public void setTag(Object obj) {
        this.f20b.p(obj);
    }

    @Override // android.view.ActionMode
    public void setTitle(int i) {
        this.f20b.q(i);
    }

    @Override // android.view.ActionMode
    public void setTitle(CharSequence charSequence) {
        this.f20b.r(charSequence);
    }

    @Override // android.view.ActionMode
    public void setTitleOptionalHint(boolean z) {
        this.f20b.s(z);
    }
}
