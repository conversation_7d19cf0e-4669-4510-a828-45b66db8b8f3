package a.h.e;

import a.h.e.f;
import a.h.e.g;
import android.os.Handler;

/* loaded from: classes.dex */
class c {

    /* renamed from: a, reason: collision with root package name */
    private final g.c f292a;

    /* renamed from: b, reason: collision with root package name */
    private final Handler f293b;

    c(g.c c<PERSON><PERSON>, Handler handler) {
        this.f292a = cVar;
        this.f293b = handler;
    }

    void a(f.e eVar) {
        int i = eVar.f314b;
        if (!(i == 0)) {
            this.f293b.post(new b(this, this.f292a, i));
        } else {
            this.f293b.post(new a(this, this.f292a, eVar.f313a));
        }
    }
}
