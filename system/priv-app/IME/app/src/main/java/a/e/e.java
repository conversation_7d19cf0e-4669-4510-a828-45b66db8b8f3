package a.e;

/* loaded from: classes.dex */
public class e<E> implements Cloneable {
    private static final Object e = new Object();

    /* renamed from: a, reason: collision with root package name */
    private boolean f73a = false;

    /* renamed from: b, reason: collision with root package name */
    private long[] f74b;

    /* renamed from: c, reason: collision with root package name */
    private Object[] f75c;

    /* renamed from: d, reason: collision with root package name */
    private int f76d;

    public e() {
        int f = d.f(10);
        this.f74b = new long[f];
        this.f75c = new Object[f];
    }

    private void c() {
        int i = this.f76d;
        long[] jArr = this.f74b;
        Object[] objArr = this.f75c;
        int i2 = 0;
        for (int i3 = 0; i3 < i; i3++) {
            Object obj = objArr[i3];
            if (obj != e) {
                if (i3 != i2) {
                    jArr[i2] = jArr[i3];
                    objArr[i2] = obj;
                    objArr[i3] = null;
                }
                i2++;
            }
        }
        this.f73a = false;
        this.f76d = i2;
    }

    public void a() {
        int i = this.f76d;
        Object[] objArr = this.f75c;
        for (int i2 = 0; i2 < i; i2++) {
            objArr[i2] = null;
        }
        this.f76d = 0;
        this.f73a = false;
    }

    /* renamed from: b, reason: merged with bridge method [inline-methods] */
    public e<E> clone() {
        try {
            e<E> eVar = (e) super.clone();
            eVar.f74b = (long[]) this.f74b.clone();
            eVar.f75c = (Object[]) this.f75c.clone();
            return eVar;
        } catch (CloneNotSupportedException e2) {
            throw new AssertionError(e2);
        }
    }

    public E d(long j) {
        return e(j, null);
    }

    public E e(long j, E e2) {
        int b2 = d.b(this.f74b, this.f76d, j);
        if (b2 >= 0) {
            Object[] objArr = this.f75c;
            if (objArr[b2] != e) {
                return (E) objArr[b2];
            }
        }
        return e2;
    }

    public int f(long j) {
        if (this.f73a) {
            c();
        }
        return d.b(this.f74b, this.f76d, j);
    }

    public long g(int i) {
        if (this.f73a) {
            c();
        }
        return this.f74b[i];
    }

    public void h(long j, E e2) {
        int b2 = d.b(this.f74b, this.f76d, j);
        if (b2 >= 0) {
            this.f75c[b2] = e2;
            return;
        }
        int i = ~b2;
        int i2 = this.f76d;
        if (i < i2) {
            Object[] objArr = this.f75c;
            if (objArr[i] == e) {
                this.f74b[i] = j;
                objArr[i] = e2;
                return;
            }
        }
        if (this.f73a && i2 >= this.f74b.length) {
            c();
            i = ~d.b(this.f74b, this.f76d, j);
        }
        int i3 = this.f76d;
        if (i3 >= this.f74b.length) {
            int f = d.f(i3 + 1);
            long[] jArr = new long[f];
            Object[] objArr2 = new Object[f];
            long[] jArr2 = this.f74b;
            System.arraycopy(jArr2, 0, jArr, 0, jArr2.length);
            Object[] objArr3 = this.f75c;
            System.arraycopy(objArr3, 0, objArr2, 0, objArr3.length);
            this.f74b = jArr;
            this.f75c = objArr2;
        }
        int i4 = this.f76d;
        if (i4 - i != 0) {
            long[] jArr3 = this.f74b;
            int i5 = i + 1;
            System.arraycopy(jArr3, i, jArr3, i5, i4 - i);
            Object[] objArr4 = this.f75c;
            System.arraycopy(objArr4, i, objArr4, i5, this.f76d - i);
        }
        this.f74b[i] = j;
        this.f75c[i] = e2;
        this.f76d++;
    }

    public void i(long j) {
        int b2 = d.b(this.f74b, this.f76d, j);
        if (b2 >= 0) {
            Object[] objArr = this.f75c;
            Object obj = objArr[b2];
            Object obj2 = e;
            if (obj != obj2) {
                objArr[b2] = obj2;
                this.f73a = true;
            }
        }
    }

    public void j(int i) {
        Object[] objArr = this.f75c;
        Object obj = objArr[i];
        Object obj2 = e;
        if (obj != obj2) {
            objArr[i] = obj2;
            this.f73a = true;
        }
    }

    public int k() {
        if (this.f73a) {
            c();
        }
        return this.f76d;
    }

    public E l(int i) {
        if (this.f73a) {
            c();
        }
        return (E) this.f75c[i];
    }

    public String toString() {
        if (k() <= 0) {
            return "{}";
        }
        StringBuilder sb = new StringBuilder(this.f76d * 28);
        sb.append('{');
        for (int i = 0; i < this.f76d; i++) {
            if (i > 0) {
                sb.append(", ");
            }
            if (this.f73a) {
                c();
            }
            sb.append(this.f74b[i]);
            sb.append('=');
            E l = l(i);
            if (l != this) {
                sb.append(l);
            } else {
                sb.append("(this Map)");
            }
        }
        sb.append('}');
        return sb.toString();
    }
}
