package a.c.a.b;

import java.util.Iterator;
import java.util.Map;
import java.util.WeakHashMap;

/* loaded from: classes.dex */
public class b<K, V> implements Iterable<Map.Entry<K, V>> {

    /* renamed from: a, reason: collision with root package name */
    c<K, V> f50a;

    /* renamed from: b, reason: collision with root package name */
    private c<K, V> f51b;

    /* renamed from: c, reason: collision with root package name */
    private WeakHashMap<f<K, V>, Boolean> f52c = new WeakHashMap<>();

    /* renamed from: d, reason: collision with root package name */
    private int f53d = 0;

    static class a<K, V> extends e<K, V> {
        a(c<K, V> cVar, c<K, V> cVar2) {
            super(cVar, cVar2);
        }

        @Override // a.c.a.b.b.e
        c<K, V> b(c<K, V> cVar) {
            return cVar.f57d;
        }

        @Override // a.c.a.b.b.e
        c<K, V> c(c<K, V> cVar) {
            return cVar.f56c;
        }
    }

    /* renamed from: a.c.a.b.b$b, reason: collision with other inner class name */
    private static class C0000b<K, V> extends e<K, V> {
        C0000b(c<K, V> cVar, c<K, V> cVar2) {
            super(cVar, cVar2);
        }

        @Override // a.c.a.b.b.e
        c<K, V> b(c<K, V> cVar) {
            return cVar.f56c;
        }

        @Override // a.c.a.b.b.e
        c<K, V> c(c<K, V> cVar) {
            return cVar.f57d;
        }
    }

    static class c<K, V> implements Map.Entry<K, V> {

        /* renamed from: a, reason: collision with root package name */
        final K f54a;

        /* renamed from: b, reason: collision with root package name */
        final V f55b;

        /* renamed from: c, reason: collision with root package name */
        c<K, V> f56c;

        /* renamed from: d, reason: collision with root package name */
        c<K, V> f57d;

        c(K k, V v) {
            this.f54a = k;
            this.f55b = v;
        }

        @Override // java.util.Map.Entry
        public boolean equals(Object obj) {
            if (obj == this) {
                return true;
            }
            if (!(obj instanceof c)) {
                return false;
            }
            c cVar = (c) obj;
            return this.f54a.equals(cVar.f54a) && this.f55b.equals(cVar.f55b);
        }

        @Override // java.util.Map.Entry
        public K getKey() {
            return this.f54a;
        }

        @Override // java.util.Map.Entry
        public V getValue() {
            return this.f55b;
        }

        @Override // java.util.Map.Entry
        public int hashCode() {
            return this.f55b.hashCode() ^ this.f54a.hashCode();
        }

        @Override // java.util.Map.Entry
        public V setValue(V v) {
            throw new UnsupportedOperationException("An entry modification is not supported");
        }

        public String toString() {
            return this.f54a + "=" + this.f55b;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public class d implements Iterator<Map.Entry<K, V>>, f<K, V> {

        /* renamed from: a, reason: collision with root package name */
        private c<K, V> f58a;

        /* renamed from: b, reason: collision with root package name */
        private boolean f59b = true;

        d() {
        }

        @Override // a.c.a.b.b.f
        public void a(c<K, V> cVar) {
            c<K, V> cVar2 = this.f58a;
            if (cVar == cVar2) {
                c<K, V> cVar3 = cVar2.f57d;
                this.f58a = cVar3;
                this.f59b = cVar3 == null;
            }
        }

        @Override // java.util.Iterator
        public boolean hasNext() {
            if (this.f59b) {
                return b.this.f50a != null;
            }
            c<K, V> cVar = this.f58a;
            return (cVar == null || cVar.f56c == null) ? false : true;
        }

        @Override // java.util.Iterator
        public Object next() {
            c<K, V> cVar;
            if (this.f59b) {
                this.f59b = false;
                cVar = b.this.f50a;
            } else {
                c<K, V> cVar2 = this.f58a;
                cVar = cVar2 != null ? cVar2.f56c : null;
            }
            this.f58a = cVar;
            return cVar;
        }
    }

    private static abstract class e<K, V> implements Iterator<Map.Entry<K, V>>, f<K, V> {

        /* renamed from: a, reason: collision with root package name */
        c<K, V> f61a;

        /* renamed from: b, reason: collision with root package name */
        c<K, V> f62b;

        e(c<K, V> cVar, c<K, V> cVar2) {
            this.f61a = cVar2;
            this.f62b = cVar;
        }

        @Override // a.c.a.b.b.f
        public void a(c<K, V> cVar) {
            c<K, V> cVar2 = null;
            if (this.f61a == cVar && cVar == this.f62b) {
                this.f62b = null;
                this.f61a = null;
            }
            c<K, V> cVar3 = this.f61a;
            if (cVar3 == cVar) {
                this.f61a = b(cVar3);
            }
            c<K, V> cVar4 = this.f62b;
            if (cVar4 == cVar) {
                c<K, V> cVar5 = this.f61a;
                if (cVar4 != cVar5 && cVar5 != null) {
                    cVar2 = c(cVar4);
                }
                this.f62b = cVar2;
            }
        }

        abstract c<K, V> b(c<K, V> cVar);

        abstract c<K, V> c(c<K, V> cVar);

        @Override // java.util.Iterator
        public boolean hasNext() {
            return this.f62b != null;
        }

        @Override // java.util.Iterator
        public Object next() {
            c<K, V> cVar = this.f62b;
            c<K, V> cVar2 = this.f61a;
            this.f62b = (cVar == cVar2 || cVar2 == null) ? null : c(cVar);
            return cVar;
        }
    }

    interface f<K, V> {
        void a(c<K, V> cVar);
    }

    public Iterator<Map.Entry<K, V>> a() {
        C0000b c0000b = new C0000b(this.f51b, this.f50a);
        this.f52c.put(c0000b, Boolean.FALSE);
        return c0000b;
    }

    public Map.Entry<K, V> b() {
        return this.f50a;
    }

    protected c<K, V> c(K k) {
        c<K, V> cVar = this.f50a;
        while (cVar != null && !cVar.f54a.equals(k)) {
            cVar = cVar.f56c;
        }
        return cVar;
    }

    public b<K, V>.d d() {
        b<K, V>.d dVar = new d();
        this.f52c.put(dVar, Boolean.FALSE);
        return dVar;
    }

    public Map.Entry<K, V> e() {
        return this.f51b;
    }

    /* JADX WARN: Code restructure failed: missing block: B:31:0x0048, code lost:
    
        if (r1.hasNext() != false) goto L28;
     */
    /* JADX WARN: Code restructure failed: missing block: B:33:0x0050, code lost:
    
        if (((a.c.a.b.b.e) r6).hasNext() != false) goto L28;
     */
    /* JADX WARN: Code restructure failed: missing block: B:34:?, code lost:
    
        return true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:36:0x0054, code lost:
    
        return false;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public boolean equals(java.lang.Object r6) {
        /*
            r5 = this;
            r0 = 1
            if (r6 != r5) goto L4
            return r0
        L4:
            boolean r1 = r6 instanceof a.c.a.b.b
            r2 = 0
            if (r1 != 0) goto La
            return r2
        La:
            a.c.a.b.b r6 = (a.c.a.b.b) r6
            int r1 = r5.f53d
            int r3 = r6.f53d
            if (r1 == r3) goto L13
            return r2
        L13:
            java.util.Iterator r5 = r5.iterator()
            java.util.Iterator r6 = r6.iterator()
        L1b:
            r1 = r5
            a.c.a.b.b$e r1 = (a.c.a.b.b.e) r1
            boolean r3 = r1.hasNext()
            if (r3 == 0) goto L44
            r3 = r6
            a.c.a.b.b$e r3 = (a.c.a.b.b.e) r3
            boolean r4 = r3.hasNext()
            if (r4 == 0) goto L44
            java.lang.Object r1 = r1.next()
            java.util.Map$Entry r1 = (java.util.Map.Entry) r1
            java.lang.Object r3 = r3.next()
            if (r1 != 0) goto L3b
            if (r3 != 0) goto L43
        L3b:
            if (r1 == 0) goto L1b
            boolean r1 = r1.equals(r3)
            if (r1 != 0) goto L1b
        L43:
            return r2
        L44:
            boolean r5 = r1.hasNext()
            if (r5 != 0) goto L53
            a.c.a.b.b$e r6 = (a.c.a.b.b.e) r6
            boolean r5 = r6.hasNext()
            if (r5 != 0) goto L53
            goto L54
        L53:
            r0 = r2
        L54:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: a.c.a.b.b.equals(java.lang.Object):boolean");
    }

    protected c<K, V> f(K k, V v) {
        c<K, V> cVar = new c<>(k, v);
        this.f53d++;
        c<K, V> cVar2 = this.f51b;
        if (cVar2 == null) {
            this.f50a = cVar;
        } else {
            cVar2.f56c = cVar;
            cVar.f57d = cVar2;
        }
        this.f51b = cVar;
        return cVar;
    }

    public V g(K k) {
        c<K, V> c2 = c(k);
        if (c2 == null) {
            return null;
        }
        this.f53d--;
        if (!this.f52c.isEmpty()) {
            Iterator<f<K, V>> it = this.f52c.keySet().iterator();
            while (it.hasNext()) {
                it.next().a(c2);
            }
        }
        c<K, V> cVar = c2.f57d;
        c<K, V> cVar2 = c2.f56c;
        if (cVar != null) {
            cVar.f56c = cVar2;
        } else {
            this.f50a = cVar2;
        }
        c<K, V> cVar3 = c2.f56c;
        if (cVar3 != null) {
            cVar3.f57d = cVar;
        } else {
            this.f51b = cVar;
        }
        c2.f56c = null;
        c2.f57d = null;
        return c2.f55b;
    }

    public int hashCode() {
        Iterator<Map.Entry<K, V>> it = iterator();
        int i = 0;
        while (true) {
            e eVar = (e) it;
            if (!eVar.hasNext()) {
                return i;
            }
            i += ((Map.Entry) eVar.next()).hashCode();
        }
    }

    @Override // java.lang.Iterable
    public Iterator<Map.Entry<K, V>> iterator() {
        a aVar = new a(this.f50a, this.f51b);
        this.f52c.put(aVar, Boolean.FALSE);
        return aVar;
    }

    public int size() {
        return this.f53d;
    }

    public String toString() {
        StringBuilder j = b.b.a.a.a.j("[");
        Iterator<Map.Entry<K, V>> it = iterator();
        while (true) {
            e eVar = (e) it;
            if (!eVar.hasNext()) {
                j.append("]");
                return j.toString();
            }
            j.append(((Map.Entry) eVar.next()).toString());
            if (eVar.hasNext()) {
                j.append(", ");
            }
        }
    }
}
