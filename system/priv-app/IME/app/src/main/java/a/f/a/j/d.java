package a.f.a.j;

import a.f.a.h;
import a.f.a.j.o.o;
import androidx.constraintlayout.motion.widget.MotionLayout;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;

/* loaded from: classes.dex */
public class d {

    /* renamed from: b, reason: collision with root package name */
    private int f194b;

    /* renamed from: c, reason: collision with root package name */
    private boolean f195c;

    /* renamed from: d, reason: collision with root package name */
    public final e f196d;
    public final a e;
    public d f;
    a.f.a.h i;

    /* renamed from: a, reason: collision with root package name */
    private HashSet<d> f193a = null;
    public int g = 0;
    int h = RecyclerView.UNDEFINED_DURATION;

    public enum a {
        NONE,
        LEFT,
        TOP,
        RIGHT,
        BOTTOM,
        BASELINE,
        CENTER,
        CENTER_X,
        CENTER_Y
    }

    public d(e eVar, a aVar) {
        this.f196d = eVar;
        this.e = aVar;
    }

    public boolean a(d dVar, int i) {
        return b(dVar, i, RecyclerView.UNDEFINED_DURATION, false);
    }

    public boolean b(d dVar, int i, int i2, boolean z) {
        if (dVar == null) {
            n();
            return true;
        }
        if (!z && !m(dVar)) {
            return false;
        }
        this.f = dVar;
        if (dVar.f193a == null) {
            dVar.f193a = new HashSet<>();
        }
        HashSet<d> hashSet = this.f.f193a;
        if (hashSet != null) {
            hashSet.add(this);
        }
        this.g = i;
        this.h = i2;
        return true;
    }

    public void c(int i, ArrayList<o> arrayList, o oVar) {
        HashSet<d> hashSet = this.f193a;
        if (hashSet != null) {
            Iterator<d> it = hashSet.iterator();
            while (it.hasNext()) {
                a.f.a.j.o.i.a(it.next().f196d, i, arrayList, oVar);
            }
        }
    }

    public HashSet<d> d() {
        return this.f193a;
    }

    public int e() {
        if (this.f195c) {
            return this.f194b;
        }
        return 0;
    }

    public int f() {
        d dVar;
        if (this.f196d.P() == 8) {
            return 0;
        }
        return (this.h == Integer.MIN_VALUE || (dVar = this.f) == null || dVar.f196d.P() != 8) ? this.g : this.h;
    }

    public final d g() {
        switch (this.e.ordinal()) {
            case 0:
            case 5:
            case 6:
            case MotionLayout.TOUCH_UP_NEVER_TO_END /* 7 */:
            case 8:
                return null;
            case 1:
                return this.f196d.N;
            case 2:
                return this.f196d.O;
            case 3:
                return this.f196d.L;
            case 4:
                return this.f196d.M;
            default:
                throw new AssertionError(this.e.name());
        }
    }

    public a.f.a.h h() {
        return this.i;
    }

    public boolean i() {
        HashSet<d> hashSet = this.f193a;
        if (hashSet == null) {
            return false;
        }
        Iterator<d> it = hashSet.iterator();
        while (it.hasNext()) {
            if (it.next().g().l()) {
                return true;
            }
        }
        return false;
    }

    public boolean j() {
        HashSet<d> hashSet = this.f193a;
        return hashSet != null && hashSet.size() > 0;
    }

    public boolean k() {
        return this.f195c;
    }

    public boolean l() {
        return this.f != null;
    }

    public boolean m(d dVar) {
        a aVar = a.CENTER_Y;
        a aVar2 = a.RIGHT;
        a aVar3 = a.CENTER_X;
        a aVar4 = a.LEFT;
        a aVar5 = a.BASELINE;
        if (dVar == null) {
            return false;
        }
        a aVar6 = dVar.e;
        a aVar7 = this.e;
        if (aVar6 == aVar7) {
            return aVar7 != aVar5 || (dVar.f196d.T() && this.f196d.T());
        }
        switch (aVar7.ordinal()) {
            case 0:
            case MotionLayout.TOUCH_UP_NEVER_TO_END /* 7 */:
            case 8:
                return false;
            case 1:
            case 3:
                boolean z = aVar6 == aVar4 || aVar6 == aVar2;
                if (dVar.f196d instanceof h) {
                    return z || aVar6 == aVar3;
                }
                return z;
            case 2:
            case 4:
                boolean z2 = aVar6 == a.TOP || aVar6 == a.BOTTOM;
                if (dVar.f196d instanceof h) {
                    return z2 || aVar6 == aVar;
                }
                return z2;
            case 5:
                return (aVar6 == aVar4 || aVar6 == aVar2) ? false : true;
            case 6:
                return (aVar6 == aVar5 || aVar6 == aVar3 || aVar6 == aVar) ? false : true;
            default:
                throw new AssertionError(this.e.name());
        }
    }

    public void n() {
        HashSet<d> hashSet;
        d dVar = this.f;
        if (dVar != null && (hashSet = dVar.f193a) != null) {
            hashSet.remove(this);
            if (this.f.f193a.size() == 0) {
                this.f.f193a = null;
            }
        }
        this.f193a = null;
        this.f = null;
        this.g = 0;
        this.h = RecyclerView.UNDEFINED_DURATION;
        this.f195c = false;
        this.f194b = 0;
    }

    public void o() {
        this.f195c = false;
        this.f194b = 0;
    }

    public void p() {
        a.f.a.h hVar = this.i;
        if (hVar == null) {
            this.i = new a.f.a.h(h.a.f127a);
        } else {
            hVar.d();
        }
    }

    public void q(int i) {
        this.f194b = i;
        this.f195c = true;
    }

    public void r(int i) {
        if (l()) {
            this.h = i;
        }
    }

    public String toString() {
        return this.f196d.t() + ":" + this.e.toString();
    }
}
