package a.e;

import java.util.Map;

/* JADX INFO: Add missing generic type declarations: [E] */
/* loaded from: classes.dex */
class b<E> extends g<E, E> {

    /* renamed from: d, reason: collision with root package name */
    final /* synthetic */ c f65d;

    b(c cVar) {
        this.f65d = cVar;
    }

    @Override // a.e.g
    protected void a() {
        this.f65d.clear();
    }

    @Override // a.e.g
    protected Object b(int i, int i2) {
        return this.f65d.f67b[i];
    }

    @Override // a.e.g
    protected Map<E, E> c() {
        throw new UnsupportedOperationException("not a map");
    }

    @Override // a.e.g
    protected int d() {
        return this.f65d.f68c;
    }

    @Override // a.e.g
    protected int e(Object obj) {
        return this.f65d.c(obj);
    }

    @Override // a.e.g
    protected int f(Object obj) {
        return this.f65d.c(obj);
    }

    @Override // a.e.g
    protected void g(E e, E e2) {
        this.f65d.add(e);
    }

    @Override // a.e.g
    protected void h(int i) {
        this.f65d.f(i);
    }

    @Override // a.e.g
    protected E i(int i, E e) {
        throw new UnsupportedOperationException("not a map");
    }
}
