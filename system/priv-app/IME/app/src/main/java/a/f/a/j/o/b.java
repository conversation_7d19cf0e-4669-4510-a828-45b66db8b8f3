package a.f.a.j.o;

import a.f.a.j.e;
import java.util.ArrayList;

/* loaded from: classes.dex */
public class b {

    /* renamed from: a, reason: collision with root package name */
    private final ArrayList<a.f.a.j.e> f214a = new ArrayList<>();

    /* renamed from: b, reason: collision with root package name */
    private a f215b = new a();

    /* renamed from: c, reason: collision with root package name */
    private a.f.a.j.f f216c;

    public static class a {

        /* renamed from: a, reason: collision with root package name */
        public e.a f217a;

        /* renamed from: b, reason: collision with root package name */
        public e.a f218b;

        /* renamed from: c, reason: collision with root package name */
        public int f219c;

        /* renamed from: d, reason: collision with root package name */
        public int f220d;
        public int e;
        public int f;
        public int g;
        public boolean h;
        public boolean i;
        public int j;
    }

    /* renamed from: a.f.a.j.o.b$b, reason: collision with other inner class name */
    public interface InterfaceC0003b {
        void a();

        void b(a.f.a.j.e eVar, a aVar);
    }

    public b(a.f.a.j.f fVar) {
        this.f216c = fVar;
    }

    private boolean a(InterfaceC0003b interfaceC0003b, a.f.a.j.e eVar, int i) {
        e.a aVar = e.a.FIXED;
        this.f215b.f217a = eVar.z();
        this.f215b.f218b = eVar.O();
        this.f215b.f219c = eVar.Q();
        this.f215b.f220d = eVar.w();
        a aVar2 = this.f215b;
        aVar2.i = false;
        aVar2.j = i;
        e.a aVar3 = aVar2.f217a;
        e.a aVar4 = e.a.MATCH_CONSTRAINT;
        boolean z = aVar3 == aVar4;
        boolean z2 = aVar2.f218b == aVar4;
        boolean z3 = z && eVar.a0 > 0.0f;
        boolean z4 = z2 && eVar.a0 > 0.0f;
        if (z3 && eVar.t[0] == 4) {
            aVar2.f217a = aVar;
        }
        if (z4 && eVar.t[1] == 4) {
            aVar2.f218b = aVar;
        }
        interfaceC0003b.b(eVar, aVar2);
        eVar.S0(this.f215b.e);
        eVar.A0(this.f215b.f);
        eVar.z0(this.f215b.h);
        eVar.q0(this.f215b.g);
        a aVar5 = this.f215b;
        aVar5.j = 0;
        return aVar5.i;
    }

    private void b(a.f.a.j.f fVar, int i, int i2, int i3) {
        int F = fVar.F();
        int E = fVar.E();
        fVar.M0(0);
        fVar.L0(0);
        fVar.S0(i2);
        fVar.A0(i3);
        fVar.M0(F);
        fVar.L0(E);
        this.f216c.r1(i);
        this.f216c.Y0();
    }

    /* JADX WARN: Code restructure failed: missing block: B:76:0x016d, code lost:
    
        if (r3.e.j != false) goto L107;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public long c(a.f.a.j.f r22, int r23, int r24, int r25, int r26, int r27) {
        /*
            Method dump skipped, instructions count: 906
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: a.f.a.j.o.b.c(a.f.a.j.f, int, int, int, int, int):long");
    }

    public void d(a.f.a.j.f fVar) {
        this.f214a.clear();
        int size = fVar.N0.size();
        for (int i = 0; i < size; i++) {
            a.f.a.j.e eVar = fVar.N0.get(i);
            e.a z = eVar.z();
            e.a aVar = e.a.MATCH_CONSTRAINT;
            if (z == aVar || eVar.O() == aVar) {
                this.f214a.add(eVar);
            }
        }
        fVar.P0.i();
    }
}
