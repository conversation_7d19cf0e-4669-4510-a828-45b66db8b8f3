package a.h.e;

import android.util.Base64;
import java.util.List;
import java.util.Objects;

/* loaded from: classes.dex */
public final class e {

    /* renamed from: a, reason: collision with root package name */
    private final String f295a;

    /* renamed from: b, reason: collision with root package name */
    private final String f296b;

    /* renamed from: c, reason: collision with root package name */
    private final String f297c;

    /* renamed from: d, reason: collision with root package name */
    private final List<List<byte[]>> f298d;
    private final String e;

    public e(String str, String str2, String str3, List<List<byte[]>> list) {
        this.f295a = str;
        this.f296b = str2;
        this.f297c = str3;
        Objects.requireNonNull(list);
        this.f298d = list;
        this.e = str + "-" + str2 + "-" + str3;
    }

    public List<List<byte[]>> a() {
        return this.f298d;
    }

    String b() {
        return this.e;
    }

    public String c() {
        return this.f295a;
    }

    public String d() {
        return this.f296b;
    }

    public String e() {
        return this.f297c;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder();
        StringBuilder j = b.b.a.a.a.j("FontRequest {mProviderAuthority: ");
        j.append(this.f295a);
        j.append(", mProviderPackage: ");
        j.append(this.f296b);
        j.append(", mQuery: ");
        j.append(this.f297c);
        j.append(", mCertificates:");
        sb.append(j.toString());
        for (int i = 0; i < this.f298d.size(); i++) {
            sb.append(" [");
            List<byte[]> list = this.f298d.get(i);
            for (int i2 = 0; i2 < list.size(); i2++) {
                sb.append(" \"");
                sb.append(Base64.encodeToString(list.get(i2), 0));
                sb.append("\"");
            }
            sb.append(" ]");
        }
        sb.append("}");
        sb.append("mCertificatesArray: 0");
        return sb.toString();
    }
}
