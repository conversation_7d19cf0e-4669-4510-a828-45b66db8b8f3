package a.h.g;

/* loaded from: classes.dex */
public class e<T> implements d<T> {

    /* renamed from: a, reason: collision with root package name */
    private final Object[] f355a;

    /* renamed from: b, reason: collision with root package name */
    private int f356b;

    public e(int i) {
        if (i <= 0) {
            throw new IllegalArgumentException("The max pool size must be > 0");
        }
        this.f355a = new Object[i];
    }

    @Override // a.h.g.d
    public boolean a(T t) {
        int i;
        boolean z;
        int i2 = 0;
        while (true) {
            i = this.f356b;
            if (i2 >= i) {
                z = false;
                break;
            }
            if (this.f355a[i2] == t) {
                z = true;
                break;
            }
            i2++;
        }
        if (z) {
            throw new IllegalStateException("Already in the pool!");
        }
        Object[] objArr = this.f355a;
        if (i >= objArr.length) {
            return false;
        }
        objArr[i] = t;
        this.f356b = i + 1;
        return true;
    }

    @Override // a.h.g.d
    public T b() {
        int i = this.f356b;
        if (i <= 0) {
            return null;
        }
        int i2 = i - 1;
        Object[] objArr = this.f355a;
        T t = (T) objArr[i2];
        objArr[i2] = null;
        this.f356b = i - 1;
        return t;
    }
}
