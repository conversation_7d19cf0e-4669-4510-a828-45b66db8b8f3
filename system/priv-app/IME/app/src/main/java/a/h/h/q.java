package a.h.h;

import a.h.h.a;
import a.h.h.x.b;
import android.annotation.SuppressLint;
import android.os.Build;
import android.util.Log;
import android.view.PointerIcon;
import android.view.View;
import android.view.ViewParent;
import android.view.ViewTreeObserver;
import android.view.WindowInsets;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityManager;
import java.util.ArrayList;
import java.util.List;
import java.util.WeakHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import org.libpag.R;

@SuppressLint({"PrivateConstructorForUtilityClass"})
/* loaded from: classes.dex */
public class q {

    /* renamed from: a, reason: collision with root package name */
    private static final AtomicInteger f375a = new AtomicInteger(1);

    /* renamed from: b, reason: collision with root package name */
    private static WeakHashMap<View, s> f376b = null;

    /* renamed from: c, reason: collision with root package name */
    private static boolean f377c = false;

    /* renamed from: d, reason: collision with root package name */
    private static final int[] f378d = {R.id.accessibility_custom_action_0, R.id.accessibility_custom_action_1, R.id.accessibility_custom_action_2, R.id.accessibility_custom_action_3, R.id.accessibility_custom_action_4, R.id.accessibility_custom_action_5, R.id.accessibility_custom_action_6, R.id.accessibility_custom_action_7, R.id.accessibility_custom_action_8, R.id.accessibility_custom_action_9, R.id.accessibility_custom_action_10, R.id.accessibility_custom_action_11, R.id.accessibility_custom_action_12, R.id.accessibility_custom_action_13, R.id.accessibility_custom_action_14, R.id.accessibility_custom_action_15, R.id.accessibility_custom_action_16, R.id.accessibility_custom_action_17, R.id.accessibility_custom_action_18, R.id.accessibility_custom_action_19, R.id.accessibility_custom_action_20, R.id.accessibility_custom_action_21, R.id.accessibility_custom_action_22, R.id.accessibility_custom_action_23, R.id.accessibility_custom_action_24, R.id.accessibility_custom_action_25, R.id.accessibility_custom_action_26, R.id.accessibility_custom_action_27, R.id.accessibility_custom_action_28, R.id.accessibility_custom_action_29, R.id.accessibility_custom_action_30, R.id.accessibility_custom_action_31};
    public static final /* synthetic */ int e = 0;

    static class a implements ViewTreeObserver.OnGlobalLayoutListener, View.OnAttachStateChangeListener {

        /* renamed from: a, reason: collision with root package name */
        private WeakHashMap<View, Boolean> f379a = new WeakHashMap<>();

        a() {
        }

        @Override // android.view.ViewTreeObserver.OnGlobalLayoutListener
        public void onGlobalLayout() {
        }

        @Override // android.view.View.OnAttachStateChangeListener
        public void onViewAttachedToWindow(View view) {
            view.getViewTreeObserver().addOnGlobalLayoutListener(this);
        }

        @Override // android.view.View.OnAttachStateChangeListener
        public void onViewDetachedFromWindow(View view) {
        }
    }

    static abstract class b<T> {

        /* renamed from: a, reason: collision with root package name */
        private final int f380a;

        /* renamed from: b, reason: collision with root package name */
        private final Class<T> f381b;

        /* renamed from: c, reason: collision with root package name */
        private final int f382c;

        /* renamed from: d, reason: collision with root package name */
        private final int f383d;

        b(int i, Class<T> cls, int i2) {
            this.f380a = i;
            this.f381b = cls;
            this.f383d = 0;
            this.f382c = i2;
        }

        b(int i, Class<T> cls, int i2, int i3) {
            this.f380a = i;
            this.f381b = cls;
            this.f383d = i2;
            this.f382c = i3;
        }

        boolean a(Boolean bool, Boolean bool2) {
            return (bool == null ? false : bool.booleanValue()) == (bool2 == null ? false : bool2.booleanValue());
        }

        abstract T b(View view);

        abstract void c(View view, T t);

        T d(View view) {
            if (Build.VERSION.SDK_INT >= this.f382c) {
                return b(view);
            }
            T t = (T) view.getTag(this.f380a);
            if (this.f381b.isInstance(t)) {
                return t;
            }
            return null;
        }

        void e(View view, T t) {
            if (Build.VERSION.SDK_INT >= this.f382c) {
                c(view, t);
                return;
            }
            if (f(d(view), t)) {
                a.h.h.a e = q.e(view);
                if (e == null) {
                    e = new a.h.h.a();
                }
                q.n(view, e);
                view.setTag(this.f380a, t);
                q.i(view, this.f383d);
            }
        }

        abstract boolean f(T t, T t2);
    }

    static {
        new a();
    }

    public static int a(View view, CharSequence charSequence, a.h.h.x.d dVar) {
        List<b.a> g = g(view);
        int i = 0;
        int i2 = -1;
        while (true) {
            int[] iArr = f378d;
            if (i >= iArr.length || i2 != -1) {
                break;
            }
            int i3 = iArr[i];
            boolean z = true;
            for (int i4 = 0; i4 < g.size(); i4++) {
                z &= g.get(i4).b() != i3;
            }
            if (z) {
                i2 = i3;
            }
            i++;
        }
        if (i2 != -1) {
            b(view, new b.a(i2, charSequence, dVar));
        }
        return i2;
    }

    private static void b(View view, b.a aVar) {
        a.h.h.a e2 = e(view);
        if (e2 == null) {
            e2 = new a.h.h.a();
        }
        n(view, e2);
        l(aVar.b(), view);
        g(view).add(aVar);
        i(view, 0);
    }

    public static s c(View view) {
        if (f376b == null) {
            f376b = new WeakHashMap<>();
        }
        s sVar = f376b.get(view);
        if (sVar != null) {
            return sVar;
        }
        s sVar2 = new s(view);
        f376b.put(view, sVar2);
        return sVar2;
    }

    public static w d(View view, w wVar) {
        WindowInsets p = wVar.p();
        if (p != null) {
            WindowInsets dispatchApplyWindowInsets = view.dispatchApplyWindowInsets(p);
            if (!dispatchApplyWindowInsets.equals(p)) {
                return w.r(dispatchApplyWindowInsets, view);
            }
        }
        return wVar;
    }

    public static a.h.h.a e(View view) {
        View.AccessibilityDelegate accessibilityDelegate = view.getAccessibilityDelegate();
        if (accessibilityDelegate == null) {
            return null;
        }
        return accessibilityDelegate instanceof a.C0009a ? ((a.C0009a) accessibilityDelegate).f361a : new a.h.h.a(accessibilityDelegate);
    }

    public static CharSequence f(View view) {
        return new n(R.id.tag_accessibility_pane_title, CharSequence.class, 8, 28).d(view);
    }

    private static List<b.a> g(View view) {
        ArrayList arrayList = (ArrayList) view.getTag(R.id.tag_accessibility_actions);
        if (arrayList != null) {
            return arrayList;
        }
        ArrayList arrayList2 = new ArrayList();
        view.setTag(R.id.tag_accessibility_actions, arrayList2);
        return arrayList2;
    }

    public static w h(View view) {
        WindowInsets rootWindowInsets = view.getRootWindowInsets();
        if (rootWindowInsets == null) {
            return null;
        }
        w r = w.r(rootWindowInsets, null);
        r.o(r);
        r.d(view.getRootView());
        return r;
    }

    static void i(View view, int i) {
        AccessibilityManager accessibilityManager = (AccessibilityManager) view.getContext().getSystemService("accessibility");
        if (accessibilityManager.isEnabled()) {
            boolean z = f(view) != null && view.getVisibility() == 0;
            if (view.getAccessibilityLiveRegion() != 0 || z) {
                AccessibilityEvent obtain = AccessibilityEvent.obtain();
                obtain.setEventType(z ? 32 : 2048);
                obtain.setContentChangeTypes(i);
                if (z) {
                    obtain.getText().add(f(view));
                    if (view.getImportantForAccessibility() == 0) {
                        view.setImportantForAccessibility(1);
                    }
                    ViewParent parent = view.getParent();
                    while (true) {
                        if (!(parent instanceof View)) {
                            break;
                        }
                        if (((View) parent).getImportantForAccessibility() == 4) {
                            view.setImportantForAccessibility(2);
                            break;
                        }
                        parent = parent.getParent();
                    }
                }
                view.sendAccessibilityEventUnchecked(obtain);
                return;
            }
            if (i == 32) {
                AccessibilityEvent obtain2 = AccessibilityEvent.obtain();
                view.onInitializeAccessibilityEvent(obtain2);
                obtain2.setEventType(32);
                obtain2.setContentChangeTypes(i);
                obtain2.setSource(view);
                view.onPopulateAccessibilityEvent(obtain2);
                obtain2.getText().add(f(view));
                accessibilityManager.sendAccessibilityEvent(obtain2);
                return;
            }
            if (view.getParent() != null) {
                try {
                    view.getParent().notifySubtreeAccessibilityStateChanged(view, view, i);
                } catch (AbstractMethodError e2) {
                    Log.e("ViewCompat", view.getParent().getClass().getSimpleName() + " does not fully implement ViewParent", e2);
                }
            }
        }
    }

    public static w j(View view, w wVar) {
        WindowInsets p = wVar.p();
        if (p != null) {
            WindowInsets onApplyWindowInsets = view.onApplyWindowInsets(p);
            if (!onApplyWindowInsets.equals(p)) {
                return w.r(onApplyWindowInsets, view);
            }
        }
        return wVar;
    }

    public static void k(View view, int i) {
        l(i, view);
        i(view, 0);
    }

    private static void l(int i, View view) {
        List<b.a> g = g(view);
        for (int i2 = 0; i2 < g.size(); i2++) {
            if (g.get(i2).b() == i) {
                g.remove(i2);
                return;
            }
        }
    }

    public static void m(View view, b.a aVar, CharSequence charSequence, a.h.h.x.d dVar) {
        if (dVar != null) {
            b(view, aVar.a(null, dVar));
        } else {
            l(aVar.b(), view);
            i(view, 0);
        }
    }

    public static void n(View view, a.h.h.a aVar) {
        if (aVar == null && (view.getAccessibilityDelegate() instanceof a.C0009a)) {
            aVar = new a.h.h.a();
        }
        view.setAccessibilityDelegate(aVar == null ? null : aVar.c());
    }

    public static void o(View view, boolean z) {
        new p(R.id.tag_accessibility_heading, Boolean.class, 28).e(view, Boolean.valueOf(z));
    }

    public static void p(View view, j jVar) {
        if (jVar == null) {
            view.setOnApplyWindowInsetsListener((View.OnApplyWindowInsetsListener) view.getTag(R.id.tag_window_insets_animation_callback));
        } else {
            view.setOnApplyWindowInsetsListener(new r(view, jVar));
        }
    }

    public static void q(View view, l lVar) {
        view.setPointerIcon((PointerIcon) (lVar != null ? lVar.a() : null));
    }
}
