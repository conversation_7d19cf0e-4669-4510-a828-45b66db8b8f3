package a.f.a.i.a;

import java.lang.reflect.Array;
import java.text.DecimalFormat;
import java.util.Arrays;

/* loaded from: classes.dex */
public abstract class j {

    /* renamed from: a, reason: collision with root package name */
    protected b f168a;

    /* renamed from: b, reason: collision with root package name */
    protected int[] f169b = new int[10];

    /* renamed from: c, reason: collision with root package name */
    protected float[] f170c = new float[10];

    /* renamed from: d, reason: collision with root package name */
    private int f171d;
    private String e;

    public float a(float f) {
        return (float) this.f168a.b(f, 0);
    }

    public void b(int i, float f) {
        int[] iArr = this.f169b;
        if (iArr.length < this.f171d + 1) {
            this.f169b = Arrays.copyOf(iArr, iArr.length * 2);
            float[] fArr = this.f170c;
            this.f170c = Arrays.copyOf(fArr, fArr.length * 2);
        }
        int[] iArr2 = this.f169b;
        int i2 = this.f171d;
        iArr2[i2] = i;
        this.f170c[i2] = f;
        this.f171d = i2 + 1;
    }

    public void c(String str) {
        this.e = str;
    }

    public void d(int i) {
        int i2;
        int i3 = this.f171d;
        if (i3 == 0) {
            return;
        }
        int[] iArr = this.f169b;
        float[] fArr = this.f170c;
        int[] iArr2 = new int[iArr.length + 10];
        iArr2[0] = i3 - 1;
        iArr2[1] = 0;
        int i4 = 2;
        while (i4 > 0) {
            int i5 = i4 - 1;
            int i6 = iArr2[i5];
            i4 = i5 - 1;
            int i7 = iArr2[i4];
            if (i6 < i7) {
                int i8 = iArr[i7];
                int i9 = i6;
                int i10 = i9;
                while (i9 < i7) {
                    if (iArr[i9] <= i8) {
                        int i11 = iArr[i10];
                        iArr[i10] = iArr[i9];
                        iArr[i9] = i11;
                        float f = fArr[i10];
                        fArr[i10] = fArr[i9];
                        fArr[i9] = f;
                        i10++;
                    }
                    i9++;
                }
                int i12 = iArr[i10];
                iArr[i10] = iArr[i7];
                iArr[i7] = i12;
                float f2 = fArr[i10];
                fArr[i10] = fArr[i7];
                fArr[i7] = f2;
                int i13 = i4 + 1;
                iArr2[i4] = i10 - 1;
                int i14 = i13 + 1;
                iArr2[i13] = i6;
                int i15 = i14 + 1;
                iArr2[i14] = i7;
                i4 = i15 + 1;
                iArr2[i15] = i10 + 1;
            }
        }
        int i16 = 1;
        for (int i17 = 1; i17 < this.f171d; i17++) {
            int[] iArr3 = this.f169b;
            if (iArr3[i17 - 1] != iArr3[i17]) {
                i16++;
            }
        }
        double[] dArr = new double[i16];
        double[][] dArr2 = (double[][]) Array.newInstance((Class<?>) double.class, i16, 1);
        int i18 = 0;
        for (0; i2 < this.f171d; i2 + 1) {
            if (i2 > 0) {
                int[] iArr4 = this.f169b;
                i2 = iArr4[i2] == iArr4[i2 - 1] ? i2 + 1 : 0;
            }
            dArr[i18] = this.f169b[i2] * 0.01d;
            dArr2[i18][0] = this.f170c[i2];
            i18++;
        }
        this.f168a = b.a(i, dArr, dArr2);
    }

    public String toString() {
        String str = this.e;
        DecimalFormat decimalFormat = new DecimalFormat("##.##");
        for (int i = 0; i < this.f171d; i++) {
            str = str + "[" + this.f169b[i] + " , " + decimalFormat.format(this.f170c[i]) + "] ";
        }
        return str;
    }
}
