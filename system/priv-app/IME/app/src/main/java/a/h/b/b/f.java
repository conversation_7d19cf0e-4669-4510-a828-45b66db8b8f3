package a.h.b.b;

import android.graphics.Typeface;
import android.os.Handler;
import android.os.Looper;

/* loaded from: classes.dex */
public abstract class f {

    class a implements Runnable {

        /* renamed from: a, reason: collision with root package name */
        final /* synthetic */ Typeface f275a;

        a(Typeface typeface) {
            this.f275a = typeface;
        }

        @Override // java.lang.Runnable
        public void run() {
            f.this.e(this.f275a);
        }
    }

    class b implements Runnable {

        /* renamed from: a, reason: collision with root package name */
        final /* synthetic */ int f277a;

        b(int i) {
            this.f277a = i;
        }

        @Override // java.lang.Runnable
        public void run() {
            f.this.d(this.f277a);
        }
    }

    public static Handler c(Handler handler) {
        return handler == null ? new Handler(Looper.getMainLooper()) : handler;
    }

    public final void a(int i, Handler handler) {
        c(handler).post(new b(i));
    }

    public final void b(Typeface typeface, Handler handler) {
        c(handler).post(new a(typeface));
    }

    public abstract void d(int i);

    public abstract void e(Typeface typeface);
}
