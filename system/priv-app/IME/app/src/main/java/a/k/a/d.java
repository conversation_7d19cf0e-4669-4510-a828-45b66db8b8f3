package a.k.a;

import a.k.a.b;
import android.os.Looper;
import android.util.AndroidRuntimeException;

/* loaded from: classes.dex */
public final class d extends b<d> {
    private e r;
    private float s;

    public <K> d(K k, c<K> cVar) {
        super(k, cVar);
        this.r = null;
        this.s = Float.MAX_VALUE;
    }

    @Override // a.k.a.b
    boolean h(long j) {
        double d2;
        double d3;
        long j2;
        e eVar;
        if (this.s != Float.MAX_VALUE) {
            this.r.a();
            long j3 = j / 2;
            b.h g = this.r.g(this.f447b, this.f446a, j3);
            this.r.d(this.s);
            this.s = Float.MAX_VALUE;
            e eVar2 = this.r;
            d2 = g.f450a;
            d3 = g.f451b;
            eVar = eVar2;
            j2 = j3;
        } else {
            e eVar3 = this.r;
            d2 = this.f447b;
            d3 = this.f446a;
            j2 = j;
            eVar = eVar3;
        }
        b.h g2 = eVar.g(d2, d3, j2);
        float f = g2.f450a;
        this.f447b = f;
        this.f446a = g2.f451b;
        float max = Math.max(f, this.g);
        this.f447b = max;
        float min = Math.min(max, Float.MAX_VALUE);
        this.f447b = min;
        if (!this.r.b(min, this.f446a)) {
            return false;
        }
        this.f447b = this.r.a();
        this.f446a = 0.0f;
        return true;
    }

    public void i(float f) {
        if (this.f) {
            this.s = f;
            return;
        }
        if (this.r == null) {
            this.r = new e(f);
        }
        this.r.d(f);
        e eVar = this.r;
        if (eVar == null) {
            throw new UnsupportedOperationException("Incomplete SpringAnimation: Either final position or a spring force needs to be set.");
        }
        double a2 = eVar.a();
        if (a2 > Float.MAX_VALUE) {
            throw new UnsupportedOperationException("Final position of the spring cannot be greater than the max value.");
        }
        if (a2 < this.g) {
            throw new UnsupportedOperationException("Final position of the spring cannot be less than the min value.");
        }
        this.r.f(d());
        if (Looper.myLooper() != Looper.getMainLooper()) {
            throw new AndroidRuntimeException("Animations may only be started on the main thread");
        }
        boolean z = this.f;
        if (z || z) {
            return;
        }
        this.f = true;
        if (!this.f448c) {
            this.f447b = this.e.a(this.f449d);
        }
        float f2 = this.f447b;
        if (f2 > Float.MAX_VALUE || f2 < this.g) {
            throw new IllegalArgumentException("Starting value need to be in between min value and max value");
        }
        a.c().a(this, 0L);
    }

    public d j(e eVar) {
        this.r = eVar;
        return this;
    }
}
