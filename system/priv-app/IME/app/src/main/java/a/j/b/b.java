package a.j.b;

import a.j.b.a;
import android.graphics.Rect;
import java.util.Comparator;
import java.util.Objects;

/* loaded from: classes.dex */
class b {

    public interface a<T> {
    }

    /* renamed from: a.j.b.b$b, reason: collision with other inner class name */
    public interface InterfaceC0016b<T, V> {
    }

    /* JADX INFO: Access modifiers changed from: private */
    static class c<T> implements Comparator<T> {

        /* renamed from: a, reason: collision with root package name */
        private final Rect f428a = new Rect();

        /* renamed from: b, reason: collision with root package name */
        private final Rect f429b = new Rect();

        /* renamed from: c, reason: collision with root package name */
        private final boolean f430c;

        /* renamed from: d, reason: collision with root package name */
        private final a<T> f431d;

        c(boolean z, a<T> aVar) {
            this.f430c = z;
            this.f431d = aVar;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // java.util.Comparator
        public int compare(T t, T t2) {
            Rect rect = this.f428a;
            Rect rect2 = this.f429b;
            Objects.requireNonNull((a.C0015a) this.f431d);
            ((a.h.h.x.b) t).h(rect);
            Objects.requireNonNull((a.C0015a) this.f431d);
            ((a.h.h.x.b) t2).h(rect2);
            int i = rect.top;
            int i2 = rect2.top;
            if (i < i2) {
                return -1;
            }
            if (i > i2) {
                return 1;
            }
            int i3 = rect.left;
            int i4 = rect2.left;
            if (i3 < i4) {
                return this.f430c ? 1 : -1;
            }
            if (i3 > i4) {
                return this.f430c ? -1 : 1;
            }
            int i5 = rect.bottom;
            int i6 = rect2.bottom;
            if (i5 < i6) {
                return -1;
            }
            if (i5 > i6) {
                return 1;
            }
            int i7 = rect.right;
            int i8 = rect2.right;
            if (i7 < i8) {
                return this.f430c ? 1 : -1;
            }
            if (i7 > i8) {
                return this.f430c ? -1 : 1;
            }
            return 0;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x0026, code lost:
    
        if (r10.bottom <= r12.top) goto L24;
     */
    /* JADX WARN: Code restructure failed: missing block: B:11:0x0045, code lost:
    
        r7 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:34:0x0043, code lost:
    
        r7 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:38:0x0033, code lost:
    
        if (r10.right <= r12.left) goto L24;
     */
    /* JADX WARN: Code restructure failed: missing block: B:40:0x003a, code lost:
    
        if (r10.top >= r12.bottom) goto L24;
     */
    /* JADX WARN: Code restructure failed: missing block: B:42:0x0041, code lost:
    
        if (r10.left >= r12.right) goto L24;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static boolean a(int r9, android.graphics.Rect r10, android.graphics.Rect r11, android.graphics.Rect r12) {
        /*
            boolean r0 = b(r9, r10, r11)
            boolean r1 = b(r9, r10, r12)
            r2 = 0
            if (r1 != 0) goto L7d
            if (r0 != 0) goto Lf
            goto L7d
        Lf:
            java.lang.String r0 = "direction must be one of {FOCUS_UP, FOCUS_DOWN, FOCUS_LEFT, FOCUS_RIGHT}."
            r1 = 130(0x82, float:1.82E-43)
            r3 = 33
            r4 = 66
            r5 = 17
            r6 = 1
            if (r9 == r5) goto L3d
            if (r9 == r3) goto L36
            if (r9 == r4) goto L2f
            if (r9 != r1) goto L29
            int r7 = r10.bottom
            int r8 = r12.top
            if (r7 > r8) goto L45
            goto L43
        L29:
            java.lang.IllegalArgumentException r9 = new java.lang.IllegalArgumentException
            r9.<init>(r0)
            throw r9
        L2f:
            int r7 = r10.right
            int r8 = r12.left
            if (r7 > r8) goto L45
            goto L43
        L36:
            int r7 = r10.top
            int r8 = r12.bottom
            if (r7 < r8) goto L45
            goto L43
        L3d:
            int r7 = r10.left
            int r8 = r12.right
            if (r7 < r8) goto L45
        L43:
            r7 = r6
            goto L46
        L45:
            r7 = r2
        L46:
            if (r7 != 0) goto L49
            return r6
        L49:
            if (r9 == r5) goto L7c
            if (r9 != r4) goto L4e
            goto L7c
        L4e:
            int r11 = e(r9, r10, r11)
            if (r9 == r5) goto L6f
            if (r9 == r3) goto L6a
            if (r9 == r4) goto L65
            if (r9 != r1) goto L5f
            int r9 = r12.bottom
            int r10 = r10.bottom
            goto L73
        L5f:
            java.lang.IllegalArgumentException r9 = new java.lang.IllegalArgumentException
            r9.<init>(r0)
            throw r9
        L65:
            int r9 = r12.right
            int r10 = r10.right
            goto L73
        L6a:
            int r9 = r10.top
            int r10 = r12.top
            goto L73
        L6f:
            int r9 = r10.left
            int r10 = r12.left
        L73:
            int r9 = r9 - r10
            int r9 = java.lang.Math.max(r6, r9)
            if (r11 >= r9) goto L7b
            r2 = r6
        L7b:
            return r2
        L7c:
            return r6
        L7d:
            return r2
        */
        throw new UnsupportedOperationException("Method not decompiled: a.j.b.b.a(int, android.graphics.Rect, android.graphics.Rect, android.graphics.Rect):boolean");
    }

    private static boolean b(int i, Rect rect, Rect rect2) {
        if (i != 17) {
            if (i != 33) {
                if (i != 66) {
                    if (i != 130) {
                        throw new IllegalArgumentException("direction must be one of {FOCUS_UP, FOCUS_DOWN, FOCUS_LEFT, FOCUS_RIGHT}.");
                    }
                }
            }
            return rect2.right >= rect.left && rect2.left <= rect.right;
        }
        return rect2.bottom >= rect.top && rect2.top <= rect.bottom;
    }

    /* JADX WARN: Code restructure failed: missing block: B:24:0x00a5, code lost:
    
        if (r13 < ((r14 * r14) + ((r12 * 13) * r12))) goto L37;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:13:0x005a  */
    /* JADX WARN: Removed duplicated region for block: B:27:0x00ac  */
    /* JADX WARN: Removed duplicated region for block: B:30:0x00b0 A[SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static <L, T> T c(L r16, a.j.b.b.InterfaceC0016b<L, T> r17, a.j.b.b.a<T> r18, T r19, android.graphics.Rect r20, int r21) {
        /*
            r0 = r20
            r1 = r21
            android.graphics.Rect r2 = new android.graphics.Rect
            r2.<init>(r0)
            r3 = 1
            r4 = 17
            r5 = 0
            if (r1 == r4) goto L3a
            r4 = 33
            if (r1 == r4) goto L31
            r4 = 66
            if (r1 == r4) goto L2a
            r4 = 130(0x82, float:1.82E-43)
            if (r1 != r4) goto L22
            int r4 = r20.height()
            int r4 = r4 + r3
            int r4 = -r4
            goto L36
        L22:
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            java.lang.String r1 = "direction must be one of {FOCUS_UP, FOCUS_DOWN, FOCUS_LEFT, FOCUS_RIGHT}."
            r0.<init>(r1)
            throw r0
        L2a:
            int r4 = r20.width()
            int r4 = r4 + r3
            int r4 = -r4
            goto L3f
        L31:
            int r4 = r20.height()
            int r4 = r4 + r3
        L36:
            r2.offset(r5, r4)
            goto L42
        L3a:
            int r4 = r20.width()
            int r4 = r4 + r3
        L3f:
            r2.offset(r4, r5)
        L42:
            r4 = 0
            r6 = r17
            a.j.b.a$b r6 = (a.j.b.a.b) r6
            java.util.Objects.requireNonNull(r6)
            r6 = r16
            a.e.i r6 = (a.e.i) r6
            int r7 = r6.k()
            android.graphics.Rect r8 = new android.graphics.Rect
            r8.<init>()
            r9 = r5
        L58:
            if (r9 >= r7) goto Lb3
            java.lang.Object r10 = r6.l(r9)
            a.h.h.x.b r10 = (a.h.h.x.b) r10
            r11 = r19
            if (r10 != r11) goto L65
            goto Lb0
        L65:
            r12 = r18
            a.j.b.a$a r12 = (a.j.b.a.C0015a) r12
            java.util.Objects.requireNonNull(r12)
            r10.h(r8)
            boolean r12 = d(r0, r8, r1)
            if (r12 != 0) goto L76
            goto La9
        L76:
            boolean r12 = d(r0, r2, r1)
            if (r12 != 0) goto L7d
            goto La7
        L7d:
            boolean r12 = a(r1, r0, r8, r2)
            if (r12 == 0) goto L84
            goto La7
        L84:
            boolean r12 = a(r1, r0, r2, r8)
            if (r12 == 0) goto L8b
            goto La9
        L8b:
            int r12 = e(r1, r0, r8)
            int r13 = f(r1, r0, r8)
            int r14 = r12 * 13
            int r14 = r14 * r12
            int r13 = r13 * r13
            int r13 = r13 + r14
            int r12 = e(r1, r0, r2)
            int r14 = f(r1, r0, r2)
            int r15 = r12 * 13
            int r15 = r15 * r12
            int r14 = r14 * r14
            int r14 = r14 + r15
            if (r13 >= r14) goto La9
        La7:
            r12 = r3
            goto Laa
        La9:
            r12 = r5
        Laa:
            if (r12 == 0) goto Lb0
            r2.set(r8)
            r4 = r10
        Lb0:
            int r9 = r9 + 1
            goto L58
        Lb3:
            return r4
        */
        throw new UnsupportedOperationException("Method not decompiled: a.j.b.b.c(java.lang.Object, a.j.b.b$b, a.j.b.b$a, java.lang.Object, android.graphics.Rect, int):java.lang.Object");
    }

    private static boolean d(Rect rect, Rect rect2, int i) {
        if (i == 17) {
            int i2 = rect.right;
            int i3 = rect2.right;
            return (i2 > i3 || rect.left >= i3) && rect.left > rect2.left;
        }
        if (i == 33) {
            int i4 = rect.bottom;
            int i5 = rect2.bottom;
            return (i4 > i5 || rect.top >= i5) && rect.top > rect2.top;
        }
        if (i == 66) {
            int i6 = rect.left;
            int i7 = rect2.left;
            return (i6 < i7 || rect.right <= i7) && rect.right < rect2.right;
        }
        if (i != 130) {
            throw new IllegalArgumentException("direction must be one of {FOCUS_UP, FOCUS_DOWN, FOCUS_LEFT, FOCUS_RIGHT}.");
        }
        int i8 = rect.top;
        int i9 = rect2.top;
        return (i8 < i9 || rect.bottom <= i9) && rect.bottom < rect2.bottom;
    }

    private static int e(int i, Rect rect, Rect rect2) {
        int i2;
        int i3;
        if (i == 17) {
            i2 = rect.left;
            i3 = rect2.right;
        } else if (i == 33) {
            i2 = rect.top;
            i3 = rect2.bottom;
        } else if (i == 66) {
            i2 = rect2.left;
            i3 = rect.right;
        } else {
            if (i != 130) {
                throw new IllegalArgumentException("direction must be one of {FOCUS_UP, FOCUS_DOWN, FOCUS_LEFT, FOCUS_RIGHT}.");
            }
            i2 = rect2.top;
            i3 = rect.bottom;
        }
        return Math.max(0, i2 - i3);
    }

    private static int f(int i, Rect rect, Rect rect2) {
        int height;
        int i2;
        int height2;
        if (i != 17) {
            if (i != 33) {
                if (i != 66) {
                    if (i != 130) {
                        throw new IllegalArgumentException("direction must be one of {FOCUS_UP, FOCUS_DOWN, FOCUS_LEFT, FOCUS_RIGHT}.");
                    }
                }
            }
            height = (rect.width() / 2) + rect.left;
            i2 = rect2.left;
            height2 = rect2.width();
            return Math.abs(height - ((height2 / 2) + i2));
        }
        height = (rect.height() / 2) + rect.top;
        i2 = rect2.top;
        height2 = rect2.height();
        return Math.abs(height - ((height2 / 2) + i2));
    }
}
