package a.h;

import android.R;

/* loaded from: classes.dex */
public final class a {

    /* renamed from: a, reason: collision with root package name */
    public static final int[] f260a = {R.attr.color, R.attr.alpha, org.libpag.R.attr.alpha};

    /* renamed from: b, reason: collision with root package name */
    public static final int[] f261b = {org.libpag.R.attr.fontProviderAuthority, org.libpag.R.attr.fontProviderCerts, org.libpag.R.attr.fontProviderFetchStrategy, org.libpag.R.attr.fontProviderFetchTimeout, org.libpag.R.attr.fontProviderPackage, org.libpag.R.attr.fontProviderQuery, org.libpag.R.attr.fontProviderSystemFontFamily};

    /* renamed from: c, reason: collision with root package name */
    public static final int[] f262c = {R.attr.font, R.attr.font<PERSON>eight, <PERSON>.attr.font<PERSON>ty<PERSON>, R.attr.ttcIndex, R.attr.fontVariationSettings, org.libpag.R.attr.font, org.libpag.R.attr.fontStyle, org.libpag.R.attr.fontVariationSettings, org.libpag.R.attr.fontWeight, org.libpag.R.attr.ttcIndex};

    /* renamed from: d, reason: collision with root package name */
    public static final int[] f263d = {R.attr.startColor, R.attr.endColor, R.attr.type, R.attr.centerX, R.attr.centerY, R.attr.gradientRadius, R.attr.tileMode, R.attr.centerColor, R.attr.startX, R.attr.startY, R.attr.endX, R.attr.endY};
    public static final int[] e = {R.attr.color, R.attr.offset};
}
