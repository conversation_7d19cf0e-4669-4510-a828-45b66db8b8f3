package a.j.b;

import a.h.h.q;
import android.content.Context;
import android.util.Log;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.animation.Interpolator;
import android.widget.OverScroller;
import java.util.Arrays;
import java.util.Objects;

/* loaded from: classes.dex */
public class c {
    private static final Interpolator w = new a();

    /* renamed from: a, reason: collision with root package name */
    private int f432a;

    /* renamed from: b, reason: collision with root package name */
    private int f433b;

    /* renamed from: d, reason: collision with root package name */
    private float[] f435d;
    private float[] e;
    private float[] f;
    private float[] g;
    private int[] h;
    private int[] i;
    private int[] j;
    private int k;
    private VelocityTracker l;
    private float m;
    private float n;
    private int o;
    private int p;
    private OverScroller q;
    private final AbstractC0017c r;
    private View s;
    private boolean t;
    private final ViewGroup u;

    /* renamed from: c, reason: collision with root package name */
    private int f434c = -1;
    private final Runnable v = new b();

    static class a implements Interpolator {
        a() {
        }

        @Override // android.animation.TimeInterpolator
        public float getInterpolation(float f) {
            float f2 = f - 1.0f;
            return (f2 * f2 * f2 * f2 * f2) + 1.0f;
        }
    }

    class b implements Runnable {
        b() {
        }

        @Override // java.lang.Runnable
        public void run() {
            c.this.A(0);
        }
    }

    /* renamed from: a.j.b.c$c, reason: collision with other inner class name */
    public static abstract class AbstractC0017c {
        public abstract int a(View view, int i, int i2);

        public abstract int b(View view, int i, int i2);

        public int c(View view) {
            return 0;
        }

        public int d(View view) {
            return 0;
        }

        public void e(int i, int i2) {
        }

        public boolean f(int i) {
            return false;
        }

        public void g(int i, int i2) {
        }

        public void h(View view, int i) {
        }

        public abstract void i(int i);

        public abstract void j(View view, int i, int i2, int i3, int i4);

        public abstract void k(View view, float f, float f2);

        public abstract boolean l(View view, int i);
    }

    private c(Context context, ViewGroup viewGroup, AbstractC0017c abstractC0017c) {
        if (abstractC0017c == null) {
            throw new IllegalArgumentException("Callback may not be null");
        }
        this.u = viewGroup;
        this.r = abstractC0017c;
        ViewConfiguration viewConfiguration = ViewConfiguration.get(context);
        this.o = (int) ((context.getResources().getDisplayMetrics().density * 20.0f) + 0.5f);
        this.f433b = viewConfiguration.getScaledTouchSlop();
        this.m = viewConfiguration.getScaledMaximumFlingVelocity();
        this.n = viewConfiguration.getScaledMinimumFlingVelocity();
        this.q = new OverScroller(context, w);
    }

    private boolean c(float f, float f2, int i, int i2) {
        float abs = Math.abs(f);
        float abs2 = Math.abs(f2);
        if ((this.h[i] & i2) != i2 || (this.p & i2) == 0 || (this.j[i] & i2) == i2 || (this.i[i] & i2) == i2) {
            return false;
        }
        int i3 = this.f433b;
        if (abs <= i3 && abs2 <= i3) {
            return false;
        }
        if (abs >= abs2 * 0.5f || !this.r.f(i2)) {
            return (this.i[i] & i2) == 0 && abs > ((float) this.f433b);
        }
        int[] iArr = this.j;
        iArr[i] = iArr[i] | i2;
        return false;
    }

    private boolean e(View view, float f, float f2) {
        if (view == null) {
            return false;
        }
        boolean z = this.r.c(view) > 0;
        boolean z2 = this.r.d(view) > 0;
        if (!z || !z2) {
            return z ? Math.abs(f) > ((float) this.f433b) : z2 && Math.abs(f2) > ((float) this.f433b);
        }
        float f3 = (f2 * f2) + (f * f);
        int i = this.f433b;
        return f3 > ((float) (i * i));
    }

    private float f(float f, float f2, float f3) {
        float abs = Math.abs(f);
        if (abs < f2) {
            return 0.0f;
        }
        return abs > f3 ? f > 0.0f ? f3 : -f3 : f;
    }

    private int g(int i, int i2, int i3) {
        int abs = Math.abs(i);
        if (abs < i2) {
            return 0;
        }
        return abs > i3 ? i > 0 ? i3 : -i3 : i;
    }

    private void h(int i) {
        if (this.f435d == null || !t(i)) {
            return;
        }
        this.f435d[i] = 0.0f;
        this.e[i] = 0.0f;
        this.f[i] = 0.0f;
        this.g[i] = 0.0f;
        this.h[i] = 0;
        this.i[i] = 0;
        this.j[i] = 0;
        this.k = (~(1 << i)) & this.k;
    }

    private int i(int i, int i2, int i3) {
        if (i == 0) {
            return 0;
        }
        float width = this.u.getWidth() / 2;
        float sin = (((float) Math.sin((Math.min(1.0f, Math.abs(i) / r3) - 0.5f) * 0.47123894f)) * width) + width;
        int abs = Math.abs(i2);
        return Math.min(abs > 0 ? Math.round(Math.abs(sin / abs) * 1000.0f) * 4 : (int) (((Math.abs(i) / i3) + 1.0f) * 256.0f), 600);
    }

    public static c k(ViewGroup viewGroup, float f, AbstractC0017c abstractC0017c) {
        c cVar = new c(viewGroup.getContext(), viewGroup, abstractC0017c);
        cVar.f433b = (int) ((1.0f / f) * cVar.f433b);
        return cVar;
    }

    public static c l(ViewGroup viewGroup, AbstractC0017c abstractC0017c) {
        return new c(viewGroup.getContext(), viewGroup, abstractC0017c);
    }

    private void m(float f, float f2) {
        this.t = true;
        this.r.k(this.s, f, f2);
        this.t = false;
        if (this.f432a == 1) {
            A(0);
        }
    }

    private boolean o(int i, int i2, int i3, int i4) {
        float f;
        float f2;
        float f3;
        float f4;
        int left = this.s.getLeft();
        int top = this.s.getTop();
        int i5 = i - left;
        int i6 = i2 - top;
        if (i5 == 0 && i6 == 0) {
            this.q.abortAnimation();
            A(0);
            return false;
        }
        View view = this.s;
        int g = g(i3, (int) this.n, (int) this.m);
        int g2 = g(i4, (int) this.n, (int) this.m);
        int abs = Math.abs(i5);
        int abs2 = Math.abs(i6);
        int abs3 = Math.abs(g);
        int abs4 = Math.abs(g2);
        int i7 = abs3 + abs4;
        int i8 = abs + abs2;
        if (g != 0) {
            f = abs3;
            f2 = i7;
        } else {
            f = abs;
            f2 = i8;
        }
        float f5 = f / f2;
        if (g2 != 0) {
            f3 = abs4;
            f4 = i7;
        } else {
            f3 = abs2;
            f4 = i8;
        }
        int i9 = i(i5, g, this.r.c(view));
        this.q.startScroll(left, top, i5, i6, (int) ((i(i6, g2, this.r.d(view)) * (f3 / f4)) + (i9 * f5)));
        A(2);
        return true;
    }

    private boolean u(int i) {
        if (t(i)) {
            return true;
        }
        Log.e("ViewDragHelper", "Ignoring pointerId=" + i + " because ACTION_DOWN was not received for this pointer before ACTION_MOVE. It likely happened because  ViewDragHelper did not receive all the events in the event stream.");
        return false;
    }

    private void w() {
        this.l.computeCurrentVelocity(1000, this.m);
        m(f(this.l.getXVelocity(this.f434c), this.n, this.m), f(this.l.getYVelocity(this.f434c), this.n, this.m));
    }

    private void x(float f, float f2, int i) {
        int i2 = c(f, f2, i, 1) ? 1 : 0;
        if (c(f2, f, i, 4)) {
            i2 |= 4;
        }
        if (c(f, f2, i, 2)) {
            i2 |= 2;
        }
        if (c(f2, f, i, 8)) {
            i2 |= 8;
        }
        if (i2 != 0) {
            int[] iArr = this.i;
            iArr[i] = iArr[i] | i2;
            this.r.e(i2, i);
        }
    }

    private void y(float f, float f2, int i) {
        float[] fArr = this.f435d;
        if (fArr == null || fArr.length <= i) {
            int i2 = i + 1;
            float[] fArr2 = new float[i2];
            float[] fArr3 = new float[i2];
            float[] fArr4 = new float[i2];
            float[] fArr5 = new float[i2];
            int[] iArr = new int[i2];
            int[] iArr2 = new int[i2];
            int[] iArr3 = new int[i2];
            if (fArr != null) {
                System.arraycopy(fArr, 0, fArr2, 0, fArr.length);
                float[] fArr6 = this.e;
                System.arraycopy(fArr6, 0, fArr3, 0, fArr6.length);
                float[] fArr7 = this.f;
                System.arraycopy(fArr7, 0, fArr4, 0, fArr7.length);
                float[] fArr8 = this.g;
                System.arraycopy(fArr8, 0, fArr5, 0, fArr8.length);
                int[] iArr4 = this.h;
                System.arraycopy(iArr4, 0, iArr, 0, iArr4.length);
                int[] iArr5 = this.i;
                System.arraycopy(iArr5, 0, iArr2, 0, iArr5.length);
                int[] iArr6 = this.j;
                System.arraycopy(iArr6, 0, iArr3, 0, iArr6.length);
            }
            this.f435d = fArr2;
            this.e = fArr3;
            this.f = fArr4;
            this.g = fArr5;
            this.h = iArr;
            this.i = iArr2;
            this.j = iArr3;
        }
        float[] fArr9 = this.f435d;
        this.f[i] = f;
        fArr9[i] = f;
        float[] fArr10 = this.e;
        this.g[i] = f2;
        fArr10[i] = f2;
        int[] iArr7 = this.h;
        int i3 = (int) f;
        int i4 = (int) f2;
        int i5 = i3 < this.u.getLeft() + this.o ? 1 : 0;
        if (i4 < this.u.getTop() + this.o) {
            i5 |= 4;
        }
        if (i3 > this.u.getRight() - this.o) {
            i5 |= 2;
        }
        if (i4 > this.u.getBottom() - this.o) {
            i5 |= 8;
        }
        iArr7[i] = i5;
        this.k |= 1 << i;
    }

    private void z(MotionEvent motionEvent) {
        int pointerCount = motionEvent.getPointerCount();
        for (int i = 0; i < pointerCount; i++) {
            int pointerId = motionEvent.getPointerId(i);
            if (u(pointerId)) {
                float x = motionEvent.getX(i);
                float y = motionEvent.getY(i);
                this.f[pointerId] = x;
                this.g[pointerId] = y;
            }
        }
    }

    void A(int i) {
        this.u.removeCallbacks(this.v);
        if (this.f432a != i) {
            this.f432a = i;
            this.r.i(i);
            if (this.f432a == 0) {
                this.s = null;
            }
        }
    }

    public void B(int i) {
        this.p = i;
    }

    public void C(float f) {
        this.n = f;
    }

    public boolean D(int i, int i2) {
        if (this.t) {
            return o(i, i2, (int) this.l.getXVelocity(this.f434c), (int) this.l.getYVelocity(this.f434c));
        }
        throw new IllegalStateException("Cannot settleCapturedViewAt outside of a call to Callback#onViewReleased");
    }

    /* JADX WARN: Code restructure failed: missing block: B:51:0x00de, code lost:
    
        if (r12 != r11) goto L54;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public boolean E(android.view.MotionEvent r17) {
        /*
            Method dump skipped, instructions count: 314
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: a.j.b.c.E(android.view.MotionEvent):boolean");
    }

    public boolean F(View view, int i, int i2) {
        this.s = view;
        this.f434c = -1;
        boolean o = o(i, i2, 0, 0);
        if (!o && this.f432a == 0 && this.s != null) {
            this.s = null;
        }
        return o;
    }

    boolean G(View view, int i) {
        if (view == this.s && this.f434c == i) {
            return true;
        }
        if (view == null || !this.r.l(view, i)) {
            return false;
        }
        this.f434c = i;
        b(view, i);
        return true;
    }

    public void a() {
        this.f434c = -1;
        float[] fArr = this.f435d;
        if (fArr != null) {
            Arrays.fill(fArr, 0.0f);
            Arrays.fill(this.e, 0.0f);
            Arrays.fill(this.f, 0.0f);
            Arrays.fill(this.g, 0.0f);
            Arrays.fill(this.h, 0);
            Arrays.fill(this.i, 0);
            Arrays.fill(this.j, 0);
            this.k = 0;
        }
        VelocityTracker velocityTracker = this.l;
        if (velocityTracker != null) {
            velocityTracker.recycle();
            this.l = null;
        }
    }

    public void b(View view, int i) {
        if (view.getParent() != this.u) {
            StringBuilder j = b.b.a.a.a.j("captureChildView: parameter must be a descendant of the ViewDragHelper's tracked parent view (");
            j.append(this.u);
            j.append(")");
            throw new IllegalArgumentException(j.toString());
        }
        this.s = view;
        this.f434c = i;
        this.r.h(view, i);
        A(1);
    }

    /* JADX WARN: Code restructure failed: missing block: B:15:0x003d, code lost:
    
        if (r7 > (r3 * r3)) goto L20;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x003f, code lost:
    
        r3 = true;
     */
    /* JADX WARN: Removed duplicated region for block: B:18:0x0062 A[LOOP:0: B:2:0x0005->B:18:0x0062, LOOP_END] */
    /* JADX WARN: Removed duplicated region for block: B:19:0x0061 A[SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public boolean d(int r10) {
        /*
            r9 = this;
            float[] r0 = r9.f435d
            int r0 = r0.length
            r1 = 0
            r2 = r1
        L5:
            if (r2 >= r0) goto L65
            boolean r3 = r9.t(r2)
            r4 = 1
            if (r3 != 0) goto Lf
            goto L41
        Lf:
            r3 = r10 & 1
            if (r3 != r4) goto L15
            r3 = r4
            goto L16
        L15:
            r3 = r1
        L16:
            r5 = 2
            r6 = r10 & 2
            if (r6 != r5) goto L1d
            r5 = r4
            goto L1e
        L1d:
            r5 = r1
        L1e:
            float[] r6 = r9.f
            r6 = r6[r2]
            float[] r7 = r9.f435d
            r7 = r7[r2]
            float r6 = r6 - r7
            float[] r7 = r9.g
            r7 = r7[r2]
            float[] r8 = r9.e
            r8 = r8[r2]
            float r7 = r7 - r8
            if (r3 == 0) goto L43
            if (r5 == 0) goto L43
            float r6 = r6 * r6
            float r7 = r7 * r7
            float r7 = r7 + r6
            int r3 = r9.f433b
            int r3 = r3 * r3
            float r3 = (float) r3
            int r3 = (r7 > r3 ? 1 : (r7 == r3 ? 0 : -1))
            if (r3 <= 0) goto L41
        L3f:
            r3 = r4
            goto L5f
        L41:
            r3 = r1
            goto L5f
        L43:
            if (r3 == 0) goto L51
            float r3 = java.lang.Math.abs(r6)
            int r5 = r9.f433b
            float r5 = (float) r5
            int r3 = (r3 > r5 ? 1 : (r3 == r5 ? 0 : -1))
            if (r3 <= 0) goto L41
            goto L3f
        L51:
            if (r5 == 0) goto L41
            float r3 = java.lang.Math.abs(r7)
            int r5 = r9.f433b
            float r5 = (float) r5
            int r3 = (r3 > r5 ? 1 : (r3 == r5 ? 0 : -1))
            if (r3 <= 0) goto L41
            goto L3f
        L5f:
            if (r3 == 0) goto L62
            return r4
        L62:
            int r2 = r2 + 1
            goto L5
        L65:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: a.j.b.c.d(int):boolean");
    }

    public boolean j(boolean z) {
        if (this.f432a == 2) {
            boolean computeScrollOffset = this.q.computeScrollOffset();
            int currX = this.q.getCurrX();
            int currY = this.q.getCurrY();
            int left = currX - this.s.getLeft();
            int top = currY - this.s.getTop();
            if (left != 0) {
                View view = this.s;
                int i = q.e;
                view.offsetLeftAndRight(left);
            }
            if (top != 0) {
                View view2 = this.s;
                int i2 = q.e;
                view2.offsetTopAndBottom(top);
            }
            if (left != 0 || top != 0) {
                this.r.j(this.s, currX, currY, left, top);
            }
            if (computeScrollOffset && currX == this.q.getFinalX() && currY == this.q.getFinalY()) {
                this.q.abortAnimation();
                computeScrollOffset = false;
            }
            if (!computeScrollOffset) {
                if (z) {
                    this.u.post(this.v);
                } else {
                    A(0);
                }
            }
        }
        return this.f432a == 2;
    }

    public View n(int i, int i2) {
        for (int childCount = this.u.getChildCount() - 1; childCount >= 0; childCount--) {
            ViewGroup viewGroup = this.u;
            Objects.requireNonNull(this.r);
            View childAt = viewGroup.getChildAt(childCount);
            if (i >= childAt.getLeft() && i < childAt.getRight() && i2 >= childAt.getTop() && i2 < childAt.getBottom()) {
                return childAt;
            }
        }
        return null;
    }

    public View p() {
        return this.s;
    }

    public int q() {
        return this.o;
    }

    public int r() {
        return this.f433b;
    }

    public int s() {
        return this.f432a;
    }

    public boolean t(int i) {
        return (this.k & (1 << i)) != 0;
    }

    public void v(MotionEvent motionEvent) {
        int i;
        int actionMasked = motionEvent.getActionMasked();
        int actionIndex = motionEvent.getActionIndex();
        if (actionMasked == 0) {
            a();
        }
        if (this.l == null) {
            this.l = VelocityTracker.obtain();
        }
        this.l.addMovement(motionEvent);
        int i2 = 0;
        if (actionMasked == 0) {
            float x = motionEvent.getX();
            float y = motionEvent.getY();
            int pointerId = motionEvent.getPointerId(0);
            View n = n((int) x, (int) y);
            y(x, y, pointerId);
            G(n, pointerId);
            int i3 = this.h[pointerId];
            int i4 = this.p;
            if ((i3 & i4) != 0) {
                this.r.g(i3 & i4, pointerId);
                return;
            }
            return;
        }
        if (actionMasked != 1) {
            if (actionMasked == 2) {
                if (this.f432a != 1) {
                    int pointerCount = motionEvent.getPointerCount();
                    while (i2 < pointerCount) {
                        int pointerId2 = motionEvent.getPointerId(i2);
                        if (u(pointerId2)) {
                            float x2 = motionEvent.getX(i2);
                            float y2 = motionEvent.getY(i2);
                            float f = x2 - this.f435d[pointerId2];
                            float f2 = y2 - this.e[pointerId2];
                            x(f, f2, pointerId2);
                            if (this.f432a != 1) {
                                View n2 = n((int) x2, (int) y2);
                                if (e(n2, f, f2) && G(n2, pointerId2)) {
                                    break;
                                }
                            } else {
                                break;
                            }
                        }
                        i2++;
                    }
                } else {
                    if (!u(this.f434c)) {
                        return;
                    }
                    int findPointerIndex = motionEvent.findPointerIndex(this.f434c);
                    float x3 = motionEvent.getX(findPointerIndex);
                    float y3 = motionEvent.getY(findPointerIndex);
                    float[] fArr = this.f;
                    int i5 = this.f434c;
                    int i6 = (int) (x3 - fArr[i5]);
                    int i7 = (int) (y3 - this.g[i5]);
                    int left = this.s.getLeft() + i6;
                    int top = this.s.getTop() + i7;
                    int left2 = this.s.getLeft();
                    int top2 = this.s.getTop();
                    if (i6 != 0) {
                        left = this.r.a(this.s, left, i6);
                        int i8 = q.e;
                        this.s.offsetLeftAndRight(left - left2);
                    }
                    int i9 = left;
                    if (i7 != 0) {
                        top = this.r.b(this.s, top, i7);
                        int i10 = q.e;
                        this.s.offsetTopAndBottom(top - top2);
                    }
                    int i11 = top;
                    if (i6 != 0 || i7 != 0) {
                        this.r.j(this.s, i9, i11, i9 - left2, i11 - top2);
                    }
                }
                z(motionEvent);
                return;
            }
            if (actionMasked != 3) {
                if (actionMasked != 5) {
                    if (actionMasked != 6) {
                        return;
                    }
                    int pointerId3 = motionEvent.getPointerId(actionIndex);
                    if (this.f432a == 1 && pointerId3 == this.f434c) {
                        int pointerCount2 = motionEvent.getPointerCount();
                        while (true) {
                            if (i2 >= pointerCount2) {
                                i = -1;
                                break;
                            }
                            int pointerId4 = motionEvent.getPointerId(i2);
                            if (pointerId4 != this.f434c) {
                                View n3 = n((int) motionEvent.getX(i2), (int) motionEvent.getY(i2));
                                View view = this.s;
                                if (n3 == view && G(view, pointerId4)) {
                                    i = this.f434c;
                                    break;
                                }
                            }
                            i2++;
                        }
                        if (i == -1) {
                            w();
                        }
                    }
                    h(pointerId3);
                    return;
                }
                int pointerId5 = motionEvent.getPointerId(actionIndex);
                float x4 = motionEvent.getX(actionIndex);
                float y4 = motionEvent.getY(actionIndex);
                y(x4, y4, pointerId5);
                if (this.f432a == 0) {
                    G(n((int) x4, (int) y4), pointerId5);
                    int i12 = this.h[pointerId5];
                    int i13 = this.p;
                    if ((i12 & i13) != 0) {
                        this.r.g(i12 & i13, pointerId5);
                        return;
                    }
                    return;
                }
                int i14 = (int) x4;
                int i15 = (int) y4;
                View view2 = this.s;
                if (view2 != null && i14 >= view2.getLeft() && i14 < view2.getRight() && i15 >= view2.getTop() && i15 < view2.getBottom()) {
                    i2 = 1;
                }
                if (i2 != 0) {
                    G(this.s, pointerId5);
                    return;
                }
                return;
            }
            if (this.f432a == 1) {
                m(0.0f, 0.0f);
            }
        } else if (this.f432a == 1) {
            w();
        }
        a();
    }
}
