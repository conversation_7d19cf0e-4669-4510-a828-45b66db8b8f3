package a.f.a.i.a;

/* loaded from: classes.dex */
public class p {

    /* renamed from: a, reason: collision with root package name */
    float f185a;

    /* renamed from: b, reason: collision with root package name */
    float f186b;

    /* renamed from: c, reason: collision with root package name */
    float f187c;

    /* renamed from: d, reason: collision with root package name */
    float f188d;
    float e;
    float f;

    public void a(float f, float f2, int i, int i2, float[] fArr) {
        float f3 = fArr[0];
        float f4 = fArr[1];
        float f5 = (f2 - 0.5f) * 2.0f;
        float f6 = f3 + this.f187c;
        float f7 = f4 + this.f188d;
        float f8 = (this.f185a * (f - 0.5f) * 2.0f) + f6;
        float f9 = (this.f186b * f5) + f7;
        float radians = (float) Math.toRadians(this.f);
        float radians2 = (float) Math.toRadians(this.e);
        double d2 = radians;
        double d3 = i2 * f5;
        float sin = (((float) ((Math.sin(d2) * ((-i) * r7)) - (Math.cos(d2) * d3))) * radians2) + f8;
        float cos = (radians2 * ((float) ((Math.cos(d2) * (i * r7)) - (Math.sin(d2) * d3)))) + f9;
        fArr[0] = sin;
        fArr[1] = cos;
    }

    public void b() {
        this.e = 0.0f;
        this.f188d = 0.0f;
        this.f187c = 0.0f;
        this.f186b = 0.0f;
        this.f185a = 0.0f;
    }

    public void c(e eVar, float f) {
        if (eVar != null) {
            this.e = eVar.b(f);
        }
    }

    public void d(j jVar, float f) {
        if (jVar != null) {
            double d2 = f;
            this.e = (float) jVar.f168a.e(d2, 0);
            this.f = (float) jVar.f168a.b(d2, 0);
        }
    }

    public void e(e eVar, e eVar2, float f) {
        if (eVar != null) {
            this.f185a = eVar.b(f);
        }
        if (eVar2 != null) {
            this.f186b = eVar2.b(f);
        }
    }

    public void f(j jVar, j jVar2, float f) {
        if (jVar != null) {
            this.f185a = (float) jVar.f168a.e(f, 0);
        }
        if (jVar2 != null) {
            this.f186b = (float) jVar2.f168a.e(f, 0);
        }
    }

    public void g(e eVar, e eVar2, float f) {
        if (eVar != null) {
            this.f187c = eVar.b(f);
        }
        if (eVar2 != null) {
            this.f188d = eVar2.b(f);
        }
    }

    public void h(j jVar, j jVar2, float f) {
        if (jVar != null) {
            this.f187c = (float) jVar.f168a.e(f, 0);
        }
        if (jVar2 != null) {
            this.f188d = (float) jVar2.f168a.e(f, 0);
        }
    }
}
