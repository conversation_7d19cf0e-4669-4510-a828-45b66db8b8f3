package a.f.a;

import a.f.a.b;
import a.f.a.h;
import java.util.Arrays;
import java.util.Objects;

/* loaded from: classes.dex */
public class d {
    public static boolean p = false;
    private static int q = 1000;
    public static long r;

    /* renamed from: c, reason: collision with root package name */
    private a f117c;
    b[] f;
    final c l;
    private a o;

    /* renamed from: a, reason: collision with root package name */
    public boolean f115a = false;

    /* renamed from: b, reason: collision with root package name */
    int f116b = 0;

    /* renamed from: d, reason: collision with root package name */
    private int f118d = 32;
    private int e = 32;
    public boolean g = false;
    private boolean[] h = new boolean[32];
    int i = 1;
    int j = 0;
    private int k = 32;
    private h[] m = new h[q];
    private int n = 0;

    interface a {
        h a(d dVar, boolean[] zArr);

        void b(h hVar);

        void clear();

        boolean isEmpty();
    }

    public d() {
        this.f = null;
        this.f = new b[32];
        u();
        c cVar = new c();
        this.l = cVar;
        this.f117c = new g(cVar);
        this.o = new b(cVar);
    }

    private h a(h.a aVar, String str) {
        h a2 = this.l.f113c.a();
        if (a2 == null) {
            a2 = new h(aVar);
        } else {
            a2.d();
        }
        a2.i = aVar;
        int i = this.n;
        int i2 = q;
        if (i >= i2) {
            int i3 = i2 * 2;
            q = i3;
            this.m = (h[]) Arrays.copyOf(this.m, i3);
        }
        h[] hVarArr = this.m;
        int i4 = this.n;
        this.n = i4 + 1;
        hVarArr[i4] = a2;
        return a2;
    }

    private final void i(b bVar) {
        int i;
        if (bVar.e) {
            bVar.f107a.e(this, bVar.f108b);
        } else {
            b[] bVarArr = this.f;
            int i2 = this.j;
            bVarArr[i2] = bVar;
            h hVar = bVar.f107a;
            hVar.f125c = i2;
            this.j = i2 + 1;
            hVar.f(this, bVar);
        }
        if (this.f115a) {
            int i3 = 0;
            while (i3 < this.j) {
                if (this.f[i3] == null) {
                    System.out.println("WTF");
                }
                b[] bVarArr2 = this.f;
                if (bVarArr2[i3] != null && bVarArr2[i3].e) {
                    b bVar2 = bVarArr2[i3];
                    bVar2.f107a.e(this, bVar2.f108b);
                    this.l.f112b.b(bVar2);
                    this.f[i3] = null;
                    int i4 = i3 + 1;
                    int i5 = i4;
                    while (true) {
                        i = this.j;
                        if (i4 >= i) {
                            break;
                        }
                        b[] bVarArr3 = this.f;
                        int i6 = i4 - 1;
                        bVarArr3[i6] = bVarArr3[i4];
                        if (bVarArr3[i6].f107a.f125c == i4) {
                            bVarArr3[i6].f107a.f125c = i6;
                        }
                        i5 = i4;
                        i4++;
                    }
                    if (i5 < i) {
                        this.f[i5] = null;
                    }
                    this.j = i - 1;
                    i3--;
                }
                i3++;
            }
            this.f115a = false;
        }
    }

    private void j() {
        for (int i = 0; i < this.j; i++) {
            b bVar = this.f[i];
            bVar.f107a.e = bVar.f108b;
        }
    }

    private void q() {
        int i = this.f118d * 2;
        this.f118d = i;
        this.f = (b[]) Arrays.copyOf(this.f, i);
        c cVar = this.l;
        cVar.f114d = (h[]) Arrays.copyOf(cVar.f114d, this.f118d);
        int i2 = this.f118d;
        this.h = new boolean[i2];
        this.e = i2;
        this.k = i2;
    }

    private final int t(a aVar) {
        for (int i = 0; i < this.i; i++) {
            this.h[i] = false;
        }
        boolean z = false;
        int i2 = 0;
        while (!z) {
            i2++;
            if (i2 >= this.i * 2) {
                return i2;
            }
            h hVar = ((b) aVar).f107a;
            if (hVar != null) {
                this.h[hVar.f124b] = true;
            }
            h a2 = aVar.a(this, this.h);
            if (a2 != null) {
                boolean[] zArr = this.h;
                int i3 = a2.f124b;
                if (zArr[i3]) {
                    return i2;
                }
                zArr[i3] = true;
            }
            if (a2 != null) {
                float f = Float.MAX_VALUE;
                int i4 = -1;
                for (int i5 = 0; i5 < this.j; i5++) {
                    b bVar = this.f[i5];
                    if (bVar.f107a.i != h.a.f127a && !bVar.e && bVar.f110d.d(a2)) {
                        float b2 = bVar.f110d.b(a2);
                        if (b2 < 0.0f) {
                            float f2 = (-bVar.f108b) / b2;
                            if (f2 < f) {
                                i4 = i5;
                                f = f2;
                            }
                        }
                    }
                }
                if (i4 > -1) {
                    b bVar2 = this.f[i4];
                    bVar2.f107a.f125c = -1;
                    bVar2.m(a2);
                    h hVar2 = bVar2.f107a;
                    hVar2.f125c = i4;
                    hVar2.f(this, bVar2);
                }
            } else {
                z = true;
            }
        }
        return i2;
    }

    private void u() {
        for (int i = 0; i < this.j; i++) {
            b bVar = this.f[i];
            if (bVar != null) {
                this.l.f112b.b(bVar);
            }
            this.f[i] = null;
        }
    }

    public void b(h hVar, h hVar2, int i, float f, h hVar3, h hVar4, int i2, int i3) {
        int i4;
        float f2;
        b m = m();
        if (hVar2 == hVar3) {
            m.f110d.f(hVar, 1.0f);
            m.f110d.f(hVar4, 1.0f);
            m.f110d.f(hVar2, -2.0f);
        } else {
            if (f == 0.5f) {
                m.f110d.f(hVar, 1.0f);
                m.f110d.f(hVar2, -1.0f);
                m.f110d.f(hVar3, -1.0f);
                m.f110d.f(hVar4, 1.0f);
                if (i > 0 || i2 > 0) {
                    i4 = (-i) + i2;
                    f2 = i4;
                }
            } else if (f <= 0.0f) {
                m.f110d.f(hVar, -1.0f);
                m.f110d.f(hVar2, 1.0f);
                f2 = i;
            } else if (f >= 1.0f) {
                m.f110d.f(hVar4, -1.0f);
                m.f110d.f(hVar3, 1.0f);
                i4 = -i2;
                f2 = i4;
            } else {
                float f3 = 1.0f - f;
                m.f110d.f(hVar, f3 * 1.0f);
                m.f110d.f(hVar2, f3 * (-1.0f));
                m.f110d.f(hVar3, (-1.0f) * f);
                m.f110d.f(hVar4, 1.0f * f);
                if (i > 0 || i2 > 0) {
                    m.f108b = (i2 * f) + ((-i) * f3);
                }
            }
            m.f108b = f2;
        }
        if (i3 != 8) {
            m.c(this, i3);
        }
        c(m);
    }

    /* JADX WARN: Removed duplicated region for block: B:85:0x013b A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:86:0x013c  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void c(a.f.a.b r11) {
        /*
            Method dump skipped, instructions count: 323
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: a.f.a.d.c(a.f.a.b):void");
    }

    public b d(h hVar, h hVar2, int i, int i2) {
        if (i2 == 8 && hVar2.f && hVar.f125c == -1) {
            hVar.e(this, hVar2.e + i);
            return null;
        }
        b m = m();
        boolean z = false;
        if (i != 0) {
            if (i < 0) {
                i *= -1;
                z = true;
            }
            m.f108b = i;
        }
        if (z) {
            m.f110d.f(hVar, 1.0f);
            m.f110d.f(hVar2, -1.0f);
        } else {
            m.f110d.f(hVar, -1.0f);
            m.f110d.f(hVar2, 1.0f);
        }
        if (i2 != 8) {
            m.c(this, i2);
        }
        c(m);
        return m;
    }

    public void e(h hVar, int i) {
        b m;
        b.a aVar;
        float f;
        int i2 = hVar.f125c;
        if (i2 == -1) {
            hVar.e(this, i);
            for (int i3 = 0; i3 < this.f116b + 1; i3++) {
                h hVar2 = this.l.f114d[i3];
            }
            return;
        }
        if (i2 != -1) {
            b bVar = this.f[i2];
            if (!bVar.e) {
                if (bVar.f110d.k() == 0) {
                    bVar.e = true;
                } else {
                    m = m();
                    if (i < 0) {
                        m.f108b = i * (-1);
                        aVar = m.f110d;
                        f = 1.0f;
                    } else {
                        m.f108b = i;
                        aVar = m.f110d;
                        f = -1.0f;
                    }
                    aVar.f(hVar, f);
                }
            }
            bVar.f108b = i;
            return;
        }
        m = m();
        m.f107a = hVar;
        float f2 = i;
        hVar.e = f2;
        m.f108b = f2;
        m.e = true;
        c(m);
    }

    public void f(h hVar, h hVar2, int i, int i2) {
        b m = m();
        h n = n();
        n.f126d = 0;
        m.g(hVar, hVar2, n, i);
        if (i2 != 8) {
            m.f110d.f(k(i2, null), (int) (m.f110d.b(n) * (-1.0f)));
        }
        c(m);
    }

    public void g(h hVar, h hVar2, int i, int i2) {
        b m = m();
        h n = n();
        n.f126d = 0;
        m.h(hVar, hVar2, n, i);
        if (i2 != 8) {
            m.f110d.f(k(i2, null), (int) (m.f110d.b(n) * (-1.0f)));
        }
        c(m);
    }

    public void h(h hVar, h hVar2, h hVar3, h hVar4, float f, int i) {
        b m = m();
        m.e(hVar, hVar2, hVar3, hVar4, f);
        if (i != 8) {
            m.c(this, i);
        }
        c(m);
    }

    public h k(int i, String str) {
        if (this.i + 1 >= this.e) {
            q();
        }
        h a2 = a(h.a.f130d, str);
        int i2 = this.f116b + 1;
        this.f116b = i2;
        this.i++;
        a2.f124b = i2;
        a2.f126d = i;
        this.l.f114d[i2] = a2;
        this.f117c.b(a2);
        return a2;
    }

    public h l(Object obj) {
        h hVar = null;
        if (obj == null) {
            return null;
        }
        if (this.i + 1 >= this.e) {
            q();
        }
        if (obj instanceof a.f.a.j.d) {
            a.f.a.j.d dVar = (a.f.a.j.d) obj;
            hVar = dVar.h();
            if (hVar == null) {
                dVar.p();
                hVar = dVar.h();
            }
            int i = hVar.f124b;
            if (i == -1 || i > this.f116b || this.l.f114d[i] == null) {
                if (i != -1) {
                    hVar.d();
                }
                int i2 = this.f116b + 1;
                this.f116b = i2;
                this.i++;
                hVar.f124b = i2;
                hVar.i = h.a.f127a;
                this.l.f114d[i2] = hVar;
            }
        }
        return hVar;
    }

    public b m() {
        b a2 = this.l.f112b.a();
        if (a2 == null) {
            a2 = new b(this.l);
            r++;
        } else {
            a2.f107a = null;
            a2.f110d.clear();
            a2.f108b = 0.0f;
            a2.e = false;
        }
        h.b();
        return a2;
    }

    public h n() {
        if (this.i + 1 >= this.e) {
            q();
        }
        h a2 = a(h.a.f129c, null);
        int i = this.f116b + 1;
        this.f116b = i;
        this.i++;
        a2.f124b = i;
        this.l.f114d[i] = a2;
        return a2;
    }

    public c o() {
        return this.l;
    }

    public int p(Object obj) {
        h h = ((a.f.a.j.d) obj).h();
        if (h != null) {
            return (int) (h.e + 0.5f);
        }
        return 0;
    }

    public void r() {
        if (this.f117c.isEmpty()) {
            j();
            return;
        }
        if (this.g) {
            boolean z = false;
            int i = 0;
            while (true) {
                if (i >= this.j) {
                    z = true;
                    break;
                } else if (!this.f[i].e) {
                    break;
                } else {
                    i++;
                }
            }
            if (z) {
                j();
                return;
            }
        }
        s(this.f117c);
    }

    void s(a aVar) {
        float f;
        int i;
        boolean z;
        h.a aVar2 = h.a.f127a;
        int i2 = 0;
        while (true) {
            f = 0.0f;
            i = 1;
            if (i2 >= this.j) {
                z = false;
                break;
            }
            b[] bVarArr = this.f;
            if (bVarArr[i2].f107a.i != aVar2 && bVarArr[i2].f108b < 0.0f) {
                z = true;
                break;
            }
            i2++;
        }
        if (z) {
            boolean z2 = false;
            int i3 = 0;
            while (!z2) {
                i3 += i;
                float f2 = Float.MAX_VALUE;
                int i4 = 0;
                int i5 = -1;
                int i6 = -1;
                int i7 = 0;
                while (i4 < this.j) {
                    b bVar = this.f[i4];
                    if (bVar.f107a.i != aVar2 && !bVar.e && bVar.f108b < f) {
                        int k = bVar.f110d.k();
                        int i8 = 0;
                        while (i8 < k) {
                            h g = bVar.f110d.g(i8);
                            float b2 = bVar.f110d.b(g);
                            if (b2 > f) {
                                for (int i9 = 0; i9 < 9; i9++) {
                                    float f3 = g.g[i9] / b2;
                                    if ((f3 < f2 && i9 == i7) || i9 > i7) {
                                        i6 = g.f124b;
                                        i7 = i9;
                                        f2 = f3;
                                        i5 = i4;
                                    }
                                }
                            }
                            i8++;
                            f = 0.0f;
                        }
                    }
                    i4++;
                    f = 0.0f;
                }
                if (i5 != -1) {
                    b bVar2 = this.f[i5];
                    bVar2.f107a.f125c = -1;
                    bVar2.m(this.l.f114d[i6]);
                    h hVar = bVar2.f107a;
                    hVar.f125c = i5;
                    hVar.f(this, bVar2);
                } else {
                    z2 = true;
                }
                if (i3 > this.i / 2) {
                    z2 = true;
                }
                f = 0.0f;
                i = 1;
            }
        }
        t(aVar);
        j();
    }

    public void v() {
        c cVar;
        int i = 0;
        while (true) {
            cVar = this.l;
            h[] hVarArr = cVar.f114d;
            if (i >= hVarArr.length) {
                break;
            }
            h hVar = hVarArr[i];
            if (hVar != null) {
                hVar.d();
            }
            i++;
        }
        cVar.f113c.c(this.m, this.n);
        this.n = 0;
        Arrays.fill(this.l.f114d, (Object) null);
        this.f116b = 0;
        this.f117c.clear();
        this.i = 1;
        for (int i2 = 0; i2 < this.j; i2++) {
            b[] bVarArr = this.f;
            if (bVarArr[i2] != null) {
                Objects.requireNonNull(bVarArr[i2]);
            }
        }
        u();
        this.j = 0;
        this.o = new b(this.l);
    }
}
