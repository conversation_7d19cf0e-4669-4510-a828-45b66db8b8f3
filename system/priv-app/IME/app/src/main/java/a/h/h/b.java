package a.h.h;

import android.content.Context;
import android.util.Log;
import android.view.MenuItem;
import android.view.SubMenu;
import android.view.View;

/* loaded from: classes.dex */
public abstract class b {

    /* renamed from: a, reason: collision with root package name */
    private a f362a;

    /* renamed from: b, reason: collision with root package name */
    private InterfaceC0010b f363b;

    public interface a {
        void b(boolean z);
    }

    /* renamed from: a.h.h.b$b, reason: collision with other inner class name */
    public interface InterfaceC0010b {
    }

    public b(Context context) {
    }

    public boolean a() {
        return false;
    }

    public boolean b() {
        return true;
    }

    public abstract View c();

    public View d(MenuItem menuItem) {
        return c();
    }

    public boolean e() {
        return false;
    }

    public void f(SubMenu subMenu) {
    }

    public boolean g() {
        return false;
    }

    public void h() {
        this.f363b = null;
        this.f362a = null;
    }

    public void i(a aVar) {
        this.f362a = aVar;
    }

    public void j(InterfaceC0010b interfaceC0010b) {
        if (this.f363b != null) {
            StringBuilder j = b.b.a.a.a.j("setVisibilityListener: Setting a new ActionProvider.VisibilityListener when one is already set. Are you reusing this ");
            j.append(getClass().getSimpleName());
            j.append(" instance while it is still in use somewhere else?");
            Log.w("ActionProvider(support)", j.toString());
        }
        this.f363b = interfaceC0010b;
    }

    public void k(boolean z) {
        a aVar = this.f362a;
        if (aVar != null) {
            aVar.b(z);
        }
    }
}
