package a.f.a.j.o;

import a.f.a.j.o.f;
import java.util.Iterator;

/* loaded from: classes.dex */
class k extends p {
    public k(a.f.a.j.e eVar) {
        super(eVar);
    }

    private void n(f fVar) {
        this.h.k.add(fVar);
        fVar.l.add(this.h);
    }

    @Override // a.f.a.j.o.p, a.f.a.j.o.d
    public void a(d dVar) {
        f fVar;
        int c1;
        a.f.a.j.a aVar = (a.f.a.j.a) this.f244b;
        int b1 = aVar.b1();
        Iterator<f> it = this.h.l.iterator();
        int i = 0;
        int i2 = -1;
        while (it.hasNext()) {
            int i3 = it.next().g;
            if (i2 == -1 || i3 < i2) {
                i2 = i3;
            }
            if (i < i3) {
                i = i3;
            }
        }
        if (b1 == 0 || b1 == 2) {
            fVar = this.h;
            c1 = aVar.c1() + i2;
        } else {
            fVar = this.h;
            c1 = aVar.c1() + i;
        }
        fVar.c(c1);
    }

    @Override // a.f.a.j.o.p
    void d() {
        p pVar;
        a.f.a.j.e eVar = this.f244b;
        if (eVar instanceof a.f.a.j.a) {
            this.h.f226b = true;
            a.f.a.j.a aVar = (a.f.a.j.a) eVar;
            int b1 = aVar.b1();
            boolean a1 = aVar.a1();
            int i = 0;
            if (b1 == 0) {
                this.h.e = f.a.f232d;
                while (i < aVar.O0) {
                    a.f.a.j.e eVar2 = aVar.N0[i];
                    if (a1 || eVar2.P() != 8) {
                        f fVar = eVar2.f204d.h;
                        fVar.k.add(this.h);
                        this.h.l.add(fVar);
                    }
                    i++;
                }
            } else {
                if (b1 != 1) {
                    if (b1 == 2) {
                        this.h.e = f.a.f;
                        while (i < aVar.O0) {
                            a.f.a.j.e eVar3 = aVar.N0[i];
                            if (a1 || eVar3.P() != 8) {
                                f fVar2 = eVar3.e.h;
                                fVar2.k.add(this.h);
                                this.h.l.add(fVar2);
                            }
                            i++;
                        }
                    } else {
                        if (b1 != 3) {
                            return;
                        }
                        this.h.e = f.a.g;
                        while (i < aVar.O0) {
                            a.f.a.j.e eVar4 = aVar.N0[i];
                            if (a1 || eVar4.P() != 8) {
                                f fVar3 = eVar4.e.i;
                                fVar3.k.add(this.h);
                                this.h.l.add(fVar3);
                            }
                            i++;
                        }
                    }
                    n(this.f244b.e.h);
                    pVar = this.f244b.e;
                    n(pVar.i);
                }
                this.h.e = f.a.e;
                while (i < aVar.O0) {
                    a.f.a.j.e eVar5 = aVar.N0[i];
                    if (a1 || eVar5.P() != 8) {
                        f fVar4 = eVar5.f204d.i;
                        fVar4.k.add(this.h);
                        this.h.l.add(fVar4);
                    }
                    i++;
                }
            }
            n(this.f244b.f204d.h);
            pVar = this.f244b.f204d;
            n(pVar.i);
        }
    }

    @Override // a.f.a.j.o.p
    public void e() {
        a.f.a.j.e eVar = this.f244b;
        if (eVar instanceof a.f.a.j.a) {
            int b1 = ((a.f.a.j.a) eVar).b1();
            if (b1 == 0 || b1 == 1) {
                this.f244b.U0(this.h.g);
            } else {
                this.f244b.V0(this.h.g);
            }
        }
    }

    @Override // a.f.a.j.o.p
    void f() {
        this.f245c = null;
        this.h.b();
    }

    @Override // a.f.a.j.o.p
    boolean l() {
        return false;
    }
}
