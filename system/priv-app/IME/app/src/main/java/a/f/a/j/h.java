package a.f.a.j;

import a.f.a.j.d;
import a.f.a.j.e;
import java.util.HashMap;

/* loaded from: classes.dex */
public class h extends e {
    protected float N0 = -1.0f;
    protected int O0 = -1;
    protected int P0 = -1;
    protected boolean Q0 = true;
    private d R0 = this.M;
    private int S0 = 0;
    private boolean T0;

    public h() {
        this.U.clear();
        this.U.add(this.R0);
        int length = this.T.length;
        for (int i = 0; i < length; i++) {
            this.T[i] = this.R0;
        }
    }

    @Override // a.f.a.j.e
    public void X0(a.f.a.d dVar, boolean z) {
        if (this.X == null) {
            return;
        }
        int p = dVar.p(this.R0);
        if (this.S0 == 1) {
            this.c0 = p;
            this.d0 = 0;
            A0(this.X.w());
            S0(0);
            return;
        }
        this.c0 = 0;
        this.d0 = p;
        S0(this.X.Q());
        A0(0);
    }

    public d Y0() {
        return this.R0;
    }

    public int Z0() {
        return this.S0;
    }

    public int a1() {
        return this.O0;
    }

    public int b1() {
        return this.P0;
    }

    public float c1() {
        return this.N0;
    }

    public void d1(int i) {
        this.R0.q(i);
        this.T0 = true;
    }

    public void e1(int i) {
        if (i > -1) {
            this.N0 = -1.0f;
            this.O0 = i;
            this.P0 = -1;
        }
    }

    @Override // a.f.a.j.e
    public void f(a.f.a.d dVar, boolean z) {
        a.f.a.h l;
        e.a aVar = e.a.WRAP_CONTENT;
        f fVar = (f) this.X;
        if (fVar == null) {
            return;
        }
        d o = fVar.o(d.a.LEFT);
        d o2 = fVar.o(d.a.RIGHT);
        e eVar = this.X;
        boolean z2 = eVar != null && eVar.W[0] == aVar;
        if (this.S0 == 0) {
            o = fVar.o(d.a.TOP);
            o2 = fVar.o(d.a.BOTTOM);
            e eVar2 = this.X;
            z2 = eVar2 != null && eVar2.W[1] == aVar;
        }
        if (this.T0 && this.R0.k()) {
            a.f.a.h l2 = dVar.l(this.R0);
            dVar.e(l2, this.R0.e());
            if (this.O0 != -1) {
                if (z2) {
                    l = dVar.l(o2);
                    dVar.f(l, l2, 0, 5);
                }
                this.T0 = false;
                return;
            }
            if (this.P0 != -1 && z2) {
                l = dVar.l(o2);
                dVar.f(l2, dVar.l(o), 0, 5);
                dVar.f(l, l2, 0, 5);
            }
            this.T0 = false;
            return;
        }
        if (this.O0 != -1) {
            a.f.a.h l3 = dVar.l(this.R0);
            dVar.d(l3, dVar.l(o), this.O0, 8);
            if (z2) {
                dVar.f(dVar.l(o2), l3, 0, 5);
                return;
            }
            return;
        }
        if (this.P0 != -1) {
            a.f.a.h l4 = dVar.l(this.R0);
            a.f.a.h l5 = dVar.l(o2);
            dVar.d(l4, l5, -this.P0, 8);
            if (z2) {
                dVar.f(l4, dVar.l(o), 0, 5);
                dVar.f(l5, l4, 0, 5);
                return;
            }
            return;
        }
        if (this.N0 != -1.0f) {
            a.f.a.h l6 = dVar.l(this.R0);
            a.f.a.h l7 = dVar.l(o2);
            float f = this.N0;
            a.f.a.b m = dVar.m();
            m.f110d.f(l6, -1.0f);
            m.f110d.f(l7, f);
            dVar.c(m);
        }
    }

    @Override // a.f.a.j.e
    public boolean f0() {
        return this.T0;
    }

    public void f1(int i) {
        if (i > -1) {
            this.N0 = -1.0f;
            this.O0 = -1;
            this.P0 = i;
        }
    }

    @Override // a.f.a.j.e
    public boolean g() {
        return true;
    }

    @Override // a.f.a.j.e
    public boolean g0() {
        return this.T0;
    }

    public void g1(float f) {
        if (f > -1.0f) {
            this.N0 = f;
            this.O0 = -1;
            this.P0 = -1;
        }
    }

    public void h1(int i) {
        if (this.S0 == i) {
            return;
        }
        this.S0 = i;
        this.U.clear();
        this.R0 = this.S0 == 1 ? this.L : this.M;
        this.U.add(this.R0);
        int length = this.T.length;
        for (int i2 = 0; i2 < length; i2++) {
            this.T[i2] = this.R0;
        }
    }

    @Override // a.f.a.j.e
    public void l(e eVar, HashMap<e, e> hashMap) {
        super.l(eVar, hashMap);
        h hVar = (h) eVar;
        this.N0 = hVar.N0;
        this.O0 = hVar.O0;
        this.P0 = hVar.P0;
        this.Q0 = hVar.Q0;
        h1(hVar.S0);
    }

    @Override // a.f.a.j.e
    public d o(d.a aVar) {
        int ordinal = aVar.ordinal();
        if (ordinal != 1) {
            if (ordinal != 2) {
                if (ordinal != 3) {
                    if (ordinal != 4) {
                        return null;
                    }
                }
            }
            if (this.S0 == 0) {
                return this.R0;
            }
            return null;
        }
        if (this.S0 == 1) {
            return this.R0;
        }
        return null;
    }
}
