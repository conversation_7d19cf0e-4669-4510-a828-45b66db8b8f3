package a.f.a.i.a;

/* loaded from: classes.dex */
public class f extends b {

    /* renamed from: a, reason: collision with root package name */
    private double[] f156a;

    /* renamed from: b, reason: collision with root package name */
    private double[][] f157b;

    /* renamed from: c, reason: collision with root package name */
    double[] f158c;

    public f(double[] dArr, double[][] dArr2) {
        int length = dArr2[0].length;
        this.f158c = new double[length];
        this.f156a = dArr;
        this.f157b = dArr2;
        if (length > 2) {
            double d2 = 0.0d;
            int i = 0;
            double d3 = 0.0d;
            while (i < dArr.length) {
                double d4 = dArr2[i][0];
                double d5 = dArr2[i][0];
                if (i > 0) {
                    Math.hypot(d4 - d2, d5 - d3);
                }
                i++;
                d2 = d4;
                d3 = d5;
            }
        }
    }

    @Override // a.f.a.i.a.b
    public double b(double d2, int i) {
        double[] dArr = this.f156a;
        int length = dArr.length;
        int i2 = 0;
        if (d2 <= dArr[0]) {
            return (e(dArr[0], i) * (d2 - dArr[0])) + this.f157b[0][i];
        }
        int i3 = length - 1;
        if (d2 >= dArr[i3]) {
            return (e(dArr[i3], i) * (d2 - dArr[i3])) + this.f157b[i3][i];
        }
        while (i2 < length - 1) {
            double[] dArr2 = this.f156a;
            if (d2 == dArr2[i2]) {
                return this.f157b[i2][i];
            }
            int i4 = i2 + 1;
            if (d2 < dArr2[i4]) {
                double d3 = (d2 - dArr2[i2]) / (dArr2[i4] - dArr2[i2]);
                double[][] dArr3 = this.f157b;
                return (dArr3[i4][i] * d3) + ((1.0d - d3) * dArr3[i2][i]);
            }
            i2 = i4;
        }
        return 0.0d;
    }

    @Override // a.f.a.i.a.b
    public void c(double d2, double[] dArr) {
        double[] dArr2 = this.f156a;
        int length = dArr2.length;
        int i = 0;
        int length2 = this.f157b[0].length;
        if (d2 <= dArr2[0]) {
            f(dArr2[0], this.f158c);
            for (int i2 = 0; i2 < length2; i2++) {
                dArr[i2] = ((d2 - this.f156a[0]) * this.f158c[i2]) + this.f157b[0][i2];
            }
            return;
        }
        int i3 = length - 1;
        if (d2 >= dArr2[i3]) {
            f(dArr2[i3], this.f158c);
            while (i < length2) {
                dArr[i] = ((d2 - this.f156a[i3]) * this.f158c[i]) + this.f157b[i3][i];
                i++;
            }
            return;
        }
        int i4 = 0;
        while (i4 < length - 1) {
            if (d2 == this.f156a[i4]) {
                for (int i5 = 0; i5 < length2; i5++) {
                    dArr[i5] = this.f157b[i4][i5];
                }
            }
            double[] dArr3 = this.f156a;
            int i6 = i4 + 1;
            if (d2 < dArr3[i6]) {
                double d3 = (d2 - dArr3[i4]) / (dArr3[i6] - dArr3[i4]);
                while (i < length2) {
                    double[][] dArr4 = this.f157b;
                    dArr[i] = (dArr4[i6][i] * d3) + ((1.0d - d3) * dArr4[i4][i]);
                    i++;
                }
                return;
            }
            i4 = i6;
        }
    }

    @Override // a.f.a.i.a.b
    public void d(double d2, float[] fArr) {
        double[] dArr = this.f156a;
        int length = dArr.length;
        int i = 0;
        int length2 = this.f157b[0].length;
        if (d2 <= dArr[0]) {
            f(dArr[0], this.f158c);
            for (int i2 = 0; i2 < length2; i2++) {
                fArr[i2] = (float) (((d2 - this.f156a[0]) * this.f158c[i2]) + this.f157b[0][i2]);
            }
            return;
        }
        int i3 = length - 1;
        if (d2 >= dArr[i3]) {
            f(dArr[i3], this.f158c);
            while (i < length2) {
                fArr[i] = (float) (((d2 - this.f156a[i3]) * this.f158c[i]) + this.f157b[i3][i]);
                i++;
            }
            return;
        }
        int i4 = 0;
        while (i4 < length - 1) {
            if (d2 == this.f156a[i4]) {
                for (int i5 = 0; i5 < length2; i5++) {
                    fArr[i5] = (float) this.f157b[i4][i5];
                }
            }
            double[] dArr2 = this.f156a;
            int i6 = i4 + 1;
            if (d2 < dArr2[i6]) {
                double d3 = (d2 - dArr2[i4]) / (dArr2[i6] - dArr2[i4]);
                while (i < length2) {
                    double[][] dArr3 = this.f157b;
                    fArr[i] = (float) ((dArr3[i6][i] * d3) + ((1.0d - d3) * dArr3[i4][i]));
                    i++;
                }
                return;
            }
            i4 = i6;
        }
    }

    @Override // a.f.a.i.a.b
    public double e(double d2, int i) {
        double[] dArr = this.f156a;
        int length = dArr.length;
        int i2 = 0;
        if (d2 < dArr[0]) {
            d2 = dArr[0];
        } else {
            int i3 = length - 1;
            if (d2 >= dArr[i3]) {
                d2 = dArr[i3];
            }
        }
        while (i2 < length - 1) {
            double[] dArr2 = this.f156a;
            int i4 = i2 + 1;
            if (d2 <= dArr2[i4]) {
                double d3 = dArr2[i4] - dArr2[i2];
                double d4 = dArr2[i2];
                double[][] dArr3 = this.f157b;
                return (dArr3[i4][i] - dArr3[i2][i]) / d3;
            }
            i2 = i4;
        }
        return 0.0d;
    }

    @Override // a.f.a.i.a.b
    public void f(double d2, double[] dArr) {
        double[] dArr2 = this.f156a;
        int length = dArr2.length;
        int length2 = this.f157b[0].length;
        if (d2 <= dArr2[0]) {
            d2 = dArr2[0];
        } else {
            int i = length - 1;
            if (d2 >= dArr2[i]) {
                d2 = dArr2[i];
            }
        }
        int i2 = 0;
        while (i2 < length - 1) {
            double[] dArr3 = this.f156a;
            int i3 = i2 + 1;
            if (d2 <= dArr3[i3]) {
                double d3 = dArr3[i3] - dArr3[i2];
                double d4 = dArr3[i2];
                for (int i4 = 0; i4 < length2; i4++) {
                    double[][] dArr4 = this.f157b;
                    dArr[i4] = (dArr4[i3][i4] - dArr4[i2][i4]) / d3;
                }
                return;
            }
            i2 = i3;
        }
    }

    @Override // a.f.a.i.a.b
    public double[] g() {
        return this.f156a;
    }
}
