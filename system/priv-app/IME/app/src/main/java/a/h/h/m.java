package a.h.h;

import a.h.h.q;
import android.view.View;

/* loaded from: classes.dex */
class m extends q.b<Boolean> {
    m(int i, Class cls, int i2) {
        super(i, cls, i2);
    }

    @Override // a.h.h.q.b
    Boolean b(View view) {
        return Boolean.valueOf(view.isScreenReaderFocusable());
    }

    @Override // a.h.h.q.b
    void c(View view, Boolean bool) {
        view.setScreenReaderFocusable(bool.booleanValue());
    }

    @Override // a.h.h.q.b
    boolean f(Boolean bool, Boolean bool2) {
        return !a(bool, bool2);
    }
}
