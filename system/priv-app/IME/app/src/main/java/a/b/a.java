package a.b;

import android.content.Context;
import android.view.View;
import androidx.constraintlayout.motion.widget.MotionLayout;

/* loaded from: classes.dex */
public final class a {
    public static String a() {
        StackTraceElement stackTraceElement = new Throwable().getStackTrace()[1];
        StringBuilder j = b.b.a.a.a.j(".(");
        j.append(stackTraceElement.getFileName());
        j.append(":");
        j.append(stackTraceElement.getLineNumber());
        j.append(") ");
        j.append(stackTraceElement.getMethodName());
        j.append("()");
        return j.toString();
    }

    public static String b() {
        StackTraceElement stackTraceElement = new Throwable().getStackTrace()[1];
        StringBuilder j = b.b.a.a.a.j(".(");
        j.append(stackTraceElement.getFileName());
        j.append(":");
        j.append(stackTraceElement.getLineNumber());
        j.append(")");
        return j.toString();
    }

    public static String c(Context context, int i) {
        if (i == -1) {
            return "UNKNOWN";
        }
        try {
            return context.getResources().getResourceEntryName(i);
        } catch (Exception unused) {
            return b.b.a.a.a.e("?", i);
        }
    }

    public static String d(View view) {
        try {
            return view.getContext().getResources().getResourceEntryName(view.getId());
        } catch (Exception unused) {
            return "UNKNOWN";
        }
    }

    public static String e(MotionLayout motionLayout, int i) {
        return i == -1 ? "UNDEFINED" : motionLayout.getContext().getResources().getResourceEntryName(i);
    }
}
