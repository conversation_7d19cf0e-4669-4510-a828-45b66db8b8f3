package a.h.g;

import android.util.Log;
import java.io.Writer;

@Deprecated
/* loaded from: classes.dex */
public class b extends Writer {

    /* renamed from: a, reason: collision with root package name */
    private final String f353a;

    /* renamed from: b, reason: collision with root package name */
    private StringBuilder f354b = new StringBuilder(128);

    public b(String str) {
        this.f353a = str;
    }

    private void a() {
        if (this.f354b.length() > 0) {
            Log.d(this.f353a, this.f354b.toString());
            StringBuilder sb = this.f354b;
            sb.delete(0, sb.length());
        }
    }

    @Override // java.io.Writer, java.io.Closeable, java.lang.AutoCloseable
    public void close() {
        a();
    }

    @Override // java.io.Writer, java.io.Flushable
    public void flush() {
        a();
    }

    @Override // java.io.Writer
    public void write(char[] cArr, int i, int i2) {
        for (int i3 = 0; i3 < i2; i3++) {
            char c2 = cArr[i + i3];
            if (c2 == '\n') {
                a();
            } else {
                this.f354b.append(c2);
            }
        }
    }
}
