package a.h.e;

import java.util.Comparator;

/* loaded from: classes.dex */
class d {

    /* renamed from: a, reason: collision with root package name */
    private static final Comparator<byte[]> f294a = new a();

    class a implements Comparator<byte[]> {
        a() {
        }

        @Override // java.util.Comparator
        public int compare(byte[] bArr, byte[] bArr2) {
            int i;
            int i2;
            byte[] bArr3 = bArr;
            byte[] bArr4 = bArr2;
            if (bArr3.length == bArr4.length) {
                for (int i3 = 0; i3 < bArr3.length; i3++) {
                    if (bArr3[i3] != bArr4[i3]) {
                        i = bArr3[i3];
                        i2 = bArr4[i3];
                    }
                }
                return 0;
            }
            i = bArr3.length;
            i2 = bArr4.length;
            return i - i2;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:28:0x0098 A[LOOP:1: B:14:0x0053->B:28:0x0098, LO<PERSON>_END] */
    /* JADX WARN: Removed duplicated region for block: B:29:0x009c A[EDGE_INSN: B:29:0x009c->B:30:0x009c BREAK  A[LOOP:1: B:14:0x0053->B:28:0x0098], SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    static a.h.e.g.a a(android.content.Context r20, a.h.e.e r21, android.os.CancellationSignal r22) {
        /*
            Method dump skipped, instructions count: 448
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: a.h.e.d.a(android.content.Context, a.h.e.e, android.os.CancellationSignal):a.h.e.g$a");
    }
}
