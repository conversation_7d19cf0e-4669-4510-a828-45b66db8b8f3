package a.k.a;

import a.e.h;
import android.os.SystemClock;
import android.view.Choreographer;
import java.util.ArrayList;

/* loaded from: classes.dex */
class a {
    public static final ThreadLocal<a> g = new ThreadLocal<>();

    /* renamed from: d, reason: collision with root package name */
    private c f440d;

    /* renamed from: a, reason: collision with root package name */
    private final h<b, Long> f437a = new h<>();

    /* renamed from: b, reason: collision with root package name */
    final ArrayList<b> f438b = new ArrayList<>();

    /* renamed from: c, reason: collision with root package name */
    private final C0018a f439c = new C0018a();
    long e = 0;
    private boolean f = false;

    /* renamed from: a.k.a.a$a, reason: collision with other inner class name */
    class C0018a {
        C0018a() {
        }

        void a() {
            a.this.e = SystemClock.uptimeMillis();
            a aVar = a.this;
            aVar.b(aVar.e);
            if (a.this.f438b.size() > 0) {
                a.this.d().a();
            }
        }
    }

    interface b {
        boolean a(long j);
    }

    static abstract class c {

        /* renamed from: a, reason: collision with root package name */
        final C0018a f442a;

        c(C0018a c0018a) {
            this.f442a = c0018a;
        }

        abstract void a();
    }

    private static class d extends c {

        /* renamed from: b, reason: collision with root package name */
        private final Choreographer f443b;

        /* renamed from: c, reason: collision with root package name */
        private final Choreographer.FrameCallback f444c;

        /* renamed from: a.k.a.a$d$a, reason: collision with other inner class name */
        class ChoreographerFrameCallbackC0019a implements Choreographer.FrameCallback {
            ChoreographerFrameCallbackC0019a() {
            }

            @Override // android.view.Choreographer.FrameCallback
            public void doFrame(long j) {
                d.this.f442a.a();
            }
        }

        d(C0018a c0018a) {
            super(c0018a);
            this.f443b = Choreographer.getInstance();
            this.f444c = new ChoreographerFrameCallbackC0019a();
        }

        @Override // a.k.a.a.c
        void a() {
            this.f443b.postFrameCallback(this.f444c);
        }
    }

    a() {
    }

    public static a c() {
        ThreadLocal<a> threadLocal = g;
        if (threadLocal.get() == null) {
            threadLocal.set(new a());
        }
        return threadLocal.get();
    }

    public void a(b bVar, long j) {
        if (this.f438b.size() == 0) {
            if (this.f440d == null) {
                this.f440d = new d(this.f439c);
            }
            this.f440d.a();
        }
        if (!this.f438b.contains(bVar)) {
            this.f438b.add(bVar);
        }
        if (j > 0) {
            this.f437a.put(bVar, Long.valueOf(SystemClock.uptimeMillis() + j));
        }
    }

    void b(long j) {
        long uptimeMillis = SystemClock.uptimeMillis();
        for (int i = 0; i < this.f438b.size(); i++) {
            b bVar = this.f438b.get(i);
            if (bVar != null) {
                Long orDefault = this.f437a.getOrDefault(bVar, null);
                boolean z = true;
                if (orDefault != null) {
                    if (orDefault.longValue() < uptimeMillis) {
                        this.f437a.remove(bVar);
                    } else {
                        z = false;
                    }
                }
                if (z) {
                    bVar.a(j);
                }
            }
        }
        if (!this.f) {
            return;
        }
        int size = this.f438b.size();
        while (true) {
            size--;
            if (size < 0) {
                this.f = false;
                return;
            } else if (this.f438b.get(size) == null) {
                this.f438b.remove(size);
            }
        }
    }

    c d() {
        if (this.f440d == null) {
            this.f440d = new d(this.f439c);
        }
        return this.f440d;
    }

    public void e(b bVar) {
        this.f437a.remove(bVar);
        int indexOf = this.f438b.indexOf(bVar);
        if (indexOf >= 0) {
            this.f438b.set(indexOf, null);
            this.f = true;
        }
    }
}
