package a.f.b.a;

/* loaded from: classes.dex */
public class e {

    /* renamed from: a, reason: collision with root package name */
    public float f254a;

    /* renamed from: b, reason: collision with root package name */
    public int f255b;

    /* renamed from: c, reason: collision with root package name */
    public int f256c;

    /* renamed from: d, reason: collision with root package name */
    public int f257d;
    public int e;

    public int a() {
        return this.e - this.f256c;
    }

    public int b() {
        return this.f257d - this.f255b;
    }
}
