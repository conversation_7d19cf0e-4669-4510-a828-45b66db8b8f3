package a.b.f;

import android.app.Activity;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.content.res.XmlResourceParser;
import android.graphics.PorterDuff;
import android.util.AttributeSet;
import android.util.Log;
import android.util.Xml;
import android.view.InflateException;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.SubMenu;
import android.view.View;
import androidx.appcompat.view.menu.j;
import androidx.appcompat.widget.G;
import androidx.appcompat.widget.q;
import java.io.IOException;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

/* loaded from: classes.dex */
public class g extends MenuInflater {
    static final Class<?>[] e;
    static final Class<?>[] f;

    /* renamed from: a, reason: collision with root package name */
    final Object[] f25a;

    /* renamed from: b, reason: collision with root package name */
    final Object[] f26b;

    /* renamed from: c, reason: collision with root package name */
    Context f27c;

    /* renamed from: d, reason: collision with root package name */
    private Object f28d;

    private static class a implements MenuItem.OnMenuItemClickListener {

        /* renamed from: c, reason: collision with root package name */
        private static final Class<?>[] f29c = {MenuItem.class};

        /* renamed from: a, reason: collision with root package name */
        private Object f30a;

        /* renamed from: b, reason: collision with root package name */
        private Method f31b;

        public a(Object obj, String str) {
            this.f30a = obj;
            Class<?> cls = obj.getClass();
            try {
                this.f31b = cls.getMethod(str, f29c);
            } catch (Exception e) {
                StringBuilder k = b.b.a.a.a.k("Couldn't resolve menu item onClick handler ", str, " in class ");
                k.append(cls.getName());
                InflateException inflateException = new InflateException(k.toString());
                inflateException.initCause(e);
                throw inflateException;
            }
        }

        @Override // android.view.MenuItem.OnMenuItemClickListener
        public boolean onMenuItemClick(MenuItem menuItem) {
            try {
                if (this.f31b.getReturnType() == Boolean.TYPE) {
                    return ((Boolean) this.f31b.invoke(this.f30a, menuItem)).booleanValue();
                }
                this.f31b.invoke(this.f30a, menuItem);
                return true;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    private class b {
        a.h.h.b A;
        private CharSequence B;
        private CharSequence C;

        /* renamed from: a, reason: collision with root package name */
        private Menu f32a;
        private boolean h;
        private int i;
        private int j;
        private CharSequence k;
        private CharSequence l;
        private int m;
        private char n;
        private int o;
        private char p;
        private int q;
        private int r;
        private boolean s;
        private boolean t;
        private boolean u;
        private int v;
        private int w;
        private String x;
        private String y;
        private String z;
        private ColorStateList D = null;
        private PorterDuff.Mode E = null;

        /* renamed from: b, reason: collision with root package name */
        private int f33b = 0;

        /* renamed from: c, reason: collision with root package name */
        private int f34c = 0;

        /* renamed from: d, reason: collision with root package name */
        private int f35d = 0;
        private int e = 0;
        private boolean f = true;
        private boolean g = true;

        public b(Menu menu) {
            this.f32a = menu;
        }

        private <T> T d(String str, Class<?>[] clsArr, Object[] objArr) {
            try {
                Constructor<?> constructor = Class.forName(str, false, g.this.f27c.getClassLoader()).getConstructor(clsArr);
                constructor.setAccessible(true);
                return (T) constructor.newInstance(objArr);
            } catch (Exception e) {
                Log.w("SupportMenuInflater", "Cannot instantiate class: " + str, e);
                return null;
            }
        }

        private void h(MenuItem menuItem) {
            boolean z = false;
            menuItem.setChecked(this.s).setVisible(this.t).setEnabled(this.u).setCheckable(this.r >= 1).setTitleCondensed(this.l).setIcon(this.m);
            int i = this.v;
            if (i >= 0) {
                menuItem.setShowAsAction(i);
            }
            if (this.z != null) {
                if (g.this.f27c.isRestricted()) {
                    throw new IllegalStateException("The android:onClick attribute cannot be used within a restricted context");
                }
                menuItem.setOnMenuItemClickListener(new a(g.this.getRealOwner(), this.z));
            }
            if (this.r >= 2) {
                if (menuItem instanceof androidx.appcompat.view.menu.i) {
                    ((androidx.appcompat.view.menu.i) menuItem).q(true);
                } else if (menuItem instanceof j) {
                    ((j) menuItem).h(true);
                }
            }
            String str = this.x;
            if (str != null) {
                menuItem.setActionView((View) d(str, g.e, g.this.f25a));
                z = true;
            }
            int i2 = this.w;
            if (i2 > 0) {
                if (z) {
                    Log.w("SupportMenuInflater", "Ignoring attribute 'itemActionViewLayout'. Action view already specified.");
                } else {
                    menuItem.setActionView(i2);
                }
            }
            a.h.h.b bVar = this.A;
            if (bVar != null) {
                if (menuItem instanceof a.h.d.a.b) {
                    ((a.h.d.a.b) menuItem).a(bVar);
                } else {
                    Log.w("MenuItemCompat", "setActionProvider: item does not implement SupportMenuItem; ignoring");
                }
            }
            CharSequence charSequence = this.B;
            boolean z2 = menuItem instanceof a.h.d.a.b;
            if (z2) {
                ((a.h.d.a.b) menuItem).setContentDescription(charSequence);
            } else {
                menuItem.setContentDescription(charSequence);
            }
            CharSequence charSequence2 = this.C;
            if (z2) {
                ((a.h.d.a.b) menuItem).setTooltipText(charSequence2);
            } else {
                menuItem.setTooltipText(charSequence2);
            }
            char c2 = this.n;
            int i3 = this.o;
            if (z2) {
                ((a.h.d.a.b) menuItem).setAlphabeticShortcut(c2, i3);
            } else {
                menuItem.setAlphabeticShortcut(c2, i3);
            }
            char c3 = this.p;
            int i4 = this.q;
            if (z2) {
                ((a.h.d.a.b) menuItem).setNumericShortcut(c3, i4);
            } else {
                menuItem.setNumericShortcut(c3, i4);
            }
            PorterDuff.Mode mode = this.E;
            if (mode != null) {
                if (z2) {
                    ((a.h.d.a.b) menuItem).setIconTintMode(mode);
                } else {
                    menuItem.setIconTintMode(mode);
                }
            }
            ColorStateList colorStateList = this.D;
            if (colorStateList != null) {
                if (z2) {
                    ((a.h.d.a.b) menuItem).setIconTintList(colorStateList);
                } else {
                    menuItem.setIconTintList(colorStateList);
                }
            }
        }

        public void a() {
            this.h = true;
            h(this.f32a.add(this.f33b, this.i, this.j, this.k));
        }

        public SubMenu b() {
            this.h = true;
            SubMenu addSubMenu = this.f32a.addSubMenu(this.f33b, this.i, this.j, this.k);
            h(addSubMenu.getItem());
            return addSubMenu;
        }

        public boolean c() {
            return this.h;
        }

        public void e(AttributeSet attributeSet) {
            TypedArray obtainStyledAttributes = g.this.f27c.obtainStyledAttributes(attributeSet, a.b.b.q);
            this.f33b = obtainStyledAttributes.getResourceId(1, 0);
            this.f34c = obtainStyledAttributes.getInt(3, 0);
            this.f35d = obtainStyledAttributes.getInt(4, 0);
            this.e = obtainStyledAttributes.getInt(5, 0);
            this.f = obtainStyledAttributes.getBoolean(2, true);
            this.g = obtainStyledAttributes.getBoolean(0, true);
            obtainStyledAttributes.recycle();
        }

        /* JADX WARN: Multi-variable type inference failed */
        public void f(AttributeSet attributeSet) {
            G u = G.u(g.this.f27c, attributeSet, a.b.b.r);
            this.i = u.n(2, 0);
            this.j = (u.k(5, this.f34c) & (-65536)) | (u.k(6, this.f35d) & 65535);
            this.k = u.p(7);
            this.l = u.p(8);
            this.m = u.n(0, 0);
            String o = u.o(9);
            this.n = o == null ? (char) 0 : o.charAt(0);
            this.o = u.k(16, 4096);
            String o2 = u.o(10);
            this.p = o2 == null ? (char) 0 : o2.charAt(0);
            this.q = u.k(20, 4096);
            this.r = u.s(11) ? u.a(11, false) : this.e;
            this.s = u.a(3, false);
            this.t = u.a(4, this.f);
            this.u = u.a(1, this.g);
            this.v = u.k(21, -1);
            this.z = u.o(12);
            this.w = u.n(13, 0);
            this.x = u.o(15);
            String o3 = u.o(14);
            this.y = o3;
            boolean z = o3 != null;
            if (z && this.w == 0 && this.x == null) {
                this.A = (a.h.h.b) d(o3, g.f, g.this.f26b);
            } else {
                if (z) {
                    Log.w("SupportMenuInflater", "Ignoring attribute 'actionProviderClass'. Action view already specified.");
                }
                this.A = null;
            }
            this.B = u.p(17);
            this.C = u.p(22);
            if (u.s(19)) {
                this.E = q.c(u.k(19, -1), this.E);
            } else {
                this.E = null;
            }
            if (u.s(18)) {
                this.D = u.c(18);
            } else {
                this.D = null;
            }
            u.w();
            this.h = false;
        }

        public void g() {
            this.f33b = 0;
            this.f34c = 0;
            this.f35d = 0;
            this.e = 0;
            this.f = true;
            this.g = true;
        }
    }

    static {
        Class<?>[] clsArr = {Context.class};
        e = clsArr;
        f = clsArr;
    }

    public g(Context context) {
        super(context);
        this.f27c = context;
        Object[] objArr = {context};
        this.f25a = objArr;
        this.f26b = objArr;
    }

    private Object findRealOwner(Object obj) {
        return (!(obj instanceof Activity) && (obj instanceof ContextWrapper)) ? findRealOwner(((ContextWrapper) obj).getBaseContext()) : obj;
    }

    private void parseMenu(XmlPullParser xmlPullParser, AttributeSet attributeSet, Menu menu) {
        b bVar = new b(menu);
        int eventType = xmlPullParser.getEventType();
        while (true) {
            if (eventType == 2) {
                String name = xmlPullParser.getName();
                if (!name.equals("menu")) {
                    throw new RuntimeException(b.b.a.a.a.g("Expecting menu, got ", name));
                }
                eventType = xmlPullParser.next();
            } else {
                eventType = xmlPullParser.next();
                if (eventType == 1) {
                    break;
                }
            }
        }
        String str = null;
        boolean z = false;
        boolean z2 = false;
        while (!z) {
            if (eventType == 1) {
                throw new RuntimeException("Unexpected end of document");
            }
            if (eventType != 2) {
                if (eventType == 3) {
                    String name2 = xmlPullParser.getName();
                    if (z2 && name2.equals(str)) {
                        str = null;
                        z2 = false;
                    } else if (name2.equals("group")) {
                        bVar.g();
                    } else if (name2.equals("item")) {
                        if (!bVar.c()) {
                            a.h.h.b bVar2 = bVar.A;
                            if (bVar2 == null || !bVar2.a()) {
                                bVar.a();
                            } else {
                                bVar.b();
                            }
                        }
                    } else if (name2.equals("menu")) {
                        z = true;
                    }
                }
            } else if (!z2) {
                String name3 = xmlPullParser.getName();
                if (name3.equals("group")) {
                    bVar.e(attributeSet);
                } else if (name3.equals("item")) {
                    bVar.f(attributeSet);
                } else if (name3.equals("menu")) {
                    parseMenu(xmlPullParser, attributeSet, bVar.b());
                } else {
                    str = name3;
                    z2 = true;
                }
            }
            eventType = xmlPullParser.next();
        }
    }

    Object getRealOwner() {
        if (this.f28d == null) {
            this.f28d = findRealOwner(this.f27c);
        }
        return this.f28d;
    }

    @Override // android.view.MenuInflater
    public void inflate(int i, Menu menu) {
        if (!(menu instanceof a.h.d.a.a)) {
            super.inflate(i, menu);
            return;
        }
        XmlResourceParser xmlResourceParser = null;
        try {
            try {
                try {
                    xmlResourceParser = this.f27c.getResources().getLayout(i);
                    parseMenu(xmlResourceParser, Xml.asAttributeSet(xmlResourceParser), menu);
                    xmlResourceParser.close();
                } catch (XmlPullParserException e2) {
                    throw new InflateException("Error inflating menu XML", e2);
                }
            } catch (IOException e3) {
                throw new InflateException("Error inflating menu XML", e3);
            }
        } catch (Throwable th) {
            if (xmlResourceParser != null) {
                xmlResourceParser.close();
            }
            throw th;
        }
    }
}
