package a.f.a;

import a.f.a.b;
import java.util.Arrays;

/* loaded from: classes.dex */
public class a implements b.a {

    /* renamed from: b, reason: collision with root package name */
    private final b f104b;

    /* renamed from: c, reason: collision with root package name */
    protected final c f105c;

    /* renamed from: a, reason: collision with root package name */
    int f103a = 0;

    /* renamed from: d, reason: collision with root package name */
    private int f106d = 8;
    private int[] e = new int[8];
    private int[] f = new int[8];
    private float[] g = new float[8];
    private int h = -1;
    private int i = -1;
    private boolean j = false;

    a(b bVar, c cVar) {
        this.f104b = bVar;
        this.f105c = cVar;
    }

    @Override // a.f.a.b.a
    public float a(int i) {
        int i2 = this.h;
        for (int i3 = 0; i2 != -1 && i3 < this.f103a; i3++) {
            if (i3 == i) {
                return this.g[i2];
            }
            i2 = this.f[i2];
        }
        return 0.0f;
    }

    @Override // a.f.a.b.a
    public final float b(h hVar) {
        int i = this.h;
        for (int i2 = 0; i != -1 && i2 < this.f103a; i2++) {
            if (this.e[i] == hVar.f124b) {
                return this.g[i];
            }
            i = this.f[i];
        }
        return 0.0f;
    }

    @Override // a.f.a.b.a
    public final float c(h hVar, boolean z) {
        int i = this.h;
        if (i == -1) {
            return 0.0f;
        }
        int i2 = 0;
        int i3 = -1;
        while (i != -1 && i2 < this.f103a) {
            if (this.e[i] == hVar.f124b) {
                if (i == this.h) {
                    this.h = this.f[i];
                } else {
                    int[] iArr = this.f;
                    iArr[i3] = iArr[i];
                }
                if (z) {
                    hVar.c(this.f104b);
                }
                hVar.l--;
                this.f103a--;
                this.e[i] = -1;
                if (this.j) {
                    this.i = i;
                }
                return this.g[i];
            }
            i2++;
            i3 = i;
            i = this.f[i];
        }
        return 0.0f;
    }

    @Override // a.f.a.b.a
    public final void clear() {
        int i = this.h;
        for (int i2 = 0; i != -1 && i2 < this.f103a; i2++) {
            h hVar = this.f105c.f114d[this.e[i]];
            if (hVar != null) {
                hVar.c(this.f104b);
            }
            i = this.f[i];
        }
        this.h = -1;
        this.i = -1;
        this.j = false;
        this.f103a = 0;
    }

    @Override // a.f.a.b.a
    public boolean d(h hVar) {
        int i = this.h;
        if (i == -1) {
            return false;
        }
        for (int i2 = 0; i != -1 && i2 < this.f103a; i2++) {
            if (this.e[i] == hVar.f124b) {
                return true;
            }
            i = this.f[i];
        }
        return false;
    }

    @Override // a.f.a.b.a
    public float e(b bVar, boolean z) {
        float b2 = b(bVar.f107a);
        c(bVar.f107a, z);
        b.a aVar = bVar.f110d;
        int k = aVar.k();
        for (int i = 0; i < k; i++) {
            h g = aVar.g(i);
            h(g, aVar.b(g) * b2, z);
        }
        return b2;
    }

    @Override // a.f.a.b.a
    public final void f(h hVar, float f) {
        if (f == 0.0f) {
            c(hVar, true);
            return;
        }
        int i = this.h;
        if (i == -1) {
            this.h = 0;
            this.g[0] = f;
            this.e[0] = hVar.f124b;
            this.f[0] = -1;
            hVar.l++;
            hVar.a(this.f104b);
            this.f103a++;
            if (this.j) {
                return;
            }
            int i2 = this.i + 1;
            this.i = i2;
            int[] iArr = this.e;
            if (i2 >= iArr.length) {
                this.j = true;
                this.i = iArr.length - 1;
                return;
            }
            return;
        }
        int i3 = -1;
        for (int i4 = 0; i != -1 && i4 < this.f103a; i4++) {
            int[] iArr2 = this.e;
            int i5 = iArr2[i];
            int i6 = hVar.f124b;
            if (i5 == i6) {
                this.g[i] = f;
                return;
            }
            if (iArr2[i] < i6) {
                i3 = i;
            }
            i = this.f[i];
        }
        int i7 = this.i;
        int i8 = i7 + 1;
        if (this.j) {
            int[] iArr3 = this.e;
            if (iArr3[i7] != -1) {
                i7 = iArr3.length;
            }
        } else {
            i7 = i8;
        }
        int[] iArr4 = this.e;
        if (i7 >= iArr4.length && this.f103a < iArr4.length) {
            int i9 = 0;
            while (true) {
                int[] iArr5 = this.e;
                if (i9 >= iArr5.length) {
                    break;
                }
                if (iArr5[i9] == -1) {
                    i7 = i9;
                    break;
                }
                i9++;
            }
        }
        int[] iArr6 = this.e;
        if (i7 >= iArr6.length) {
            i7 = iArr6.length;
            int i10 = this.f106d * 2;
            this.f106d = i10;
            this.j = false;
            this.i = i7 - 1;
            this.g = Arrays.copyOf(this.g, i10);
            this.e = Arrays.copyOf(this.e, this.f106d);
            this.f = Arrays.copyOf(this.f, this.f106d);
        }
        this.e[i7] = hVar.f124b;
        this.g[i7] = f;
        int[] iArr7 = this.f;
        if (i3 != -1) {
            iArr7[i7] = iArr7[i3];
            iArr7[i3] = i7;
        } else {
            iArr7[i7] = this.h;
            this.h = i7;
        }
        hVar.l++;
        hVar.a(this.f104b);
        int i11 = this.f103a + 1;
        this.f103a = i11;
        if (!this.j) {
            this.i++;
        }
        int[] iArr8 = this.e;
        if (i11 >= iArr8.length) {
            this.j = true;
        }
        if (this.i >= iArr8.length) {
            this.j = true;
            this.i = iArr8.length - 1;
        }
    }

    @Override // a.f.a.b.a
    public h g(int i) {
        int i2 = this.h;
        for (int i3 = 0; i2 != -1 && i3 < this.f103a; i3++) {
            if (i3 == i) {
                return this.f105c.f114d[this.e[i2]];
            }
            i2 = this.f[i2];
        }
        return null;
    }

    @Override // a.f.a.b.a
    public void h(h hVar, float f, boolean z) {
        if (f <= -0.001f || f >= 0.001f) {
            int i = this.h;
            if (i == -1) {
                this.h = 0;
                this.g[0] = f;
                this.e[0] = hVar.f124b;
                this.f[0] = -1;
                hVar.l++;
                hVar.a(this.f104b);
                this.f103a++;
                if (this.j) {
                    return;
                }
                int i2 = this.i + 1;
                this.i = i2;
                int[] iArr = this.e;
                if (i2 >= iArr.length) {
                    this.j = true;
                    this.i = iArr.length - 1;
                    return;
                }
                return;
            }
            int i3 = -1;
            for (int i4 = 0; i != -1 && i4 < this.f103a; i4++) {
                int[] iArr2 = this.e;
                int i5 = iArr2[i];
                int i6 = hVar.f124b;
                if (i5 == i6) {
                    float[] fArr = this.g;
                    float f2 = fArr[i] + f;
                    if (f2 > -0.001f && f2 < 0.001f) {
                        f2 = 0.0f;
                    }
                    fArr[i] = f2;
                    if (f2 == 0.0f) {
                        if (i == this.h) {
                            this.h = this.f[i];
                        } else {
                            int[] iArr3 = this.f;
                            iArr3[i3] = iArr3[i];
                        }
                        if (z) {
                            hVar.c(this.f104b);
                        }
                        if (this.j) {
                            this.i = i;
                        }
                        hVar.l--;
                        this.f103a--;
                        return;
                    }
                    return;
                }
                if (iArr2[i] < i6) {
                    i3 = i;
                }
                i = this.f[i];
            }
            int i7 = this.i;
            int i8 = i7 + 1;
            if (this.j) {
                int[] iArr4 = this.e;
                if (iArr4[i7] != -1) {
                    i7 = iArr4.length;
                }
            } else {
                i7 = i8;
            }
            int[] iArr5 = this.e;
            if (i7 >= iArr5.length && this.f103a < iArr5.length) {
                int i9 = 0;
                while (true) {
                    int[] iArr6 = this.e;
                    if (i9 >= iArr6.length) {
                        break;
                    }
                    if (iArr6[i9] == -1) {
                        i7 = i9;
                        break;
                    }
                    i9++;
                }
            }
            int[] iArr7 = this.e;
            if (i7 >= iArr7.length) {
                i7 = iArr7.length;
                int i10 = this.f106d * 2;
                this.f106d = i10;
                this.j = false;
                this.i = i7 - 1;
                this.g = Arrays.copyOf(this.g, i10);
                this.e = Arrays.copyOf(this.e, this.f106d);
                this.f = Arrays.copyOf(this.f, this.f106d);
            }
            this.e[i7] = hVar.f124b;
            this.g[i7] = f;
            int[] iArr8 = this.f;
            if (i3 != -1) {
                iArr8[i7] = iArr8[i3];
                iArr8[i3] = i7;
            } else {
                iArr8[i7] = this.h;
                this.h = i7;
            }
            hVar.l++;
            hVar.a(this.f104b);
            this.f103a++;
            if (!this.j) {
                this.i++;
            }
            int i11 = this.i;
            int[] iArr9 = this.e;
            if (i11 >= iArr9.length) {
                this.j = true;
                this.i = iArr9.length - 1;
            }
        }
    }

    @Override // a.f.a.b.a
    public void i(float f) {
        int i = this.h;
        for (int i2 = 0; i != -1 && i2 < this.f103a; i2++) {
            float[] fArr = this.g;
            fArr[i] = fArr[i] / f;
            i = this.f[i];
        }
    }

    @Override // a.f.a.b.a
    public void j() {
        int i = this.h;
        for (int i2 = 0; i != -1 && i2 < this.f103a; i2++) {
            float[] fArr = this.g;
            fArr[i] = fArr[i] * (-1.0f);
            i = this.f[i];
        }
    }

    @Override // a.f.a.b.a
    public int k() {
        return this.f103a;
    }

    public String toString() {
        int i = this.h;
        String str = "";
        for (int i2 = 0; i != -1 && i2 < this.f103a; i2++) {
            StringBuilder j = b.b.a.a.a.j(b.b.a.a.a.g(str, " -> "));
            j.append(this.g[i]);
            j.append(" : ");
            StringBuilder j2 = b.b.a.a.a.j(j.toString());
            j2.append(this.f105c.f114d[this.e[i]]);
            str = j2.toString();
            i = this.f[i];
        }
        return str;
    }
}
