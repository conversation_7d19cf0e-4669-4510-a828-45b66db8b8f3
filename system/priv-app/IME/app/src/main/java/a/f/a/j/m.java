package a.f.a.j;

import a.f.a.j.e;
import a.f.a.j.o.b;

/* loaded from: classes.dex */
public class m extends j {
    private int P0 = 0;
    private int Q0 = 0;
    private int R0 = 0;
    private int S0 = 0;
    private int T0 = 0;
    private int U0 = 0;
    private boolean V0 = false;
    private int W0 = 0;
    private int X0 = 0;
    protected b.a Y0 = new b.a();
    b.InterfaceC0003b Z0 = null;

    public void Z0(boolean z) {
        int i = this.R0;
        if (i > 0 || this.S0 > 0) {
            if (z) {
                this.T0 = this.S0;
                this.U0 = i;
            } else {
                this.T0 = i;
                this.U0 = this.S0;
            }
        }
    }

    public void a1() {
        for (int i = 0; i < this.O0; i++) {
            e eVar = this.N0[i];
            if (eVar != null) {
                eVar.G0(true);
            }
        }
    }

    @Override // a.f.a.j.j, a.f.a.j.i
    public void b(f fVar) {
        a1();
    }

    public int b1() {
        return this.X0;
    }

    public int c1() {
        return this.W0;
    }

    public int d1() {
        return this.Q0;
    }

    public int e1() {
        return this.T0;
    }

    public int f1() {
        return this.U0;
    }

    public int g1() {
        return this.P0;
    }

    public void h1(int i, int i2, int i3, int i4) {
    }

    protected void i1(e eVar, e.a aVar, int i, e.a aVar2, int i2) {
        b.InterfaceC0003b interfaceC0003b;
        e eVar2;
        while (true) {
            interfaceC0003b = this.Z0;
            if (interfaceC0003b != null || (eVar2 = this.X) == null) {
                break;
            } else {
                this.Z0 = ((f) eVar2).R0;
            }
        }
        b.a aVar3 = this.Y0;
        aVar3.f217a = aVar;
        aVar3.f218b = aVar2;
        aVar3.f219c = i;
        aVar3.f220d = i2;
        interfaceC0003b.b(eVar, aVar3);
        eVar.S0(this.Y0.e);
        eVar.A0(this.Y0.f);
        eVar.z0(this.Y0.h);
        eVar.q0(this.Y0.g);
    }

    public boolean j1() {
        return this.V0;
    }

    protected void k1(boolean z) {
        this.V0 = z;
    }

    public void l1(int i, int i2) {
        this.W0 = i;
        this.X0 = i2;
    }

    public void m1(int i) {
        this.P0 = i;
        this.Q0 = i;
        this.R0 = i;
        this.S0 = i;
    }

    public void n1(int i) {
        this.Q0 = i;
    }

    public void o1(int i) {
        this.S0 = i;
    }

    public void p1(int i) {
        this.T0 = i;
    }

    public void q1(int i) {
        this.U0 = i;
    }

    public void r1(int i) {
        this.R0 = i;
        this.T0 = i;
        this.U0 = i;
    }

    public void s1(int i) {
        this.P0 = i;
    }
}
