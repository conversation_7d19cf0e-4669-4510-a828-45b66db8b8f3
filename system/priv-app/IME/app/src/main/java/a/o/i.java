package a.o;

import android.animation.Animator;
import android.animation.TimeInterpolator;
import android.graphics.Path;
import android.util.Property;
import android.util.SparseArray;
import android.util.SparseIntArray;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ListView;
import java.util.ArrayList;
import java.util.Iterator;

/* loaded from: classes.dex */
public abstract class i implements Cloneable {
    private static final int[] u = {2, 1, 3, 4};
    private static final f v = new a();
    private static ThreadLocal<a.e.a<Animator, b>> w = new ThreadLocal<>();
    private ArrayList<p> k;
    private ArrayList<p> l;
    private c s;

    /* renamed from: a, reason: collision with root package name */
    private String f492a = getClass().getName();

    /* renamed from: b, reason: collision with root package name */
    private long f493b = -1;

    /* renamed from: c, reason: collision with root package name */
    long f494c = -1;

    /* renamed from: d, reason: collision with root package name */
    private TimeInterpolator f495d = null;
    ArrayList<Integer> e = new ArrayList<>();
    ArrayList<View> f = new ArrayList<>();
    private q g = new q();
    private q h = new q();
    o i = null;
    private int[] j = u;
    ArrayList<Animator> m = new ArrayList<>();
    private int n = 0;
    private boolean o = false;
    private boolean p = false;
    private ArrayList<d> q = null;
    private ArrayList<Animator> r = new ArrayList<>();
    private f t = v;

    static class a extends f {
        a() {
        }

        @Override // a.o.f
        public Path a(float f, float f2, float f3, float f4) {
            Path path = new Path();
            path.moveTo(f, f2);
            path.lineTo(f3, f4);
            return path;
        }
    }

    private static class b {

        /* renamed from: a, reason: collision with root package name */
        View f496a;

        /* renamed from: b, reason: collision with root package name */
        String f497b;

        /* renamed from: c, reason: collision with root package name */
        p f498c;

        /* renamed from: d, reason: collision with root package name */
        C f499d;
        i e;

        b(View view, String str, i iVar, C c2, p pVar) {
            this.f496a = view;
            this.f497b = str;
            this.f498c = pVar;
            this.f499d = c2;
            this.e = iVar;
        }
    }

    public static abstract class c {
    }

    public interface d {
        void a(i iVar);

        void b(i iVar);

        void c(i iVar);

        void d(i iVar);

        void e(i iVar);
    }

    private static void c(q qVar, View view, p pVar) {
        qVar.f515a.put(view, pVar);
        int id = view.getId();
        if (id >= 0) {
            if (qVar.f516b.indexOfKey(id) >= 0) {
                qVar.f516b.put(id, null);
            } else {
                qVar.f516b.put(id, view);
            }
        }
        int i = a.h.h.q.e;
        String transitionName = view.getTransitionName();
        if (transitionName != null) {
            if (qVar.f518d.e(transitionName) >= 0) {
                qVar.f518d.put(transitionName, null);
            } else {
                qVar.f518d.put(transitionName, view);
            }
        }
        if (view.getParent() instanceof ListView) {
            ListView listView = (ListView) view.getParent();
            if (listView.getAdapter().hasStableIds()) {
                long itemIdAtPosition = listView.getItemIdAtPosition(listView.getPositionForView(view));
                if (qVar.f517c.f(itemIdAtPosition) < 0) {
                    view.setHasTransientState(true);
                    qVar.f517c.h(itemIdAtPosition, view);
                    return;
                }
                View d2 = qVar.f517c.d(itemIdAtPosition);
                if (d2 != null) {
                    d2.setHasTransientState(false);
                    qVar.f517c.h(itemIdAtPosition, null);
                }
            }
        }
    }

    private void f(View view, boolean z) {
        if (view == null) {
            return;
        }
        view.getId();
        if (view.getParent() instanceof ViewGroup) {
            p pVar = new p(view);
            if (z) {
                h(pVar);
            } else {
                e(pVar);
            }
            pVar.f514c.add(this);
            g(pVar);
            c(z ? this.g : this.h, view, pVar);
        }
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            for (int i = 0; i < viewGroup.getChildCount(); i++) {
                f(viewGroup.getChildAt(i), z);
            }
        }
    }

    private static a.e.a<Animator, b> s() {
        a.e.a<Animator, b> aVar = w.get();
        if (aVar != null) {
            return aVar;
        }
        a.e.a<Animator, b> aVar2 = new a.e.a<>();
        w.set(aVar2);
        return aVar2;
    }

    private static boolean y(p pVar, p pVar2, String str) {
        Object obj = pVar.f512a.get(str);
        Object obj2 = pVar2.f512a.get(str);
        if (obj == null && obj2 == null) {
            return false;
        }
        if (obj == null || obj2 == null) {
            return true;
        }
        return true ^ obj.equals(obj2);
    }

    /* JADX WARN: Multi-variable type inference failed */
    void A(ViewGroup viewGroup) {
        b orDefault;
        p pVar;
        View view;
        View view2;
        View d2;
        this.k = new ArrayList<>();
        this.l = new ArrayList<>();
        q qVar = this.g;
        q qVar2 = this.h;
        a.e.a aVar = new a.e.a(qVar.f515a);
        a.e.a aVar2 = new a.e.a(qVar2.f515a);
        int i = 0;
        while (true) {
            int[] iArr = this.j;
            if (i >= iArr.length) {
                break;
            }
            int i2 = iArr[i];
            if (i2 == 1) {
                int size = aVar.size();
                while (true) {
                    size--;
                    if (size >= 0) {
                        View view3 = (View) aVar.h(size);
                        if (view3 != null && x(view3) && (pVar = (p) aVar2.remove(view3)) != null && x(pVar.f513b)) {
                            this.k.add((p) aVar.i(size));
                            this.l.add(pVar);
                        }
                    }
                }
            } else if (i2 == 2) {
                a.e.a<String, View> aVar3 = qVar.f518d;
                a.e.a<String, View> aVar4 = qVar2.f518d;
                int size2 = aVar3.size();
                for (int i3 = 0; i3 < size2; i3++) {
                    View k = aVar3.k(i3);
                    if (k != null && x(k) && (view = aVar4.get(aVar3.h(i3))) != null && x(view)) {
                        p pVar2 = (p) aVar.getOrDefault(k, null);
                        p pVar3 = (p) aVar2.getOrDefault(view, null);
                        if (pVar2 != null && pVar3 != null) {
                            this.k.add(pVar2);
                            this.l.add(pVar3);
                            aVar.remove(k);
                            aVar2.remove(view);
                        }
                    }
                }
            } else if (i2 == 3) {
                SparseArray<View> sparseArray = qVar.f516b;
                SparseArray<View> sparseArray2 = qVar2.f516b;
                int size3 = sparseArray.size();
                for (int i4 = 0; i4 < size3; i4++) {
                    View valueAt = sparseArray.valueAt(i4);
                    if (valueAt != null && x(valueAt) && (view2 = sparseArray2.get(sparseArray.keyAt(i4))) != null && x(view2)) {
                        p pVar4 = (p) aVar.getOrDefault(valueAt, null);
                        p pVar5 = (p) aVar2.getOrDefault(view2, null);
                        if (pVar4 != null && pVar5 != null) {
                            this.k.add(pVar4);
                            this.l.add(pVar5);
                            aVar.remove(valueAt);
                            aVar2.remove(view2);
                        }
                    }
                }
            } else if (i2 == 4) {
                a.e.e<View> eVar = qVar.f517c;
                a.e.e<View> eVar2 = qVar2.f517c;
                int k2 = eVar.k();
                for (int i5 = 0; i5 < k2; i5++) {
                    View l = eVar.l(i5);
                    if (l != null && x(l) && (d2 = eVar2.d(eVar.g(i5))) != null && x(d2)) {
                        p pVar6 = (p) aVar.getOrDefault(l, null);
                        p pVar7 = (p) aVar2.getOrDefault(d2, null);
                        if (pVar6 != null && pVar7 != null) {
                            this.k.add(pVar6);
                            this.l.add(pVar7);
                            aVar.remove(l);
                            aVar2.remove(d2);
                        }
                    }
                }
            }
            i++;
        }
        for (int i6 = 0; i6 < aVar.size(); i6++) {
            p pVar8 = (p) aVar.k(i6);
            if (x(pVar8.f513b)) {
                this.k.add(pVar8);
                this.l.add(null);
            }
        }
        for (int i7 = 0; i7 < aVar2.size(); i7++) {
            p pVar9 = (p) aVar2.k(i7);
            if (x(pVar9.f513b)) {
                this.l.add(pVar9);
                this.k.add(null);
            }
        }
        a.e.a<Animator, b> s = s();
        int size4 = s.size();
        Property<View, Float> property = s.f521b;
        B b2 = new B(viewGroup);
        for (int i8 = size4 - 1; i8 >= 0; i8--) {
            Animator h = s.h(i8);
            if (h != null && (orDefault = s.getOrDefault(h, null)) != null && orDefault.f496a != null && b2.equals(orDefault.f499d)) {
                p pVar10 = orDefault.f498c;
                View view4 = orDefault.f496a;
                p v2 = v(view4, true);
                p q = q(view4, true);
                if (v2 == null && q == null) {
                    q = this.h.f515a.get(view4);
                }
                if (!(v2 == null && q == null) && orDefault.e.w(pVar10, q)) {
                    if (h.isRunning() || h.isStarted()) {
                        h.cancel();
                    } else {
                        s.remove(h);
                    }
                }
            }
        }
        m(viewGroup, this.g, this.h, this.k, this.l);
        E();
    }

    public i B(d dVar) {
        ArrayList<d> arrayList = this.q;
        if (arrayList == null) {
            return this;
        }
        arrayList.remove(dVar);
        if (this.q.size() == 0) {
            this.q = null;
        }
        return this;
    }

    public i C(View view) {
        this.f.remove(view);
        return this;
    }

    public void D(View view) {
        if (this.o) {
            if (!this.p) {
                a.e.a<Animator, b> s = s();
                int size = s.size();
                Property<View, Float> property = s.f521b;
                B b2 = new B(view);
                for (int i = size - 1; i >= 0; i--) {
                    b k = s.k(i);
                    if (k.f496a != null && b2.equals(k.f499d)) {
                        s.h(i).resume();
                    }
                }
                ArrayList<d> arrayList = this.q;
                if (arrayList != null && arrayList.size() > 0) {
                    ArrayList arrayList2 = (ArrayList) this.q.clone();
                    int size2 = arrayList2.size();
                    for (int i2 = 0; i2 < size2; i2++) {
                        ((d) arrayList2.get(i2)).b(this);
                    }
                }
            }
            this.o = false;
        }
    }

    protected void E() {
        L();
        a.e.a<Animator, b> s = s();
        Iterator<Animator> it = this.r.iterator();
        while (it.hasNext()) {
            Animator next = it.next();
            if (s.containsKey(next)) {
                L();
                if (next != null) {
                    next.addListener(new j(this, s));
                    long j = this.f494c;
                    if (j >= 0) {
                        next.setDuration(j);
                    }
                    long j2 = this.f493b;
                    if (j2 >= 0) {
                        next.setStartDelay(next.getStartDelay() + j2);
                    }
                    TimeInterpolator timeInterpolator = this.f495d;
                    if (timeInterpolator != null) {
                        next.setInterpolator(timeInterpolator);
                    }
                    next.addListener(new k(this));
                    next.start();
                }
            }
        }
        this.r.clear();
        n();
    }

    public i F(long j) {
        this.f494c = j;
        return this;
    }

    public void G(c cVar) {
        this.s = cVar;
    }

    public i H(TimeInterpolator timeInterpolator) {
        this.f495d = timeInterpolator;
        return this;
    }

    public void I(f fVar) {
        if (fVar == null) {
            fVar = v;
        }
        this.t = fVar;
    }

    public void J(n nVar) {
    }

    public i K(long j) {
        this.f493b = j;
        return this;
    }

    protected void L() {
        if (this.n == 0) {
            ArrayList<d> arrayList = this.q;
            if (arrayList != null && arrayList.size() > 0) {
                ArrayList arrayList2 = (ArrayList) this.q.clone();
                int size = arrayList2.size();
                for (int i = 0; i < size; i++) {
                    ((d) arrayList2.get(i)).c(this);
                }
            }
            this.p = false;
        }
        this.n++;
    }

    String M(String str) {
        StringBuilder j = b.b.a.a.a.j(str);
        j.append(getClass().getSimpleName());
        j.append("@");
        j.append(Integer.toHexString(hashCode()));
        j.append(": ");
        String sb = j.toString();
        if (this.f494c != -1) {
            sb = sb + "dur(" + this.f494c + ") ";
        }
        if (this.f493b != -1) {
            sb = sb + "dly(" + this.f493b + ") ";
        }
        if (this.f495d != null) {
            sb = sb + "interp(" + this.f495d + ") ";
        }
        if (this.e.size() <= 0 && this.f.size() <= 0) {
            return sb;
        }
        String g = b.b.a.a.a.g(sb, "tgts(");
        if (this.e.size() > 0) {
            for (int i = 0; i < this.e.size(); i++) {
                if (i > 0) {
                    g = b.b.a.a.a.g(g, ", ");
                }
                StringBuilder j2 = b.b.a.a.a.j(g);
                j2.append(this.e.get(i));
                g = j2.toString();
            }
        }
        if (this.f.size() > 0) {
            for (int i2 = 0; i2 < this.f.size(); i2++) {
                if (i2 > 0) {
                    g = b.b.a.a.a.g(g, ", ");
                }
                StringBuilder j3 = b.b.a.a.a.j(g);
                j3.append(this.f.get(i2));
                g = j3.toString();
            }
        }
        return b.b.a.a.a.g(g, ")");
    }

    public i a(d dVar) {
        if (this.q == null) {
            this.q = new ArrayList<>();
        }
        this.q.add(dVar);
        return this;
    }

    public i b(View view) {
        this.f.add(view);
        return this;
    }

    protected void d() {
        for (int size = this.m.size() - 1; size >= 0; size--) {
            this.m.get(size).cancel();
        }
        ArrayList<d> arrayList = this.q;
        if (arrayList == null || arrayList.size() <= 0) {
            return;
        }
        ArrayList arrayList2 = (ArrayList) this.q.clone();
        int size2 = arrayList2.size();
        for (int i = 0; i < size2; i++) {
            ((d) arrayList2.get(i)).d(this);
        }
    }

    public abstract void e(p pVar);

    void g(p pVar) {
    }

    public abstract void h(p pVar);

    void i(ViewGroup viewGroup, boolean z) {
        j(z);
        if (this.e.size() <= 0 && this.f.size() <= 0) {
            f(viewGroup, z);
            return;
        }
        for (int i = 0; i < this.e.size(); i++) {
            View findViewById = viewGroup.findViewById(this.e.get(i).intValue());
            if (findViewById != null) {
                p pVar = new p(findViewById);
                if (z) {
                    h(pVar);
                } else {
                    e(pVar);
                }
                pVar.f514c.add(this);
                g(pVar);
                c(z ? this.g : this.h, findViewById, pVar);
            }
        }
        for (int i2 = 0; i2 < this.f.size(); i2++) {
            View view = this.f.get(i2);
            p pVar2 = new p(view);
            if (z) {
                h(pVar2);
            } else {
                e(pVar2);
            }
            pVar2.f514c.add(this);
            g(pVar2);
            c(z ? this.g : this.h, view, pVar2);
        }
    }

    void j(boolean z) {
        q qVar;
        if (z) {
            this.g.f515a.clear();
            this.g.f516b.clear();
            qVar = this.g;
        } else {
            this.h.f515a.clear();
            this.h.f516b.clear();
            qVar = this.h;
        }
        qVar.f517c.a();
    }

    @Override // 
    /* renamed from: k, reason: merged with bridge method [inline-methods] */
    public i clone() {
        try {
            i iVar = (i) super.clone();
            iVar.r = new ArrayList<>();
            iVar.g = new q();
            iVar.h = new q();
            iVar.k = null;
            iVar.l = null;
            return iVar;
        } catch (CloneNotSupportedException unused) {
            return null;
        }
    }

    public Animator l(ViewGroup viewGroup, p pVar, p pVar2) {
        return null;
    }

    protected void m(ViewGroup viewGroup, q qVar, q qVar2, ArrayList<p> arrayList, ArrayList<p> arrayList2) {
        Animator l;
        int i;
        View view;
        Animator animator;
        p pVar;
        Animator animator2;
        p pVar2;
        a.e.a<Animator, b> s = s();
        SparseIntArray sparseIntArray = new SparseIntArray();
        int size = arrayList.size();
        int i2 = 0;
        while (i2 < size) {
            p pVar3 = arrayList.get(i2);
            p pVar4 = arrayList2.get(i2);
            if (pVar3 != null && !pVar3.f514c.contains(this)) {
                pVar3 = null;
            }
            if (pVar4 != null && !pVar4.f514c.contains(this)) {
                pVar4 = null;
            }
            if (pVar3 != null || pVar4 != null) {
                if ((pVar3 == null || pVar4 == null || w(pVar3, pVar4)) && (l = l(viewGroup, pVar3, pVar4)) != null) {
                    if (pVar4 != null) {
                        View view2 = pVar4.f513b;
                        String[] u2 = u();
                        if (u2 != null && u2.length > 0) {
                            pVar2 = new p(view2);
                            p pVar5 = qVar2.f515a.get(view2);
                            if (pVar5 != null) {
                                int i3 = 0;
                                while (i3 < u2.length) {
                                    pVar2.f512a.put(u2[i3], pVar5.f512a.get(u2[i3]));
                                    i3++;
                                    l = l;
                                    size = size;
                                    pVar5 = pVar5;
                                }
                            }
                            Animator animator3 = l;
                            i = size;
                            int size2 = s.size();
                            int i4 = 0;
                            while (true) {
                                if (i4 >= size2) {
                                    animator2 = animator3;
                                    break;
                                }
                                b bVar = s.get(s.h(i4));
                                if (bVar.f498c != null && bVar.f496a == view2 && bVar.f497b.equals(this.f492a) && bVar.f498c.equals(pVar2)) {
                                    animator2 = null;
                                    break;
                                }
                                i4++;
                            }
                        } else {
                            i = size;
                            animator2 = l;
                            pVar2 = null;
                        }
                        view = view2;
                        animator = animator2;
                        pVar = pVar2;
                    } else {
                        i = size;
                        view = pVar3.f513b;
                        animator = l;
                        pVar = null;
                    }
                    if (animator != null) {
                        String str = this.f492a;
                        Property<View, Float> property = s.f521b;
                        s.put(animator, new b(view, str, this, new B(viewGroup), pVar));
                        this.r.add(animator);
                    }
                    i2++;
                    size = i;
                }
            }
            i = size;
            i2++;
            size = i;
        }
        if (sparseIntArray.size() != 0) {
            for (int i5 = 0; i5 < sparseIntArray.size(); i5++) {
                Animator animator4 = this.r.get(sparseIntArray.keyAt(i5));
                animator4.setStartDelay(animator4.getStartDelay() + (sparseIntArray.valueAt(i5) - Long.MAX_VALUE));
            }
        }
    }

    protected void n() {
        int i = this.n - 1;
        this.n = i;
        if (i == 0) {
            ArrayList<d> arrayList = this.q;
            if (arrayList != null && arrayList.size() > 0) {
                ArrayList arrayList2 = (ArrayList) this.q.clone();
                int size = arrayList2.size();
                for (int i2 = 0; i2 < size; i2++) {
                    ((d) arrayList2.get(i2)).e(this);
                }
            }
            for (int i3 = 0; i3 < this.g.f517c.k(); i3++) {
                View l = this.g.f517c.l(i3);
                if (l != null) {
                    int i4 = a.h.h.q.e;
                    l.setHasTransientState(false);
                }
            }
            for (int i5 = 0; i5 < this.h.f517c.k(); i5++) {
                View l2 = this.h.f517c.l(i5);
                if (l2 != null) {
                    int i6 = a.h.h.q.e;
                    l2.setHasTransientState(false);
                }
            }
            this.p = true;
        }
    }

    public c o() {
        return this.s;
    }

    public TimeInterpolator p() {
        return this.f495d;
    }

    p q(View view, boolean z) {
        o oVar = this.i;
        if (oVar != null) {
            return oVar.q(view, z);
        }
        ArrayList<p> arrayList = z ? this.k : this.l;
        if (arrayList == null) {
            return null;
        }
        int size = arrayList.size();
        int i = -1;
        int i2 = 0;
        while (true) {
            if (i2 >= size) {
                break;
            }
            p pVar = arrayList.get(i2);
            if (pVar == null) {
                return null;
            }
            if (pVar.f513b == view) {
                i = i2;
                break;
            }
            i2++;
        }
        if (i >= 0) {
            return (z ? this.l : this.k).get(i);
        }
        return null;
    }

    public f r() {
        return this.t;
    }

    public long t() {
        return this.f493b;
    }

    public String toString() {
        return M("");
    }

    public String[] u() {
        return null;
    }

    public p v(View view, boolean z) {
        o oVar = this.i;
        if (oVar != null) {
            return oVar.v(view, z);
        }
        return (z ? this.g : this.h).f515a.getOrDefault(view, null);
    }

    public boolean w(p pVar, p pVar2) {
        if (pVar == null || pVar2 == null) {
            return false;
        }
        String[] u2 = u();
        if (u2 == null) {
            Iterator<String> it = pVar.f512a.keySet().iterator();
            while (it.hasNext()) {
                if (y(pVar, pVar2, it.next())) {
                }
            }
            return false;
        }
        for (String str : u2) {
            if (!y(pVar, pVar2, str)) {
            }
        }
        return false;
        return true;
    }

    boolean x(View view) {
        return (this.e.size() == 0 && this.f.size() == 0) || this.e.contains(Integer.valueOf(view.getId())) || this.f.contains(view);
    }

    public void z(View view) {
        if (this.p) {
            return;
        }
        a.e.a<Animator, b> s = s();
        int size = s.size();
        Property<View, Float> property = s.f521b;
        B b2 = new B(view);
        for (int i = size - 1; i >= 0; i--) {
            b k = s.k(i);
            if (k.f496a != null && b2.equals(k.f499d)) {
                s.h(i).pause();
            }
        }
        ArrayList<d> arrayList = this.q;
        if (arrayList != null && arrayList.size() > 0) {
            ArrayList arrayList2 = (ArrayList) this.q.clone();
            int size2 = arrayList2.size();
            for (int i2 = 0; i2 < size2; i2++) {
                ((d) arrayList2.get(i2)).a(this);
            }
        }
        this.o = true;
    }
}
