package a.f.b.a;

import a.f.a.i.a.o;
import android.util.Log;
import android.util.SparseArray;
import android.view.View;
import androidx.constraintlayout.motion.widget.MotionLayout;
import java.lang.reflect.Array;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/* loaded from: classes.dex */
public abstract class f extends o {

    static class a extends f {
        a() {
        }

        @Override // a.f.b.a.f
        public boolean h(View view, float f, long j, a.f.a.i.a.d dVar) {
            view.setAlpha(f(f, j, view, dVar));
            return this.h;
        }
    }

    public static class b extends f {
        String k;
        SparseArray<androidx.constraintlayout.widget.a> l;
        SparseArray<float[]> m = new SparseArray<>();
        float[] n;
        float[] o;

        public b(String str, SparseArray<androidx.constraintlayout.widget.a> sparseArray) {
            this.k = str.split(",")[1];
            this.l = sparseArray;
        }

        @Override // a.f.a.i.a.o
        public void b(int i, float f, float f2, int i2, float f3) {
            throw new RuntimeException("don't call for custom attribute call setPoint(pos, ConstraintAttribute,...)");
        }

        @Override // a.f.a.i.a.o
        public void e(int i) {
            int size = this.l.size();
            int g = this.l.valueAt(0).g();
            double[] dArr = new double[size];
            int i2 = g + 2;
            this.n = new float[i2];
            this.o = new float[g];
            double[][] dArr2 = (double[][]) Array.newInstance((Class<?>) double.class, size, i2);
            for (int i3 = 0; i3 < size; i3++) {
                int keyAt = this.l.keyAt(i3);
                androidx.constraintlayout.widget.a valueAt = this.l.valueAt(i3);
                float[] valueAt2 = this.m.valueAt(i3);
                dArr[i3] = keyAt * 0.01d;
                valueAt.e(this.n);
                int i4 = 0;
                while (true) {
                    if (i4 < this.n.length) {
                        dArr2[i3][i4] = r8[i4];
                        i4++;
                    }
                }
                dArr2[i3][g] = valueAt2[0];
                dArr2[i3][g + 1] = valueAt2[1];
            }
            this.f181a = a.f.a.i.a.b.a(i, dArr, dArr2);
        }

        @Override // a.f.b.a.f
        public boolean h(View view, float f, long j, a.f.a.i.a.d dVar) {
            this.f181a.d(f, this.n);
            float[] fArr = this.n;
            float f2 = fArr[fArr.length - 2];
            float f3 = fArr[fArr.length - 1];
            long j2 = j - this.i;
            if (Float.isNaN(this.j)) {
                float a2 = dVar.a(view, this.k, 0);
                this.j = a2;
                if (Float.isNaN(a2)) {
                    this.j = 0.0f;
                }
            }
            float f4 = (float) ((((j2 * 1.0E-9d) * f2) + this.j) % 1.0d);
            this.j = f4;
            this.i = j;
            float a3 = a(f4);
            this.h = false;
            int i = 0;
            while (true) {
                float[] fArr2 = this.o;
                if (i >= fArr2.length) {
                    break;
                }
                boolean z = this.h;
                float[] fArr3 = this.n;
                this.h = z | (((double) fArr3[i]) != 0.0d);
                fArr2[i] = (fArr3[i] * a3) + f3;
                i++;
            }
            a.f.b.a.a.b(this.l.valueAt(0), view, this.o);
            if (f2 != 0.0f) {
                this.h = true;
            }
            return this.h;
        }

        public void i(int i, androidx.constraintlayout.widget.a aVar, float f, int i2, float f2) {
            this.l.append(i, aVar);
            this.m.append(i, new float[]{f, f2});
            this.f182b = Math.max(this.f182b, i2);
        }
    }

    static class c extends f {
        c() {
        }

        @Override // a.f.b.a.f
        public boolean h(View view, float f, long j, a.f.a.i.a.d dVar) {
            view.setElevation(f(f, j, view, dVar));
            return this.h;
        }
    }

    public static class d extends f {
        @Override // a.f.b.a.f
        public boolean h(View view, float f, long j, a.f.a.i.a.d dVar) {
            return this.h;
        }

        public boolean i(View view, a.f.a.i.a.d dVar, float f, long j, double d2, double d3) {
            view.setRotation(f(f, j, view, dVar) + ((float) Math.toDegrees(Math.atan2(d3, d2))));
            return this.h;
        }
    }

    static class e extends f {
        boolean k = false;

        e() {
        }

        @Override // a.f.b.a.f
        public boolean h(View view, float f, long j, a.f.a.i.a.d dVar) {
            if (view instanceof MotionLayout) {
                ((MotionLayout) view).setProgress(f(f, j, view, dVar));
            } else {
                if (this.k) {
                    return false;
                }
                Method method = null;
                try {
                    method = view.getClass().getMethod("setProgress", Float.TYPE);
                } catch (NoSuchMethodException unused) {
                    this.k = true;
                }
                Method method2 = method;
                if (method2 != null) {
                    try {
                        method2.invoke(view, Float.valueOf(f(f, j, view, dVar)));
                    } catch (IllegalAccessException | InvocationTargetException e) {
                        Log.e("ViewTimeCycle", "unable to setProgress", e);
                    }
                }
            }
            return this.h;
        }
    }

    /* renamed from: a.f.b.a.f$f, reason: collision with other inner class name */
    static class C0006f extends f {
        C0006f() {
        }

        @Override // a.f.b.a.f
        public boolean h(View view, float f, long j, a.f.a.i.a.d dVar) {
            view.setRotation(f(f, j, view, dVar));
            return this.h;
        }
    }

    static class g extends f {
        g() {
        }

        @Override // a.f.b.a.f
        public boolean h(View view, float f, long j, a.f.a.i.a.d dVar) {
            view.setRotationX(f(f, j, view, dVar));
            return this.h;
        }
    }

    static class h extends f {
        h() {
        }

        @Override // a.f.b.a.f
        public boolean h(View view, float f, long j, a.f.a.i.a.d dVar) {
            view.setRotationY(f(f, j, view, dVar));
            return this.h;
        }
    }

    static class i extends f {
        i() {
        }

        @Override // a.f.b.a.f
        public boolean h(View view, float f, long j, a.f.a.i.a.d dVar) {
            view.setScaleX(f(f, j, view, dVar));
            return this.h;
        }
    }

    static class j extends f {
        j() {
        }

        @Override // a.f.b.a.f
        public boolean h(View view, float f, long j, a.f.a.i.a.d dVar) {
            view.setScaleY(f(f, j, view, dVar));
            return this.h;
        }
    }

    static class k extends f {
        k() {
        }

        @Override // a.f.b.a.f
        public boolean h(View view, float f, long j, a.f.a.i.a.d dVar) {
            view.setTranslationX(f(f, j, view, dVar));
            return this.h;
        }
    }

    static class l extends f {
        l() {
        }

        @Override // a.f.b.a.f
        public boolean h(View view, float f, long j, a.f.a.i.a.d dVar) {
            view.setTranslationY(f(f, j, view, dVar));
            return this.h;
        }
    }

    static class m extends f {
        m() {
        }

        @Override // a.f.b.a.f
        public boolean h(View view, float f, long j, a.f.a.i.a.d dVar) {
            view.setTranslationZ(f(f, j, view, dVar));
            return this.h;
        }
    }

    public static f g(String str, long j2) {
        f gVar;
        switch (str) {
            case "rotationX":
                gVar = new g();
                break;
            case "rotationY":
                gVar = new h();
                break;
            case "translationX":
                gVar = new k();
                break;
            case "translationY":
                gVar = new l();
                break;
            case "translationZ":
                gVar = new m();
                break;
            case "progress":
                gVar = new e();
                break;
            case "scaleX":
                gVar = new i();
                break;
            case "scaleY":
                gVar = new j();
                break;
            case "rotation":
                gVar = new C0006f();
                break;
            case "elevation":
                gVar = new c();
                break;
            case "transitionPathRotate":
                gVar = new d();
                break;
            case "alpha":
                gVar = new a();
                break;
            default:
                return null;
        }
        gVar.c(j2);
        return gVar;
    }

    public float f(float f, long j2, View view, a.f.a.i.a.d dVar) {
        this.f181a.d(f, this.g);
        float[] fArr = this.g;
        float f2 = fArr[1];
        if (f2 == 0.0f) {
            this.h = false;
            return fArr[2];
        }
        if (Float.isNaN(this.j)) {
            float a2 = dVar.a(view, this.f, 0);
            this.j = a2;
            if (Float.isNaN(a2)) {
                this.j = 0.0f;
            }
        }
        float f3 = (float) (((((j2 - this.i) * 1.0E-9d) * f2) + this.j) % 1.0d);
        this.j = f3;
        dVar.b(view, this.f, 0, f3);
        this.i = j2;
        float f4 = this.g[0];
        float a3 = (a(this.j) * f4) + this.g[2];
        this.h = (f4 == 0.0f && f2 == 0.0f) ? false : true;
        return a3;
    }

    public abstract boolean h(View view, float f, long j2, a.f.a.i.a.d dVar);
}
