package a.h.e;

import android.content.Context;
import android.graphics.Typeface;
import android.net.Uri;
import android.os.Handler;
import java.util.Objects;

/* loaded from: classes.dex */
public class g {

    public static class a {

        /* renamed from: a, reason: collision with root package name */
        private final int f315a;

        /* renamed from: b, reason: collision with root package name */
        private final b[] f316b;

        @Deprecated
        public a(int i, b[] bVarArr) {
            this.f315a = i;
            this.f316b = bVarArr;
        }

        public b[] a() {
            return this.f316b;
        }

        public int b() {
            return this.f315a;
        }
    }

    public static class b {

        /* renamed from: a, reason: collision with root package name */
        private final Uri f317a;

        /* renamed from: b, reason: collision with root package name */
        private final int f318b;

        /* renamed from: c, reason: collision with root package name */
        private final int f319c;

        /* renamed from: d, reason: collision with root package name */
        private final boolean f320d;
        private final int e;

        @Deprecated
        public b(Uri uri, int i, int i2, boolean z, int i3) {
            Objects.requireNonNull(uri);
            this.f317a = uri;
            this.f318b = i;
            this.f319c = i2;
            this.f320d = z;
            this.e = i3;
        }

        public int a() {
            return this.e;
        }

        public int b() {
            return this.f318b;
        }

        public Uri c() {
            return this.f317a;
        }

        public int d() {
            return this.f319c;
        }

        public boolean e() {
            return this.f320d;
        }
    }

    public static class c {
        public void a(int i) {
            throw null;
        }

        public void b(Typeface typeface) {
            throw null;
        }
    }

    public static Typeface a(Context context, e eVar, int i, boolean z, int i2, Handler handler, c cVar) {
        a.h.e.c cVar2 = new a.h.e.c(cVar, handler);
        return z ? f.d(context, eVar, cVar2, i, i2) : f.c(context, eVar, i, null, cVar2);
    }
}
