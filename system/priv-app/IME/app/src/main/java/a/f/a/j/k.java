package a.f.a.j;

import a.f.a.j.e;

/* loaded from: classes.dex */
public class k {

    /* renamed from: a, reason: collision with root package name */
    static boolean[] f213a = new boolean[3];

    static void a(f fVar, a.f.a.d dVar, e eVar) {
        e.a aVar = e.a.MATCH_PARENT;
        eVar.o = -1;
        eVar.p = -1;
        e.a aVar2 = fVar.W[0];
        e.a aVar3 = e.a.WRAP_CONTENT;
        if (aVar2 != aVar3 && eVar.W[0] == aVar) {
            int i = eVar.L.g;
            int Q = fVar.Q() - eVar.N.g;
            d dVar2 = eVar.L;
            dVar2.i = dVar.l(dVar2);
            d dVar3 = eVar.N;
            dVar3.i = dVar.l(dVar3);
            dVar.e(eVar.L.i, i);
            dVar.e(eVar.N.i, Q);
            eVar.o = 2;
            eVar.c0 = i;
            int i2 = Q - i;
            eVar.Y = i2;
            int i3 = eVar.j0;
            if (i2 < i3) {
                eVar.Y = i3;
            }
        }
        if (fVar.W[1] == aVar3 || eVar.W[1] != aVar) {
            return;
        }
        int i4 = eVar.M.g;
        int w = fVar.w() - eVar.O.g;
        d dVar4 = eVar.M;
        dVar4.i = dVar.l(dVar4);
        d dVar5 = eVar.O;
        dVar5.i = dVar.l(dVar5);
        dVar.e(eVar.M.i, i4);
        dVar.e(eVar.O.i, w);
        if (eVar.i0 > 0 || eVar.P() == 8) {
            d dVar6 = eVar.P;
            dVar6.i = dVar.l(dVar6);
            dVar.e(eVar.P.i, eVar.i0 + i4);
        }
        eVar.p = 2;
        eVar.d0 = i4;
        int i5 = w - i4;
        eVar.Z = i5;
        int i6 = eVar.k0;
        if (i5 < i6) {
            eVar.Z = i6;
        }
    }

    public static final boolean b(int i, int i2) {
        return (i & i2) == i2;
    }
}
