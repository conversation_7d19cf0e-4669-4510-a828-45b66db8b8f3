package a.h.b.b;

import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Typeface;
import android.os.Handler;
import android.util.Base64;
import android.util.TypedValue;
import android.util.Xml;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

/* loaded from: classes.dex */
public final class a {
    public static Typeface a(Context context, int i) {
        if (context.isRestricted()) {
            return null;
        }
        return e(context, i, new TypedValue(), 0, null, null, false, true);
    }

    public static Typeface b(Context context, int i) {
        if (context.isRestricted()) {
            return null;
        }
        return e(context, i, new TypedValue(), 0, null, null, false, false);
    }

    public static Typeface c(Context context, int i, TypedValue typedValue, int i2, f fVar) {
        if (context.isRestricted()) {
            return null;
        }
        return e(context, i, typedValue, i2, fVar, null, true, false);
    }

    public static void d(Context context, int i, f fVar, Handler handler) {
        if (context.isRestricted()) {
            fVar.a(-4, null);
        } else {
            e(context, i, new TypedValue(), 0, fVar, null, false, false);
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:13:0x00a0, code lost:
    
        r20.a(-3, r21);
     */
    /* JADX WARN: Code restructure failed: missing block: B:37:0x009e, code lost:
    
        if (r20 != null) goto L35;
     */
    /* JADX WARN: Code restructure failed: missing block: B:5:0x0026, code lost:
    
        if (r20 != null) goto L35;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static android.graphics.Typeface e(android.content.Context r16, int r17, android.util.TypedValue r18, int r19, a.h.b.b.f r20, android.os.Handler r21, boolean r22, boolean r23) {
        /*
            r9 = r17
            r0 = r18
            r5 = r19
            r10 = r20
            r11 = r21
            android.content.res.Resources r3 = r16.getResources()
            r1 = 1
            r3.getValue(r9, r0, r1)
            java.lang.String r12 = "ResourcesCompat"
            java.lang.CharSequence r1 = r0.string
            if (r1 == 0) goto Lc7
            java.lang.String r13 = r1.toString()
            java.lang.String r0 = "res/"
            boolean r0 = r13.startsWith(r0)
            r14 = -3
            r15 = 0
            if (r0 != 0) goto L2a
            if (r10 == 0) goto La3
            goto La0
        L2a:
            android.graphics.Typeface r0 = a.h.c.c.e(r3, r9, r5)
            if (r0 == 0) goto L38
            if (r10 == 0) goto L35
            r10.b(r0, r11)
        L35:
            r15 = r0
            goto La3
        L38:
            if (r23 == 0) goto L3c
            goto La3
        L3c:
            java.lang.String r0 = r13.toLowerCase()     // Catch: java.io.IOException -> L80 org.xmlpull.v1.XmlPullParserException -> L89
            java.lang.String r1 = ".xml"
            boolean r0 = r0.endsWith(r1)     // Catch: java.io.IOException -> L80 org.xmlpull.v1.XmlPullParserException -> L89
            if (r0 == 0) goto L6e
            android.content.res.XmlResourceParser r0 = r3.getXml(r9)     // Catch: java.io.IOException -> L80 org.xmlpull.v1.XmlPullParserException -> L89
            a.h.b.b.b r2 = f(r0, r3)     // Catch: java.io.IOException -> L80 org.xmlpull.v1.XmlPullParserException -> L89
            if (r2 != 0) goto L5d
            java.lang.String r0 = "Failed to find font-family tag"
            android.util.Log.e(r12, r0)     // Catch: java.io.IOException -> L80 org.xmlpull.v1.XmlPullParserException -> L89
            if (r10 == 0) goto La3
            r10.a(r14, r11)     // Catch: java.io.IOException -> L80 org.xmlpull.v1.XmlPullParserException -> L89
            goto La3
        L5d:
            r1 = r16
            r4 = r17
            r5 = r19
            r6 = r20
            r7 = r21
            r8 = r22
            android.graphics.Typeface r15 = a.h.c.c.b(r1, r2, r3, r4, r5, r6, r7, r8)     // Catch: java.io.IOException -> L80 org.xmlpull.v1.XmlPullParserException -> L89
            goto La3
        L6e:
            r0 = r16
            android.graphics.Typeface r0 = a.h.c.c.c(r0, r3, r9, r13, r5)     // Catch: java.io.IOException -> L80 org.xmlpull.v1.XmlPullParserException -> L89
            if (r10 == 0) goto L35
            if (r0 == 0) goto L7c
            r10.b(r0, r11)     // Catch: java.io.IOException -> L80 org.xmlpull.v1.XmlPullParserException -> L89
            goto L35
        L7c:
            r10.a(r14, r11)     // Catch: java.io.IOException -> L80 org.xmlpull.v1.XmlPullParserException -> L89
            goto L35
        L80:
            r0 = move-exception
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r1.<init>()
            java.lang.String r2 = "Failed to read xml resource "
            goto L91
        L89:
            r0 = move-exception
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r1.<init>()
            java.lang.String r2 = "Failed to parse xml resource "
        L91:
            r1.append(r2)
            r1.append(r13)
            java.lang.String r1 = r1.toString()
            android.util.Log.e(r12, r1, r0)
            if (r10 == 0) goto La3
        La0:
            r10.a(r14, r11)
        La3:
            if (r15 != 0) goto Lc6
            if (r10 != 0) goto Lc6
            if (r23 == 0) goto Laa
            goto Lc6
        Laa:
            android.content.res.Resources$NotFoundException r0 = new android.content.res.Resources$NotFoundException
            java.lang.String r1 = "Font resource ID #0x"
            java.lang.StringBuilder r1 = b.b.a.a.a.j(r1)
            java.lang.String r2 = java.lang.Integer.toHexString(r17)
            r1.append(r2)
            java.lang.String r2 = " could not be retrieved."
            r1.append(r2)
            java.lang.String r1 = r1.toString()
            r0.<init>(r1)
            throw r0
        Lc6:
            return r15
        Lc7:
            android.content.res.Resources$NotFoundException r1 = new android.content.res.Resources$NotFoundException
            java.lang.String r2 = "Resource \""
            java.lang.StringBuilder r2 = b.b.a.a.a.j(r2)
            java.lang.String r3 = r3.getResourceName(r9)
            r2.append(r3)
            java.lang.String r3 = "\" ("
            r2.append(r3)
            java.lang.String r3 = java.lang.Integer.toHexString(r17)
            r2.append(r3)
            java.lang.String r3 = ") is not a Font: "
            r2.append(r3)
            r2.append(r0)
            java.lang.String r0 = r2.toString()
            r1.<init>(r0)
            throw r1
        */
        throw new UnsupportedOperationException("Method not decompiled: a.h.b.b.a.e(android.content.Context, int, android.util.TypedValue, int, a.h.b.b.f, android.os.Handler, boolean, boolean):android.graphics.Typeface");
    }

    public static b f(XmlPullParser xmlPullParser, Resources resources) {
        int next;
        do {
            next = xmlPullParser.next();
            if (next == 2) {
                break;
            }
        } while (next != 1);
        if (next != 2) {
            throw new XmlPullParserException("No start tag found");
        }
        xmlPullParser.require(2, null, "font-family");
        if (xmlPullParser.getName().equals("font-family")) {
            TypedArray obtainAttributes = resources.obtainAttributes(Xml.asAttributeSet(xmlPullParser), a.h.a.f261b);
            String string = obtainAttributes.getString(0);
            String string2 = obtainAttributes.getString(4);
            String string3 = obtainAttributes.getString(5);
            int resourceId = obtainAttributes.getResourceId(1, 0);
            int integer = obtainAttributes.getInteger(2, 1);
            int integer2 = obtainAttributes.getInteger(3, 500);
            String string4 = obtainAttributes.getString(6);
            obtainAttributes.recycle();
            if (string != null && string2 != null && string3 != null) {
                while (xmlPullParser.next() != 3) {
                    h(xmlPullParser);
                }
                return new e(new a.h.e.e(string, string2, string3, g(resources, resourceId)), integer, integer2, string4);
            }
            ArrayList arrayList = new ArrayList();
            while (xmlPullParser.next() != 3) {
                if (xmlPullParser.getEventType() == 2) {
                    if (xmlPullParser.getName().equals("font")) {
                        TypedArray obtainAttributes2 = resources.obtainAttributes(Xml.asAttributeSet(xmlPullParser), a.h.a.f262c);
                        int i = obtainAttributes2.getInt(obtainAttributes2.hasValue(8) ? 8 : 1, 400);
                        boolean z = 1 == obtainAttributes2.getInt(obtainAttributes2.hasValue(6) ? 6 : 2, 0);
                        int i2 = obtainAttributes2.hasValue(9) ? 9 : 3;
                        String string5 = obtainAttributes2.getString(obtainAttributes2.hasValue(7) ? 7 : 4);
                        int i3 = obtainAttributes2.getInt(i2, 0);
                        int i4 = obtainAttributes2.hasValue(5) ? 5 : 0;
                        int resourceId2 = obtainAttributes2.getResourceId(i4, 0);
                        String string6 = obtainAttributes2.getString(i4);
                        obtainAttributes2.recycle();
                        while (xmlPullParser.next() != 3) {
                            h(xmlPullParser);
                        }
                        arrayList.add(new d(string6, i, z, string5, i3, resourceId2));
                    } else {
                        h(xmlPullParser);
                    }
                }
            }
            if (!arrayList.isEmpty()) {
                return new c((d[]) arrayList.toArray(new d[arrayList.size()]));
            }
        } else {
            h(xmlPullParser);
        }
        return null;
    }

    public static List<List<byte[]>> g(Resources resources, int i) {
        if (i == 0) {
            return Collections.emptyList();
        }
        TypedArray obtainTypedArray = resources.obtainTypedArray(i);
        try {
            if (obtainTypedArray.length() == 0) {
                return Collections.emptyList();
            }
            ArrayList arrayList = new ArrayList();
            if (obtainTypedArray.getType(0) == 1) {
                for (int i2 = 0; i2 < obtainTypedArray.length(); i2++) {
                    int resourceId = obtainTypedArray.getResourceId(i2, 0);
                    if (resourceId != 0) {
                        arrayList.add(i(resources.getStringArray(resourceId)));
                    }
                }
            } else {
                arrayList.add(i(resources.getStringArray(i)));
            }
            return arrayList;
        } finally {
            obtainTypedArray.recycle();
        }
    }

    private static void h(XmlPullParser xmlPullParser) {
        int i = 1;
        while (i > 0) {
            int next = xmlPullParser.next();
            if (next == 2) {
                i++;
            } else if (next == 3) {
                i--;
            }
        }
    }

    private static List<byte[]> i(String[] strArr) {
        ArrayList arrayList = new ArrayList();
        for (String str : strArr) {
            arrayList.add(Base64.decode(str, 0));
        }
        return arrayList;
    }
}
