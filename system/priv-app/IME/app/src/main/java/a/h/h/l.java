package a.h.h;

import android.content.Context;
import android.view.PointerIcon;

/* loaded from: classes.dex */
public final class l {

    /* renamed from: a, reason: collision with root package name */
    private Object f374a;

    private l(Object obj) {
        this.f374a = obj;
    }

    public static l b(Context context, int i) {
        return new l(PointerIcon.getSystemIcon(context, i));
    }

    public Object a() {
        return this.f374a;
    }
}
