package a.o;

import android.view.View;
import android.view.ViewGroup;
import org.libpag.R;

/* loaded from: classes.dex */
class z extends l {

    /* renamed from: a, reason: collision with root package name */
    final /* synthetic */ ViewGroup f522a;

    /* renamed from: b, reason: collision with root package name */
    final /* synthetic */ View f523b;

    /* renamed from: c, reason: collision with root package name */
    final /* synthetic */ View f524c;

    /* renamed from: d, reason: collision with root package name */
    final /* synthetic */ A f525d;

    z(A a2, ViewGroup viewGroup, View view, View view2) {
        this.f525d = a2;
        this.f522a = viewGroup;
        this.f523b = view;
        this.f524c = view2;
    }

    @Override // a.o.l, a.o.i.d
    public void a(i iVar) {
        new r(this.f522a).b(this.f523b);
    }

    @Override // a.o.l, a.o.i.d
    public void b(i iVar) {
        if (this.f523b.getParent() == null) {
            new r(this.f522a).a(this.f523b);
        } else {
            this.f525d.d();
        }
    }

    @Override // a.o.i.d
    public void e(i iVar) {
        this.f524c.setTag(R.id.save_overlay_view, null);
        new r(this.f522a).b(this.f523b);
        iVar.B(this);
    }
}
