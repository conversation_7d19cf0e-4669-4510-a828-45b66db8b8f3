package a.h.c;

import a.h.e.g;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Typeface;
import android.os.CancellationSignal;
import java.util.concurrent.ConcurrentHashMap;

/* loaded from: classes.dex */
class e {

    /* renamed from: a, reason: collision with root package name */
    @SuppressLint({"BanConcurrentHashMap"})
    private ConcurrentHashMap<Long, a.h.b.b.c> f287a = new ConcurrentHashMap<>();

    e() {
    }

    public Typeface a(Context context, a.h.b.b.c cVar, Resources resources, int i) {
        throw null;
    }

    public Typeface b(Context context, CancellationSignal cancellationSignal, g.b[] bVarArr, int i) {
        throw null;
    }

    public Typeface c(Context context, Resources resources, int i, String str, int i2) {
        throw null;
    }
}
