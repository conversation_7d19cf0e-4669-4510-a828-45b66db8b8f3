package a.f.a.j.o;

import a.f.a.j.e;
import java.util.ArrayList;

/* loaded from: classes.dex */
public class i {
    public static o a(a.f.a.j.e eVar, int i, ArrayList<o> arrayList, o oVar) {
        a.f.a.j.d dVar;
        int i2;
        int i3 = i == 0 ? eVar.L0 : eVar.M0;
        if (i3 != -1 && (oVar == null || i3 != oVar.f240b)) {
            int i4 = 0;
            while (true) {
                if (i4 >= arrayList.size()) {
                    break;
                }
                o oVar2 = arrayList.get(i4);
                if (oVar2.f240b == i3) {
                    if (oVar != null) {
                        oVar.d(i, oVar2);
                        arrayList.remove(oVar);
                    }
                    oVar = oVar2;
                } else {
                    i4++;
                }
            }
        } else if (i3 != -1) {
            return oVar;
        }
        if (oVar == null) {
            if (eVar instanceof a.f.a.j.j) {
                a.f.a.j.j jVar = (a.f.a.j.j) eVar;
                int i5 = 0;
                while (true) {
                    if (i5 >= jVar.O0) {
                        i2 = -1;
                        break;
                    }
                    a.f.a.j.e eVar2 = jVar.N0[i5];
                    if ((i == 0 && (i2 = eVar2.L0) != -1) || (i == 1 && (i2 = eVar2.M0) != -1)) {
                        break;
                    }
                    i5++;
                }
                if (i2 != -1) {
                    int i6 = 0;
                    while (true) {
                        if (i6 >= arrayList.size()) {
                            break;
                        }
                        o oVar3 = arrayList.get(i6);
                        if (oVar3.f240b == i2) {
                            oVar = oVar3;
                            break;
                        }
                        i6++;
                    }
                }
            }
            if (oVar == null) {
                oVar = new o(i);
            }
            arrayList.add(oVar);
        }
        if (oVar.a(eVar)) {
            if (eVar instanceof a.f.a.j.h) {
                a.f.a.j.h hVar = (a.f.a.j.h) eVar;
                hVar.Y0().c(hVar.Z0() == 0 ? 1 : 0, arrayList, oVar);
            }
            if (i == 0) {
                eVar.L0 = oVar.f240b;
                eVar.L.c(i, arrayList, oVar);
                dVar = eVar.N;
            } else {
                eVar.M0 = oVar.f240b;
                eVar.M.c(i, arrayList, oVar);
                eVar.P.c(i, arrayList, oVar);
                dVar = eVar.O;
            }
            dVar.c(i, arrayList, oVar);
            eVar.S.c(i, arrayList, oVar);
        }
        return oVar;
    }

    private static o b(ArrayList<o> arrayList, int i) {
        int size = arrayList.size();
        for (int i2 = 0; i2 < size; i2++) {
            o oVar = arrayList.get(i2);
            if (i == oVar.f240b) {
                return oVar;
            }
        }
        return null;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:216:0x0363  */
    /* JADX WARN: Removed duplicated region for block: B:237:0x0398 A[ADDED_TO_REGION] */
    /* JADX WARN: Removed duplicated region for block: B:243:0x0394  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static boolean c(a.f.a.j.f r19, a.f.a.j.o.b.InterfaceC0003b r20) {
        /*
            Method dump skipped, instructions count: 927
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: a.f.a.j.o.i.c(a.f.a.j.f, a.f.a.j.o.b$b):boolean");
    }

    public static boolean d(e.a aVar, e.a aVar2, e.a aVar3, e.a aVar4) {
        e.a aVar5 = e.a.MATCH_PARENT;
        e.a aVar6 = e.a.WRAP_CONTENT;
        e.a aVar7 = e.a.FIXED;
        return (aVar3 == aVar7 || aVar3 == aVar6 || (aVar3 == aVar5 && aVar != aVar6)) || (aVar4 == aVar7 || aVar4 == aVar6 || (aVar4 == aVar5 && aVar2 != aVar6));
    }
}
