package a.c.a.a;

/* loaded from: classes.dex */
public class a extends c {

    /* renamed from: c, reason: collision with root package name */
    private static volatile a f44c;

    /* renamed from: a, reason: collision with root package name */
    private c f45a;

    /* renamed from: b, reason: collision with root package name */
    private c f46b;

    private a() {
        b bVar = new b();
        this.f46b = bVar;
        this.f45a = bVar;
    }

    public static a b() {
        if (f44c != null) {
            return f44c;
        }
        synchronized (a.class) {
            if (f44c == null) {
                f44c = new a();
            }
        }
        return f44c;
    }

    @Override // a.c.a.a.c
    public boolean a() {
        return this.f45a.a();
    }
}
