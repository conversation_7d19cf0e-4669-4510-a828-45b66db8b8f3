package a.f.a.i.a;

/* loaded from: classes.dex */
public class k implements m {

    /* renamed from: b, reason: collision with root package name */
    private double f173b;

    /* renamed from: c, reason: collision with root package name */
    private double f174c;

    /* renamed from: d, reason: collision with root package name */
    private float f175d;
    private float e;
    private float f;
    private float g;
    private float h;

    /* renamed from: a, reason: collision with root package name */
    double f172a = 0.5d;
    private int i = 0;

    @Override // a.f.a.i.a.m
    public boolean a() {
        double d2 = this.e - this.f174c;
        double d3 = this.f173b;
        double d4 = this.f;
        return Math.sqrt((((d3 * d2) * d2) + ((d4 * d4) * ((double) this.g))) / d3) <= ((double) this.h);
    }

    @Override // a.f.a.i.a.m
    public float b() {
        return 0.0f;
    }

    public void c(float f, float f2, float f3, float f4, float f5, float f6, float f7, int i) {
        this.f174c = f2;
        this.f172a = f6;
        this.e = f;
        this.f173b = f5;
        this.g = f4;
        this.h = f7;
        this.i = i;
        this.f175d = 0.0f;
    }

    @Override // a.f.a.i.a.m
    public float getInterpolation(float f) {
        k kVar = this;
        double d2 = f - kVar.f175d;
        double d3 = kVar.f173b;
        double d4 = kVar.f172a;
        int sqrt = (int) ((9.0d / ((Math.sqrt(d3 / kVar.g) * d2) * 4.0d)) + 1.0d);
        double d5 = d2 / sqrt;
        int i = 0;
        while (i < sqrt) {
            double d6 = kVar.e;
            double d7 = kVar.f174c;
            int i2 = sqrt;
            int i3 = i;
            double d8 = kVar.f;
            double d9 = kVar.g;
            double d10 = ((((((-d3) * (d6 - d7)) - (d8 * d4)) / d9) * d5) / 2.0d) + d8;
            double d11 = ((((-((((d5 * d10) / 2.0d) + d6) - d7)) * d3) - (d10 * d4)) / d9) * d5;
            float f2 = (float) (d8 + d11);
            this.f = f2;
            float f3 = (float) ((((d11 / 2.0d) + d8) * d5) + d6);
            this.e = f3;
            int i4 = this.i;
            if (i4 > 0) {
                if (f3 < 0.0f && (i4 & 1) == 1) {
                    this.e = -f3;
                    this.f = -f2;
                }
                float f4 = this.e;
                if (f4 > 1.0f && (i4 & 2) == 2) {
                    this.e = 2.0f - f4;
                    this.f = -this.f;
                }
            }
            sqrt = i2;
            i = i3 + 1;
            kVar = this;
        }
        k kVar2 = kVar;
        kVar2.f175d = f;
        return kVar2.e;
    }
}
