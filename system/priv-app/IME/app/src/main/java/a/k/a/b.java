package a.k.a;

import a.k.a.a;
import a.k.a.b;
import android.os.Looper;
import android.util.AndroidRuntimeException;
import android.view.View;
import java.util.ArrayList;

/* loaded from: classes.dex */
public abstract class b<T extends b<T>> implements a.b {
    public static final k l = new c("scaleX");
    public static final k m = new d("scaleY");
    public static final k n = new e("rotation");
    public static final k o = new f("rotationX");
    public static final k p = new g("rotationY");
    public static final k q = new a("alpha");

    /* renamed from: d, reason: collision with root package name */
    final Object f449d;
    final a.k.a.c e;
    private float i;

    /* renamed from: a, reason: collision with root package name */
    float f446a = 0.0f;

    /* renamed from: b, reason: collision with root package name */
    float f447b = Float.MAX_VALUE;

    /* renamed from: c, reason: collision with root package name */
    boolean f448c = false;
    boolean f = false;
    float g = -3.4028235E38f;
    private long h = 0;
    private final ArrayList<i> j = new ArrayList<>();
    private final ArrayList<j> k = new ArrayList<>();

    static class a extends k {
        a(String str) {
            super(str, null);
        }

        @Override // a.k.a.c
        public float a(View view) {
            return view.getAlpha();
        }

        @Override // a.k.a.c
        public void b(View view, float f) {
            view.setAlpha(f);
        }
    }

    /* renamed from: a.k.a.b$b, reason: collision with other inner class name */
    static class C0020b extends k {
    }

    static class c extends k {
        c(String str) {
            super(str, null);
        }

        @Override // a.k.a.c
        public float a(View view) {
            return view.getScaleX();
        }

        @Override // a.k.a.c
        public void b(View view, float f) {
            view.setScaleX(f);
        }
    }

    static class d extends k {
        d(String str) {
            super(str, null);
        }

        @Override // a.k.a.c
        public float a(View view) {
            return view.getScaleY();
        }

        @Override // a.k.a.c
        public void b(View view, float f) {
            view.setScaleY(f);
        }
    }

    static class e extends k {
        e(String str) {
            super(str, null);
        }

        @Override // a.k.a.c
        public float a(View view) {
            return view.getRotation();
        }

        @Override // a.k.a.c
        public void b(View view, float f) {
            view.setRotation(f);
        }
    }

    static class f extends k {
        f(String str) {
            super(str, null);
        }

        @Override // a.k.a.c
        public float a(View view) {
            return view.getRotationX();
        }

        @Override // a.k.a.c
        public void b(View view, float f) {
            view.setRotationX(f);
        }
    }

    static class g extends k {
        g(String str) {
            super(str, null);
        }

        @Override // a.k.a.c
        public float a(View view) {
            return view.getRotationY();
        }

        @Override // a.k.a.c
        public void b(View view, float f) {
            view.setRotationY(f);
        }
    }

    static class h {

        /* renamed from: a, reason: collision with root package name */
        float f450a;

        /* renamed from: b, reason: collision with root package name */
        float f451b;

        h() {
        }
    }

    public interface i {
        void a(b bVar, boolean z, float f, float f2);
    }

    public interface j {
        void a(b bVar, float f, float f2);
    }

    public static abstract class k extends a.k.a.c<View> {
        k(String str, C0020b c0020b) {
            super(str);
        }
    }

    <K> b(K k2, a.k.a.c<K> cVar) {
        float f2;
        this.f449d = k2;
        this.e = cVar;
        if (cVar == n || cVar == o || cVar == p) {
            f2 = 0.1f;
        } else {
            if (cVar == q || cVar == l || cVar == m) {
                this.i = 0.00390625f;
                return;
            }
            f2 = 1.0f;
        }
        this.i = f2;
    }

    private void c(boolean z) {
        this.f = false;
        a.k.a.a.c().e(this);
        this.h = 0L;
        this.f448c = false;
        for (int i2 = 0; i2 < this.j.size(); i2++) {
            if (this.j.get(i2) != null) {
                this.j.get(i2).a(this, z, this.f447b, this.f446a);
            }
        }
        e(this.j);
    }

    private static <T> void e(ArrayList<T> arrayList) {
        for (int size = arrayList.size() - 1; size >= 0; size--) {
            if (arrayList.get(size) == null) {
                arrayList.remove(size);
            }
        }
    }

    @Override // a.k.a.a.b
    public boolean a(long j2) {
        long j3 = this.h;
        if (j3 == 0) {
            this.h = j2;
            f(this.f447b);
            return false;
        }
        this.h = j2;
        boolean h2 = h(j2 - j3);
        float min = Math.min(this.f447b, Float.MAX_VALUE);
        this.f447b = min;
        float max = Math.max(min, this.g);
        this.f447b = max;
        f(max);
        if (h2) {
            c(false);
        }
        return h2;
    }

    public void b() {
        if (Looper.myLooper() != Looper.getMainLooper()) {
            throw new AndroidRuntimeException("Animations may only be canceled on the main thread");
        }
        if (this.f) {
            c(true);
        }
    }

    float d() {
        return this.i * 0.75f;
    }

    void f(float f2) {
        this.e.b(this.f449d, f2);
        for (int i2 = 0; i2 < this.k.size(); i2++) {
            if (this.k.get(i2) != null) {
                this.k.get(i2).a(this, this.f447b, this.f446a);
            }
        }
        e(this.k);
    }

    public T g(float f2) {
        this.f447b = f2;
        this.f448c = true;
        return this;
    }

    abstract boolean h(long j2);
}
