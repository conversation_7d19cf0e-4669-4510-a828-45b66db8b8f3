package a.f.a.i.a;

import java.io.PrintStream;
import java.lang.reflect.Array;
import java.util.Arrays;

/* loaded from: classes.dex */
public class l extends c {

    /* renamed from: d, reason: collision with root package name */
    g f176d;

    l(String str) {
        this.f141a = str;
        double[] dArr = new double[str.length() / 2];
        int indexOf = str.indexOf(40) + 1;
        int indexOf2 = str.indexOf(44, indexOf);
        int i = 0;
        while (indexOf2 != -1) {
            dArr[i] = Double.parseDouble(str.substring(indexOf, indexOf2).trim());
            indexOf = indexOf2 + 1;
            indexOf2 = str.indexOf(44, indexOf);
            i++;
        }
        dArr[i] = Double.parseDouble(str.substring(indexOf, str.indexOf(41, indexOf)).trim());
        double[] copyOf = Arrays.copyOf(dArr, i + 1);
        int length = (copyOf.length * 3) - 2;
        int length2 = copyOf.length - 1;
        double d2 = 1.0d / length2;
        double[][] dArr2 = (double[][]) Array.newInstance((Class<?>) double.class, length, 1);
        double[] dArr3 = new double[length];
        for (int i2 = 0; i2 < copyOf.length; i2++) {
            double d3 = copyOf[i2];
            int i3 = i2 + length2;
            dArr2[i3][0] = d3;
            double d4 = i2 * d2;
            dArr3[i3] = d4;
            if (i2 > 0) {
                int i4 = (length2 * 2) + i2;
                dArr2[i4][0] = d3 + 1.0d;
                dArr3[i4] = d4 + 1.0d;
                int i5 = i2 - 1;
                dArr2[i5][0] = (d3 - 1.0d) - d2;
                dArr3[i5] = (d4 - 1.0d) - d2;
            }
        }
        g gVar = new g(dArr3, dArr2);
        PrintStream printStream = System.out;
        StringBuilder j = b.b.a.a.a.j(" 0 ");
        j.append(gVar.b(0.0d, 0));
        printStream.println(j.toString());
        PrintStream printStream2 = System.out;
        StringBuilder j2 = b.b.a.a.a.j(" 1 ");
        j2.append(gVar.b(1.0d, 0));
        printStream2.println(j2.toString());
        this.f176d = gVar;
    }

    @Override // a.f.a.i.a.c
    public double a(double d2) {
        return this.f176d.b(d2, 0);
    }

    @Override // a.f.a.i.a.c
    public double b(double d2) {
        return this.f176d.e(d2, 0);
    }
}
