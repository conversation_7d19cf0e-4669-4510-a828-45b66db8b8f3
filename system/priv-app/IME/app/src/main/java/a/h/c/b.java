package a.h.c;

import android.graphics.Insets;

/* loaded from: classes.dex */
public final class b {
    public static final b e = new b(0, 0, 0, 0);

    /* renamed from: a, reason: collision with root package name */
    public final int f279a;

    /* renamed from: b, reason: collision with root package name */
    public final int f280b;

    /* renamed from: c, reason: collision with root package name */
    public final int f281c;

    /* renamed from: d, reason: collision with root package name */
    public final int f282d;

    private b(int i, int i2, int i3, int i4) {
        this.f279a = i;
        this.f280b = i2;
        this.f281c = i3;
        this.f282d = i4;
    }

    public static b a(int i, int i2, int i3, int i4) {
        return (i == 0 && i2 == 0 && i3 == 0 && i4 == 0) ? e : new b(i, i2, i3, i4);
    }

    public Insets b() {
        return Insets.of(this.f279a, this.f280b, this.f281c, this.f282d);
    }

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || b.class != obj.getClass()) {
            return false;
        }
        b bVar = (b) obj;
        return this.f282d == bVar.f282d && this.f279a == bVar.f279a && this.f281c == bVar.f281c && this.f280b == bVar.f280b;
    }

    public int hashCode() {
        return (((((this.f279a * 31) + this.f280b) * 31) + this.f281c) * 31) + this.f282d;
    }

    public String toString() {
        StringBuilder j = b.b.a.a.a.j("Insets{left=");
        j.append(this.f279a);
        j.append(", top=");
        j.append(this.f280b);
        j.append(", right=");
        j.append(this.f281c);
        j.append(", bottom=");
        j.append(this.f282d);
        j.append('}');
        return j.toString();
    }
}
