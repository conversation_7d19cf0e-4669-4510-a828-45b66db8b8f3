package a.h.h.x;

import a.h.h.x.d;
import android.R;
import android.graphics.Rect;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ClickableSpan;
import android.util.Log;
import android.view.View;
import android.view.accessibility.AccessibilityNodeInfo;
import com.google.android.material.button.MaterialButton;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/* loaded from: classes.dex */
public class b {

    /* renamed from: a, reason: collision with root package name */
    private final AccessibilityNodeInfo f405a;

    /* renamed from: b, reason: collision with root package name */
    public int f406b = -1;

    /* renamed from: c, reason: collision with root package name */
    private int f407c = -1;

    public static class a {
        public static final a e = new a(1, null);
        public static final a f = new a(2, null);
        public static final a g;
        public static final a h;
        public static final a i;
        public static final a j;
        public static final a k;
        public static final a l;
        public static final a m;
        public static final a n;
        public static final a o;

        /* renamed from: a, reason: collision with root package name */
        final Object f408a;

        /* renamed from: b, reason: collision with root package name */
        private final int f409b;

        /* renamed from: c, reason: collision with root package name */
        private final Class<? extends d.a> f410c;

        /* renamed from: d, reason: collision with root package name */
        protected final a.h.h.x.d f411d;

        static {
            new AccessibilityNodeInfo.AccessibilityAction(4, null);
            new AccessibilityNodeInfo.AccessibilityAction(8, null);
            g = new a(16, null);
            new AccessibilityNodeInfo.AccessibilityAction(32, null);
            new AccessibilityNodeInfo.AccessibilityAction(64, null);
            new AccessibilityNodeInfo.AccessibilityAction(128, null);
            new AccessibilityNodeInfo.AccessibilityAction(256, null);
            new AccessibilityNodeInfo.AccessibilityAction(512, null);
            new AccessibilityNodeInfo.AccessibilityAction(1024, null);
            new AccessibilityNodeInfo.AccessibilityAction(2048, null);
            h = new a(4096, null);
            i = new a(8192, null);
            new AccessibilityNodeInfo.AccessibilityAction(16384, null);
            new AccessibilityNodeInfo.AccessibilityAction(32768, null);
            new AccessibilityNodeInfo.AccessibilityAction(65536, null);
            new AccessibilityNodeInfo.AccessibilityAction(131072, null);
            j = new a(262144, null);
            k = new a(524288, null);
            l = new a(1048576, null);
            new AccessibilityNodeInfo.AccessibilityAction(2097152, null);
            if (AccessibilityNodeInfo.AccessibilityAction.ACTION_SHOW_ON_SCREEN == null) {
                new AccessibilityNodeInfo.AccessibilityAction(R.id.accessibilityActionShowOnScreen, null);
            }
            if (AccessibilityNodeInfo.AccessibilityAction.ACTION_SCROLL_TO_POSITION == null) {
                new AccessibilityNodeInfo.AccessibilityAction(R.id.accessibilityActionScrollToPosition, null);
            }
            m = new a(AccessibilityNodeInfo.AccessibilityAction.ACTION_SCROLL_UP, R.id.accessibilityActionScrollUp, null, null, null);
            if (AccessibilityNodeInfo.AccessibilityAction.ACTION_SCROLL_LEFT == null) {
                new AccessibilityNodeInfo.AccessibilityAction(R.id.accessibilityActionScrollLeft, null);
            }
            n = new a(AccessibilityNodeInfo.AccessibilityAction.ACTION_SCROLL_DOWN, R.id.accessibilityActionScrollDown, null, null, null);
            if (AccessibilityNodeInfo.AccessibilityAction.ACTION_SCROLL_RIGHT == null) {
                new AccessibilityNodeInfo.AccessibilityAction(R.id.accessibilityActionScrollRight, null);
            }
            if (AccessibilityNodeInfo.AccessibilityAction.ACTION_PAGE_UP == null) {
                new AccessibilityNodeInfo.AccessibilityAction(R.id.accessibilityActionPageUp, null);
            }
            if (AccessibilityNodeInfo.AccessibilityAction.ACTION_PAGE_DOWN == null) {
                new AccessibilityNodeInfo.AccessibilityAction(R.id.accessibilityActionPageDown, null);
            }
            if (AccessibilityNodeInfo.AccessibilityAction.ACTION_PAGE_LEFT == null) {
                new AccessibilityNodeInfo.AccessibilityAction(R.id.accessibilityActionPageLeft, null);
            }
            if (AccessibilityNodeInfo.AccessibilityAction.ACTION_PAGE_RIGHT == null) {
                new AccessibilityNodeInfo.AccessibilityAction(R.id.accessibilityActionPageRight, null);
            }
            if (AccessibilityNodeInfo.AccessibilityAction.ACTION_CONTEXT_CLICK == null) {
                new AccessibilityNodeInfo.AccessibilityAction(R.id.accessibilityActionContextClick, null);
            }
            o = new a(AccessibilityNodeInfo.AccessibilityAction.ACTION_SET_PROGRESS, R.id.accessibilityActionSetProgress, null, null, d.b.class);
            if (AccessibilityNodeInfo.AccessibilityAction.ACTION_MOVE_WINDOW == null) {
                new AccessibilityNodeInfo.AccessibilityAction(R.id.accessibilityActionMoveWindow, null);
            }
            if (AccessibilityNodeInfo.AccessibilityAction.ACTION_SHOW_TOOLTIP == null) {
                new AccessibilityNodeInfo.AccessibilityAction(R.id.accessibilityActionShowTooltip, null);
            }
            if (AccessibilityNodeInfo.AccessibilityAction.ACTION_HIDE_TOOLTIP == null) {
                new AccessibilityNodeInfo.AccessibilityAction(R.id.accessibilityActionHideTooltip, null);
            }
            if (AccessibilityNodeInfo.AccessibilityAction.ACTION_PRESS_AND_HOLD == null) {
                new AccessibilityNodeInfo.AccessibilityAction(R.id.accessibilityActionPressAndHold, null);
            }
            if (AccessibilityNodeInfo.AccessibilityAction.ACTION_IME_ENTER == null) {
                new AccessibilityNodeInfo.AccessibilityAction(R.id.accessibilityActionImeEnter, null);
            }
        }

        public a(int i2, CharSequence charSequence) {
            this(null, i2, null, null, null);
        }

        public a(int i2, CharSequence charSequence, a.h.h.x.d dVar) {
            this(null, i2, charSequence, dVar, null);
        }

        a(Object obj, int i2, CharSequence charSequence, a.h.h.x.d dVar, Class<? extends d.a> cls) {
            this.f409b = i2;
            this.f411d = dVar;
            this.f408a = obj == null ? new AccessibilityNodeInfo.AccessibilityAction(i2, charSequence) : obj;
            this.f410c = cls;
        }

        public a a(CharSequence charSequence, a.h.h.x.d dVar) {
            return new a(null, this.f409b, charSequence, dVar, this.f410c);
        }

        public int b() {
            return ((AccessibilityNodeInfo.AccessibilityAction) this.f408a).getId();
        }

        public boolean c(View view, Bundle bundle) {
            d.a aVar;
            Exception e2;
            if (this.f411d == null) {
                return false;
            }
            d.a aVar2 = null;
            Class<? extends d.a> cls = this.f410c;
            if (cls != null) {
                try {
                    aVar = cls.getDeclaredConstructor(new Class[0]).newInstance(new Object[0]);
                } catch (Exception e3) {
                    aVar = null;
                    e2 = e3;
                }
                try {
                    Objects.requireNonNull(aVar);
                } catch (Exception e4) {
                    e2 = e4;
                    Class<? extends d.a> cls2 = this.f410c;
                    Log.e("A11yActionCompat", "Failed to execute command with argument class ViewCommandArgument: " + (cls2 == null ? "null" : cls2.getName()), e2);
                    aVar2 = aVar;
                    return this.f411d.a(view, aVar2);
                }
                aVar2 = aVar;
            }
            return this.f411d.a(view, aVar2);
        }

        public boolean equals(Object obj) {
            if (obj == null || !(obj instanceof a)) {
                return false;
            }
            a aVar = (a) obj;
            Object obj2 = this.f408a;
            return obj2 == null ? aVar.f408a == null : obj2.equals(aVar.f408a);
        }

        public int hashCode() {
            Object obj = this.f408a;
            if (obj != null) {
                return obj.hashCode();
            }
            return 0;
        }
    }

    /* renamed from: a.h.h.x.b$b, reason: collision with other inner class name */
    public static class C0011b {

        /* renamed from: a, reason: collision with root package name */
        final Object f412a;

        C0011b(Object obj) {
            this.f412a = obj;
        }

        public static C0011b a(int i, int i2, boolean z) {
            return new C0011b(AccessibilityNodeInfo.CollectionInfo.obtain(i, i2, z));
        }

        public static C0011b b(int i, int i2, boolean z, int i3) {
            return new C0011b(AccessibilityNodeInfo.CollectionInfo.obtain(i, i2, z, i3));
        }
    }

    public static class c {

        /* renamed from: a, reason: collision with root package name */
        final Object f413a;

        c(Object obj) {
            this.f413a = obj;
        }

        public static c a(int i, int i2, int i3, int i4, boolean z, boolean z2) {
            return new c(AccessibilityNodeInfo.CollectionItemInfo.obtain(i, i2, i3, i4, z, z2));
        }
    }

    public static class d {

        /* renamed from: a, reason: collision with root package name */
        final Object f414a;

        d(Object obj) {
            this.f414a = obj;
        }

        public static d a(int i, float f, float f2, float f3) {
            return new d(AccessibilityNodeInfo.RangeInfo.obtain(i, f, f2, f3));
        }
    }

    private b(AccessibilityNodeInfo accessibilityNodeInfo) {
        this.f405a = accessibilityNodeInfo;
    }

    public static b C() {
        return new b(AccessibilityNodeInfo.obtain());
    }

    public static b D(View view) {
        return new b(AccessibilityNodeInfo.obtain(view));
    }

    public static b E(b bVar) {
        return new b(AccessibilityNodeInfo.obtain(bVar.f405a));
    }

    private List<Integer> e(String str) {
        ArrayList<Integer> integerArrayList = this.f405a.getExtras().getIntegerArrayList(str);
        if (integerArrayList != null) {
            return integerArrayList;
        }
        ArrayList<Integer> arrayList = new ArrayList<>();
        this.f405a.getExtras().putIntegerArrayList(str, arrayList);
        return arrayList;
    }

    private static String f(int i) {
        if (i == 1) {
            return "ACTION_FOCUS";
        }
        if (i == 2) {
            return "ACTION_CLEAR_FOCUS";
        }
        switch (i) {
            case 4:
                return "ACTION_SELECT";
            case 8:
                return "ACTION_CLEAR_SELECTION";
            case MaterialButton.ICON_GRAVITY_TOP /* 16 */:
                return "ACTION_CLICK";
            case MaterialButton.ICON_GRAVITY_TEXT_TOP /* 32 */:
                return "ACTION_LONG_CLICK";
            case 64:
                return "ACTION_ACCESSIBILITY_FOCUS";
            case 128:
                return "ACTION_CLEAR_ACCESSIBILITY_FOCUS";
            case 256:
                return "ACTION_NEXT_AT_MOVEMENT_GRANULARITY";
            case 512:
                return "ACTION_PREVIOUS_AT_MOVEMENT_GRANULARITY";
            case 1024:
                return "ACTION_NEXT_HTML_ELEMENT";
            case 2048:
                return "ACTION_PREVIOUS_HTML_ELEMENT";
            case 4096:
                return "ACTION_SCROLL_FORWARD";
            case 8192:
                return "ACTION_SCROLL_BACKWARD";
            case 16384:
                return "ACTION_COPY";
            case 32768:
                return "ACTION_PASTE";
            case 65536:
                return "ACTION_CUT";
            case 131072:
                return "ACTION_SET_SELECTION";
            case 262144:
                return "ACTION_EXPAND";
            case 524288:
                return "ACTION_COLLAPSE";
            case 2097152:
                return "ACTION_SET_TEXT";
            case R.id.accessibilityActionMoveWindow:
                return "ACTION_MOVE_WINDOW";
            case R.id.accessibilityActionImeEnter:
                return "ACTION_IME_ENTER";
            default:
                switch (i) {
                    case R.id.accessibilityActionShowOnScreen:
                        return "ACTION_SHOW_ON_SCREEN";
                    case R.id.accessibilityActionScrollToPosition:
                        return "ACTION_SCROLL_TO_POSITION";
                    case R.id.accessibilityActionScrollUp:
                        return "ACTION_SCROLL_UP";
                    case R.id.accessibilityActionScrollLeft:
                        return "ACTION_SCROLL_LEFT";
                    case R.id.accessibilityActionScrollDown:
                        return "ACTION_SCROLL_DOWN";
                    case R.id.accessibilityActionScrollRight:
                        return "ACTION_SCROLL_RIGHT";
                    case R.id.accessibilityActionContextClick:
                        return "ACTION_CONTEXT_CLICK";
                    case R.id.accessibilityActionSetProgress:
                        return "ACTION_SET_PROGRESS";
                    default:
                        switch (i) {
                            case R.id.accessibilityActionShowTooltip:
                                return "ACTION_SHOW_TOOLTIP";
                            case R.id.accessibilityActionHideTooltip:
                                return "ACTION_HIDE_TOOLTIP";
                            case R.id.accessibilityActionPageUp:
                                return "ACTION_PAGE_UP";
                            case R.id.accessibilityActionPageDown:
                                return "ACTION_PAGE_DOWN";
                            case R.id.accessibilityActionPageLeft:
                                return "ACTION_PAGE_LEFT";
                            case R.id.accessibilityActionPageRight:
                                return "ACTION_PAGE_RIGHT";
                            case R.id.accessibilityActionPressAndHold:
                                return "ACTION_PRESS_AND_HOLD";
                            default:
                                return "ACTION_UNKNOWN";
                        }
                }
        }
    }

    public static ClickableSpan[] l(CharSequence charSequence) {
        if (charSequence instanceof Spanned) {
            return (ClickableSpan[]) ((Spanned) charSequence).getSpans(0, charSequence.length(), ClickableSpan.class);
        }
        return null;
    }

    public static b s0(AccessibilityNodeInfo accessibilityNodeInfo) {
        return new b(accessibilityNodeInfo);
    }

    public boolean A() {
        return this.f405a.isShowingHintText();
    }

    public boolean B() {
        return this.f405a.isVisibleToUser();
    }

    public boolean F(int i, Bundle bundle) {
        return this.f405a.performAction(i, bundle);
    }

    public void G() {
        this.f405a.recycle();
    }

    public boolean H(a aVar) {
        return this.f405a.removeAction((AccessibilityNodeInfo.AccessibilityAction) aVar.f408a);
    }

    public void I(boolean z) {
        this.f405a.setAccessibilityFocused(z);
    }

    @Deprecated
    public void J(Rect rect) {
        this.f405a.setBoundsInParent(rect);
    }

    public void K(Rect rect) {
        this.f405a.setBoundsInScreen(rect);
    }

    public void L(boolean z) {
        this.f405a.setCanOpenPopup(z);
    }

    public void M(boolean z) {
        this.f405a.setCheckable(z);
    }

    public void N(boolean z) {
        this.f405a.setChecked(z);
    }

    public void O(CharSequence charSequence) {
        this.f405a.setClassName(charSequence);
    }

    public void P(boolean z) {
        this.f405a.setClickable(z);
    }

    public void Q(Object obj) {
        this.f405a.setCollectionInfo(obj == null ? null : (AccessibilityNodeInfo.CollectionInfo) ((C0011b) obj).f412a);
    }

    public void R(Object obj) {
        this.f405a.setCollectionItemInfo((AccessibilityNodeInfo.CollectionItemInfo) ((c) obj).f413a);
    }

    public void S(CharSequence charSequence) {
        this.f405a.setContentDescription(charSequence);
    }

    public void T(boolean z) {
        this.f405a.setEnabled(z);
    }

    public void U(CharSequence charSequence) {
        this.f405a.setError(charSequence);
    }

    public void V(boolean z) {
        this.f405a.setFocusable(z);
    }

    public void W(boolean z) {
        this.f405a.setFocused(z);
    }

    public void X(boolean z) {
        this.f405a.setHeading(z);
    }

    public void Y(CharSequence charSequence) {
        this.f405a.setHintText(charSequence);
    }

    public void Z(boolean z) {
        this.f405a.setLongClickable(z);
    }

    public void a(int i) {
        this.f405a.addAction(i);
    }

    public void a0(int i) {
        this.f405a.setMaxTextLength(i);
    }

    public void b(a aVar) {
        this.f405a.addAction((AccessibilityNodeInfo.AccessibilityAction) aVar.f408a);
    }

    public void b0(CharSequence charSequence) {
        this.f405a.setPackageName(charSequence);
    }

    public void c(View view) {
        this.f405a.addChild(view);
    }

    public void c0(CharSequence charSequence) {
        this.f405a.setPaneTitle(charSequence);
    }

    public void d(View view, int i) {
        this.f405a.addChild(view, i);
    }

    public void d0(View view) {
        this.f406b = -1;
        this.f405a.setParent(view);
    }

    public void e0(View view, int i) {
        this.f406b = i;
        this.f405a.setParent(view, i);
    }

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || !(obj instanceof b)) {
            return false;
        }
        b bVar = (b) obj;
        AccessibilityNodeInfo accessibilityNodeInfo = this.f405a;
        if (accessibilityNodeInfo == null) {
            if (bVar.f405a != null) {
                return false;
            }
        } else if (!accessibilityNodeInfo.equals(bVar.f405a)) {
            return false;
        }
        return this.f407c == bVar.f407c && this.f406b == bVar.f406b;
    }

    public void f0(d dVar) {
        this.f405a.setRangeInfo((AccessibilityNodeInfo.RangeInfo) dVar.f414a);
    }

    public int g() {
        return this.f405a.getActions();
    }

    public void g0(CharSequence charSequence) {
        this.f405a.getExtras().putCharSequence("AccessibilityNodeInfo.roleDescription", charSequence);
    }

    @Deprecated
    public void h(Rect rect) {
        this.f405a.getBoundsInParent(rect);
    }

    public void h0(boolean z) {
        this.f405a.setScreenReaderFocusable(z);
    }

    public int hashCode() {
        AccessibilityNodeInfo accessibilityNodeInfo = this.f405a;
        if (accessibilityNodeInfo == null) {
            return 0;
        }
        return accessibilityNodeInfo.hashCode();
    }

    public void i(Rect rect) {
        this.f405a.getBoundsInScreen(rect);
    }

    public void i0(boolean z) {
        this.f405a.setScrollable(z);
    }

    public int j() {
        return this.f405a.getChildCount();
    }

    public void j0(boolean z) {
        this.f405a.setSelected(z);
    }

    public CharSequence k() {
        return this.f405a.getClassName();
    }

    public void k0(boolean z) {
        this.f405a.setShowingHintText(z);
    }

    public void l0(View view) {
        this.f407c = -1;
        this.f405a.setSource(view);
    }

    public CharSequence m() {
        return this.f405a.getContentDescription();
    }

    public void m0(View view, int i) {
        this.f407c = i;
        this.f405a.setSource(view, i);
    }

    public Bundle n() {
        return this.f405a.getExtras();
    }

    public void n0(CharSequence charSequence) {
        this.f405a.setStateDescription(charSequence);
    }

    public CharSequence o() {
        return this.f405a.getPackageName();
    }

    public void o0(CharSequence charSequence) {
        this.f405a.setText(charSequence);
    }

    public CharSequence p() {
        if (!(!e("androidx.view.accessibility.AccessibilityNodeInfoCompat.SPANS_START_KEY").isEmpty())) {
            return this.f405a.getText();
        }
        List<Integer> e = e("androidx.view.accessibility.AccessibilityNodeInfoCompat.SPANS_START_KEY");
        List<Integer> e2 = e("androidx.view.accessibility.AccessibilityNodeInfoCompat.SPANS_END_KEY");
        List<Integer> e3 = e("androidx.view.accessibility.AccessibilityNodeInfoCompat.SPANS_FLAGS_KEY");
        List<Integer> e4 = e("androidx.view.accessibility.AccessibilityNodeInfoCompat.SPANS_ID_KEY");
        SpannableString spannableString = new SpannableString(TextUtils.substring(this.f405a.getText(), 0, this.f405a.getText().length()));
        for (int i = 0; i < e.size(); i++) {
            spannableString.setSpan(new a.h.h.x.a(e4.get(i).intValue(), this, n().getInt("androidx.view.accessibility.AccessibilityNodeInfoCompat.SPANS_ACTION_ID_KEY")), e.get(i).intValue(), e2.get(i).intValue(), e3.get(i).intValue());
        }
        return spannableString;
    }

    public void p0(View view) {
        this.f405a.setTraversalAfter(view);
    }

    public boolean q() {
        return this.f405a.isAccessibilityFocused();
    }

    public void q0(boolean z) {
        this.f405a.setVisibleToUser(z);
    }

    public boolean r() {
        return this.f405a.isChecked();
    }

    public AccessibilityNodeInfo r0() {
        return this.f405a;
    }

    public boolean s() {
        return this.f405a.isClickable();
    }

    public boolean t() {
        return this.f405a.isEnabled();
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r2v10, types: [java.util.ArrayList] */
    /* JADX WARN: Type inference failed for: r2v8, types: [java.util.List] */
    /* JADX WARN: Type inference failed for: r2v9, types: [java.util.List] */
    public String toString() {
        ?? emptyList;
        StringBuilder sb = new StringBuilder();
        sb.append(super.toString());
        Rect rect = new Rect();
        this.f405a.getBoundsInParent(rect);
        sb.append("; boundsInParent: " + rect);
        this.f405a.getBoundsInScreen(rect);
        sb.append("; boundsInScreen: " + rect);
        sb.append("; packageName: ");
        sb.append(this.f405a.getPackageName());
        sb.append("; className: ");
        sb.append(k());
        sb.append("; text: ");
        sb.append(p());
        sb.append("; contentDescription: ");
        sb.append(m());
        sb.append("; viewId: ");
        sb.append(this.f405a.getViewIdResourceName());
        sb.append("; checkable: ");
        sb.append(this.f405a.isCheckable());
        sb.append("; checked: ");
        sb.append(this.f405a.isChecked());
        sb.append("; focusable: ");
        sb.append(u());
        sb.append("; focused: ");
        sb.append(this.f405a.isFocused());
        sb.append("; selected: ");
        sb.append(this.f405a.isSelected());
        sb.append("; clickable: ");
        sb.append(this.f405a.isClickable());
        sb.append("; longClickable: ");
        sb.append(this.f405a.isLongClickable());
        sb.append("; enabled: ");
        sb.append(t());
        sb.append("; password: ");
        sb.append(this.f405a.isPassword());
        sb.append("; scrollable: " + this.f405a.isScrollable());
        sb.append("; [");
        List<AccessibilityNodeInfo.AccessibilityAction> actionList = this.f405a.getActionList();
        if (actionList != null) {
            emptyList = new ArrayList();
            int size = actionList.size();
            for (int i = 0; i < size; i++) {
                emptyList.add(new a(actionList.get(i), 0, null, null, null));
            }
        } else {
            emptyList = Collections.emptyList();
        }
        for (int i2 = 0; i2 < emptyList.size(); i2++) {
            a aVar = (a) emptyList.get(i2);
            String f = f(aVar.b());
            if (f.equals("ACTION_UNKNOWN") && ((AccessibilityNodeInfo.AccessibilityAction) aVar.f408a).getLabel() != null) {
                f = ((AccessibilityNodeInfo.AccessibilityAction) aVar.f408a).getLabel().toString();
            }
            sb.append(f);
            if (i2 != emptyList.size() - 1) {
                sb.append(", ");
            }
        }
        sb.append("]");
        return sb.toString();
    }

    public boolean u() {
        return this.f405a.isFocusable();
    }

    public boolean v() {
        return this.f405a.isFocused();
    }

    public boolean w() {
        return this.f405a.isLongClickable();
    }

    public boolean x() {
        return this.f405a.isPassword();
    }

    public boolean y() {
        return this.f405a.isScrollable();
    }

    public boolean z() {
        return this.f405a.isSelected();
    }
}
