package a.j.a;

import android.os.Parcel;
import android.os.Parcelable;

/* loaded from: classes.dex */
public abstract class a implements Parcelable {

    /* renamed from: b, reason: collision with root package name */
    public static final a f424b = new C0014a();

    /* renamed from: a, reason: collision with root package name */
    private final Parcelable f425a;

    /* renamed from: a.j.a.a$a, reason: collision with other inner class name */
    static class C0014a extends a {
        C0014a() {
            super((C0014a) null);
        }
    }

    a(C0014a c0014a) {
        this.f425a = null;
    }

    protected a(Parcelable parcelable) {
        if (parcelable == null) {
            throw new IllegalArgumentException("superState must not be null");
        }
        this.f425a = parcelable == f424b ? null : parcelable;
    }

    @Override // android.os.Parcelable
    public int describeContents() {
        return 0;
    }

    public final Parcelable f() {
        return this.f425a;
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeParcelable(this.f425a, i);
    }
}
