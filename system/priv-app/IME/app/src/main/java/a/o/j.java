package a.o;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;

/* loaded from: classes.dex */
class j extends AnimatorListenerAdapter {

    /* renamed from: a, reason: collision with root package name */
    final /* synthetic */ a.e.a f500a;

    /* renamed from: b, reason: collision with root package name */
    final /* synthetic */ i f501b;

    j(i iVar, a.e.a aVar) {
        this.f501b = iVar;
        this.f500a = aVar;
    }

    @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
    public void onAnimationEnd(Animator animator) {
        this.f500a.remove(animator);
        this.f501b.m.remove(animator);
    }

    @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
    public void onAnimationStart(Animator animator) {
        this.f501b.m.add(animator);
    }
}
