package a.i.a;

import a.i.a.b;
import android.content.Context;
import android.database.ContentObserver;
import android.database.Cursor;
import android.database.DataSetObserver;
import android.os.Handler;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.Filter;
import android.widget.Filterable;

/* loaded from: classes.dex */
public abstract class a extends BaseAdapter implements Filterable, b.a {

    /* renamed from: a, reason: collision with root package name */
    protected boolean f417a;

    /* renamed from: b, reason: collision with root package name */
    protected boolean f418b;

    /* renamed from: c, reason: collision with root package name */
    protected Cursor f419c;

    /* renamed from: d, reason: collision with root package name */
    protected Context f420d;
    protected int e;
    protected C0013a f;
    protected DataSetObserver g;
    protected a.i.a.b h;

    /* renamed from: a.i.a.a$a, reason: collision with other inner class name */
    private class C0013a extends ContentObserver {
        C0013a() {
            super(new Handler());
        }

        @Override // android.database.ContentObserver
        public boolean deliverSelfNotifications() {
            return true;
        }

        @Override // android.database.ContentObserver
        public void onChange(boolean z) {
            Cursor cursor;
            a aVar = a.this;
            if (!aVar.f418b || (cursor = aVar.f419c) == null || cursor.isClosed()) {
                return;
            }
            aVar.f417a = aVar.f419c.requery();
        }
    }

    private class b extends DataSetObserver {
        b() {
        }

        @Override // android.database.DataSetObserver
        public void onChanged() {
            a aVar = a.this;
            aVar.f417a = true;
            aVar.notifyDataSetChanged();
        }

        @Override // android.database.DataSetObserver
        public void onInvalidated() {
            a aVar = a.this;
            aVar.f417a = false;
            aVar.notifyDataSetInvalidated();
        }
    }

    public a(Context context, Cursor cursor, boolean z) {
        b bVar;
        int i = z ? 1 : 2;
        if ((i & 1) == 1) {
            i |= 2;
            this.f418b = true;
        } else {
            this.f418b = false;
        }
        boolean z2 = cursor != null;
        this.f419c = cursor;
        this.f417a = z2;
        this.f420d = context;
        this.e = z2 ? cursor.getColumnIndexOrThrow("_id") : -1;
        if ((i & 2) == 2) {
            this.f = new C0013a();
            bVar = new b();
        } else {
            bVar = null;
            this.f = null;
        }
        this.g = bVar;
        if (z2) {
            C0013a c0013a = this.f;
            if (c0013a != null) {
                cursor.registerContentObserver(c0013a);
            }
            DataSetObserver dataSetObserver = this.g;
            if (dataSetObserver != null) {
                cursor.registerDataSetObserver(dataSetObserver);
            }
        }
    }

    @Override // a.i.a.b.a
    public abstract CharSequence a(Cursor cursor);

    @Override // a.i.a.b.a
    public void b(Cursor cursor) {
        Cursor cursor2 = this.f419c;
        if (cursor == cursor2) {
            cursor2 = null;
        } else {
            if (cursor2 != null) {
                C0013a c0013a = this.f;
                if (c0013a != null) {
                    cursor2.unregisterContentObserver(c0013a);
                }
                DataSetObserver dataSetObserver = this.g;
                if (dataSetObserver != null) {
                    cursor2.unregisterDataSetObserver(dataSetObserver);
                }
            }
            this.f419c = cursor;
            if (cursor != null) {
                C0013a c0013a2 = this.f;
                if (c0013a2 != null) {
                    cursor.registerContentObserver(c0013a2);
                }
                DataSetObserver dataSetObserver2 = this.g;
                if (dataSetObserver2 != null) {
                    cursor.registerDataSetObserver(dataSetObserver2);
                }
                this.e = cursor.getColumnIndexOrThrow("_id");
                this.f417a = true;
                notifyDataSetChanged();
            } else {
                this.e = -1;
                this.f417a = false;
                notifyDataSetInvalidated();
            }
        }
        if (cursor2 != null) {
            cursor2.close();
        }
    }

    public abstract void d(View view, Context context, Cursor cursor);

    public Cursor e() {
        return this.f419c;
    }

    public abstract View f(Context context, Cursor cursor, ViewGroup viewGroup);

    public abstract View g(Context context, Cursor cursor, ViewGroup viewGroup);

    @Override // android.widget.Adapter
    public int getCount() {
        Cursor cursor;
        if (!this.f417a || (cursor = this.f419c) == null) {
            return 0;
        }
        return cursor.getCount();
    }

    @Override // android.widget.BaseAdapter, android.widget.SpinnerAdapter
    public View getDropDownView(int i, View view, ViewGroup viewGroup) {
        if (!this.f417a) {
            return null;
        }
        this.f419c.moveToPosition(i);
        if (view == null) {
            view = f(this.f420d, this.f419c, viewGroup);
        }
        d(view, this.f420d, this.f419c);
        return view;
    }

    @Override // android.widget.Filterable
    public Filter getFilter() {
        if (this.h == null) {
            this.h = new a.i.a.b(this);
        }
        return this.h;
    }

    @Override // android.widget.Adapter
    public Object getItem(int i) {
        Cursor cursor;
        if (!this.f417a || (cursor = this.f419c) == null) {
            return null;
        }
        cursor.moveToPosition(i);
        return this.f419c;
    }

    @Override // android.widget.Adapter
    public long getItemId(int i) {
        Cursor cursor;
        if (this.f417a && (cursor = this.f419c) != null && cursor.moveToPosition(i)) {
            return this.f419c.getLong(this.e);
        }
        return 0L;
    }

    @Override // android.widget.Adapter
    public View getView(int i, View view, ViewGroup viewGroup) {
        if (!this.f417a) {
            throw new IllegalStateException("this should only be called when the cursor is valid");
        }
        if (!this.f419c.moveToPosition(i)) {
            throw new IllegalStateException(b.b.a.a.a.e("couldn't move cursor to position ", i));
        }
        if (view == null) {
            view = g(this.f420d, this.f419c, viewGroup);
        }
        d(view, this.f420d, this.f419c);
        return view;
    }
}
