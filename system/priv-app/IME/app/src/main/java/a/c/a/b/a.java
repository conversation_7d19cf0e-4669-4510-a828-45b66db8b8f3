package a.c.a.b;

import a.c.a.b.b;
import java.util.HashMap;
import java.util.Map;

/* loaded from: classes.dex */
public class a<K, V> extends b<K, V> {
    private HashMap<K, b.c<K, V>> e = new HashMap<>();

    @Override // a.c.a.b.b
    protected b.c<K, V> c(K k) {
        return this.e.get(k);
    }

    public boolean contains(K k) {
        return this.e.containsKey(k);
    }

    @Override // a.c.a.b.b
    public V g(K k) {
        V v = (V) super.g(k);
        this.e.remove(k);
        return v;
    }

    public Map.Entry<K, V> h(K k) {
        if (this.e.containsKey(k)) {
            return this.e.get(k).f57d;
        }
        return null;
    }

    public V i(K k, V v) {
        b.c<K, V> cVar = this.e.get(k);
        if (cVar != null) {
            return cVar.f55b;
        }
        this.e.put(k, f(k, v));
        return null;
    }
}
