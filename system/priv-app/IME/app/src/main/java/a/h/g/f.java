package a.h.g;

/* loaded from: classes.dex */
public class f<T> extends e<T> {

    /* renamed from: c, reason: collision with root package name */
    private final Object f357c;

    public f(int i) {
        super(i);
        this.f357c = new Object();
    }

    @Override // a.h.g.e, a.h.g.d
    public boolean a(T t) {
        boolean a2;
        synchronized (this.f357c) {
            a2 = super.a(t);
        }
        return a2;
    }

    @Override // a.h.g.e, a.h.g.d
    public T b() {
        T t;
        synchronized (this.f357c) {
            t = (T) super.b();
        }
        return t;
    }
}
