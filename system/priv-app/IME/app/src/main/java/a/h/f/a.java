package a.h.f;

import a.h.f.d;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import com.google.android.material.button.MaterialButton;
import java.util.Locale;

/* loaded from: classes.dex */
public final class a {

    /* renamed from: d, reason: collision with root package name */
    static final c f329d;
    private static final String e;
    private static final String f;
    static final a g;
    static final a h;
    public static final /* synthetic */ int i = 0;

    /* renamed from: a, reason: collision with root package name */
    private final boolean f330a;

    /* renamed from: b, reason: collision with root package name */
    private final int f331b;

    /* renamed from: c, reason: collision with root package name */
    private final c f332c;

    /* renamed from: a.h.f.a$a, reason: collision with other inner class name */
    public static final class C0007a {

        /* renamed from: a, reason: collision with root package name */
        private boolean f333a;

        /* renamed from: b, reason: collision with root package name */
        private int f334b;

        /* renamed from: c, reason: collision with root package name */
        private c f335c;

        public C0007a() {
            Locale locale = Locale.getDefault();
            int i = e.f352b;
            this.f333a = TextUtils.getLayoutDirectionFromLocale(locale) == 1;
            this.f335c = a.f329d;
            this.f334b = 2;
        }

        public a a() {
            return (this.f334b == 2 && this.f335c == a.f329d) ? this.f333a ? a.h : a.g : new a(this.f333a, this.f334b, this.f335c);
        }
    }

    private static class b {
        private static final byte[] e = new byte[1792];

        /* renamed from: a, reason: collision with root package name */
        private final CharSequence f336a;

        /* renamed from: b, reason: collision with root package name */
        private final int f337b;

        /* renamed from: c, reason: collision with root package name */
        private int f338c;

        /* renamed from: d, reason: collision with root package name */
        private char f339d;

        static {
            for (int i = 0; i < 1792; i++) {
                e[i] = Character.getDirectionality(i);
            }
        }

        b(CharSequence charSequence, boolean z) {
            this.f336a = charSequence;
            this.f337b = charSequence.length();
        }

        byte a() {
            char charAt = this.f336a.charAt(this.f338c - 1);
            this.f339d = charAt;
            if (Character.isLowSurrogate(charAt)) {
                int codePointBefore = Character.codePointBefore(this.f336a, this.f338c);
                this.f338c -= Character.charCount(codePointBefore);
                return Character.getDirectionality(codePointBefore);
            }
            this.f338c--;
            char c2 = this.f339d;
            return c2 < 1792 ? e[c2] : Character.getDirectionality(c2);
        }

        /* JADX WARN: Code restructure failed: missing block: B:45:0x006b, code lost:
        
            if (r3 != 0) goto L31;
         */
        /* JADX WARN: Code restructure failed: missing block: B:46:0x006d, code lost:
        
            return 0;
         */
        /* JADX WARN: Code restructure failed: missing block: B:47:0x006e, code lost:
        
            if (r4 == 0) goto L33;
         */
        /* JADX WARN: Code restructure failed: missing block: B:48:0x0070, code lost:
        
            return r4;
         */
        /* JADX WARN: Code restructure failed: missing block: B:50:0x0073, code lost:
        
            if (r9.f338c <= 0) goto L63;
         */
        /* JADX WARN: Code restructure failed: missing block: B:52:0x0079, code lost:
        
            switch(a()) {
                case 14: goto L66;
                case 15: goto L66;
                case 16: goto L65;
                case 17: goto L65;
                case 18: goto L64;
                default: goto L70;
            };
         */
        /* JADX WARN: Code restructure failed: missing block: B:54:0x007d, code lost:
        
            r5 = r5 + 1;
         */
        /* JADX WARN: Code restructure failed: missing block: B:58:0x0080, code lost:
        
            if (r3 != r5) goto L43;
         */
        /* JADX WARN: Code restructure failed: missing block: B:59:0x0086, code lost:
        
            r5 = r5 - 1;
         */
        /* JADX WARN: Code restructure failed: missing block: B:62:0x0082, code lost:
        
            return 1;
         */
        /* JADX WARN: Code restructure failed: missing block: B:64:0x0083, code lost:
        
            if (r3 != r5) goto L43;
         */
        /* JADX WARN: Code restructure failed: missing block: B:66:0x0085, code lost:
        
            return -1;
         */
        /* JADX WARN: Code restructure failed: missing block: B:69:0x0089, code lost:
        
            return 0;
         */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        int b() {
            /*
                r9 = this;
                r0 = 0
                r9.f338c = r0
                r1 = -1
                r2 = 1
                r3 = r0
                r4 = r3
                r5 = r4
            L8:
                int r6 = r9.f338c
                int r7 = r9.f337b
                if (r6 >= r7) goto L6b
                if (r3 != 0) goto L6b
                java.lang.CharSequence r7 = r9.f336a
                char r6 = r7.charAt(r6)
                r9.f339d = r6
                boolean r6 = java.lang.Character.isHighSurrogate(r6)
                if (r6 == 0) goto L34
                java.lang.CharSequence r6 = r9.f336a
                int r7 = r9.f338c
                int r6 = java.lang.Character.codePointAt(r6, r7)
                int r7 = r9.f338c
                int r8 = java.lang.Character.charCount(r6)
                int r8 = r8 + r7
                r9.f338c = r8
                byte r6 = java.lang.Character.getDirectionality(r6)
                goto L48
            L34:
                int r6 = r9.f338c
                int r6 = r6 + r2
                r9.f338c = r6
                char r6 = r9.f339d
                r7 = 1792(0x700, float:2.511E-42)
                if (r6 >= r7) goto L44
                byte[] r7 = a.h.f.a.b.e
                r6 = r7[r6]
                goto L48
            L44:
                byte r6 = java.lang.Character.getDirectionality(r6)
            L48:
                if (r6 == 0) goto L66
                if (r6 == r2) goto L63
                r7 = 2
                if (r6 == r7) goto L63
                r7 = 9
                if (r6 == r7) goto L8
                switch(r6) {
                    case 14: goto L5f;
                    case 15: goto L5f;
                    case 16: goto L5b;
                    case 17: goto L5b;
                    case 18: goto L57;
                    default: goto L56;
                }
            L56:
                goto L69
            L57:
                int r5 = r5 + (-1)
                r4 = r0
                goto L8
            L5b:
                int r5 = r5 + 1
                r4 = r2
                goto L8
            L5f:
                int r5 = r5 + 1
                r4 = r1
                goto L8
            L63:
                if (r5 != 0) goto L69
                return r2
            L66:
                if (r5 != 0) goto L69
                return r1
            L69:
                r3 = r5
                goto L8
            L6b:
                if (r3 != 0) goto L6e
                return r0
            L6e:
                if (r4 == 0) goto L71
                return r4
            L71:
                int r4 = r9.f338c
                if (r4 <= 0) goto L89
                byte r4 = r9.a()
                switch(r4) {
                    case 14: goto L83;
                    case 15: goto L83;
                    case 16: goto L80;
                    case 17: goto L80;
                    case 18: goto L7d;
                    default: goto L7c;
                }
            L7c:
                goto L71
            L7d:
                int r5 = r5 + 1
                goto L71
            L80:
                if (r3 != r5) goto L86
                return r2
            L83:
                if (r3 != r5) goto L86
                return r1
            L86:
                int r5 = r5 + (-1)
                goto L71
            L89:
                return r0
            */
            throw new UnsupportedOperationException("Method not decompiled: a.h.f.a.b.b():int");
        }

        int c() {
            this.f338c = this.f337b;
            int i = 0;
            while (true) {
                int i2 = i;
                while (this.f338c > 0) {
                    byte a2 = a();
                    if (a2 != 0) {
                        if (a2 == 1 || a2 == 2) {
                            if (i == 0) {
                                return 1;
                            }
                            if (i2 == 0) {
                                break;
                            }
                        } else if (a2 != 9) {
                            switch (a2) {
                                case 14:
                                case 15:
                                    if (i2 == i) {
                                        return -1;
                                    }
                                    i--;
                                    break;
                                case MaterialButton.ICON_GRAVITY_TOP /* 16 */:
                                case 17:
                                    if (i2 == i) {
                                        return 1;
                                    }
                                    i--;
                                    break;
                                case 18:
                                    i++;
                                    break;
                                default:
                                    if (i2 != 0) {
                                        break;
                                    } else {
                                        break;
                                    }
                            }
                        } else {
                            continue;
                        }
                    } else {
                        if (i == 0) {
                            return -1;
                        }
                        if (i2 == 0) {
                            break;
                        }
                    }
                }
                return 0;
            }
        }
    }

    static {
        c cVar = d.f346c;
        f329d = cVar;
        e = Character.toString((char) 8206);
        f = Character.toString((char) 8207);
        g = new a(false, 2, cVar);
        h = new a(true, 2, cVar);
    }

    a(boolean z, int i2, c cVar) {
        this.f330a = z;
        this.f331b = i2;
        this.f332c = cVar;
    }

    public CharSequence a(CharSequence charSequence) {
        return b(charSequence, this.f332c, true);
    }

    public CharSequence b(CharSequence charSequence, c cVar, boolean z) {
        if (charSequence == null) {
            return null;
        }
        boolean a2 = ((d.c) cVar).a(charSequence, 0, charSequence.length());
        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder();
        String str = "";
        if (((this.f331b & 2) != 0) && z) {
            boolean a3 = ((d.c) (a2 ? d.f345b : d.f344a)).a(charSequence, 0, charSequence.length());
            spannableStringBuilder.append((CharSequence) ((this.f330a || !(a3 || new b(charSequence, false).b() == 1)) ? (!this.f330a || (a3 && new b(charSequence, false).b() != -1)) ? "" : f : e));
        }
        if (a2 != this.f330a) {
            spannableStringBuilder.append(a2 ? (char) 8235 : (char) 8234);
            spannableStringBuilder.append(charSequence);
            spannableStringBuilder.append((char) 8236);
        } else {
            spannableStringBuilder.append(charSequence);
        }
        if (z) {
            boolean a4 = ((d.c) (a2 ? d.f345b : d.f344a)).a(charSequence, 0, charSequence.length());
            if (!this.f330a && (a4 || new b(charSequence, false).c() == 1)) {
                str = e;
            } else if (this.f330a && (!a4 || new b(charSequence, false).c() == -1)) {
                str = f;
            }
            spannableStringBuilder.append((CharSequence) str);
        }
        return spannableStringBuilder;
    }

    public String c(String str) {
        c cVar = this.f332c;
        if (str == null) {
            return null;
        }
        return b(str, cVar, true).toString();
    }
}
