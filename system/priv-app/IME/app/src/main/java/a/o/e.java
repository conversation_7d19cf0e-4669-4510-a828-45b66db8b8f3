package a.o;

import a.o.i;
import android.annotation.SuppressLint;
import android.graphics.Rect;
import android.view.View;
import android.view.ViewGroup;
import java.util.ArrayList;

@SuppressLint({"RestrictedApi"})
/* loaded from: classes.dex */
public class e extends androidx.fragment.app.A {

    class a extends i.c {
        a(e eVar, Rect rect) {
        }
    }

    class b implements i.d {

        /* renamed from: a, reason: collision with root package name */
        final /* synthetic */ View f486a;

        /* renamed from: b, reason: collision with root package name */
        final /* synthetic */ ArrayList f487b;

        b(e eVar, View view, ArrayList arrayList) {
            this.f486a = view;
            this.f487b = arrayList;
        }

        @Override // a.o.i.d
        public void a(i iVar) {
        }

        @Override // a.o.i.d
        public void b(i iVar) {
        }

        @Override // a.o.i.d
        public void c(i iVar) {
        }

        @Override // a.o.i.d
        public void d(i iVar) {
        }

        @Override // a.o.i.d
        public void e(i iVar) {
            iVar.B(this);
            this.f486a.setVisibility(8);
            int size = this.f487b.size();
            for (int i = 0; i < size; i++) {
                ((View) this.f487b.get(i)).setVisibility(0);
            }
        }
    }

    class c extends l {

        /* renamed from: a, reason: collision with root package name */
        final /* synthetic */ Object f488a;

        /* renamed from: b, reason: collision with root package name */
        final /* synthetic */ ArrayList f489b;

        /* renamed from: c, reason: collision with root package name */
        final /* synthetic */ Object f490c;

        /* renamed from: d, reason: collision with root package name */
        final /* synthetic */ ArrayList f491d;
        final /* synthetic */ Object e;
        final /* synthetic */ ArrayList f;

        c(Object obj, ArrayList arrayList, Object obj2, ArrayList arrayList2, Object obj3, ArrayList arrayList3) {
            this.f488a = obj;
            this.f489b = arrayList;
            this.f490c = obj2;
            this.f491d = arrayList2;
            this.e = obj3;
            this.f = arrayList3;
        }

        @Override // a.o.l, a.o.i.d
        public void c(i iVar) {
            Object obj = this.f488a;
            if (obj != null) {
                e.this.n(obj, this.f489b, null);
            }
            Object obj2 = this.f490c;
            if (obj2 != null) {
                e.this.n(obj2, this.f491d, null);
            }
            Object obj3 = this.e;
            if (obj3 != null) {
                e.this.n(obj3, this.f, null);
            }
        }

        @Override // a.o.i.d
        public void e(i iVar) {
            iVar.B(this);
        }
    }

    class d extends i.c {
        d(e eVar, Rect rect) {
        }
    }

    private static boolean v(i iVar) {
        return (androidx.fragment.app.A.k(iVar.e) && androidx.fragment.app.A.k(null) && androidx.fragment.app.A.k(null)) ? false : true;
    }

    @Override // androidx.fragment.app.A
    public void a(Object obj, View view) {
        if (obj != null) {
            ((i) obj).b(view);
        }
    }

    @Override // androidx.fragment.app.A
    public void b(Object obj, ArrayList<View> arrayList) {
        i iVar = (i) obj;
        if (iVar == null) {
            return;
        }
        int i = 0;
        if (iVar instanceof o) {
            o oVar = (o) iVar;
            int P = oVar.P();
            while (i < P) {
                b(oVar.O(i), arrayList);
                i++;
            }
            return;
        }
        if (v(iVar) || !androidx.fragment.app.A.k(iVar.f)) {
            return;
        }
        int size = arrayList.size();
        while (i < size) {
            iVar.b(arrayList.get(i));
            i++;
        }
    }

    @Override // androidx.fragment.app.A
    public void c(ViewGroup viewGroup, Object obj) {
        m.a(viewGroup, (i) obj);
    }

    @Override // androidx.fragment.app.A
    public boolean e(Object obj) {
        return obj instanceof i;
    }

    @Override // androidx.fragment.app.A
    public Object g(Object obj) {
        if (obj != null) {
            return ((i) obj).clone();
        }
        return null;
    }

    @Override // androidx.fragment.app.A
    public Object l(Object obj, Object obj2, Object obj3) {
        o oVar = new o();
        if (obj != null) {
            oVar.N((i) obj);
        }
        if (obj2 != null) {
            oVar.N((i) obj2);
        }
        if (obj3 != null) {
            oVar.N((i) obj3);
        }
        return oVar;
    }

    @Override // androidx.fragment.app.A
    public void m(Object obj, View view) {
        if (obj != null) {
            ((i) obj).C(view);
        }
    }

    @Override // androidx.fragment.app.A
    public void n(Object obj, ArrayList<View> arrayList, ArrayList<View> arrayList2) {
        i iVar = (i) obj;
        int i = 0;
        if (iVar instanceof o) {
            o oVar = (o) iVar;
            int P = oVar.P();
            while (i < P) {
                n(oVar.O(i), arrayList, arrayList2);
                i++;
            }
            return;
        }
        if (v(iVar)) {
            return;
        }
        ArrayList<View> arrayList3 = iVar.f;
        if (arrayList3.size() == arrayList.size() && arrayList3.containsAll(arrayList)) {
            int size = arrayList2 == null ? 0 : arrayList2.size();
            while (i < size) {
                iVar.b(arrayList2.get(i));
                i++;
            }
            for (int size2 = arrayList.size() - 1; size2 >= 0; size2--) {
                iVar.C(arrayList.get(size2));
            }
        }
    }

    @Override // androidx.fragment.app.A
    public void o(Object obj, View view, ArrayList<View> arrayList) {
        ((i) obj).a(new b(this, view, arrayList));
    }

    @Override // androidx.fragment.app.A
    public void p(Object obj, Object obj2, ArrayList<View> arrayList, Object obj3, ArrayList<View> arrayList2, Object obj4, ArrayList<View> arrayList3) {
        ((i) obj).a(new c(obj2, arrayList, obj3, arrayList2, obj4, arrayList3));
    }

    @Override // androidx.fragment.app.A
    public void q(Object obj, Rect rect) {
        if (obj != null) {
            ((i) obj).G(new d(this, rect));
        }
    }

    @Override // androidx.fragment.app.A
    public void r(Object obj, View view) {
        if (view != null) {
            Rect rect = new Rect();
            j(view, rect);
            ((i) obj).G(new a(this, rect));
        }
    }

    @Override // androidx.fragment.app.A
    public void s(Object obj, View view, ArrayList<View> arrayList) {
        o oVar = (o) obj;
        ArrayList<View> arrayList2 = oVar.f;
        arrayList2.clear();
        int size = arrayList.size();
        for (int i = 0; i < size; i++) {
            androidx.fragment.app.A.d(arrayList2, arrayList.get(i));
        }
        arrayList2.add(view);
        arrayList.add(view);
        b(oVar, arrayList);
    }

    @Override // androidx.fragment.app.A
    public void t(Object obj, ArrayList<View> arrayList, ArrayList<View> arrayList2) {
        o oVar = (o) obj;
        if (oVar != null) {
            oVar.f.clear();
            oVar.f.addAll(arrayList2);
            n(oVar, arrayList, arrayList2);
        }
    }

    @Override // androidx.fragment.app.A
    public Object u(Object obj) {
        if (obj == null) {
            return null;
        }
        o oVar = new o();
        oVar.N((i) obj);
        return oVar;
    }
}
