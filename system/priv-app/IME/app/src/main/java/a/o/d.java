package a.o;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.util.Property;
import android.view.View;
import android.view.ViewGroup;
import java.util.Map;

/* loaded from: classes.dex */
public class d extends A {

    class a extends l {

        /* renamed from: a, reason: collision with root package name */
        final /* synthetic */ View f483a;

        a(d dVar, View view) {
            this.f483a = view;
        }

        @Override // a.o.i.d
        public void e(i iVar) {
            View view = this.f483a;
            Property<View, Float> property = s.f521b;
            view.setTransitionAlpha(1.0f);
            iVar.B(this);
        }
    }

    private static class b extends AnimatorListenerAdapter {

        /* renamed from: a, reason: collision with root package name */
        private final View f484a;

        /* renamed from: b, reason: collision with root package name */
        private boolean f485b = false;

        b(View view) {
            this.f484a = view;
        }

        @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
        public void onAnimationEnd(Animator animator) {
            View view = this.f484a;
            Property<View, Float> property = s.f521b;
            view.setTransitionAlpha(1.0f);
            if (this.f485b) {
                this.f484a.setLayerType(0, null);
            }
        }

        @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
        public void onAnimationStart(Animator animator) {
            View view = this.f484a;
            int i = a.h.h.q.e;
            if (view.hasOverlappingRendering() && this.f484a.getLayerType() == 0) {
                this.f485b = true;
                this.f484a.setLayerType(2, null);
            }
        }
    }

    public d(int i) {
        R(i);
    }

    private Animator S(View view, float f, float f2) {
        if (f == f2) {
            return null;
        }
        Property<View, Float> property = s.f521b;
        view.setTransitionAlpha(f);
        ObjectAnimator ofFloat = ObjectAnimator.ofFloat(view, s.f521b, f2);
        ofFloat.addListener(new b(view));
        a(new a(this, view));
        return ofFloat;
    }

    @Override // a.o.A
    public Animator P(ViewGroup viewGroup, View view, p pVar, p pVar2) {
        Float f;
        float floatValue = (pVar == null || (f = (Float) pVar.f512a.get("android:fade:transitionAlpha")) == null) ? 0.0f : f.floatValue();
        return S(view, floatValue != 1.0f ? floatValue : 0.0f, 1.0f);
    }

    @Override // a.o.A
    public Animator Q(ViewGroup viewGroup, View view, p pVar, p pVar2) {
        Property<View, Float> property = s.f521b;
        Float f = (Float) pVar.f512a.get("android:fade:transitionAlpha");
        return S(view, f != null ? f.floatValue() : 1.0f, 0.0f);
    }

    @Override // a.o.A, a.o.i
    public void h(p pVar) {
        super.h(pVar);
        Map<String, Object> map = pVar.f512a;
        View view = pVar.f513b;
        Property<View, Float> property = s.f521b;
        map.put("android:fade:transitionAlpha", Float.valueOf(view.getTransitionAlpha()));
    }
}
