package a.f.a.i.a;

import java.util.Arrays;
import java.util.HashMap;

/* loaded from: classes.dex */
public class d {

    /* renamed from: a, reason: collision with root package name */
    HashMap<Object, HashMap<String, float[]>> f143a = new HashMap<>();

    public float a(Object obj, String str, int i) {
        HashMap<String, float[]> hashMap;
        float[] fArr;
        if (this.f143a.containsKey(obj) && (hashMap = this.f143a.get(obj)) != null && hashMap.containsKey(str) && (fArr = hashMap.get(str)) != null && fArr.length > i) {
            return fArr[i];
        }
        return Float.NaN;
    }

    public void b(Object obj, String str, int i, float f) {
        HashMap<String, float[]> hashMap;
        if (this.f143a.containsKey(obj)) {
            hashMap = this.f143a.get(obj);
            if (hashMap == null) {
                hashMap = new HashMap<>();
            }
            if (hashMap.containsKey(str)) {
                float[] fArr = hashMap.get(str);
                if (fArr == null) {
                    fArr = new float[0];
                }
                if (fArr.length <= i) {
                    fArr = Arrays.copyOf(fArr, i + 1);
                }
                fArr[i] = f;
                hashMap.put(str, fArr);
                return;
            }
            float[] fArr2 = new float[i + 1];
            fArr2[i] = f;
            hashMap.put(str, fArr2);
        } else {
            hashMap = new HashMap<>();
            float[] fArr3 = new float[i + 1];
            fArr3[i] = f;
            hashMap.put(str, fArr3);
        }
        this.f143a.put(obj, hashMap);
    }
}
