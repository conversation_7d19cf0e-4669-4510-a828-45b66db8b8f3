package a.f.a.j;

import a.f.a.j.d;
import androidx.constraintlayout.motion.widget.MotionLayout;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Objects;

/* loaded from: classes.dex */
public class e {
    public boolean A;
    boolean A0;
    public boolean B;
    boolean B0;
    int C;
    int C0;
    float D;
    int D0;
    private int[] E;
    boolean E0;
    private float F;
    boolean F0;
    private boolean G;
    public float[] G0;
    private boolean H;
    protected e[] H0;
    private boolean I;
    protected e[] I0;
    private int J;
    e J0;
    private int K;
    e K0;
    public d L;
    public int L0;
    public d M;
    public int M0;
    public d N;
    public d O;
    public d P;
    d Q;
    d R;
    public d S;
    public d[] T;
    protected ArrayList<d> U;
    private boolean[] V;
    public a[] W;
    public e X;
    int Y;
    int Z;
    public float a0;

    /* renamed from: b, reason: collision with root package name */
    public a.f.a.j.o.c f202b;
    protected int b0;

    /* renamed from: c, reason: collision with root package name */
    public a.f.a.j.o.c f203c;
    protected int c0;
    protected int d0;
    int e0;
    int f0;
    protected int g0;
    protected int h0;
    int i0;
    public String j;
    protected int j0;
    private boolean k;
    protected int k0;
    private boolean l;
    float l0;
    private boolean m;
    float m0;
    private boolean n;
    private Object n0;
    public int o;
    private int o0;
    public int p;
    private int p0;
    private int q;
    private String q0;
    public int r;
    private String r0;
    public int s;
    int s0;
    public int[] t;
    int t0;
    public int u;
    int u0;
    public int v;
    int v0;
    public float w;
    boolean w0;
    public int x;
    boolean x0;
    public int y;
    boolean y0;
    public float z;
    boolean z0;

    /* renamed from: a, reason: collision with root package name */
    public boolean f201a = false;

    /* renamed from: d, reason: collision with root package name */
    public a.f.a.j.o.l f204d = null;
    public a.f.a.j.o.n e = null;
    public boolean[] f = {true, true};
    private boolean g = true;
    private int h = -1;
    private int i = -1;

    public enum a {
        FIXED,
        WRAP_CONTENT,
        MATCH_CONSTRAINT,
        MATCH_PARENT
    }

    public e() {
        new HashMap();
        this.k = false;
        this.l = false;
        this.m = false;
        this.n = false;
        this.o = -1;
        this.p = -1;
        this.q = 0;
        this.r = 0;
        this.s = 0;
        this.t = new int[2];
        this.u = 0;
        this.v = 0;
        this.w = 1.0f;
        this.x = 0;
        this.y = 0;
        this.z = 1.0f;
        this.C = -1;
        this.D = 1.0f;
        this.E = new int[]{Integer.MAX_VALUE, Integer.MAX_VALUE};
        this.F = 0.0f;
        this.G = false;
        this.I = false;
        this.J = 0;
        this.K = 0;
        d dVar = new d(this, d.a.LEFT);
        this.L = dVar;
        d dVar2 = new d(this, d.a.TOP);
        this.M = dVar2;
        d dVar3 = new d(this, d.a.RIGHT);
        this.N = dVar3;
        d dVar4 = new d(this, d.a.BOTTOM);
        this.O = dVar4;
        d dVar5 = new d(this, d.a.BASELINE);
        this.P = dVar5;
        this.Q = new d(this, d.a.CENTER_X);
        this.R = new d(this, d.a.CENTER_Y);
        d dVar6 = new d(this, d.a.CENTER);
        this.S = dVar6;
        this.T = new d[]{dVar, dVar3, dVar2, dVar4, dVar5, dVar6};
        ArrayList<d> arrayList = new ArrayList<>();
        this.U = arrayList;
        this.V = new boolean[2];
        a aVar = a.FIXED;
        this.W = new a[]{aVar, aVar};
        this.X = null;
        this.Y = 0;
        this.Z = 0;
        this.a0 = 0.0f;
        this.b0 = -1;
        this.c0 = 0;
        this.d0 = 0;
        this.e0 = 0;
        this.f0 = 0;
        this.g0 = 0;
        this.h0 = 0;
        this.i0 = 0;
        this.l0 = 0.5f;
        this.m0 = 0.5f;
        this.o0 = 0;
        this.p0 = 0;
        this.q0 = null;
        this.r0 = null;
        this.C0 = 0;
        this.D0 = 0;
        this.G0 = new float[]{-1.0f, -1.0f};
        this.H0 = new e[]{null, null};
        this.I0 = new e[]{null, null};
        this.J0 = null;
        this.K0 = null;
        this.L0 = -1;
        this.M0 = -1;
        arrayList.add(this.L);
        this.U.add(this.M);
        this.U.add(this.N);
        this.U.add(this.O);
        this.U.add(this.Q);
        this.U.add(this.R);
        this.U.add(this.S);
        this.U.add(this.P);
    }

    private void K(StringBuilder sb, String str, int i, int i2, int i3, int i4, int i5, float f) {
        sb.append(str);
        sb.append(" :  {\n");
        p0(sb, "      size", i, 0);
        p0(sb, "      min", i2, 0);
        p0(sb, "      max", i3, Integer.MAX_VALUE);
        p0(sb, "      matchMin", i4, 0);
        p0(sb, "      matchDef", i5, 0);
        o0(sb, "      matchPercent", f, 1.0f);
        sb.append("    },\n");
    }

    private void L(StringBuilder sb, String str, d dVar) {
        if (dVar.f == null) {
            return;
        }
        sb.append("    ");
        sb.append(str);
        sb.append(" : [ '");
        sb.append(dVar.f);
        sb.append("'");
        if (dVar.h != Integer.MIN_VALUE || dVar.g != 0) {
            sb.append(",");
            sb.append(dVar.g);
            if (dVar.h != Integer.MIN_VALUE) {
                sb.append(",");
                sb.append(dVar.h);
                sb.append(",");
            }
        }
        sb.append(" ] ,\n");
    }

    private boolean X(int i) {
        int i2 = i * 2;
        d[] dVarArr = this.T;
        if (dVarArr[i2].f != null && dVarArr[i2].f.f != dVarArr[i2]) {
            int i3 = i2 + 1;
            if (dVarArr[i3].f != null && dVarArr[i3].f.f == dVarArr[i3]) {
                return true;
            }
        }
        return false;
    }

    /* JADX WARN: Removed duplicated region for block: B:136:0x0381  */
    /* JADX WARN: Removed duplicated region for block: B:150:0x03f5  */
    /* JADX WARN: Removed duplicated region for block: B:168:0x043c  */
    /* JADX WARN: Removed duplicated region for block: B:198:0x0487  */
    /* JADX WARN: Removed duplicated region for block: B:215:0x041e  */
    /* JADX WARN: Removed duplicated region for block: B:217:0x03cb  */
    /* JADX WARN: Removed duplicated region for block: B:253:0x02c4  */
    /* JADX WARN: Removed duplicated region for block: B:255:0x02c8  */
    /* JADX WARN: Removed duplicated region for block: B:27:0x0087  */
    /* JADX WARN: Removed duplicated region for block: B:292:0x04d0  */
    /* JADX WARN: Removed duplicated region for block: B:294:0x00d2  */
    /* JADX WARN: Removed duplicated region for block: B:29:0x0091  */
    /* JADX WARN: Removed duplicated region for block: B:345:0x00b2  */
    /* JADX WARN: Removed duplicated region for block: B:346:0x008b  */
    /* JADX WARN: Removed duplicated region for block: B:35:0x00b7  */
    /* JADX WARN: Removed duplicated region for block: B:46:0x0196  */
    /* JADX WARN: Removed duplicated region for block: B:49:0x04e1 A[ADDED_TO_REGION] */
    /* JADX WARN: Removed duplicated region for block: B:75:? A[ADDED_TO_REGION, RETURN, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:85:0x04b8 A[ADDED_TO_REGION] */
    /* JADX WARN: Removed duplicated region for block: B:95:? A[ADDED_TO_REGION, RETURN, SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private void h(a.f.a.d r36, boolean r37, boolean r38, boolean r39, boolean r40, a.f.a.h r41, a.f.a.h r42, a.f.a.j.e.a r43, boolean r44, a.f.a.j.d r45, a.f.a.j.d r46, int r47, int r48, int r49, int r50, float r51, boolean r52, boolean r53, boolean r54, boolean r55, boolean r56, int r57, int r58, int r59, int r60, float r61, boolean r62) {
        /*
            Method dump skipped, instructions count: 1308
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: a.f.a.j.e.h(a.f.a.d, boolean, boolean, boolean, boolean, a.f.a.h, a.f.a.h, a.f.a.j.e$a, boolean, a.f.a.j.d, a.f.a.j.d, int, int, int, int, float, boolean, boolean, boolean, boolean, boolean, int, int, int, int, float, boolean):void");
    }

    private void o0(StringBuilder sb, String str, float f, float f2) {
        if (f == f2) {
            return;
        }
        sb.append(str);
        sb.append(" :   ");
        sb.append(f);
        sb.append(",\n");
    }

    private void p0(StringBuilder sb, String str, int i, int i2) {
        if (i == i2) {
            return;
        }
        sb.append(str);
        sb.append(" :   ");
        sb.append(i);
        sb.append(",\n");
    }

    public int A() {
        return this.J;
    }

    public void A0(int i) {
        this.Z = i;
        int i2 = this.k0;
        if (i < i2) {
            this.Z = i2;
        }
    }

    public int B() {
        return this.K;
    }

    public void B0(float f) {
        this.l0 = f;
    }

    public int C() {
        return this.E[1];
    }

    public void C0(int i) {
        this.C0 = i;
    }

    public int D() {
        return this.E[0];
    }

    public void D0(a aVar) {
        this.W[0] = aVar;
    }

    public int E() {
        return this.k0;
    }

    protected void E0(int i, boolean z) {
        this.V[i] = z;
    }

    public int F() {
        return this.j0;
    }

    public void F0(boolean z) {
        this.H = z;
    }

    public e G(int i) {
        d dVar;
        d dVar2;
        if (i != 0) {
            if (i == 1 && (dVar2 = (dVar = this.O).f) != null && dVar2.f == dVar) {
                return dVar2.f196d;
            }
            return null;
        }
        d dVar3 = this.N;
        d dVar4 = dVar3.f;
        if (dVar4 == null || dVar4.f != dVar3) {
            return null;
        }
        return dVar4.f196d;
    }

    public void G0(boolean z) {
        this.I = z;
    }

    public e H(int i) {
        d dVar;
        d dVar2;
        if (i != 0) {
            if (i == 1 && (dVar2 = (dVar = this.M).f) != null && dVar2.f == dVar) {
                return dVar2.f196d;
            }
            return null;
        }
        d dVar3 = this.L;
        d dVar4 = dVar3.f;
        if (dVar4 == null || dVar4.f != dVar3) {
            return null;
        }
        return dVar4.f196d;
    }

    public void H0(int i, int i2) {
        this.J = i;
        this.K = i2;
        this.g = false;
    }

    public int I() {
        return R() + this.Y;
    }

    public void I0(int i) {
        this.E[1] = i;
    }

    public void J(StringBuilder sb) {
        StringBuilder j = b.b.a.a.a.j("  ");
        j.append(this.j);
        j.append(":{\n");
        sb.append(j.toString());
        sb.append("    actualWidth:" + this.Y);
        sb.append("\n");
        sb.append("    actualHeight:" + this.Z);
        sb.append("\n");
        sb.append("    actualLeft:" + this.c0);
        sb.append("\n");
        sb.append("    actualTop:" + this.d0);
        sb.append("\n");
        L(sb, "left", this.L);
        L(sb, "top", this.M);
        L(sb, "right", this.N);
        L(sb, "bottom", this.O);
        L(sb, "baseline", this.P);
        L(sb, "centerX", this.Q);
        L(sb, "centerY", this.R);
        int i = this.Y;
        int i2 = this.j0;
        int i3 = this.E[0];
        int i4 = this.u;
        int i5 = this.r;
        float f = this.w;
        float f2 = this.G0[0];
        K(sb, "    width", i, i2, i3, i4, i5, f);
        int i6 = this.Z;
        int i7 = this.k0;
        int i8 = this.E[1];
        int i9 = this.x;
        int i10 = this.s;
        float f3 = this.z;
        float f4 = this.G0[1];
        K(sb, "    height", i6, i7, i8, i9, i10, f3);
        float f5 = this.a0;
        int i11 = this.b0;
        if (f5 != 0.0f) {
            sb.append("    dimensionRatio");
            sb.append(" :  [");
            sb.append(f5);
            sb.append(",");
            sb.append(i11);
            sb.append("");
            sb.append("],\n");
        }
        o0(sb, "    horizontalBias", this.l0, 0.5f);
        o0(sb, "    verticalBias", this.m0, 0.5f);
        p0(sb, "    horizontalChainStyle", this.C0, 0);
        p0(sb, "    verticalChainStyle", this.D0, 0);
        sb.append("  }");
    }

    public void J0(int i) {
        this.E[0] = i;
    }

    public void K0(boolean z) {
        this.g = z;
    }

    public void L0(int i) {
        if (i < 0) {
            i = 0;
        }
        this.k0 = i;
    }

    public float M() {
        return this.m0;
    }

    public void M0(int i) {
        if (i < 0) {
            i = 0;
        }
        this.j0 = i;
    }

    public int N() {
        return this.D0;
    }

    public void N0(int i, int i2) {
        this.c0 = i;
        this.d0 = i2;
    }

    public a O() {
        return this.W[1];
    }

    public void O0(float f) {
        this.m0 = f;
    }

    public int P() {
        return this.p0;
    }

    public void P0(int i) {
        this.D0 = i;
    }

    public int Q() {
        if (this.p0 == 8) {
            return 0;
        }
        return this.Y;
    }

    public void Q0(a aVar) {
        this.W[1] = aVar;
    }

    public int R() {
        e eVar = this.X;
        return (eVar == null || !(eVar instanceof f)) ? this.c0 : ((f) eVar).U0 + this.c0;
    }

    public void R0(int i) {
        this.p0 = i;
    }

    public int S() {
        e eVar = this.X;
        return (eVar == null || !(eVar instanceof f)) ? this.d0 : ((f) eVar).V0 + this.d0;
    }

    public void S0(int i) {
        this.Y = i;
        int i2 = this.j0;
        if (i < i2) {
            this.Y = i2;
        }
    }

    public boolean T() {
        return this.G;
    }

    public void T0(int i) {
        if (i < 0 || i > 3) {
            return;
        }
        this.q = i;
    }

    public boolean U(int i) {
        if (i == 0) {
            return (this.L.f != null ? 1 : 0) + (this.N.f != null ? 1 : 0) < 2;
        }
        return ((this.M.f != null ? 1 : 0) + (this.O.f != null ? 1 : 0)) + (this.P.f != null ? 1 : 0) < 2;
    }

    public void U0(int i) {
        this.c0 = i;
    }

    public boolean V() {
        return (this.h == -1 && this.i == -1) ? false : true;
    }

    public void V0(int i) {
        this.d0 = i;
    }

    public boolean W(int i, int i2) {
        d dVar;
        d dVar2;
        if (i == 0) {
            d dVar3 = this.L.f;
            if (dVar3 != null && dVar3.k() && (dVar2 = this.N.f) != null && dVar2.k()) {
                return (this.N.f.e() - this.N.f()) - (this.L.f() + this.L.f.e()) >= i2;
            }
        } else {
            d dVar4 = this.M.f;
            if (dVar4 != null && dVar4.k() && (dVar = this.O.f) != null && dVar.k()) {
                return (this.O.f.e() - this.O.f()) - (this.M.f() + this.M.f.e()) >= i2;
            }
        }
        return false;
    }

    public void W0(boolean z, boolean z2) {
        int i;
        int i2;
        a aVar = a.FIXED;
        boolean k = z & this.f204d.k();
        boolean k2 = z2 & this.e.k();
        a.f.a.j.o.l lVar = this.f204d;
        int i3 = lVar.h.g;
        a.f.a.j.o.n nVar = this.e;
        int i4 = nVar.h.g;
        int i5 = lVar.i.g;
        int i6 = nVar.i.g;
        int i7 = i6 - i4;
        if (i5 - i3 < 0 || i7 < 0 || i3 == Integer.MIN_VALUE || i3 == Integer.MAX_VALUE || i4 == Integer.MIN_VALUE || i4 == Integer.MAX_VALUE || i5 == Integer.MIN_VALUE || i5 == Integer.MAX_VALUE || i6 == Integer.MIN_VALUE || i6 == Integer.MAX_VALUE) {
            i5 = 0;
            i3 = 0;
            i6 = 0;
            i4 = 0;
        }
        int i8 = i5 - i3;
        int i9 = i6 - i4;
        if (k) {
            this.c0 = i3;
        }
        if (k2) {
            this.d0 = i4;
        }
        if (this.p0 == 8) {
            this.Y = 0;
            this.Z = 0;
            return;
        }
        if (k) {
            if (this.W[0] == aVar && i8 < (i2 = this.Y)) {
                i8 = i2;
            }
            this.Y = i8;
            int i10 = this.j0;
            if (i8 < i10) {
                this.Y = i10;
            }
        }
        if (k2) {
            if (this.W[1] == aVar && i9 < (i = this.Z)) {
                i9 = i;
            }
            this.Z = i9;
            int i11 = this.k0;
            if (i9 < i11) {
                this.Z = i11;
            }
        }
    }

    public void X0(a.f.a.d dVar, boolean z) {
        int i;
        int i2;
        a.f.a.j.o.n nVar;
        a.f.a.j.o.l lVar;
        int p = dVar.p(this.L);
        int p2 = dVar.p(this.M);
        int p3 = dVar.p(this.N);
        int p4 = dVar.p(this.O);
        if (z && (lVar = this.f204d) != null) {
            a.f.a.j.o.f fVar = lVar.h;
            if (fVar.j) {
                a.f.a.j.o.f fVar2 = lVar.i;
                if (fVar2.j) {
                    p = fVar.g;
                    p3 = fVar2.g;
                }
            }
        }
        if (z && (nVar = this.e) != null) {
            a.f.a.j.o.f fVar3 = nVar.h;
            if (fVar3.j) {
                a.f.a.j.o.f fVar4 = nVar.i;
                if (fVar4.j) {
                    p2 = fVar3.g;
                    p4 = fVar4.g;
                }
            }
        }
        int i3 = p4 - p2;
        if (p3 - p < 0 || i3 < 0 || p == Integer.MIN_VALUE || p == Integer.MAX_VALUE || p2 == Integer.MIN_VALUE || p2 == Integer.MAX_VALUE || p3 == Integer.MIN_VALUE || p3 == Integer.MAX_VALUE || p4 == Integer.MIN_VALUE || p4 == Integer.MAX_VALUE) {
            p4 = 0;
            p = 0;
            p2 = 0;
            p3 = 0;
        }
        a aVar = a.MATCH_CONSTRAINT;
        int i4 = p3 - p;
        int i5 = p4 - p2;
        this.c0 = p;
        this.d0 = p2;
        if (this.p0 == 8) {
            this.Y = 0;
            this.Z = 0;
            return;
        }
        a[] aVarArr = this.W;
        a aVar2 = aVarArr[0];
        a aVar3 = a.FIXED;
        if (aVar2 == aVar3 && i4 < (i2 = this.Y)) {
            i4 = i2;
        }
        if (aVarArr[1] == aVar3 && i5 < (i = this.Z)) {
            i5 = i;
        }
        this.Y = i4;
        this.Z = i5;
        int i6 = this.k0;
        if (i5 < i6) {
            this.Z = i6;
        }
        int i7 = this.j0;
        if (i4 < i7) {
            this.Y = i7;
        }
        int i8 = this.v;
        if (i8 > 0 && aVarArr[0] == aVar) {
            this.Y = Math.min(this.Y, i8);
        }
        int i9 = this.y;
        if (i9 > 0 && this.W[1] == aVar) {
            this.Z = Math.min(this.Z, i9);
        }
        int i10 = this.Y;
        if (i4 != i10) {
            this.h = i10;
        }
        int i11 = this.Z;
        if (i5 != i11) {
            this.i = i11;
        }
    }

    public boolean Y() {
        return this.m;
    }

    public boolean Z(int i) {
        return this.V[i];
    }

    public boolean a0() {
        d dVar = this.L;
        d dVar2 = dVar.f;
        if (dVar2 != null && dVar2.f == dVar) {
            return true;
        }
        d dVar3 = this.N;
        d dVar4 = dVar3.f;
        return dVar4 != null && dVar4.f == dVar3;
    }

    public boolean b0() {
        return this.H;
    }

    public boolean c0() {
        d dVar = this.M;
        d dVar2 = dVar.f;
        if (dVar2 != null && dVar2.f == dVar) {
            return true;
        }
        d dVar3 = this.O;
        d dVar4 = dVar3.f;
        return dVar4 != null && dVar4.f == dVar3;
    }

    public void d(f fVar, a.f.a.d dVar, HashSet<e> hashSet, int i, boolean z) {
        if (z) {
            if (!hashSet.contains(this)) {
                return;
            }
            k.a(fVar, dVar, this);
            hashSet.remove(this);
            f(dVar, fVar.o1(64));
        }
        if (i == 0) {
            HashSet<d> d2 = this.L.d();
            if (d2 != null) {
                Iterator<d> it = d2.iterator();
                while (it.hasNext()) {
                    it.next().f196d.d(fVar, dVar, hashSet, i, true);
                }
            }
            HashSet<d> d3 = this.N.d();
            if (d3 != null) {
                Iterator<d> it2 = d3.iterator();
                while (it2.hasNext()) {
                    it2.next().f196d.d(fVar, dVar, hashSet, i, true);
                }
                return;
            }
            return;
        }
        HashSet<d> d4 = this.M.d();
        if (d4 != null) {
            Iterator<d> it3 = d4.iterator();
            while (it3.hasNext()) {
                it3.next().f196d.d(fVar, dVar, hashSet, i, true);
            }
        }
        HashSet<d> d5 = this.O.d();
        if (d5 != null) {
            Iterator<d> it4 = d5.iterator();
            while (it4.hasNext()) {
                it4.next().f196d.d(fVar, dVar, hashSet, i, true);
            }
        }
        HashSet<d> d6 = this.P.d();
        if (d6 != null) {
            Iterator<d> it5 = d6.iterator();
            while (it5.hasNext()) {
                it5.next().f196d.d(fVar, dVar, hashSet, i, true);
            }
        }
    }

    public boolean d0() {
        return this.I;
    }

    boolean e() {
        return (this instanceof m) || (this instanceof h);
    }

    public boolean e0() {
        return this.g && this.p0 != 8;
    }

    /* JADX WARN: Removed duplicated region for block: B:101:0x0200  */
    /* JADX WARN: Removed duplicated region for block: B:104:0x020b  */
    /* JADX WARN: Removed duplicated region for block: B:107:0x0212  */
    /* JADX WARN: Removed duplicated region for block: B:110:0x0231  */
    /* JADX WARN: Removed duplicated region for block: B:168:0x02f7  */
    /* JADX WARN: Removed duplicated region for block: B:181:0x0388  */
    /* JADX WARN: Removed duplicated region for block: B:186:0x0397  */
    /* JADX WARN: Removed duplicated region for block: B:18:0x0063  */
    /* JADX WARN: Removed duplicated region for block: B:192:0x03aa  */
    /* JADX WARN: Removed duplicated region for block: B:196:0x03b3  */
    /* JADX WARN: Removed duplicated region for block: B:199:0x03cc  */
    /* JADX WARN: Removed duplicated region for block: B:209:0x03e5  */
    /* JADX WARN: Removed duplicated region for block: B:219:0x04c8  */
    /* JADX WARN: Removed duplicated region for block: B:235:0x0540  */
    /* JADX WARN: Removed duplicated region for block: B:237:0x0545  */
    /* JADX WARN: Removed duplicated region for block: B:259:0x05d4  */
    /* JADX WARN: Removed duplicated region for block: B:262:0x061d  */
    /* JADX WARN: Removed duplicated region for block: B:268:0x064d  */
    /* JADX WARN: Removed duplicated region for block: B:272:0x0643  */
    /* JADX WARN: Removed duplicated region for block: B:273:0x05d7  */
    /* JADX WARN: Removed duplicated region for block: B:288:0x0542  */
    /* JADX WARN: Removed duplicated region for block: B:293:0x052c  */
    /* JADX WARN: Removed duplicated region for block: B:296:0x042d  */
    /* JADX WARN: Removed duplicated region for block: B:299:0x043b  */
    /* JADX WARN: Removed duplicated region for block: B:302:0x0464  */
    /* JADX WARN: Removed duplicated region for block: B:304:0x0467  */
    /* JADX WARN: Removed duplicated region for block: B:305:0x0443  */
    /* JADX WARN: Removed duplicated region for block: B:306:0x0435  */
    /* JADX WARN: Removed duplicated region for block: B:311:0x0392  */
    /* JADX WARN: Removed duplicated region for block: B:341:0x0373  */
    /* JADX WARN: Removed duplicated region for block: B:342:0x0214  */
    /* JADX WARN: Removed duplicated region for block: B:343:0x020d  */
    /* JADX WARN: Removed duplicated region for block: B:346:0x01ed  */
    /* JADX WARN: Removed duplicated region for block: B:348:0x0097  */
    /* JADX WARN: Removed duplicated region for block: B:355:0x00ba  */
    /* JADX WARN: Removed duplicated region for block: B:53:0x0145  */
    /* JADX WARN: Removed duplicated region for block: B:69:0x0180  */
    /* JADX WARN: Removed duplicated region for block: B:98:0x01f8  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void f(a.f.a.d r51, boolean r52) {
        /*
            Method dump skipped, instructions count: 1765
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: a.f.a.j.e.f(a.f.a.d, boolean):void");
    }

    public boolean f0() {
        return this.k || (this.L.k() && this.N.k());
    }

    public boolean g() {
        return this.p0 != 8;
    }

    public boolean g0() {
        return this.l || (this.M.k() && this.O.k());
    }

    public boolean h0() {
        return this.n;
    }

    /* JADX WARN: Code restructure failed: missing block: B:63:0x0137, code lost:
    
        if (r10 != null) goto L73;
     */
    /* JADX WARN: Code restructure failed: missing block: B:75:0x015e, code lost:
    
        if (r10.l() != false) goto L72;
     */
    /* JADX WARN: Code restructure failed: missing block: B:76:0x0160, code lost:
    
        r11.n();
     */
    /* JADX WARN: Code restructure failed: missing block: B:84:0x018b, code lost:
    
        if (r10.l() != false) goto L72;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void i(a.f.a.j.d.a r11, a.f.a.j.e r12, a.f.a.j.d.a r13, int r14) {
        /*
            Method dump skipped, instructions count: 402
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: a.f.a.j.e.i(a.f.a.j.d$a, a.f.a.j.e, a.f.a.j.d$a, int):void");
    }

    public void i0() {
        this.m = true;
    }

    public void j(d dVar, d dVar2, int i) {
        if (dVar.f196d == this) {
            i(dVar.e, dVar2.f196d, dVar2.e, i);
        }
    }

    public void j0() {
        this.n = true;
    }

    public void k(e eVar, float f, int i) {
        d.a aVar = d.a.CENTER;
        o(aVar).b(eVar.o(aVar), i, 0, true);
        this.F = f;
    }

    public void k0() {
        this.L.n();
        this.M.n();
        this.N.n();
        this.O.n();
        this.P.n();
        this.Q.n();
        this.R.n();
        this.S.n();
        this.X = null;
        this.F = 0.0f;
        this.Y = 0;
        this.Z = 0;
        this.a0 = 0.0f;
        this.b0 = -1;
        this.c0 = 0;
        this.d0 = 0;
        this.g0 = 0;
        this.h0 = 0;
        this.i0 = 0;
        this.j0 = 0;
        this.k0 = 0;
        this.l0 = 0.5f;
        this.m0 = 0.5f;
        a[] aVarArr = this.W;
        a aVar = a.FIXED;
        aVarArr[0] = aVar;
        aVarArr[1] = aVar;
        this.n0 = null;
        this.o0 = 0;
        this.p0 = 0;
        this.r0 = null;
        this.A0 = false;
        this.B0 = false;
        this.C0 = 0;
        this.D0 = 0;
        this.E0 = false;
        this.F0 = false;
        float[] fArr = this.G0;
        fArr[0] = -1.0f;
        fArr[1] = -1.0f;
        this.o = -1;
        this.p = -1;
        int[] iArr = this.E;
        iArr[0] = Integer.MAX_VALUE;
        iArr[1] = Integer.MAX_VALUE;
        this.r = 0;
        this.s = 0;
        this.w = 1.0f;
        this.z = 1.0f;
        this.v = Integer.MAX_VALUE;
        this.y = Integer.MAX_VALUE;
        this.u = 0;
        this.x = 0;
        this.C = -1;
        this.D = 1.0f;
        boolean[] zArr = this.f;
        zArr[0] = true;
        zArr[1] = true;
        this.I = false;
        boolean[] zArr2 = this.V;
        zArr2[0] = false;
        zArr2[1] = false;
        this.g = true;
        int[] iArr2 = this.t;
        iArr2[0] = 0;
        iArr2[1] = 0;
        this.h = -1;
        this.i = -1;
    }

    public void l(e eVar, HashMap<e, e> hashMap) {
        this.o = eVar.o;
        this.p = eVar.p;
        this.r = eVar.r;
        this.s = eVar.s;
        int[] iArr = this.t;
        int[] iArr2 = eVar.t;
        iArr[0] = iArr2[0];
        iArr[1] = iArr2[1];
        this.u = eVar.u;
        this.v = eVar.v;
        this.x = eVar.x;
        this.y = eVar.y;
        this.z = eVar.z;
        this.A = eVar.A;
        this.B = eVar.B;
        this.C = eVar.C;
        this.D = eVar.D;
        int[] iArr3 = eVar.E;
        this.E = Arrays.copyOf(iArr3, iArr3.length);
        this.F = eVar.F;
        this.G = eVar.G;
        this.H = eVar.H;
        this.L.n();
        this.M.n();
        this.N.n();
        this.O.n();
        this.P.n();
        this.Q.n();
        this.R.n();
        this.S.n();
        this.W = (a[]) Arrays.copyOf(this.W, 2);
        this.X = this.X == null ? null : hashMap.get(eVar.X);
        this.Y = eVar.Y;
        this.Z = eVar.Z;
        this.a0 = eVar.a0;
        this.b0 = eVar.b0;
        this.c0 = eVar.c0;
        this.d0 = eVar.d0;
        this.e0 = eVar.e0;
        this.f0 = eVar.f0;
        this.g0 = eVar.g0;
        this.h0 = eVar.h0;
        this.i0 = eVar.i0;
        this.j0 = eVar.j0;
        this.k0 = eVar.k0;
        this.l0 = eVar.l0;
        this.m0 = eVar.m0;
        this.n0 = eVar.n0;
        this.o0 = eVar.o0;
        this.p0 = eVar.p0;
        this.q0 = eVar.q0;
        this.r0 = eVar.r0;
        this.s0 = eVar.s0;
        this.t0 = eVar.t0;
        this.u0 = eVar.u0;
        this.v0 = eVar.v0;
        this.w0 = eVar.w0;
        this.x0 = eVar.x0;
        this.y0 = eVar.y0;
        this.z0 = eVar.z0;
        this.A0 = eVar.A0;
        this.B0 = eVar.B0;
        this.C0 = eVar.C0;
        this.D0 = eVar.D0;
        this.E0 = eVar.E0;
        this.F0 = eVar.F0;
        float[] fArr = this.G0;
        float[] fArr2 = eVar.G0;
        fArr[0] = fArr2[0];
        fArr[1] = fArr2[1];
        e[] eVarArr = this.H0;
        e[] eVarArr2 = eVar.H0;
        eVarArr[0] = eVarArr2[0];
        eVarArr[1] = eVarArr2[1];
        e[] eVarArr3 = this.I0;
        e[] eVarArr4 = eVar.I0;
        eVarArr3[0] = eVarArr4[0];
        eVarArr3[1] = eVarArr4[1];
        e eVar2 = eVar.J0;
        this.J0 = eVar2 == null ? null : hashMap.get(eVar2);
        e eVar3 = eVar.K0;
        this.K0 = eVar3 != null ? hashMap.get(eVar3) : null;
    }

    public void l0() {
        e eVar = this.X;
        if (eVar != null && (eVar instanceof f)) {
            Objects.requireNonNull((f) eVar);
        }
        int size = this.U.size();
        for (int i = 0; i < size; i++) {
            this.U.get(i).n();
        }
    }

    public void m(a.f.a.d dVar) {
        dVar.l(this.L);
        dVar.l(this.M);
        dVar.l(this.N);
        dVar.l(this.O);
        if (this.i0 > 0) {
            dVar.l(this.P);
        }
    }

    public void m0() {
        this.k = false;
        this.l = false;
        this.m = false;
        this.n = false;
        int size = this.U.size();
        for (int i = 0; i < size; i++) {
            this.U.get(i).o();
        }
    }

    public void n() {
        if (this.f204d == null) {
            this.f204d = new a.f.a.j.o.l(this);
        }
        if (this.e == null) {
            this.e = new a.f.a.j.o.n(this);
        }
    }

    public void n0(a.f.a.c cVar) {
        this.L.p();
        this.M.p();
        this.N.p();
        this.O.p();
        this.P.p();
        this.S.p();
        this.Q.p();
        this.R.p();
    }

    public d o(d.a aVar) {
        switch (aVar.ordinal()) {
            case 0:
                return null;
            case 1:
                return this.L;
            case 2:
                return this.M;
            case 3:
                return this.N;
            case 4:
                return this.O;
            case 5:
                return this.P;
            case 6:
                return this.S;
            case MotionLayout.TOUCH_UP_NEVER_TO_END /* 7 */:
                return this.Q;
            case 8:
                return this.R;
            default:
                throw new AssertionError(aVar.name());
        }
    }

    public int p() {
        return this.i0;
    }

    public float q(int i) {
        if (i == 0) {
            return this.l0;
        }
        if (i == 1) {
            return this.m0;
        }
        return -1.0f;
    }

    public void q0(int i) {
        this.i0 = i;
        this.G = i > 0;
    }

    public int r() {
        return S() + this.Z;
    }

    public void r0(Object obj) {
        this.n0 = obj;
    }

    public Object s() {
        return this.n0;
    }

    public void s0(String str) {
        this.q0 = str;
    }

    public String t() {
        return this.q0;
    }

    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:38:0x0084 -> B:31:0x0085). Please report as a decompilation issue!!! */
    public void t0(String str) {
        float f;
        int i = 0;
        if (str == null || str.length() == 0) {
            this.a0 = 0.0f;
            return;
        }
        int i2 = -1;
        int length = str.length();
        int indexOf = str.indexOf(44);
        int i3 = 0;
        if (indexOf > 0 && indexOf < length - 1) {
            String substring = str.substring(0, indexOf);
            if (substring.equalsIgnoreCase("W")) {
                i2 = 0;
            } else if (substring.equalsIgnoreCase("H")) {
                i2 = 1;
            }
            i3 = indexOf + 1;
        }
        int indexOf2 = str.indexOf(58);
        if (indexOf2 < 0 || indexOf2 >= length - 1) {
            String substring2 = str.substring(i3);
            if (substring2.length() > 0) {
                f = Float.parseFloat(substring2);
            }
            f = i;
        } else {
            String substring3 = str.substring(i3, indexOf2);
            String substring4 = str.substring(indexOf2 + 1);
            if (substring3.length() > 0 && substring4.length() > 0) {
                float parseFloat = Float.parseFloat(substring3);
                float parseFloat2 = Float.parseFloat(substring4);
                if (parseFloat > 0.0f && parseFloat2 > 0.0f) {
                    f = i2 == 1 ? Math.abs(parseFloat2 / parseFloat) : Math.abs(parseFloat / parseFloat2);
                }
            }
            f = i;
        }
        i = (f > i ? 1 : (f == i ? 0 : -1));
        if (i > 0) {
            this.a0 = f;
            this.b0 = i2;
        }
    }

    public String toString() {
        String str;
        StringBuilder sb = new StringBuilder();
        String str2 = "";
        if (this.r0 != null) {
            StringBuilder j = b.b.a.a.a.j("type: ");
            j.append(this.r0);
            j.append(" ");
            str = j.toString();
        } else {
            str = "";
        }
        sb.append(str);
        if (this.q0 != null) {
            StringBuilder j2 = b.b.a.a.a.j("id: ");
            j2.append(this.q0);
            j2.append(" ");
            str2 = j2.toString();
        }
        sb.append(str2);
        sb.append("(");
        sb.append(this.c0);
        sb.append(", ");
        sb.append(this.d0);
        sb.append(") - (");
        sb.append(this.Y);
        sb.append(" x ");
        sb.append(this.Z);
        sb.append(")");
        return sb.toString();
    }

    public a u(int i) {
        if (i == 0) {
            return z();
        }
        if (i == 1) {
            return O();
        }
        return null;
    }

    public void u0(int i) {
        if (this.G) {
            int i2 = i - this.i0;
            int i3 = this.Z + i2;
            this.d0 = i2;
            this.M.q(i2);
            this.O.q(i3);
            this.P.q(i);
            this.l = true;
        }
    }

    public int v() {
        return this.b0;
    }

    public void v0(int i, int i2) {
        if (this.k) {
            return;
        }
        this.L.q(i);
        this.N.q(i2);
        this.c0 = i;
        this.Y = i2 - i;
        this.k = true;
    }

    public int w() {
        if (this.p0 == 8) {
            return 0;
        }
        return this.Z;
    }

    public void w0(int i) {
        this.L.q(i);
        this.c0 = i;
    }

    public float x() {
        return this.l0;
    }

    public void x0(int i) {
        this.M.q(i);
        this.d0 = i;
    }

    public int y() {
        return this.C0;
    }

    public void y0(int i, int i2) {
        if (this.l) {
            return;
        }
        this.M.q(i);
        this.O.q(i2);
        this.d0 = i;
        this.Z = i2 - i;
        if (this.G) {
            this.P.q(i + this.i0);
        }
        this.l = true;
    }

    public a z() {
        return this.W[0];
    }

    public void z0(boolean z) {
        this.G = z;
    }
}
