package a.h.h;

import android.view.View;
import android.view.ViewTreeObserver;
import java.util.Objects;

/* loaded from: classes.dex */
public final class k implements ViewTreeObserver.OnPreDrawListener, View.OnAttachStateChangeListener {

    /* renamed from: a, reason: collision with root package name */
    private final View f371a;

    /* renamed from: b, reason: collision with root package name */
    private ViewTreeObserver f372b;

    /* renamed from: c, reason: collision with root package name */
    private final Runnable f373c;

    private k(View view, Runnable runnable) {
        this.f371a = view;
        this.f372b = view.getViewTreeObserver();
        this.f373c = runnable;
    }

    public static k a(View view, Runnable runnable) {
        Objects.requireNonNull(view, "view == null");
        k kVar = new k(view, runnable);
        view.getViewTreeObserver().addOnPreDrawListener(kVar);
        view.addOnAttachStateChangeListener(kVar);
        return kVar;
    }

    public void b() {
        (this.f372b.isAlive() ? this.f372b : this.f371a.getViewTreeObserver()).removeOnPreDrawListener(this);
        this.f371a.removeOnAttachStateChangeListener(this);
    }

    @Override // android.view.ViewTreeObserver.OnPreDrawListener
    public boolean onPreDraw() {
        b();
        this.f373c.run();
        return true;
    }

    @Override // android.view.View.OnAttachStateChangeListener
    public void onViewAttachedToWindow(View view) {
        this.f372b = view.getViewTreeObserver();
    }

    @Override // android.view.View.OnAttachStateChangeListener
    public void onViewDetachedFromWindow(View view) {
        b();
    }
}
