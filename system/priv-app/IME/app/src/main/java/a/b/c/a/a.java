package a.b.c.a;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.util.SparseArray;
import android.util.TypedValue;
import androidx.appcompat.widget.x;
import java.util.WeakHashMap;

@SuppressLint({"RestrictedAPI"})
/* loaded from: classes.dex */
public final class a {

    /* renamed from: a, reason: collision with root package name */
    private static final ThreadLocal<TypedValue> f4a = new ThreadLocal<>();

    /* renamed from: b, reason: collision with root package name */
    private static final WeakHashMap<Context, SparseArray<Object>> f5b = new WeakHashMap<>(0);

    /* renamed from: c, reason: collision with root package name */
    private static final Object f6c = new Object();

    /* renamed from: d, reason: collision with root package name */
    public static final /* synthetic */ int f7d = 0;

    public static Drawable a(Context context, int i) {
        return x.c().e(context, i);
    }
}
