package a.h.g;

import java.util.Objects;

/* loaded from: classes.dex */
public class c<F, S> {
    public boolean equals(Object obj) {
        if (!(obj instanceof c)) {
            return false;
        }
        Objects.requireNonNull((c) obj);
        return Objects.equals(null, null) && Objects.equals(null, null);
    }

    public int hashCode() {
        return 0;
    }

    public String toString() {
        return "Pair{null null}";
    }
}
