package a.h.b.b;

/* loaded from: classes.dex */
public final class e implements b {

    /* renamed from: a, reason: collision with root package name */
    private final a.h.e.e f271a;

    /* renamed from: b, reason: collision with root package name */
    private final int f272b;

    /* renamed from: c, reason: collision with root package name */
    private final int f273c;

    /* renamed from: d, reason: collision with root package name */
    private final String f274d;

    public e(a.h.e.e eVar, int i, int i2, String str) {
        this.f271a = eVar;
        this.f273c = i;
        this.f272b = i2;
        this.f274d = str;
    }

    public int a() {
        return this.f273c;
    }

    public a.h.e.e b() {
        return this.f271a;
    }

    public String c() {
        return this.f274d;
    }

    public int d() {
        return this.f272b;
    }
}
