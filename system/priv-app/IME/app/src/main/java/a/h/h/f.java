package a.h.h;

import android.util.Log;
import android.view.View;
import android.view.ViewParent;

/* loaded from: classes.dex */
public class f {

    /* renamed from: a, reason: collision with root package name */
    private ViewParent f365a;

    /* renamed from: b, reason: collision with root package name */
    private ViewParent f366b;

    /* renamed from: c, reason: collision with root package name */
    private final View f367c;

    /* renamed from: d, reason: collision with root package name */
    private boolean f368d;
    private int[] e;

    public f(View view) {
        this.f367c = view;
    }

    private boolean g(int i, int i2, int i3, int i4, int[] iArr, int i5, int[] iArr2) {
        ViewParent h;
        int i6;
        int i7;
        int[] iArr3;
        if (!this.f368d || (h = h(i5)) == null) {
            return false;
        }
        if (i == 0 && i2 == 0 && i3 == 0 && i4 == 0) {
            if (iArr != null) {
                iArr[0] = 0;
                iArr[1] = 0;
            }
            return false;
        }
        if (iArr != null) {
            this.f367c.getLocationInWindow(iArr);
            i6 = iArr[0];
            i7 = iArr[1];
        } else {
            i6 = 0;
            i7 = 0;
        }
        if (iArr2 == null) {
            if (this.e == null) {
                this.e = new int[2];
            }
            int[] iArr4 = this.e;
            iArr4[0] = 0;
            iArr4[1] = 0;
            iArr3 = iArr4;
        } else {
            iArr3 = iArr2;
        }
        View view = this.f367c;
        if (h instanceof h) {
            ((h) h).onNestedScroll(view, i, i2, i3, i4, i5, iArr3);
        } else {
            iArr3[0] = iArr3[0] + i3;
            iArr3[1] = iArr3[1] + i4;
            if (h instanceof g) {
                ((g) h).onNestedScroll(view, i, i2, i3, i4, i5);
            } else if (i5 == 0) {
                try {
                    h.onNestedScroll(view, i, i2, i3, i4);
                } catch (AbstractMethodError e) {
                    Log.e("ViewParentCompat", "ViewParent " + h + " does not implement interface method onNestedScroll", e);
                }
            }
        }
        if (iArr != null) {
            this.f367c.getLocationInWindow(iArr);
            iArr[0] = iArr[0] - i6;
            iArr[1] = iArr[1] - i7;
        }
        return true;
    }

    private ViewParent h(int i) {
        if (i == 0) {
            return this.f365a;
        }
        if (i != 1) {
            return null;
        }
        return this.f366b;
    }

    public boolean a(float f, float f2, boolean z) {
        ViewParent h;
        if (!this.f368d || (h = h(0)) == null) {
            return false;
        }
        try {
            return h.onNestedFling(this.f367c, f, f2, z);
        } catch (AbstractMethodError e) {
            Log.e("ViewParentCompat", "ViewParent " + h + " does not implement interface method onNestedFling", e);
            return false;
        }
    }

    public boolean b(float f, float f2) {
        ViewParent h;
        if (!this.f368d || (h = h(0)) == null) {
            return false;
        }
        try {
            return h.onNestedPreFling(this.f367c, f, f2);
        } catch (AbstractMethodError e) {
            Log.e("ViewParentCompat", "ViewParent " + h + " does not implement interface method onNestedPreFling", e);
            return false;
        }
    }

    public boolean c(int i, int i2, int[] iArr, int[] iArr2, int i3) {
        ViewParent h;
        int i4;
        int i5;
        int[] iArr3;
        if (!this.f368d || (h = h(i3)) == null) {
            return false;
        }
        if (i == 0 && i2 == 0) {
            if (iArr2 == null) {
                return false;
            }
            iArr2[0] = 0;
            iArr2[1] = 0;
            return false;
        }
        if (iArr2 != null) {
            this.f367c.getLocationInWindow(iArr2);
            i4 = iArr2[0];
            i5 = iArr2[1];
        } else {
            i4 = 0;
            i5 = 0;
        }
        if (iArr == null) {
            if (this.e == null) {
                this.e = new int[2];
            }
            iArr3 = this.e;
        } else {
            iArr3 = iArr;
        }
        iArr3[0] = 0;
        iArr3[1] = 0;
        View view = this.f367c;
        if (h instanceof g) {
            ((g) h).onNestedPreScroll(view, i, i2, iArr3, i3);
        } else if (i3 == 0) {
            try {
                h.onNestedPreScroll(view, i, i2, iArr3);
            } catch (AbstractMethodError e) {
                Log.e("ViewParentCompat", "ViewParent " + h + " does not implement interface method onNestedPreScroll", e);
            }
        }
        if (iArr2 != null) {
            this.f367c.getLocationInWindow(iArr2);
            iArr2[0] = iArr2[0] - i4;
            iArr2[1] = iArr2[1] - i5;
        }
        return (iArr3[0] == 0 && iArr3[1] == 0) ? false : true;
    }

    public void d(int i, int i2, int i3, int i4, int[] iArr, int i5, int[] iArr2) {
        g(i, i2, i3, i4, iArr, i5, iArr2);
    }

    public boolean e(int i, int i2, int i3, int i4, int[] iArr) {
        return g(i, i2, i3, i4, iArr, 0, null);
    }

    public boolean f(int i, int i2, int i3, int i4, int[] iArr, int i5) {
        return g(i, i2, i3, i4, iArr, i5, null);
    }

    public boolean i(int i) {
        return h(i) != null;
    }

    public boolean j() {
        return this.f368d;
    }

    public void k(boolean z) {
        if (this.f368d) {
            View view = this.f367c;
            int i = q.e;
            view.stopNestedScroll();
        }
        this.f368d = z;
    }

    public boolean l(int i, int i2) {
        boolean onStartNestedScroll;
        if (h(i2) != null) {
            return true;
        }
        if (this.f368d) {
            View view = this.f367c;
            for (ViewParent parent = this.f367c.getParent(); parent != null; parent = parent.getParent()) {
                View view2 = this.f367c;
                boolean z = parent instanceof g;
                if (z) {
                    onStartNestedScroll = ((g) parent).onStartNestedScroll(view, view2, i, i2);
                } else {
                    if (i2 == 0) {
                        try {
                            onStartNestedScroll = parent.onStartNestedScroll(view, view2, i);
                        } catch (AbstractMethodError e) {
                            Log.e("ViewParentCompat", "ViewParent " + parent + " does not implement interface method onStartNestedScroll", e);
                        }
                    }
                    onStartNestedScroll = false;
                }
                if (onStartNestedScroll) {
                    if (i2 == 0) {
                        this.f365a = parent;
                    } else if (i2 == 1) {
                        this.f366b = parent;
                    }
                    View view3 = this.f367c;
                    if (z) {
                        ((g) parent).onNestedScrollAccepted(view, view3, i, i2);
                    } else if (i2 == 0) {
                        try {
                            parent.onNestedScrollAccepted(view, view3, i);
                        } catch (AbstractMethodError e2) {
                            Log.e("ViewParentCompat", "ViewParent " + parent + " does not implement interface method onNestedScrollAccepted", e2);
                        }
                    }
                    return true;
                }
                if (parent instanceof View) {
                    view = parent;
                }
            }
        }
        return false;
    }

    public void m(int i) {
        ViewParent h = h(i);
        if (h != null) {
            View view = this.f367c;
            if (h instanceof g) {
                ((g) h).onStopNestedScroll(view, i);
            } else if (i == 0) {
                try {
                    h.onStopNestedScroll(view);
                } catch (AbstractMethodError e) {
                    Log.e("ViewParentCompat", "ViewParent " + h + " does not implement interface method onStopNestedScroll", e);
                }
            }
            if (i == 0) {
                this.f365a = null;
            } else {
                if (i != 1) {
                    return;
                }
                this.f366b = null;
            }
        }
    }
}
