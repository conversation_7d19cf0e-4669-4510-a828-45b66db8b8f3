package a.e;

import a.e.g.b;
import a.e.g.c;
import a.e.g.e;
import java.util.Collection;
import java.util.Map;
import java.util.Set;

/* loaded from: classes.dex */
public class a<K, V> extends h<K, V> implements Map<K, V> {
    g<K, V> h;

    /* renamed from: a.e.a$a, reason: collision with other inner class name */
    class C0001a extends g<K, V> {
        C0001a() {
        }

        @Override // a.e.g
        protected void a() {
            a.this.clear();
        }

        @Override // a.e.g
        protected Object b(int i, int i2) {
            return a.this.f97b[(i << 1) + i2];
        }

        @Override // a.e.g
        protected Map<K, V> c() {
            return a.this;
        }

        @Override // a.e.g
        protected int d() {
            return a.this.f98c;
        }

        @Override // a.e.g
        protected int e(Object obj) {
            return a.this.e(obj);
        }

        @Override // a.e.g
        protected int f(Object obj) {
            return a.this.g(obj);
        }

        @Override // a.e.g
        protected void g(K k, V v) {
            a.this.put(k, v);
        }

        @Override // a.e.g
        protected void h(int i) {
            a.this.i(i);
        }

        @Override // a.e.g
        protected V i(int i, V v) {
            int i2 = (i << 1) + 1;
            Object[] objArr = a.this.f97b;
            V v2 = (V) objArr[i2];
            objArr[i2] = v;
            return v2;
        }
    }

    public a() {
    }

    /* JADX WARN: Multi-variable type inference failed */
    public a(h hVar) {
        if (hVar != null) {
            int i = hVar.f98c;
            b(this.f98c + i);
            if (this.f98c != 0) {
                for (int i2 = 0; i2 < i; i2++) {
                    put(hVar.h(i2), hVar.k(i2));
                }
            } else if (i > 0) {
                System.arraycopy(hVar.f96a, 0, this.f96a, 0, i);
                System.arraycopy(hVar.f97b, 0, this.f97b, 0, i << 1);
                this.f98c = i;
            }
        }
    }

    private g<K, V> l() {
        if (this.h == null) {
            this.h = new C0001a();
        }
        return this.h;
    }

    @Override // java.util.Map
    public Set<Map.Entry<K, V>> entrySet() {
        g<K, V> l = l();
        if (l.f81a == null) {
            l.f81a = l.new b();
        }
        return l.f81a;
    }

    @Override // java.util.Map
    public Set<K> keySet() {
        g<K, V> l = l();
        if (l.f82b == null) {
            l.f82b = l.new c();
        }
        return l.f82b;
    }

    public boolean m(Collection<?> collection) {
        return g.k(this, collection);
    }

    @Override // java.util.Map
    public void putAll(Map<? extends K, ? extends V> map) {
        b(map.size() + this.f98c);
        for (Map.Entry<? extends K, ? extends V> entry : map.entrySet()) {
            put(entry.getKey(), entry.getValue());
        }
    }

    @Override // java.util.Map
    public Collection<V> values() {
        g<K, V> l = l();
        if (l.f83c == null) {
            l.f83c = l.new e();
        }
        return l.f83c;
    }
}
