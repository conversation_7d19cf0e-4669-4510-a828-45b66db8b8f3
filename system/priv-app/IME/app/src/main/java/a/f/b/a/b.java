package a.f.b.a;

import a.f.a.i.a.k;
import a.f.a.i.a.m;
import a.f.a.i.a.n;
import androidx.constraintlayout.motion.widget.o;

/* loaded from: classes.dex */
public class b extends o {

    /* renamed from: a, reason: collision with root package name */
    private n f251a;

    /* renamed from: b, reason: collision with root package name */
    private k f252b;

    /* renamed from: c, reason: collision with root package name */
    private m f253c;

    public b() {
        n nVar = new n();
        this.f251a = nVar;
        this.f253c = nVar;
    }

    @Override // androidx.constraintlayout.motion.widget.o
    public float a() {
        return this.f253c.b();
    }

    public void b(float f, float f2, float f3, float f4, float f5, float f6) {
        n nVar = this.f251a;
        this.f253c = nVar;
        nVar.c(f, f2, f3, f4, f5, f6);
    }

    public boolean c() {
        return this.f253c.a();
    }

    public void d(float f, float f2, float f3, float f4, float f5, float f6, float f7, int i) {
        if (this.f252b == null) {
            this.f252b = new k();
        }
        k kVar = this.f252b;
        this.f253c = kVar;
        kVar.c(f, f2, f3, f4, f5, f6, f7, i);
    }

    @Override // android.animation.TimeInterpolator
    public float getInterpolation(float f) {
        return this.f253c.getInterpolation(f);
    }
}
