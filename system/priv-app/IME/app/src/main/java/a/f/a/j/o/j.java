package a.f.a.j.o;

/* loaded from: classes.dex */
class j extends p {
    public j(a.f.a.j.e eVar) {
        super(eVar);
        eVar.f204d.f();
        eVar.e.f();
        this.f = ((a.f.a.j.h) eVar).Z0();
    }

    private void n(f fVar) {
        this.h.k.add(fVar);
        fVar.l.add(this.h);
    }

    @Override // a.f.a.j.o.p, a.f.a.j.o.d
    public void a(d dVar) {
        f fVar = this.h;
        if (fVar.f227c && !fVar.j) {
            f fVar2 = fVar.l.get(0);
            this.h.c((int) ((((a.f.a.j.h) this.f244b).c1() * fVar2.g) + 0.5f));
        }
    }

    @Override // a.f.a.j.o.p
    void d() {
        f fVar;
        p pVar;
        f fVar2;
        a.f.a.j.h hVar = (a.f.a.j.h) this.f244b;
        int a1 = hVar.a1();
        int b1 = hVar.b1();
        if (hVar.Z0() == 1) {
            f fVar3 = this.h;
            if (a1 != -1) {
                fVar3.l.add(this.f244b.X.f204d.h);
                this.f244b.X.f204d.h.k.add(this.h);
                fVar2 = this.h;
            } else if (b1 != -1) {
                fVar3.l.add(this.f244b.X.f204d.i);
                this.f244b.X.f204d.i.k.add(this.h);
                fVar2 = this.h;
                a1 = -b1;
            } else {
                fVar3.f226b = true;
                fVar3.l.add(this.f244b.X.f204d.i);
                this.f244b.X.f204d.i.k.add(this.h);
                n(this.f244b.f204d.h);
                pVar = this.f244b.f204d;
            }
            fVar2.f = a1;
            n(this.f244b.f204d.h);
            pVar = this.f244b.f204d;
        } else {
            f fVar4 = this.h;
            if (a1 != -1) {
                fVar4.l.add(this.f244b.X.e.h);
                this.f244b.X.e.h.k.add(this.h);
                fVar = this.h;
            } else if (b1 != -1) {
                fVar4.l.add(this.f244b.X.e.i);
                this.f244b.X.e.i.k.add(this.h);
                fVar = this.h;
                a1 = -b1;
            } else {
                fVar4.f226b = true;
                fVar4.l.add(this.f244b.X.e.i);
                this.f244b.X.e.i.k.add(this.h);
                n(this.f244b.e.h);
                pVar = this.f244b.e;
            }
            fVar.f = a1;
            n(this.f244b.e.h);
            pVar = this.f244b.e;
        }
        n(pVar.i);
    }

    @Override // a.f.a.j.o.p
    public void e() {
        if (((a.f.a.j.h) this.f244b).Z0() == 1) {
            this.f244b.U0(this.h.g);
        } else {
            this.f244b.V0(this.h.g);
        }
    }

    @Override // a.f.a.j.o.p
    void f() {
        this.h.b();
    }

    @Override // a.f.a.j.o.p
    boolean l() {
        return false;
    }
}
