package a.m.a;

import a.e.i;
import androidx.lifecycle.g;
import androidx.lifecycle.l;
import androidx.lifecycle.m;
import androidx.lifecycle.p;
import androidx.lifecycle.q;
import androidx.lifecycle.r;
import java.io.FileDescriptor;
import java.io.PrintWriter;
import java.util.Objects;

/* loaded from: classes.dex */
class b extends a.m.a.a {

    /* renamed from: a, reason: collision with root package name */
    private final g f461a;

    /* renamed from: b, reason: collision with root package name */
    private final C0021b f462b;

    /* JADX WARN: Unexpected interfaces in signature: [java.lang.Object<D>] */
    public static class a<D> extends l<D> {
        @Override // androidx.lifecycle.LiveData
        protected void d() {
            throw null;
        }

        @Override // androidx.lifecycle.LiveData
        protected void e() {
            throw null;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // androidx.lifecycle.LiveData
        public void f(m<? super D> mVar) {
            super.f(mVar);
        }

        @Override // androidx.lifecycle.l, androidx.lifecycle.LiveData
        public void g(D d2) {
            super.g(d2);
        }

        public String toString() {
            StringBuilder sb = new StringBuilder(64);
            sb.append("LoaderInfo{");
            sb.append(Integer.toHexString(System.identityHashCode(this)));
            sb.append(" #");
            sb.append(0);
            sb.append(" : ");
            androidx.core.app.b.a(null, sb);
            sb.append("}}");
            return sb.toString();
        }
    }

    /* renamed from: a.m.a.b$b, reason: collision with other inner class name */
    static class C0021b extends p {

        /* renamed from: c, reason: collision with root package name */
        private static final q.a f463c = new a();

        /* renamed from: b, reason: collision with root package name */
        private i<a> f464b = new i<>(10);

        /* renamed from: a.m.a.b$b$a */
        static class a implements q.a {
            a() {
            }

            @Override // androidx.lifecycle.q.a
            public <T extends p> T a(Class<T> cls) {
                return new C0021b();
            }
        }

        C0021b() {
        }

        static C0021b d(r rVar) {
            return (C0021b) new q(rVar, f463c).a(C0021b.class);
        }

        @Override // androidx.lifecycle.p
        protected void b() {
            int k = this.f464b.k();
            i<a> iVar = this.f464b;
            if (k <= 0) {
                iVar.b();
            } else {
                Objects.requireNonNull(iVar.l(0));
                throw null;
            }
        }

        public void c(String str, FileDescriptor fileDescriptor, PrintWriter printWriter, String[] strArr) {
            if (this.f464b.k() > 0) {
                printWriter.print(str);
                printWriter.println("Loaders:");
                String str2 = str + "    ";
                if (this.f464b.k() <= 0) {
                    return;
                }
                a l = this.f464b.l(0);
                printWriter.print(str);
                printWriter.print("  #");
                printWriter.print(this.f464b.h(0));
                printWriter.print(": ");
                printWriter.println(l.toString());
                printWriter.print(str2);
                printWriter.print("mId=");
                printWriter.print(0);
                printWriter.print(" mArgs=");
                printWriter.println((Object) null);
                printWriter.print(str2);
                printWriter.print("mLoader=");
                printWriter.println((Object) null);
                throw null;
            }
        }

        void e() {
            int k = this.f464b.k();
            for (int i = 0; i < k; i++) {
                Objects.requireNonNull(this.f464b.l(i));
            }
        }
    }

    b(g gVar, r rVar) {
        this.f461a = gVar;
        this.f462b = C0021b.d(rVar);
    }

    @Override // a.m.a.a
    @Deprecated
    public void a(String str, FileDescriptor fileDescriptor, PrintWriter printWriter, String[] strArr) {
        this.f462b.c(str, fileDescriptor, printWriter, strArr);
    }

    @Override // a.m.a.a
    public void c() {
        this.f462b.e();
    }

    public String toString() {
        StringBuilder sb = new StringBuilder(128);
        sb.append("LoaderManager{");
        sb.append(Integer.toHexString(System.identityHashCode(this)));
        sb.append(" in ");
        androidx.core.app.b.a(this.f461a, sb);
        sb.append("}}");
        return sb.toString();
    }
}
