package a.f.a.i.a;

/* loaded from: classes.dex */
public class n implements m {

    /* renamed from: a, reason: collision with root package name */
    private float f177a;

    /* renamed from: b, reason: collision with root package name */
    private float f178b;

    /* renamed from: c, reason: collision with root package name */
    private float f179c;

    /* renamed from: d, reason: collision with root package name */
    private float f180d;
    private float e;
    private float f;
    private float g;
    private float h;
    private float i;
    private int j;
    private boolean k = false;
    private float l;
    private float m;

    private void e(float f, float f2, float f3, float f4, float f5) {
        if (f == 0.0f) {
            f = 1.0E-4f;
        }
        this.f177a = f;
        float f6 = f / f3;
        float f7 = (f6 * f) / 2.0f;
        if (f < 0.0f) {
            float sqrt = (float) Math.sqrt((f2 - ((((-f) / f3) * f) / 2.0f)) * f3);
            if (sqrt < f4) {
                this.j = 2;
                this.f177a = f;
                this.f178b = sqrt;
                this.f179c = 0.0f;
                float f8 = (sqrt - f) / f3;
                this.f180d = f8;
                this.e = sqrt / f3;
                this.g = ((f + sqrt) * f8) / 2.0f;
                this.h = f2;
                this.i = f2;
                return;
            }
            this.j = 3;
            this.f177a = f;
            this.f178b = f4;
            this.f179c = f4;
            float f9 = (f4 - f) / f3;
            this.f180d = f9;
            float f10 = f4 / f3;
            this.f = f10;
            float f11 = ((f + f4) * f9) / 2.0f;
            float f12 = (f10 * f4) / 2.0f;
            this.e = ((f2 - f11) - f12) / f4;
            this.g = f11;
            this.h = f2 - f12;
            this.i = f2;
            return;
        }
        if (f7 >= f2) {
            this.j = 1;
            this.f177a = f;
            this.f178b = 0.0f;
            this.g = f2;
            this.f180d = (2.0f * f2) / f;
            return;
        }
        float f13 = f2 - f7;
        float f14 = f13 / f;
        if (f14 + f6 < f5) {
            this.j = 2;
            this.f177a = f;
            this.f178b = f;
            this.f179c = 0.0f;
            this.g = f13;
            this.h = f2;
            this.f180d = f14;
            this.e = f6;
            return;
        }
        float sqrt2 = (float) Math.sqrt(((f * f) / 2.0f) + (f3 * f2));
        float f15 = (sqrt2 - f) / f3;
        this.f180d = f15;
        float f16 = sqrt2 / f3;
        this.e = f16;
        if (sqrt2 < f4) {
            this.j = 2;
            this.f177a = f;
            this.f178b = sqrt2;
            this.f179c = 0.0f;
            this.f180d = f15;
            this.e = f16;
            this.g = ((f + sqrt2) * f15) / 2.0f;
            this.h = f2;
            return;
        }
        this.j = 3;
        this.f177a = f;
        this.f178b = f4;
        this.f179c = f4;
        float f17 = (f4 - f) / f3;
        this.f180d = f17;
        float f18 = f4 / f3;
        this.f = f18;
        float f19 = ((f + f4) * f17) / 2.0f;
        float f20 = (f18 * f4) / 2.0f;
        this.e = ((f2 - f19) - f20) / f4;
        this.g = f19;
        this.h = f2 - f20;
        this.i = f2;
    }

    @Override // a.f.a.i.a.m
    public boolean a() {
        return b() < 1.0E-5f && Math.abs(this.i - this.m) < 1.0E-5f;
    }

    @Override // a.f.a.i.a.m
    public float b() {
        return this.k ? -d(this.m) : d(this.m);
    }

    public void c(float f, float f2, float f3, float f4, float f5, float f6) {
        float f7;
        n nVar;
        float f8;
        this.l = f;
        boolean z = f > f2;
        this.k = z;
        if (z) {
            f7 = f - f2;
            nVar = this;
            f8 = -f3;
        } else {
            f7 = f2 - f;
            nVar = this;
            f8 = f3;
        }
        nVar.e(f8, f7, f5, f6, f4);
    }

    public float d(float f) {
        float f2 = this.f180d;
        if (f <= f2) {
            float f3 = this.f177a;
            return (((this.f178b - f3) * f) / f2) + f3;
        }
        int i = this.j;
        if (i == 1) {
            return 0.0f;
        }
        float f4 = f - f2;
        float f5 = this.e;
        if (f4 < f5) {
            float f6 = this.f178b;
            return (((this.f179c - f6) * f4) / f5) + f6;
        }
        if (i == 2) {
            return this.h;
        }
        float f7 = f4 - f5;
        float f8 = this.f;
        if (f7 >= f8) {
            return this.i;
        }
        float f9 = this.f179c;
        return f9 - ((f7 * f9) / f8);
    }

    @Override // a.f.a.i.a.m
    public float getInterpolation(float f) {
        float f2;
        float f3 = this.f180d;
        if (f <= f3) {
            float f4 = this.f177a;
            f2 = ((((this.f178b - f4) * f) * f) / (f3 * 2.0f)) + (f4 * f);
        } else {
            int i = this.j;
            if (i == 1) {
                f2 = this.g;
            } else {
                float f5 = f - f3;
                float f6 = this.e;
                if (f5 < f6) {
                    float f7 = this.g;
                    float f8 = this.f178b;
                    f2 = ((((this.f179c - f8) * f5) * f5) / (f6 * 2.0f)) + (f8 * f5) + f7;
                } else if (i == 2) {
                    f2 = this.h;
                } else {
                    float f9 = f5 - f6;
                    float f10 = this.f;
                    if (f9 <= f10) {
                        float f11 = this.h;
                        float f12 = this.f179c * f9;
                        f2 = (f11 + f12) - ((f12 * f9) / (f10 * 2.0f));
                    } else {
                        f2 = this.i;
                    }
                }
            }
        }
        this.m = f;
        boolean z = this.k;
        float f13 = this.l;
        return z ? f13 - f2 : f13 + f2;
    }
}
