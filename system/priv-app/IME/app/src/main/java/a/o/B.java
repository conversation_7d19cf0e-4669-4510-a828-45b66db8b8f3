package a.o;

import android.view.View;
import android.view.WindowId;

/* loaded from: classes.dex */
class B implements C {

    /* renamed from: a, reason: collision with root package name */
    private final WindowId f474a;

    B(View view) {
        this.f474a = view.getWindowId();
    }

    public boolean equals(Object obj) {
        return (obj instanceof B) && ((B) obj).f474a.equals(this.f474a);
    }

    public int hashCode() {
        return this.f474a.hashCode();
    }
}
