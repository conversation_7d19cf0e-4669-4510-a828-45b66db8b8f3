package a.h.h;

import a.h.h.x.b;
import android.os.Bundle;
import android.text.style.ClickableSpan;
import android.util.SparseArray;
import android.view.View;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityNodeInfo;
import android.view.accessibility.AccessibilityNodeProvider;
import java.lang.ref.WeakReference;
import java.util.Collections;
import java.util.List;
import org.libpag.R;

/* loaded from: classes.dex */
public class a {

    /* renamed from: c, reason: collision with root package name */
    private static final View.AccessibilityDelegate f358c = new View.AccessibilityDelegate();

    /* renamed from: a, reason: collision with root package name */
    private final View.AccessibilityDelegate f359a;

    /* renamed from: b, reason: collision with root package name */
    private final View.AccessibilityDelegate f360b;

    /* renamed from: a.h.h.a$a, reason: collision with other inner class name */
    static final class C0009a extends View.AccessibilityDelegate {

        /* renamed from: a, reason: collision with root package name */
        final a f361a;

        C0009a(a aVar) {
            this.f361a = aVar;
        }

        @Override // android.view.View.AccessibilityDelegate
        public boolean dispatchPopulateAccessibilityEvent(View view, AccessibilityEvent accessibilityEvent) {
            return this.f361a.a(view, accessibilityEvent);
        }

        @Override // android.view.View.AccessibilityDelegate
        public AccessibilityNodeProvider getAccessibilityNodeProvider(View view) {
            a.h.h.x.c b2 = this.f361a.b(view);
            if (b2 != null) {
                return (AccessibilityNodeProvider) b2.c();
            }
            return null;
        }

        @Override // android.view.View.AccessibilityDelegate
        public void onInitializeAccessibilityEvent(View view, AccessibilityEvent accessibilityEvent) {
            this.f361a.d(view, accessibilityEvent);
        }

        @Override // android.view.View.AccessibilityDelegate
        public void onInitializeAccessibilityNodeInfo(View view, AccessibilityNodeInfo accessibilityNodeInfo) {
            a.h.h.x.b s0 = a.h.h.x.b.s0(accessibilityNodeInfo);
            int i = q.e;
            Boolean d2 = new m(R.id.tag_screen_reader_focusable, Boolean.class, 28).d(view);
            s0.h0(d2 == null ? false : d2.booleanValue());
            Boolean d3 = new p(R.id.tag_accessibility_heading, Boolean.class, 28).d(view);
            s0.X(d3 == null ? false : d3.booleanValue());
            s0.c0(q.f(view));
            s0.n0(new o(R.id.tag_state_description, CharSequence.class, 64, 30).d(view));
            this.f361a.e(view, s0);
            accessibilityNodeInfo.getText();
            List list = (List) view.getTag(R.id.tag_accessibility_actions);
            if (list == null) {
                list = Collections.emptyList();
            }
            for (int i2 = 0; i2 < list.size(); i2++) {
                s0.b((b.a) list.get(i2));
            }
        }

        @Override // android.view.View.AccessibilityDelegate
        public void onPopulateAccessibilityEvent(View view, AccessibilityEvent accessibilityEvent) {
            this.f361a.f(view, accessibilityEvent);
        }

        @Override // android.view.View.AccessibilityDelegate
        public boolean onRequestSendAccessibilityEvent(ViewGroup viewGroup, View view, AccessibilityEvent accessibilityEvent) {
            return this.f361a.g(viewGroup, view, accessibilityEvent);
        }

        @Override // android.view.View.AccessibilityDelegate
        public boolean performAccessibilityAction(View view, int i, Bundle bundle) {
            return this.f361a.h(view, i, bundle);
        }

        @Override // android.view.View.AccessibilityDelegate
        public void sendAccessibilityEvent(View view, int i) {
            this.f361a.i(view, i);
        }

        @Override // android.view.View.AccessibilityDelegate
        public void sendAccessibilityEventUnchecked(View view, AccessibilityEvent accessibilityEvent) {
            this.f361a.j(view, accessibilityEvent);
        }
    }

    public a() {
        this.f359a = f358c;
        this.f360b = new C0009a(this);
    }

    public a(View.AccessibilityDelegate accessibilityDelegate) {
        this.f359a = accessibilityDelegate;
        this.f360b = new C0009a(this);
    }

    public boolean a(View view, AccessibilityEvent accessibilityEvent) {
        return this.f359a.dispatchPopulateAccessibilityEvent(view, accessibilityEvent);
    }

    public a.h.h.x.c b(View view) {
        AccessibilityNodeProvider accessibilityNodeProvider = this.f359a.getAccessibilityNodeProvider(view);
        if (accessibilityNodeProvider != null) {
            return new a.h.h.x.c(accessibilityNodeProvider);
        }
        return null;
    }

    View.AccessibilityDelegate c() {
        return this.f360b;
    }

    public void d(View view, AccessibilityEvent accessibilityEvent) {
        this.f359a.onInitializeAccessibilityEvent(view, accessibilityEvent);
    }

    public void e(View view, a.h.h.x.b bVar) {
        this.f359a.onInitializeAccessibilityNodeInfo(view, bVar.r0());
    }

    public void f(View view, AccessibilityEvent accessibilityEvent) {
        this.f359a.onPopulateAccessibilityEvent(view, accessibilityEvent);
    }

    public boolean g(ViewGroup viewGroup, View view, AccessibilityEvent accessibilityEvent) {
        return this.f359a.onRequestSendAccessibilityEvent(viewGroup, view, accessibilityEvent);
    }

    public boolean h(View view, int i, Bundle bundle) {
        boolean z;
        WeakReference weakReference;
        boolean z2;
        List list = (List) view.getTag(R.id.tag_accessibility_actions);
        if (list == null) {
            list = Collections.emptyList();
        }
        boolean z3 = false;
        int i2 = 0;
        while (true) {
            if (i2 >= list.size()) {
                z = false;
                break;
            }
            b.a aVar = (b.a) list.get(i2);
            if (aVar.b() == i) {
                z = aVar.c(view, bundle);
                break;
            }
            i2++;
        }
        if (!z) {
            z = this.f359a.performAccessibilityAction(view, i, bundle);
        }
        if (z || i != R.id.accessibility_action_clickable_span) {
            return z;
        }
        int i3 = bundle.getInt("ACCESSIBILITY_CLICKABLE_SPAN_ID", -1);
        SparseArray sparseArray = (SparseArray) view.getTag(R.id.tag_accessibility_clickable_spans);
        if (sparseArray != null && (weakReference = (WeakReference) sparseArray.get(i3)) != null) {
            ClickableSpan clickableSpan = (ClickableSpan) weakReference.get();
            if (clickableSpan != null) {
                ClickableSpan[] l = a.h.h.x.b.l(view.createAccessibilityNodeInfo().getText());
                for (int i4 = 0; l != null && i4 < l.length; i4++) {
                    if (clickableSpan.equals(l[i4])) {
                        z2 = true;
                        break;
                    }
                }
            }
            z2 = false;
            if (z2) {
                clickableSpan.onClick(view);
                z3 = true;
            }
        }
        return z3;
    }

    public void i(View view, int i) {
        this.f359a.sendAccessibilityEvent(view, i);
    }

    public void j(View view, AccessibilityEvent accessibilityEvent) {
        this.f359a.sendAccessibilityEventUnchecked(view, accessibilityEvent);
    }
}
