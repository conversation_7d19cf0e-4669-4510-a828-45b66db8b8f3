package a.j.b;

import a.e.i;
import a.h.h.q;
import a.j.b.b;
import android.graphics.Rect;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewParent;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityManager;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/* loaded from: classes.dex */
public abstract class a extends a.h.h.a {
    private static final Rect n = new Rect(Integer.MAX_VALUE, Integer.MAX_VALUE, RecyclerView.UNDEFINED_DURATION, RecyclerView.UNDEFINED_DURATION);
    private static final b.a<a.h.h.x.b> o = new C0015a();
    private static final b.InterfaceC0016b<i<a.h.h.x.b>, a.h.h.x.b> p = new b();
    private final AccessibilityManager h;
    private final View i;
    private c j;

    /* renamed from: d, reason: collision with root package name */
    private final Rect f426d = new Rect();
    private final Rect e = new Rect();
    private final Rect f = new Rect();
    private final int[] g = new int[2];
    int k = RecyclerView.UNDEFINED_DURATION;
    int l = RecyclerView.UNDEFINED_DURATION;
    private int m = RecyclerView.UNDEFINED_DURATION;

    /* renamed from: a.j.b.a$a, reason: collision with other inner class name */
    static class C0015a implements b.a<a.h.h.x.b> {
        C0015a() {
        }
    }

    static class b implements b.InterfaceC0016b<i<a.h.h.x.b>, a.h.h.x.b> {
        b() {
        }
    }

    private class c extends a.h.h.x.c {
        c() {
        }

        @Override // a.h.h.x.c
        public a.h.h.x.b a(int i) {
            return a.h.h.x.b.E(a.this.w(i));
        }

        @Override // a.h.h.x.c
        public a.h.h.x.b b(int i) {
            int i2 = i == 2 ? a.this.k : a.this.l;
            if (i2 == Integer.MIN_VALUE) {
                return null;
            }
            return a.h.h.x.b.E(a.this.w(i2));
        }

        @Override // a.h.h.x.c
        public boolean d(int i, int i2, Bundle bundle) {
            return a.this.C(i, i2, bundle);
        }
    }

    public a(View view) {
        if (view == null) {
            throw new IllegalArgumentException("View may not be null");
        }
        this.i = view;
        this.h = (AccessibilityManager) view.getContext().getSystemService("accessibility");
        view.setFocusable(true);
        int i = q.e;
        if (view.getImportantForAccessibility() == 0) {
            view.setImportantForAccessibility(1);
        }
    }

    private void F(int i) {
        int i2 = this.m;
        if (i2 == i) {
            return;
        }
        this.m = i;
        E(i, 128);
        E(i2, 256);
    }

    private boolean k(int i) {
        if (this.k != i) {
            return false;
        }
        this.k = RecyclerView.UNDEFINED_DURATION;
        this.i.invalidate();
        E(i, 65536);
        return true;
    }

    private AccessibilityEvent m(int i, int i2) {
        if (i == -1) {
            AccessibilityEvent obtain = AccessibilityEvent.obtain(i2);
            this.i.onInitializeAccessibilityEvent(obtain);
            return obtain;
        }
        AccessibilityEvent obtain2 = AccessibilityEvent.obtain(i2);
        a.h.h.x.b w = w(i);
        obtain2.getText().add(w.p());
        obtain2.setContentDescription(w.m());
        obtain2.setScrollable(w.y());
        obtain2.setPassword(w.x());
        obtain2.setEnabled(w.t());
        obtain2.setChecked(w.r());
        if (obtain2.getText().isEmpty() && obtain2.getContentDescription() == null) {
            throw new RuntimeException("Callbacks must add text or a content description in populateEventForVirtualViewId()");
        }
        obtain2.setClassName(w.k());
        obtain2.setSource(this.i, i);
        obtain2.setPackageName(this.i.getContext().getPackageName());
        return obtain2;
    }

    private a.h.h.x.b n(int i) {
        a.h.h.x.b C = a.h.h.x.b.C();
        C.T(true);
        C.V(true);
        C.O("android.view.View");
        Rect rect = n;
        C.J(rect);
        C.K(rect);
        C.d0(this.i);
        A(i, C);
        if (C.p() == null && C.m() == null) {
            throw new RuntimeException("Callbacks must add text or a content description in populateNodeForVirtualViewId()");
        }
        C.h(this.e);
        if (this.e.equals(rect)) {
            throw new RuntimeException("Callbacks must set parent bounds in populateNodeForVirtualViewId()");
        }
        int g = C.g();
        if ((g & 64) != 0) {
            throw new RuntimeException("Callbacks must not add ACTION_ACCESSIBILITY_FOCUS in populateNodeForVirtualViewId()");
        }
        if ((g & 128) != 0) {
            throw new RuntimeException("Callbacks must not add ACTION_CLEAR_ACCESSIBILITY_FOCUS in populateNodeForVirtualViewId()");
        }
        C.b0(this.i.getContext().getPackageName());
        C.m0(this.i, i);
        boolean z = false;
        if (this.k == i) {
            C.I(true);
            C.a(128);
        } else {
            C.I(false);
            C.a(64);
        }
        boolean z2 = this.l == i;
        if (z2) {
            C.a(2);
        } else if (C.u()) {
            C.a(1);
        }
        C.W(z2);
        this.i.getLocationOnScreen(this.g);
        C.i(this.f426d);
        if (this.f426d.equals(rect)) {
            C.h(this.f426d);
            if (C.f406b != -1) {
                a.h.h.x.b C2 = a.h.h.x.b.C();
                for (int i2 = C.f406b; i2 != -1; i2 = C2.f406b) {
                    C2.e0(this.i, -1);
                    C2.J(n);
                    A(i2, C2);
                    C2.h(this.e);
                    Rect rect2 = this.f426d;
                    Rect rect3 = this.e;
                    rect2.offset(rect3.left, rect3.top);
                }
                C2.G();
            }
            this.f426d.offset(this.g[0] - this.i.getScrollX(), this.g[1] - this.i.getScrollY());
        }
        if (this.i.getLocalVisibleRect(this.f)) {
            this.f.offset(this.g[0] - this.i.getScrollX(), this.g[1] - this.i.getScrollY());
            if (this.f426d.intersect(this.f)) {
                C.K(this.f426d);
                Rect rect4 = this.f426d;
                if (rect4 != null && !rect4.isEmpty() && this.i.getWindowVisibility() == 0) {
                    View view = this.i;
                    while (true) {
                        Object parent = view.getParent();
                        if (parent instanceof View) {
                            view = (View) parent;
                            if (view.getAlpha() <= 0.0f || view.getVisibility() != 0) {
                                break;
                            }
                        } else if (parent != null) {
                            z = true;
                        }
                    }
                }
                if (z) {
                    C.q0(true);
                }
            }
        }
        return C;
    }

    private boolean v(int i, Rect rect) {
        a.h.h.x.b bVar;
        ArrayList arrayList = new ArrayList();
        t(arrayList);
        i iVar = new i(10);
        for (int i2 = 0; i2 < arrayList.size(); i2++) {
            iVar.i(i2, n(i2));
        }
        int i3 = this.l;
        Object obj = null;
        int i4 = RecyclerView.UNDEFINED_DURATION;
        a.h.h.x.b bVar2 = i3 == Integer.MIN_VALUE ? null : (a.h.h.x.b) iVar.e(i3);
        if (i == 1 || i == 2) {
            View view = this.i;
            int i5 = q.e;
            boolean z = view.getLayoutDirection() == 1;
            b.InterfaceC0016b<i<a.h.h.x.b>, a.h.h.x.b> interfaceC0016b = p;
            b.a<a.h.h.x.b> aVar = o;
            Objects.requireNonNull((b) interfaceC0016b);
            int k = iVar.k();
            ArrayList arrayList2 = new ArrayList(k);
            for (int i6 = 0; i6 < k; i6++) {
                arrayList2.add((a.h.h.x.b) iVar.l(i6));
            }
            Collections.sort(arrayList2, new b.c(z, aVar));
            if (i == 1) {
                int size = arrayList2.size();
                if (bVar2 != null) {
                    size = arrayList2.indexOf(bVar2);
                }
                int i7 = size - 1;
                if (i7 >= 0) {
                    obj = arrayList2.get(i7);
                }
            } else {
                if (i != 2) {
                    throw new IllegalArgumentException("direction must be one of {FOCUS_FORWARD, FOCUS_BACKWARD}.");
                }
                int size2 = arrayList2.size();
                int lastIndexOf = (bVar2 != null ? arrayList2.lastIndexOf(bVar2) : -1) + 1;
                if (lastIndexOf < size2) {
                    obj = arrayList2.get(lastIndexOf);
                }
            }
            bVar = (a.h.h.x.b) obj;
        } else {
            if (i != 17 && i != 33 && i != 66 && i != 130) {
                throw new IllegalArgumentException("direction must be one of {FOCUS_FORWARD, FOCUS_BACKWARD, FOCUS_UP, FOCUS_DOWN, FOCUS_LEFT, FOCUS_RIGHT}.");
            }
            Rect rect2 = new Rect();
            int i8 = this.l;
            if (i8 != Integer.MIN_VALUE) {
                w(i8).h(rect2);
            } else if (rect != null) {
                rect2.set(rect);
            } else {
                View view2 = this.i;
                int width = view2.getWidth();
                int height = view2.getHeight();
                if (i == 17) {
                    rect2.set(width, 0, width, height);
                } else if (i == 33) {
                    rect2.set(0, height, width, height);
                } else if (i == 66) {
                    rect2.set(-1, 0, -1, height);
                } else {
                    if (i != 130) {
                        throw new IllegalArgumentException("direction must be one of {FOCUS_UP, FOCUS_DOWN, FOCUS_LEFT, FOCUS_RIGHT}.");
                    }
                    rect2.set(0, -1, width, -1);
                }
            }
            bVar = (a.h.h.x.b) a.j.b.b.c(iVar, p, o, bVar2, rect2, i);
        }
        if (bVar != null) {
            i4 = iVar.h(iVar.g(bVar));
        }
        return D(i4);
    }

    protected abstract void A(int i, a.h.h.x.b bVar);

    protected void B(int i, boolean z) {
    }

    boolean C(int i, int i2, Bundle bundle) {
        int i3;
        if (i == -1) {
            View view = this.i;
            int i4 = q.e;
            return view.performAccessibilityAction(i2, bundle);
        }
        boolean z = true;
        if (i2 == 1) {
            return D(i);
        }
        if (i2 == 2) {
            return l(i);
        }
        if (i2 != 64) {
            return i2 != 128 ? y(i, i2, bundle) : k(i);
        }
        if (this.h.isEnabled() && this.h.isTouchExplorationEnabled() && (i3 = this.k) != i) {
            if (i3 != Integer.MIN_VALUE) {
                k(i3);
            }
            this.k = i;
            this.i.invalidate();
            E(i, 32768);
        } else {
            z = false;
        }
        return z;
    }

    public final boolean D(int i) {
        int i2;
        if ((!this.i.isFocused() && !this.i.requestFocus()) || (i2 = this.l) == i) {
            return false;
        }
        if (i2 != Integer.MIN_VALUE) {
            l(i2);
        }
        this.l = i;
        B(i, true);
        E(i, 8);
        return true;
    }

    public final boolean E(int i, int i2) {
        ViewParent parent;
        if (i == Integer.MIN_VALUE || !this.h.isEnabled() || (parent = this.i.getParent()) == null) {
            return false;
        }
        return parent.requestSendAccessibilityEvent(this.i, m(i, i2));
    }

    @Override // a.h.h.a
    public a.h.h.x.c b(View view) {
        if (this.j == null) {
            this.j = new c();
        }
        return this.j;
    }

    @Override // a.h.h.a
    public void d(View view, AccessibilityEvent accessibilityEvent) {
        super.d(view, accessibilityEvent);
    }

    @Override // a.h.h.a
    public void e(View view, a.h.h.x.b bVar) {
        super.e(view, bVar);
        z(bVar);
    }

    public final boolean l(int i) {
        if (this.l != i) {
            return false;
        }
        this.l = RecyclerView.UNDEFINED_DURATION;
        B(i, false);
        E(i, 8);
        return true;
    }

    public final boolean o(MotionEvent motionEvent) {
        int i;
        if (!this.h.isEnabled() || !this.h.isTouchExplorationEnabled()) {
            return false;
        }
        int action = motionEvent.getAction();
        if (action == 7 || action == 9) {
            int s = s(motionEvent.getX(), motionEvent.getY());
            int i2 = this.m;
            if (i2 != s) {
                this.m = s;
                E(s, 128);
                E(i2, 256);
            }
            return s != Integer.MIN_VALUE;
        }
        if (action != 10 || (i = this.m) == Integer.MIN_VALUE) {
            return false;
        }
        if (i != Integer.MIN_VALUE) {
            this.m = RecyclerView.UNDEFINED_DURATION;
            E(RecyclerView.UNDEFINED_DURATION, 128);
            E(i, 256);
        }
        return true;
    }

    public final boolean p(KeyEvent keyEvent) {
        int i = 0;
        if (keyEvent.getAction() == 1) {
            return false;
        }
        int keyCode = keyEvent.getKeyCode();
        if (keyCode == 61) {
            if (keyEvent.hasNoModifiers()) {
                return v(2, null);
            }
            if (keyEvent.hasModifiers(1)) {
                return v(1, null);
            }
            return false;
        }
        int i2 = 66;
        if (keyCode != 66) {
            switch (keyCode) {
                case 19:
                case 20:
                case 21:
                case 22:
                    if (!keyEvent.hasNoModifiers()) {
                        return false;
                    }
                    if (keyCode == 19) {
                        i2 = 33;
                    } else if (keyCode == 21) {
                        i2 = 17;
                    } else if (keyCode != 22) {
                        i2 = 130;
                    }
                    int repeatCount = keyEvent.getRepeatCount() + 1;
                    boolean z = false;
                    while (i < repeatCount && v(i2, null)) {
                        i++;
                        z = true;
                    }
                    return z;
                case 23:
                    break;
                default:
                    return false;
            }
        }
        if (!keyEvent.hasNoModifiers() || keyEvent.getRepeatCount() != 0) {
            return false;
        }
        int i3 = this.l;
        if (i3 != Integer.MIN_VALUE) {
            y(i3, 16, null);
        }
        return true;
    }

    public final int q() {
        return this.k;
    }

    public final int r() {
        return this.l;
    }

    protected abstract int s(float f, float f2);

    protected abstract void t(List<Integer> list);

    public final void u(int i) {
        ViewParent parent;
        if (i == Integer.MIN_VALUE || !this.h.isEnabled() || (parent = this.i.getParent()) == null) {
            return;
        }
        AccessibilityEvent m = m(i, 2048);
        m.setContentChangeTypes(0);
        parent.requestSendAccessibilityEvent(this.i, m);
    }

    a.h.h.x.b w(int i) {
        if (i != -1) {
            return n(i);
        }
        a.h.h.x.b D = a.h.h.x.b.D(this.i);
        View view = this.i;
        int i2 = q.e;
        view.onInitializeAccessibilityNodeInfo(D.r0());
        ArrayList arrayList = new ArrayList();
        t(arrayList);
        if (D.j() > 0 && arrayList.size() > 0) {
            throw new RuntimeException("Views cannot have both real and virtual children");
        }
        int size = arrayList.size();
        for (int i3 = 0; i3 < size; i3++) {
            D.d(this.i, ((Integer) arrayList.get(i3)).intValue());
        }
        return D;
    }

    public final void x(boolean z, int i, Rect rect) {
        int i2 = this.l;
        if (i2 != Integer.MIN_VALUE) {
            l(i2);
        }
        if (z) {
            v(i, rect);
        }
    }

    protected abstract boolean y(int i, int i2, Bundle bundle);

    protected void z(a.h.h.x.b bVar) {
    }
}
