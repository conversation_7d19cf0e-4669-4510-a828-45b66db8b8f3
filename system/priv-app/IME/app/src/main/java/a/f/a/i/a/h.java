package a.f.a.i.a;

import androidx.constraintlayout.motion.widget.MotionLayout;
import java.util.Arrays;

/* loaded from: classes.dex */
public class h {

    /* renamed from: a, reason: collision with root package name */
    float[] f163a = new float[0];

    /* renamed from: b, reason: collision with root package name */
    double[] f164b = new double[0];

    /* renamed from: c, reason: collision with root package name */
    double[] f165c;

    /* renamed from: d, reason: collision with root package name */
    String f166d;
    g e;
    int f;

    public void a(double d2, float f) {
        int length = this.f163a.length + 1;
        int binarySearch = Arrays.binarySearch(this.f164b, d2);
        if (binarySearch < 0) {
            binarySearch = (-binarySearch) - 1;
        }
        this.f164b = Arrays.copyOf(this.f164b, length);
        this.f163a = Arrays.copyOf(this.f163a, length);
        this.f165c = new double[length];
        double[] dArr = this.f164b;
        System.arraycopy(dArr, binarySearch, dArr, binarySearch + 1, (length - binarySearch) - 1);
        this.f164b[binarySearch] = d2;
        this.f163a[binarySearch] = f;
    }

    double b(double d2) {
        if (d2 < 0.0d) {
            d2 = 0.0d;
        } else if (d2 > 1.0d) {
            d2 = 1.0d;
        }
        int binarySearch = Arrays.binarySearch(this.f164b, d2);
        if (binarySearch > 0) {
            return 1.0d;
        }
        if (binarySearch == 0) {
            return 0.0d;
        }
        int i = (-binarySearch) - 1;
        float[] fArr = this.f163a;
        int i2 = i - 1;
        double d3 = fArr[i] - fArr[i2];
        double[] dArr = this.f164b;
        double d4 = d3 / (dArr[i] - dArr[i2]);
        return ((((d2 * d2) - (dArr[i2] * dArr[i2])) * d4) / 2.0d) + ((d2 - dArr[i2]) * (fArr[i2] - (dArr[i2] * d4))) + this.f165c[i2];
    }

    public double c(double d2, double d3) {
        double b2 = b(d2) + d3;
        switch (this.f) {
            case 1:
                return Math.signum(0.5d - (b2 % 1.0d));
            case 2:
                return 1.0d - Math.abs((((b2 * 4.0d) + 1.0d) % 4.0d) - 2.0d);
            case 3:
                return (((b2 * 2.0d) + 1.0d) % 2.0d) - 1.0d;
            case 4:
                return 1.0d - (((b2 * 2.0d) + 1.0d) % 2.0d);
            case 5:
                return Math.cos((d3 + b2) * 6.283185307179586d);
            case 6:
                double abs = 1.0d - Math.abs(((b2 * 4.0d) % 4.0d) - 2.0d);
                return 1.0d - (abs * abs);
            case MotionLayout.TOUCH_UP_NEVER_TO_END /* 7 */:
                return this.e.b(b2 % 1.0d, 0);
            default:
                return Math.sin(6.283185307179586d * b2);
        }
    }

    public String toString() {
        StringBuilder j = b.b.a.a.a.j("pos =");
        j.append(Arrays.toString(this.f164b));
        j.append(" period=");
        j.append(Arrays.toString(this.f163a));
        return j.toString();
    }
}
