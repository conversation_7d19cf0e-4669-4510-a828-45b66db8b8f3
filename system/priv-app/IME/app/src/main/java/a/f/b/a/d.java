package a.f.b.a;

import android.util.Log;
import android.util.SparseArray;
import android.view.View;
import androidx.constraintlayout.motion.widget.MotionLayout;
import java.lang.reflect.Array;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/* loaded from: classes.dex */
public abstract class d extends a.f.a.i.a.j {

    static class a extends d {
        a() {
        }

        @Override // a.f.b.a.d
        public void f(View view, float f) {
            view.setAlpha(a(f));
        }
    }

    public static class b extends d {
        SparseArray<androidx.constraintlayout.widget.a> f;
        float[] g;

        public b(String str, SparseArray<androidx.constraintlayout.widget.a> sparseArray) {
            String str2 = str.split(",")[1];
            this.f = sparseArray;
        }

        @Override // a.f.a.i.a.j
        public void b(int i, float f) {
            throw new RuntimeException("don't call for custom attribute call setPoint(pos, ConstraintAttribute)");
        }

        @Override // a.f.a.i.a.j
        public void d(int i) {
            int size = this.f.size();
            int g = this.f.valueAt(0).g();
            double[] dArr = new double[size];
            this.g = new float[g];
            double[][] dArr2 = (double[][]) Array.newInstance((Class<?>) double.class, size, g);
            for (int i2 = 0; i2 < size; i2++) {
                int keyAt = this.f.keyAt(i2);
                androidx.constraintlayout.widget.a valueAt = this.f.valueAt(i2);
                dArr[i2] = keyAt * 0.01d;
                valueAt.e(this.g);
                int i3 = 0;
                while (true) {
                    if (i3 < this.g.length) {
                        dArr2[i2][i3] = r6[i3];
                        i3++;
                    }
                }
            }
            this.f168a = a.f.a.i.a.b.a(i, dArr, dArr2);
        }

        @Override // a.f.b.a.d
        public void f(View view, float f) {
            this.f168a.d(f, this.g);
            a.f.b.a.a.b(this.f.valueAt(0), view, this.g);
        }

        public void g(int i, androidx.constraintlayout.widget.a aVar) {
            this.f.append(i, aVar);
        }
    }

    static class c extends d {
        c() {
        }

        @Override // a.f.b.a.d
        public void f(View view, float f) {
            view.setElevation(a(f));
        }
    }

    /* renamed from: a.f.b.a.d$d, reason: collision with other inner class name */
    public static class C0005d extends d {
        @Override // a.f.b.a.d
        public void f(View view, float f) {
        }
    }

    static class e extends d {
        e() {
        }

        @Override // a.f.b.a.d
        public void f(View view, float f) {
            view.setPivotX(a(f));
        }
    }

    static class f extends d {
        f() {
        }

        @Override // a.f.b.a.d
        public void f(View view, float f) {
            view.setPivotY(a(f));
        }
    }

    static class g extends d {
        boolean f = false;

        g() {
        }

        @Override // a.f.b.a.d
        public void f(View view, float f) {
            if (view instanceof MotionLayout) {
                ((MotionLayout) view).setProgress(a(f));
                return;
            }
            if (this.f) {
                return;
            }
            Method method = null;
            try {
                method = view.getClass().getMethod("setProgress", Float.TYPE);
            } catch (NoSuchMethodException unused) {
                this.f = true;
            }
            if (method != null) {
                try {
                    method.invoke(view, Float.valueOf(a(f)));
                } catch (IllegalAccessException | InvocationTargetException e) {
                    Log.e("ViewSpline", "unable to setProgress", e);
                }
            }
        }
    }

    static class h extends d {
        h() {
        }

        @Override // a.f.b.a.d
        public void f(View view, float f) {
            view.setRotation(a(f));
        }
    }

    static class i extends d {
        i() {
        }

        @Override // a.f.b.a.d
        public void f(View view, float f) {
            view.setRotationX(a(f));
        }
    }

    static class j extends d {
        j() {
        }

        @Override // a.f.b.a.d
        public void f(View view, float f) {
            view.setRotationY(a(f));
        }
    }

    static class k extends d {
        k() {
        }

        @Override // a.f.b.a.d
        public void f(View view, float f) {
            view.setScaleX(a(f));
        }
    }

    static class l extends d {
        l() {
        }

        @Override // a.f.b.a.d
        public void f(View view, float f) {
            view.setScaleY(a(f));
        }
    }

    static class m extends d {
        m() {
        }

        @Override // a.f.b.a.d
        public void f(View view, float f) {
            view.setTranslationX(a(f));
        }
    }

    static class n extends d {
        n() {
        }

        @Override // a.f.b.a.d
        public void f(View view, float f) {
            view.setTranslationY(a(f));
        }
    }

    static class o extends d {
        o() {
        }

        @Override // a.f.b.a.d
        public void f(View view, float f) {
            view.setTranslationZ(a(f));
        }
    }

    public static d e(String str) {
        switch (str) {
        }
        return new a();
    }

    public abstract void f(View view, float f2);
}
