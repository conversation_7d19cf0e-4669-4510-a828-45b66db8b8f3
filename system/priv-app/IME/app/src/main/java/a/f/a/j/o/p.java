package a.f.a.j.o;

import a.f.a.j.e;

/* loaded from: classes.dex */
public abstract class p implements d {

    /* renamed from: a, reason: collision with root package name */
    public int f243a;

    /* renamed from: b, reason: collision with root package name */
    a.f.a.j.e f244b;

    /* renamed from: c, reason: collision with root package name */
    m f245c;

    /* renamed from: d, reason: collision with root package name */
    protected e.a f246d;
    g e = new g(this);
    public int f = 0;
    boolean g = false;
    public f h = new f(this);
    public f i = new f(this);
    protected a j = a.NONE;

    enum a {
        NONE,
        START,
        END,
        CENTER
    }

    public p(a.f.a.j.e eVar) {
        this.f244b = eVar;
    }

    @Override // a.f.a.j.o.d
    public void a(d dVar) {
    }

    protected final void b(f fVar, f fVar2, int i) {
        fVar.l.add(fVar2);
        fVar.f = i;
        fVar2.k.add(fVar);
    }

    protected final void c(f fVar, f fVar2, int i, g gVar) {
        fVar.l.add(fVar2);
        fVar.l.add(this.e);
        fVar.h = i;
        fVar.i = gVar;
        fVar2.k.add(fVar);
        gVar.k.add(fVar);
    }

    abstract void d();

    abstract void e();

    abstract void f();

    protected final int g(int i, int i2) {
        int max;
        a.f.a.j.e eVar = this.f244b;
        if (i2 == 0) {
            int i3 = eVar.v;
            max = Math.max(eVar.u, i);
            if (i3 > 0) {
                max = Math.min(i3, i);
            }
            if (max == i) {
                return i;
            }
        } else {
            int i4 = eVar.y;
            max = Math.max(eVar.x, i);
            if (i4 > 0) {
                max = Math.min(i4, i);
            }
            if (max == i) {
                return i;
            }
        }
        return max;
    }

    protected final f h(a.f.a.j.d dVar) {
        p pVar;
        p pVar2;
        a.f.a.j.d dVar2 = dVar.f;
        if (dVar2 == null) {
            return null;
        }
        a.f.a.j.e eVar = dVar2.f196d;
        int ordinal = dVar2.e.ordinal();
        if (ordinal == 1) {
            pVar = eVar.f204d;
        } else {
            if (ordinal != 2) {
                if (ordinal == 3) {
                    pVar2 = eVar.f204d;
                } else {
                    if (ordinal != 4) {
                        if (ordinal != 5) {
                            return null;
                        }
                        return eVar.e.k;
                    }
                    pVar2 = eVar.e;
                }
                return pVar2.i;
            }
            pVar = eVar.e;
        }
        return pVar.h;
    }

    protected final f i(a.f.a.j.d dVar, int i) {
        a.f.a.j.d dVar2 = dVar.f;
        if (dVar2 == null) {
            return null;
        }
        a.f.a.j.e eVar = dVar2.f196d;
        p pVar = i == 0 ? eVar.f204d : eVar.e;
        int ordinal = dVar2.e.ordinal();
        if (ordinal == 1 || ordinal == 2) {
            return pVar.h;
        }
        if (ordinal == 3 || ordinal == 4) {
            return pVar.i;
        }
        return null;
    }

    public long j() {
        if (this.e.j) {
            return r2.g;
        }
        return 0L;
    }

    public boolean k() {
        return this.g;
    }

    abstract boolean l();

    /* JADX WARN: Code restructure failed: missing block: B:24:0x0059, code lost:
    
        if (r14.f243a == 3) goto L52;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    protected void m(a.f.a.j.d r18, a.f.a.j.d r19, int r20) {
        /*
            Method dump skipped, instructions count: 255
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: a.f.a.j.o.p.m(a.f.a.j.d, a.f.a.j.d, int):void");
    }
}
