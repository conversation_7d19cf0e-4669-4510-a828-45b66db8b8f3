package a.f.a.j.o;

import java.util.ArrayList;
import java.util.Iterator;

/* loaded from: classes.dex */
public class c extends p {
    ArrayList<p> k;
    private int l;

    /* JADX WARN: Code restructure failed: missing block: B:11:0x0026, code lost:
    
        r4.add(r0);
        r3 = r3.G(r2.f);
     */
    /* JADX WARN: Code restructure failed: missing block: B:12:0x002f, code lost:
    
        if (r3 == null) goto L47;
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x0031, code lost:
    
        r4 = r2.k;
        r0 = r2.f;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x0035, code lost:
    
        if (r0 != 0) goto L46;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x003a, code lost:
    
        if (r0 != 1) goto L49;
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x003c, code lost:
    
        r0 = r3.e;
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x003f, code lost:
    
        r3 = r2.k.iterator();
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x0049, code lost:
    
        if (r3.hasNext() == false) goto L55;
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x004b, code lost:
    
        r4 = r3.next();
        r0 = r2.f;
     */
    /* JADX WARN: Code restructure failed: missing block: B:29:0x0053, code lost:
    
        if (r0 != 0) goto L54;
     */
    /* JADX WARN: Code restructure failed: missing block: B:31:0x005a, code lost:
    
        if (r0 != 1) goto L59;
     */
    /* JADX WARN: Code restructure failed: missing block: B:33:0x005c, code lost:
    
        r4.f244b.f203c = r2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:38:0x0055, code lost:
    
        r4.f244b.f202b = r2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:42:0x0063, code lost:
    
        if (r2.f != 0) goto L32;
     */
    /* JADX WARN: Code restructure failed: missing block: B:44:0x006f, code lost:
    
        if (((a.f.a.j.f) r2.f244b.X).k1() == false) goto L32;
     */
    /* JADX WARN: Code restructure failed: missing block: B:45:0x0071, code lost:
    
        r3 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:46:0x0074, code lost:
    
        if (r3 == false) goto L37;
     */
    /* JADX WARN: Code restructure failed: missing block: B:48:0x007c, code lost:
    
        if (r2.k.size() <= 1) goto L37;
     */
    /* JADX WARN: Code restructure failed: missing block: B:49:0x007e, code lost:
    
        r3 = r2.k;
        r2.f244b = r3.get(r3.size() - 1).f244b;
     */
    /* JADX WARN: Code restructure failed: missing block: B:51:0x0091, code lost:
    
        if (r2.f != 0) goto L40;
     */
    /* JADX WARN: Code restructure failed: missing block: B:52:0x0093, code lost:
    
        r3 = r2.f244b.y();
     */
    /* JADX WARN: Code restructure failed: missing block: B:53:0x00a0, code lost:
    
        r2.l = r3;
     */
    /* JADX WARN: Code restructure failed: missing block: B:54:0x00a2, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:56:0x009a, code lost:
    
        r3 = r2.f244b.N();
     */
    /* JADX WARN: Code restructure failed: missing block: B:57:0x0073, code lost:
    
        r3 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:58:0x0026, code lost:
    
        r0 = r3.e;
     */
    /* JADX WARN: Code restructure failed: missing block: B:59:0x0026, code lost:
    
        r0 = r3.f204d;
     */
    /* JADX WARN: Code restructure failed: missing block: B:8:0x0022, code lost:
    
        if (r0 == 1) goto L18;
     */
    /* JADX WARN: Code restructure failed: missing block: B:9:0x0025, code lost:
    
        r0 = null;
     */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:13:0x003a -> B:8:0x0025). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public c(a.f.a.j.e r3, int r4) {
        /*
            r2 = this;
            r2.<init>(r3)
            java.util.ArrayList r3 = new java.util.ArrayList
            r3.<init>()
            r2.k = r3
            r2.f = r4
            a.f.a.j.e r3 = r2.f244b
        Le:
            int r4 = r2.f
            a.f.a.j.e r4 = r3.H(r4)
            if (r4 == 0) goto L18
            r3 = r4
            goto Le
        L18:
            r2.f244b = r3
            java.util.ArrayList<a.f.a.j.o.p> r4 = r2.k
            int r0 = r2.f
            r1 = 1
            if (r0 != 0) goto L22
            goto L37
        L22:
            if (r0 != r1) goto L25
            goto L3c
        L25:
            r0 = 0
        L26:
            r4.add(r0)
            int r4 = r2.f
            a.f.a.j.e r3 = r3.G(r4)
            if (r3 == 0) goto L3f
            java.util.ArrayList<a.f.a.j.o.p> r4 = r2.k
            int r0 = r2.f
            if (r0 != 0) goto L3a
        L37:
            a.f.a.j.o.l r0 = r3.f204d
            goto L26
        L3a:
            if (r0 != r1) goto L25
        L3c:
            a.f.a.j.o.n r0 = r3.e
            goto L26
        L3f:
            java.util.ArrayList<a.f.a.j.o.p> r3 = r2.k
            java.util.Iterator r3 = r3.iterator()
        L45:
            boolean r4 = r3.hasNext()
            if (r4 == 0) goto L61
            java.lang.Object r4 = r3.next()
            a.f.a.j.o.p r4 = (a.f.a.j.o.p) r4
            int r0 = r2.f
            if (r0 != 0) goto L5a
            a.f.a.j.e r4 = r4.f244b
            r4.f202b = r2
            goto L45
        L5a:
            if (r0 != r1) goto L45
            a.f.a.j.e r4 = r4.f244b
            r4.f203c = r2
            goto L45
        L61:
            int r3 = r2.f
            if (r3 != 0) goto L73
            a.f.a.j.e r3 = r2.f244b
            a.f.a.j.e r3 = r3.X
            a.f.a.j.f r3 = (a.f.a.j.f) r3
            boolean r3 = r3.k1()
            if (r3 == 0) goto L73
            r3 = r1
            goto L74
        L73:
            r3 = 0
        L74:
            if (r3 == 0) goto L8f
            java.util.ArrayList<a.f.a.j.o.p> r3 = r2.k
            int r3 = r3.size()
            if (r3 <= r1) goto L8f
            java.util.ArrayList<a.f.a.j.o.p> r3 = r2.k
            int r4 = r3.size()
            int r4 = r4 - r1
            java.lang.Object r3 = r3.get(r4)
            a.f.a.j.o.p r3 = (a.f.a.j.o.p) r3
            a.f.a.j.e r3 = r3.f244b
            r2.f244b = r3
        L8f:
            int r3 = r2.f
            if (r3 != 0) goto L9a
            a.f.a.j.e r3 = r2.f244b
            int r3 = r3.y()
            goto La0
        L9a:
            a.f.a.j.e r3 = r2.f244b
            int r3 = r3.N()
        La0:
            r2.l = r3
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: a.f.a.j.o.c.<init>(a.f.a.j.e, int):void");
    }

    private a.f.a.j.e n() {
        for (int i = 0; i < this.k.size(); i++) {
            p pVar = this.k.get(i);
            if (pVar.f244b.P() != 8) {
                return pVar.f244b;
            }
        }
        return null;
    }

    private a.f.a.j.e o() {
        for (int size = this.k.size() - 1; size >= 0; size--) {
            p pVar = this.k.get(size);
            if (pVar.f244b.P() != 8) {
                return pVar.f244b;
            }
        }
        return null;
    }

    /* JADX WARN: Code restructure failed: missing block: B:293:0x03d0, code lost:
    
        r10 = r10 - r8;
     */
    /* JADX WARN: Removed duplicated region for block: B:53:0x00d6  */
    /* JADX WARN: Removed duplicated region for block: B:63:0x00e8  */
    @Override // a.f.a.j.o.p, a.f.a.j.o.d
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void a(a.f.a.j.o.d r28) {
        /*
            Method dump skipped, instructions count: 1007
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: a.f.a.j.o.c.a(a.f.a.j.o.d):void");
    }

    /* JADX WARN: Code restructure failed: missing block: B:20:0x0076, code lost:
    
        if (r1 != null) goto L20;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x0078, code lost:
    
        r2 = r6.i;
        r2.l.add(r1);
        r2.f = -r0;
        r1.k.add(r2);
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x00c7, code lost:
    
        r6.h.f225a = r6;
        r6.i.f225a = r6;
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x00cf, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:32:0x00c4, code lost:
    
        if (r1 != null) goto L20;
     */
    @Override // a.f.a.j.o.p
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    void d() {
        /*
            r6 = this;
            java.util.ArrayList<a.f.a.j.o.p> r0 = r6.k
            java.util.Iterator r0 = r0.iterator()
        L6:
            boolean r1 = r0.hasNext()
            if (r1 == 0) goto L16
            java.lang.Object r1 = r0.next()
            a.f.a.j.o.p r1 = (a.f.a.j.o.p) r1
            r1.d()
            goto L6
        L16:
            java.util.ArrayList<a.f.a.j.o.p> r0 = r6.k
            int r0 = r0.size()
            r1 = 1
            if (r0 >= r1) goto L20
            return
        L20:
            java.util.ArrayList<a.f.a.j.o.p> r2 = r6.k
            r3 = 0
            java.lang.Object r2 = r2.get(r3)
            a.f.a.j.o.p r2 = (a.f.a.j.o.p) r2
            a.f.a.j.e r2 = r2.f244b
            java.util.ArrayList<a.f.a.j.o.p> r4 = r6.k
            int r0 = r0 - r1
            java.lang.Object r0 = r4.get(r0)
            a.f.a.j.o.p r0 = (a.f.a.j.o.p) r0
            a.f.a.j.e r0 = r0.f244b
            int r4 = r6.f
            if (r4 != 0) goto L88
            a.f.a.j.d r1 = r2.L
            a.f.a.j.d r0 = r0.N
            a.f.a.j.o.f r2 = r6.i(r1, r3)
            int r1 = r1.f()
            a.f.a.j.e r4 = r6.n()
            if (r4 == 0) goto L52
            a.f.a.j.d r1 = r4.L
            int r1 = r1.f()
        L52:
            if (r2 == 0) goto L62
            a.f.a.j.o.f r4 = r6.h
            java.util.List<a.f.a.j.o.f> r5 = r4.l
            r5.add(r2)
            r4.f = r1
            java.util.List<a.f.a.j.o.d> r1 = r2.k
            r1.add(r4)
        L62:
            a.f.a.j.o.f r1 = r6.i(r0, r3)
            int r0 = r0.f()
            a.f.a.j.e r2 = r6.o()
            if (r2 == 0) goto L76
            a.f.a.j.d r0 = r2.N
            int r0 = r0.f()
        L76:
            if (r1 == 0) goto Lc7
        L78:
            a.f.a.j.o.f r2 = r6.i
            int r0 = -r0
            java.util.List<a.f.a.j.o.f> r3 = r2.l
            r3.add(r1)
            r2.f = r0
            java.util.List<a.f.a.j.o.d> r0 = r1.k
            r0.add(r2)
            goto Lc7
        L88:
            a.f.a.j.d r2 = r2.M
            a.f.a.j.d r0 = r0.O
            a.f.a.j.o.f r3 = r6.i(r2, r1)
            int r2 = r2.f()
            a.f.a.j.e r4 = r6.n()
            if (r4 == 0) goto La0
            a.f.a.j.d r2 = r4.M
            int r2 = r2.f()
        La0:
            if (r3 == 0) goto Lb0
            a.f.a.j.o.f r4 = r6.h
            java.util.List<a.f.a.j.o.f> r5 = r4.l
            r5.add(r3)
            r4.f = r2
            java.util.List<a.f.a.j.o.d> r2 = r3.k
            r2.add(r4)
        Lb0:
            a.f.a.j.o.f r1 = r6.i(r0, r1)
            int r0 = r0.f()
            a.f.a.j.e r2 = r6.o()
            if (r2 == 0) goto Lc4
            a.f.a.j.d r0 = r2.O
            int r0 = r0.f()
        Lc4:
            if (r1 == 0) goto Lc7
            goto L78
        Lc7:
            a.f.a.j.o.f r0 = r6.h
            r0.f225a = r6
            a.f.a.j.o.f r0 = r6.i
            r0.f225a = r6
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: a.f.a.j.o.c.d():void");
    }

    @Override // a.f.a.j.o.p
    public void e() {
        for (int i = 0; i < this.k.size(); i++) {
            this.k.get(i).e();
        }
    }

    @Override // a.f.a.j.o.p
    void f() {
        this.f245c = null;
        Iterator<p> it = this.k.iterator();
        while (it.hasNext()) {
            it.next().f();
        }
    }

    @Override // a.f.a.j.o.p
    public long j() {
        int size = this.k.size();
        long j = 0;
        for (int i = 0; i < size; i++) {
            j = r4.i.f + this.k.get(i).j() + j + r4.h.f;
        }
        return j;
    }

    @Override // a.f.a.j.o.p
    boolean l() {
        int size = this.k.size();
        for (int i = 0; i < size; i++) {
            if (!this.k.get(i).l()) {
                return false;
            }
        }
        return true;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder("ChainRun ");
        sb.append(this.f == 0 ? "horizontal : " : "vertical : ");
        Iterator<p> it = this.k.iterator();
        while (it.hasNext()) {
            p next = it.next();
            sb.append("<");
            sb.append(next);
            sb.append("> ");
        }
        return sb.toString();
    }
}
