package a.f.a;

import java.util.Arrays;

/* loaded from: classes.dex */
public class h implements Comparable<h> {
    private static int n = 1;

    /* renamed from: a, reason: collision with root package name */
    public boolean f123a;
    public float e;
    a i;

    /* renamed from: b, reason: collision with root package name */
    public int f124b = -1;

    /* renamed from: c, reason: collision with root package name */
    int f125c = -1;

    /* renamed from: d, reason: collision with root package name */
    public int f126d = 0;
    public boolean f = false;
    float[] g = new float[9];
    float[] h = new float[9];
    b[] j = new b[16];
    int k = 0;
    public int l = 0;
    int m = -1;

    /* JADX WARN: $VALUES field not found */
    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    public static final class a {

        /* renamed from: a, reason: collision with root package name */
        public static final a f127a = new a("UNRESTRICTED", 0);

        /* renamed from: b, reason: collision with root package name */
        public static final a f128b = new a("CONSTANT", 1);

        /* renamed from: c, reason: collision with root package name */
        public static final a f129c = new a("SLACK", 2);

        /* renamed from: d, reason: collision with root package name */
        public static final a f130d = new a("ERROR", 3);
        public static final a e = new a("UNKNOWN", 4);

        private a(String str, int i) {
        }
    }

    public h(a aVar) {
        this.i = aVar;
    }

    static void b() {
        n++;
    }

    public final void a(b bVar) {
        int i = 0;
        while (true) {
            int i2 = this.k;
            if (i >= i2) {
                b[] bVarArr = this.j;
                if (i2 >= bVarArr.length) {
                    this.j = (b[]) Arrays.copyOf(bVarArr, bVarArr.length * 2);
                }
                b[] bVarArr2 = this.j;
                int i3 = this.k;
                bVarArr2[i3] = bVar;
                this.k = i3 + 1;
                return;
            }
            if (this.j[i] == bVar) {
                return;
            } else {
                i++;
            }
        }
    }

    public final void c(b bVar) {
        int i = this.k;
        int i2 = 0;
        while (i2 < i) {
            if (this.j[i2] == bVar) {
                while (i2 < i - 1) {
                    b[] bVarArr = this.j;
                    int i3 = i2 + 1;
                    bVarArr[i2] = bVarArr[i3];
                    i2 = i3;
                }
                this.k--;
                return;
            }
            i2++;
        }
    }

    @Override // java.lang.Comparable
    public int compareTo(h hVar) {
        return this.f124b - hVar.f124b;
    }

    public void d() {
        this.i = a.e;
        this.f126d = 0;
        this.f124b = -1;
        this.f125c = -1;
        this.e = 0.0f;
        this.f = false;
        this.m = -1;
        int i = this.k;
        for (int i2 = 0; i2 < i; i2++) {
            this.j[i2] = null;
        }
        this.k = 0;
        this.l = 0;
        this.f123a = false;
        Arrays.fill(this.h, 0.0f);
    }

    public void e(d dVar, float f) {
        this.e = f;
        this.f = true;
        this.m = -1;
        int i = this.k;
        this.f125c = -1;
        for (int i2 = 0; i2 < i; i2++) {
            this.j[i2].n(dVar, this, false);
        }
        this.k = 0;
    }

    public final void f(d dVar, b bVar) {
        int i = this.k;
        for (int i2 = 0; i2 < i; i2++) {
            this.j[i2].o(dVar, bVar, false);
        }
        this.k = 0;
    }

    public String toString() {
        StringBuilder j = b.b.a.a.a.j("");
        j.append(this.f124b);
        return j.toString();
    }
}
