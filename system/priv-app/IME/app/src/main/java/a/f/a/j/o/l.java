package a.f.a.j.o;

import a.f.a.j.d;
import a.f.a.j.e;
import a.f.a.j.o.f;
import a.f.a.j.o.p;
import java.util.List;

/* loaded from: classes.dex */
public class l extends p {
    private static int[] k = new int[2];

    public l(a.f.a.j.e eVar) {
        super(eVar);
        this.h.e = f.a.f232d;
        this.i.e = f.a.e;
        this.f = 0;
    }

    private void n(int[] iArr, int i, int i2, int i3, int i4, float f, int i5) {
        int i6 = i2 - i;
        int i7 = i4 - i3;
        if (i5 != -1) {
            if (i5 == 0) {
                iArr[0] = (int) ((i7 * f) + 0.5f);
                iArr[1] = i7;
                return;
            } else {
                if (i5 != 1) {
                    return;
                }
                iArr[0] = i6;
                iArr[1] = (int) ((i6 * f) + 0.5f);
                return;
            }
        }
        int i8 = (int) ((i7 * f) + 0.5f);
        int i9 = (int) ((i6 / f) + 0.5f);
        if (i8 <= i6 && i7 <= i7) {
            iArr[0] = i8;
            iArr[1] = i7;
        } else {
            if (i6 > i6 || i9 > i7) {
                return;
            }
            iArr[0] = i6;
            iArr[1] = i9;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:113:0x0287, code lost:
    
        if (r15 != 1) goto L135;
     */
    @Override // a.f.a.j.o.p, a.f.a.j.o.d
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void a(a.f.a.j.o.d r18) {
        /*
            Method dump skipped, instructions count: 1009
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: a.f.a.j.o.l.a(a.f.a.j.o.d):void");
    }

    @Override // a.f.a.j.o.p
    void d() {
        a.f.a.j.e eVar;
        a.f.a.j.e eVar2;
        f fVar;
        a.f.a.j.d dVar;
        List<d> list;
        d dVar2;
        f fVar2;
        f fVar3;
        f fVar4;
        int i;
        a.f.a.j.e eVar3;
        e.a aVar = e.a.MATCH_CONSTRAINT;
        e.a aVar2 = e.a.MATCH_PARENT;
        e.a aVar3 = e.a.FIXED;
        a.f.a.j.e eVar4 = this.f244b;
        if (eVar4.f201a) {
            this.e.c(eVar4.Q());
        }
        if (!this.e.j) {
            e.a z = this.f244b.z();
            this.f246d = z;
            if (z != aVar) {
                if (z == aVar2 && (eVar3 = this.f244b.X) != null && (eVar3.z() == aVar3 || eVar3.z() == aVar2)) {
                    int Q = (eVar3.Q() - this.f244b.L.f()) - this.f244b.N.f();
                    b(this.h, eVar3.f204d.h, this.f244b.L.f());
                    b(this.i, eVar3.f204d.i, -this.f244b.N.f());
                    this.e.c(Q);
                    return;
                }
                if (this.f246d == aVar3) {
                    this.e.c(this.f244b.Q());
                }
            }
        } else if (this.f246d == aVar2 && (eVar = this.f244b.X) != null && (eVar.z() == aVar3 || eVar.z() == aVar2)) {
            b(this.h, eVar.f204d.h, this.f244b.L.f());
            b(this.i, eVar.f204d.i, -this.f244b.N.f());
            return;
        }
        g gVar = this.e;
        if (gVar.j) {
            a.f.a.j.e eVar5 = this.f244b;
            if (eVar5.f201a) {
                a.f.a.j.d[] dVarArr = eVar5.T;
                if (dVarArr[0].f != null && dVarArr[1].f != null) {
                    if (eVar5.a0()) {
                        this.h.f = this.f244b.T[0].f();
                        fVar = this.i;
                        dVar = this.f244b.T[1];
                        fVar.f = -dVar.f();
                        return;
                    }
                    f h = h(this.f244b.T[0]);
                    if (h != null) {
                        f fVar5 = this.h;
                        int f = this.f244b.T[0].f();
                        fVar5.l.add(h);
                        fVar5.f = f;
                        h.k.add(fVar5);
                    }
                    f h2 = h(this.f244b.T[1]);
                    if (h2 != null) {
                        f fVar6 = this.i;
                        int i2 = -this.f244b.T[1].f();
                        fVar6.l.add(h2);
                        fVar6.f = i2;
                        h2.k.add(fVar6);
                    }
                    this.h.f226b = true;
                    this.i.f226b = true;
                    return;
                }
                if (dVarArr[0].f != null) {
                    f h3 = h(dVarArr[0]);
                    if (h3 == null) {
                        return;
                    }
                    f fVar7 = this.h;
                    int f2 = this.f244b.T[0].f();
                    fVar7.l.add(h3);
                    fVar7.f = f2;
                    h3.k.add(fVar7);
                } else {
                    if (dVarArr[1].f != null) {
                        f h4 = h(dVarArr[1]);
                        if (h4 != null) {
                            f fVar8 = this.i;
                            int i3 = -this.f244b.T[1].f();
                            fVar8.l.add(h4);
                            fVar8.f = i3;
                            h4.k.add(fVar8);
                            fVar3 = this.h;
                            fVar4 = this.i;
                            i = -this.e.g;
                            b(fVar3, fVar4, i);
                            return;
                        }
                        return;
                    }
                    if ((eVar5 instanceof a.f.a.j.i) || eVar5.X == null || eVar5.o(d.a.CENTER).f != null) {
                        return;
                    }
                    a.f.a.j.e eVar6 = this.f244b;
                    b(this.h, eVar6.X.f204d.h, eVar6.R());
                }
                fVar3 = this.i;
                fVar4 = this.h;
                i = this.e.g;
                b(fVar3, fVar4, i);
                return;
            }
        }
        if (this.f246d == aVar) {
            a.f.a.j.e eVar7 = this.f244b;
            int i4 = eVar7.r;
            if (i4 == 2) {
                a.f.a.j.e eVar8 = eVar7.X;
                if (eVar8 != null) {
                    g gVar2 = eVar8.e.e;
                    gVar.l.add(gVar2);
                    gVar2.k.add(this.e);
                    g gVar3 = this.e;
                    gVar3.f226b = true;
                    gVar3.k.add(this.h);
                    list = this.e.k;
                    dVar2 = this.i;
                    list.add(dVar2);
                }
            } else if (i4 == 3) {
                if (eVar7.s == 3) {
                    this.h.f225a = this;
                    this.i.f225a = this;
                    n nVar = eVar7.e;
                    nVar.h.f225a = this;
                    nVar.i.f225a = this;
                    gVar.f225a = this;
                    if (eVar7.c0()) {
                        this.e.l.add(this.f244b.e.e);
                        this.f244b.e.e.k.add(this.e);
                        n nVar2 = this.f244b.e;
                        nVar2.e.f225a = this;
                        this.e.l.add(nVar2.h);
                        this.e.l.add(this.f244b.e.i);
                        this.f244b.e.h.k.add(this.e);
                        list = this.f244b.e.i.k;
                        dVar2 = this.e;
                        list.add(dVar2);
                    } else if (this.f244b.a0()) {
                        this.f244b.e.e.l.add(this.e);
                        list = this.e.k;
                        dVar2 = this.f244b.e.e;
                        list.add(dVar2);
                    } else {
                        fVar2 = this.f244b.e.e;
                    }
                } else {
                    g gVar4 = eVar7.e.e;
                    gVar.l.add(gVar4);
                    gVar4.k.add(this.e);
                    this.f244b.e.h.k.add(this.e);
                    this.f244b.e.i.k.add(this.e);
                    g gVar5 = this.e;
                    gVar5.f226b = true;
                    gVar5.k.add(this.h);
                    this.e.k.add(this.i);
                    this.h.l.add(this.e);
                    fVar2 = this.i;
                }
                list = fVar2.l;
                dVar2 = this.e;
                list.add(dVar2);
            }
            fVar.f = -dVar.f();
            return;
        }
        a.f.a.j.e eVar9 = this.f244b;
        a.f.a.j.d[] dVarArr2 = eVar9.T;
        if (dVarArr2[0].f != null && dVarArr2[1].f != null) {
            if (eVar9.a0()) {
                this.h.f = this.f244b.T[0].f();
                fVar = this.i;
                dVar = this.f244b.T[1];
                fVar.f = -dVar.f();
                return;
            }
            f h5 = h(this.f244b.T[0]);
            f h6 = h(this.f244b.T[1]);
            if (h5 != null) {
                h5.k.add(this);
                if (h5.j) {
                    a(this);
                }
            }
            if (h6 != null) {
                h6.k.add(this);
                if (h6.j) {
                    a(this);
                }
            }
            this.j = p.a.CENTER;
            return;
        }
        if (dVarArr2[0].f != null) {
            f h7 = h(dVarArr2[0]);
            if (h7 == null) {
                return;
            }
            f fVar9 = this.h;
            int f3 = this.f244b.T[0].f();
            fVar9.l.add(h7);
            fVar9.f = f3;
            h7.k.add(fVar9);
        } else {
            if (dVarArr2[1].f != null) {
                f h8 = h(dVarArr2[1]);
                if (h8 != null) {
                    f fVar10 = this.i;
                    int i5 = -this.f244b.T[1].f();
                    fVar10.l.add(h8);
                    fVar10.f = i5;
                    h8.k.add(fVar10);
                    c(this.h, this.i, -1, this.e);
                    return;
                }
                return;
            }
            if ((eVar9 instanceof a.f.a.j.i) || (eVar2 = eVar9.X) == null) {
                return;
            } else {
                b(this.h, eVar2.f204d.h, eVar9.R());
            }
        }
        c(this.i, this.h, 1, this.e);
    }

    @Override // a.f.a.j.o.p
    public void e() {
        f fVar = this.h;
        if (fVar.j) {
            this.f244b.U0(fVar.g);
        }
    }

    @Override // a.f.a.j.o.p
    void f() {
        this.f245c = null;
        this.h.b();
        this.i.b();
        this.e.b();
        this.g = false;
    }

    @Override // a.f.a.j.o.p
    boolean l() {
        return this.f246d != e.a.MATCH_CONSTRAINT || this.f244b.r == 0;
    }

    void o() {
        this.g = false;
        this.h.b();
        this.h.j = false;
        this.i.b();
        this.i.j = false;
        this.e.j = false;
    }

    public String toString() {
        StringBuilder j = b.b.a.a.a.j("HorizontalRun ");
        j.append(this.f244b.t());
        return j.toString();
    }
}
