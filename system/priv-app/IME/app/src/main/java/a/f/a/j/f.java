package a.f.a.j;

import a.f.a.j.e;
import a.f.a.j.o.b;
import java.lang.ref.WeakReference;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Objects;

/* loaded from: classes.dex */
public class f extends n {
    private int Q0;
    int U0;
    int V0;
    a.f.a.j.o.b O0 = new a.f.a.j.o.b(this);
    public a.f.a.j.o.e P0 = new a.f.a.j.o.e(this);
    protected b.InterfaceC0003b R0 = null;
    private boolean S0 = false;
    protected a.f.a.d T0 = new a.f.a.d();
    public int W0 = 0;
    public int X0 = 0;
    c[] Y0 = new c[4];
    c[] Z0 = new c[4];
    private int a1 = 257;
    private boolean b1 = false;
    private boolean c1 = false;
    private WeakReference<d> d1 = null;
    private WeakReference<d> e1 = null;
    private WeakReference<d> f1 = null;
    private WeakReference<d> g1 = null;
    HashSet<e> h1 = new HashSet<>();
    public b.a i1 = new b.a();

    public static boolean n1(e eVar, b.InterfaceC0003b interfaceC0003b, b.a aVar, int i) {
        int i2;
        int i3;
        e.a aVar2 = e.a.WRAP_CONTENT;
        e.a aVar3 = e.a.FIXED;
        if (interfaceC0003b == null) {
            return false;
        }
        if (eVar.P() == 8 || (eVar instanceof h) || (eVar instanceof a)) {
            aVar.e = 0;
            aVar.f = 0;
            return false;
        }
        aVar.f217a = eVar.z();
        aVar.f218b = eVar.O();
        aVar.f219c = eVar.Q();
        aVar.f220d = eVar.w();
        aVar.i = false;
        aVar.j = i;
        e.a aVar4 = aVar.f217a;
        e.a aVar5 = e.a.MATCH_CONSTRAINT;
        boolean z = aVar4 == aVar5;
        boolean z2 = aVar.f218b == aVar5;
        boolean z3 = z && eVar.a0 > 0.0f;
        boolean z4 = z2 && eVar.a0 > 0.0f;
        if (z && eVar.U(0) && eVar.r == 0 && !z3) {
            aVar.f217a = aVar2;
            if (z2 && eVar.s == 0) {
                aVar.f217a = aVar3;
            }
            z = false;
        }
        if (z2 && eVar.U(1) && eVar.s == 0 && !z4) {
            aVar.f218b = aVar2;
            if (z && eVar.r == 0) {
                aVar.f218b = aVar3;
            }
            z2 = false;
        }
        if (eVar.f0()) {
            aVar.f217a = aVar3;
            z = false;
        }
        if (eVar.g0()) {
            aVar.f218b = aVar3;
            z2 = false;
        }
        if (z3) {
            if (eVar.t[0] == 4) {
                aVar.f217a = aVar3;
            } else if (!z2) {
                if (aVar.f218b == aVar3) {
                    i3 = aVar.f220d;
                } else {
                    aVar.f217a = aVar2;
                    interfaceC0003b.b(eVar, aVar);
                    i3 = aVar.f;
                }
                aVar.f217a = aVar3;
                aVar.f219c = (int) (eVar.a0 * i3);
            }
        }
        if (z4) {
            if (eVar.t[1] == 4) {
                aVar.f218b = aVar3;
            } else if (!z) {
                if (aVar.f217a == aVar3) {
                    i2 = aVar.f219c;
                } else {
                    aVar.f218b = aVar2;
                    interfaceC0003b.b(eVar, aVar);
                    i2 = aVar.e;
                }
                aVar.f218b = aVar3;
                aVar.f220d = eVar.b0 == -1 ? (int) (i2 / eVar.a0) : (int) (eVar.a0 * i2);
            }
        }
        interfaceC0003b.b(eVar, aVar);
        eVar.S0(aVar.e);
        eVar.A0(aVar.f);
        eVar.z0(aVar.h);
        eVar.q0(aVar.g);
        aVar.j = 0;
        return aVar.i;
    }

    @Override // a.f.a.j.e
    public void J(StringBuilder sb) {
        sb.append(this.j + ":{\n");
        sb.append("  actualWidth:" + this.Y);
        sb.append("\n");
        sb.append("  actualHeight:" + this.Z);
        sb.append("\n");
        Iterator<e> it = this.N0.iterator();
        while (it.hasNext()) {
            it.next().J(sb);
            sb.append(",\n");
        }
        sb.append("}");
    }

    @Override // a.f.a.j.e
    public void W0(boolean z, boolean z2) {
        super.W0(z, z2);
        int size = this.N0.size();
        for (int i = 0; i < size; i++) {
            this.N0.get(i).W0(z, z2);
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:101:0x024d  */
    /* JADX WARN: Removed duplicated region for block: B:112:0x029b A[ADDED_TO_REGION] */
    /* JADX WARN: Removed duplicated region for block: B:130:0x0316  */
    /* JADX WARN: Removed duplicated region for block: B:133:0x0331  */
    /* JADX WARN: Removed duplicated region for block: B:135:0x0340  */
    /* JADX WARN: Removed duplicated region for block: B:148:0x037d  */
    /* JADX WARN: Removed duplicated region for block: B:151:0x037f  */
    /* JADX WARN: Removed duplicated region for block: B:154:0x033d  */
    /* JADX WARN: Removed duplicated region for block: B:157:0x027f  */
    /* JADX WARN: Type inference failed for: r8v10, types: [boolean] */
    /* JADX WARN: Type inference failed for: r8v15 */
    /* JADX WARN: Type inference failed for: r8v9 */
    @Override // a.f.a.j.n
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void Y0() {
        /*
            Method dump skipped, instructions count: 929
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: a.f.a.j.f.Y0():void");
    }

    void Z0(e eVar, int i) {
        if (i == 0) {
            int i2 = this.W0 + 1;
            c[] cVarArr = this.Z0;
            if (i2 >= cVarArr.length) {
                this.Z0 = (c[]) Arrays.copyOf(cVarArr, cVarArr.length * 2);
            }
            c[] cVarArr2 = this.Z0;
            int i3 = this.W0;
            cVarArr2[i3] = new c(eVar, 0, this.S0);
            this.W0 = i3 + 1;
            return;
        }
        if (i == 1) {
            int i4 = this.X0 + 1;
            c[] cVarArr3 = this.Y0;
            if (i4 >= cVarArr3.length) {
                this.Y0 = (c[]) Arrays.copyOf(cVarArr3, cVarArr3.length * 2);
            }
            c[] cVarArr4 = this.Y0;
            int i5 = this.X0;
            cVarArr4[i5] = new c(eVar, 1, this.S0);
            this.X0 = i5 + 1;
        }
    }

    public boolean a1(a.f.a.d dVar) {
        boolean z;
        e.a aVar = e.a.FIXED;
        e.a aVar2 = e.a.WRAP_CONTENT;
        boolean o1 = o1(64);
        f(dVar, o1);
        int size = this.N0.size();
        boolean z2 = false;
        for (int i = 0; i < size; i++) {
            e eVar = this.N0.get(i);
            eVar.E0(0, false);
            eVar.E0(1, false);
            if (eVar instanceof a) {
                z2 = true;
            }
        }
        if (z2) {
            for (int i2 = 0; i2 < size; i2++) {
                e eVar2 = this.N0.get(i2);
                if (eVar2 instanceof a) {
                    ((a) eVar2).e1();
                }
            }
        }
        this.h1.clear();
        for (int i3 = 0; i3 < size; i3++) {
            e eVar3 = this.N0.get(i3);
            if (eVar3.e()) {
                if (eVar3 instanceof m) {
                    this.h1.add(eVar3);
                } else {
                    eVar3.f(dVar, o1);
                }
            }
        }
        while (this.h1.size() > 0) {
            int size2 = this.h1.size();
            Iterator<e> it = this.h1.iterator();
            while (true) {
                if (!it.hasNext()) {
                    break;
                }
                m mVar = (m) it.next();
                HashSet<e> hashSet = this.h1;
                int i4 = 0;
                while (true) {
                    if (i4 >= mVar.O0) {
                        z = false;
                        break;
                    }
                    if (hashSet.contains(mVar.N0[i4])) {
                        z = true;
                        break;
                    }
                    i4++;
                }
                if (z) {
                    mVar.f(dVar, o1);
                    this.h1.remove(mVar);
                    break;
                }
            }
            if (size2 == this.h1.size()) {
                Iterator<e> it2 = this.h1.iterator();
                while (it2.hasNext()) {
                    it2.next().f(dVar, o1);
                }
                this.h1.clear();
            }
        }
        if (a.f.a.d.p) {
            HashSet<e> hashSet2 = new HashSet<>();
            for (int i5 = 0; i5 < size; i5++) {
                e eVar4 = this.N0.get(i5);
                if (!eVar4.e()) {
                    hashSet2.add(eVar4);
                }
            }
            d(this, dVar, hashSet2, z() == aVar2 ? 0 : 1, false);
            Iterator<e> it3 = hashSet2.iterator();
            while (it3.hasNext()) {
                e next = it3.next();
                k.a(this, dVar, next);
                next.f(dVar, o1);
            }
        } else {
            for (int i6 = 0; i6 < size; i6++) {
                e eVar5 = this.N0.get(i6);
                if (eVar5 instanceof f) {
                    e.a[] aVarArr = eVar5.W;
                    e.a aVar3 = aVarArr[0];
                    e.a aVar4 = aVarArr[1];
                    if (aVar3 == aVar2) {
                        aVarArr[0] = aVar;
                    }
                    if (aVar4 == aVar2) {
                        aVarArr[1] = aVar;
                    }
                    eVar5.f(dVar, o1);
                    if (aVar3 == aVar2) {
                        eVar5.D0(aVar3);
                    }
                    if (aVar4 == aVar2) {
                        eVar5.Q0(aVar4);
                    }
                } else {
                    k.a(this, dVar, eVar5);
                    if (!eVar5.e()) {
                        eVar5.f(dVar, o1);
                    }
                }
            }
        }
        if (this.W0 > 0) {
            b.a(this, dVar, null, 0);
        }
        if (this.X0 > 0) {
            b.a(this, dVar, null, 1);
        }
        return true;
    }

    public void b1(d dVar) {
        WeakReference<d> weakReference = this.g1;
        if (weakReference == null || weakReference.get() == null || dVar.e() > this.g1.get().e()) {
            this.g1 = new WeakReference<>(dVar);
        }
    }

    public void c1(d dVar) {
        WeakReference<d> weakReference = this.e1;
        if (weakReference == null || weakReference.get() == null || dVar.e() > this.e1.get().e()) {
            this.e1 = new WeakReference<>(dVar);
        }
    }

    void d1(d dVar) {
        WeakReference<d> weakReference = this.f1;
        if (weakReference == null || weakReference.get() == null || dVar.e() > this.f1.get().e()) {
            this.f1 = new WeakReference<>(dVar);
        }
    }

    void e1(d dVar) {
        WeakReference<d> weakReference = this.d1;
        if (weakReference == null || weakReference.get() == null || dVar.e() > this.d1.get().e()) {
            this.d1 = new WeakReference<>(dVar);
        }
    }

    public void f1(a.f.a.e eVar) {
        Objects.requireNonNull(this.T0);
    }

    public b.InterfaceC0003b g1() {
        return this.R0;
    }

    public int h1() {
        return this.a1;
    }

    public a.f.a.d i1() {
        return this.T0;
    }

    public boolean j1() {
        return this.c1;
    }

    @Override // a.f.a.j.n, a.f.a.j.e
    public void k0() {
        this.T0.v();
        this.U0 = 0;
        this.V0 = 0;
        super.k0();
    }

    public boolean k1() {
        return this.S0;
    }

    public boolean l1() {
        return this.b1;
    }

    public long m1(int i, int i2, int i3, int i4, int i5, int i6, int i7, int i8, int i9) {
        this.U0 = i8;
        this.V0 = i9;
        this.O0.c(this, i, i2, i3, i4, i5);
        return 0L;
    }

    public boolean o1(int i) {
        return (this.a1 & i) == i;
    }

    public void p1(b.InterfaceC0003b interfaceC0003b) {
        this.R0 = interfaceC0003b;
        this.P0.m(interfaceC0003b);
    }

    public void q1(int i) {
        this.a1 = i;
        a.f.a.d.p = o1(512);
    }

    public void r1(int i) {
        this.Q0 = i;
    }

    public void s1(boolean z) {
        this.S0 = z;
    }

    public void t1() {
        this.O0.d(this);
    }
}
