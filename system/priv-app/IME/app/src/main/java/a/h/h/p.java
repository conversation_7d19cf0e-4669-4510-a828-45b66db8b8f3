package a.h.h;

import a.h.h.q;
import android.view.View;

/* loaded from: classes.dex */
class p extends q.b<Boolean> {
    p(int i, Class cls, int i2) {
        super(i, cls, i2);
    }

    @Override // a.h.h.q.b
    Boolean b(View view) {
        return Boolean.valueOf(view.isAccessibilityHeading());
    }

    @Override // a.h.h.q.b
    void c(View view, Boolean bool) {
        view.setAccessibilityHeading(bool.booleanValue());
    }

    @Override // a.h.h.q.b
    boolean f(Boolean bool, Boolean bool2) {
        return !a(bool, bool2);
    }
}
