package a.b.f;

import a.h.h.s;
import a.h.h.t;
import a.h.h.u;
import android.view.View;
import android.view.animation.Interpolator;
import java.util.ArrayList;
import java.util.Iterator;

/* loaded from: classes.dex */
public class h {

    /* renamed from: c, reason: collision with root package name */
    private Interpolator f38c;

    /* renamed from: d, reason: collision with root package name */
    t f39d;
    private boolean e;

    /* renamed from: b, reason: collision with root package name */
    private long f37b = -1;
    private final u f = new a();

    /* renamed from: a, reason: collision with root package name */
    final ArrayList<s> f36a = new ArrayList<>();

    class a extends u {

        /* renamed from: a, reason: collision with root package name */
        private boolean f40a = false;

        /* renamed from: b, reason: collision with root package name */
        private int f41b = 0;

        a() {
        }

        @Override // a.h.h.t
        public void a(View view) {
            int i = this.f41b + 1;
            this.f41b = i;
            if (i == h.this.f36a.size()) {
                t tVar = h.this.f39d;
                if (tVar != null) {
                    tVar.a(null);
                }
                this.f41b = 0;
                this.f40a = false;
                h.this.b();
            }
        }

        @Override // a.h.h.u, a.h.h.t
        public void b(View view) {
            if (this.f40a) {
                return;
            }
            this.f40a = true;
            t tVar = h.this.f39d;
            if (tVar != null) {
                tVar.b(null);
            }
        }
    }

    public void a() {
        if (this.e) {
            Iterator<s> it = this.f36a.iterator();
            while (it.hasNext()) {
                it.next().b();
            }
            this.e = false;
        }
    }

    void b() {
        this.e = false;
    }

    public h c(s sVar) {
        if (!this.e) {
            this.f36a.add(sVar);
        }
        return this;
    }

    public h d(s sVar, s sVar2) {
        this.f36a.add(sVar);
        sVar2.h(sVar.c());
        this.f36a.add(sVar2);
        return this;
    }

    public h e(long j) {
        if (!this.e) {
            this.f37b = j;
        }
        return this;
    }

    public h f(Interpolator interpolator) {
        if (!this.e) {
            this.f38c = interpolator;
        }
        return this;
    }

    public h g(t tVar) {
        if (!this.e) {
            this.f39d = tVar;
        }
        return this;
    }

    public void h() {
        if (this.e) {
            return;
        }
        Iterator<s> it = this.f36a.iterator();
        while (it.hasNext()) {
            s next = it.next();
            long j = this.f37b;
            if (j >= 0) {
                next.d(j);
            }
            Interpolator interpolator = this.f38c;
            if (interpolator != null) {
                next.e(interpolator);
            }
            if (this.f39d != null) {
                next.f(this.f);
            }
            next.j();
        }
        this.e = true;
    }
}
