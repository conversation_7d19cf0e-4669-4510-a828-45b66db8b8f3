package a.f.a.j.o;

import java.util.ArrayList;

/* loaded from: classes.dex */
class m {

    /* renamed from: c, reason: collision with root package name */
    public static int f236c;

    /* renamed from: a, reason: collision with root package name */
    p f237a;

    /* renamed from: b, reason: collision with root package name */
    ArrayList<p> f238b = new ArrayList<>();

    public m(p pVar, int i) {
        this.f237a = null;
        f236c++;
        this.f237a = pVar;
    }

    private long b(f fVar, long j) {
        p pVar = fVar.f228d;
        if (pVar instanceof k) {
            return j;
        }
        int size = fVar.k.size();
        long j2 = j;
        for (int i = 0; i < size; i++) {
            d dVar = fVar.k.get(i);
            if (dVar instanceof f) {
                f fVar2 = (f) dVar;
                if (fVar2.f228d != pVar) {
                    j2 = Math.min(j2, b(fVar2, fVar2.f + j));
                }
            }
        }
        if (fVar != pVar.i) {
            return j2;
        }
        long j3 = j - pVar.j();
        return Math.min(Math.min(j2, b(pVar.h, j3)), j3 - pVar.h.f);
    }

    private long c(f fVar, long j) {
        p pVar = fVar.f228d;
        if (pVar instanceof k) {
            return j;
        }
        int size = fVar.k.size();
        long j2 = j;
        for (int i = 0; i < size; i++) {
            d dVar = fVar.k.get(i);
            if (dVar instanceof f) {
                f fVar2 = (f) dVar;
                if (fVar2.f228d != pVar) {
                    j2 = Math.max(j2, c(fVar2, fVar2.f + j));
                }
            }
        }
        if (fVar != pVar.h) {
            return j2;
        }
        long j3 = j + pVar.j();
        return Math.max(Math.max(j2, c(pVar.i, j3)), j3 - pVar.i.f);
    }

    public long a(a.f.a.j.f fVar, int i) {
        long j;
        p pVar;
        p pVar2 = this.f237a;
        if (pVar2 instanceof c) {
            if (((c) pVar2).f != i) {
                return 0L;
            }
        } else if (i == 0) {
            if (!(pVar2 instanceof l)) {
                return 0L;
            }
        } else if (!(pVar2 instanceof n)) {
            return 0L;
        }
        f fVar2 = (i == 0 ? fVar.f204d : fVar.e).h;
        f fVar3 = (i == 0 ? fVar.f204d : fVar.e).i;
        boolean contains = pVar2.h.l.contains(fVar2);
        boolean contains2 = this.f237a.i.l.contains(fVar3);
        long j2 = this.f237a.j();
        if (contains && contains2) {
            long c2 = c(this.f237a.h, 0L);
            long b2 = b(this.f237a.i, 0L);
            long j3 = c2 - j2;
            p pVar3 = this.f237a;
            int i2 = pVar3.i.f;
            if (j3 >= (-i2)) {
                j3 += i2;
            }
            int i3 = pVar3.h.f;
            long j4 = ((-b2) - j2) - i3;
            if (j4 >= i3) {
                j4 -= i3;
            }
            float q = pVar3.f244b.q(i);
            float f = q > 0.0f ? (long) ((j3 / (1.0f - q)) + (j4 / q)) : 0L;
            long a2 = ((long) ((f * q) + 0.5f)) + j2 + ((long) b.b.a.a.a.a(1.0f, q, f, 0.5f));
            pVar = this.f237a;
            j = pVar.h.f + a2;
        } else {
            if (contains) {
                return Math.max(c(this.f237a.h, r12.f), this.f237a.h.f + j2);
            }
            if (contains2) {
                return Math.max(-b(this.f237a.i, r12.f), (-this.f237a.i.f) + j2);
            }
            j = this.f237a.j() + r12.h.f;
            pVar = this.f237a;
        }
        return j - pVar.i.f;
    }
}
