package a.h.h;

import android.view.View;
import android.view.WindowInsets;

/* loaded from: classes.dex */
class r implements View.OnApplyWindowInsetsListener {

    /* renamed from: a, reason: collision with root package name */
    w f384a = null;

    /* renamed from: b, reason: collision with root package name */
    final /* synthetic */ View f385b;

    /* renamed from: c, reason: collision with root package name */
    final /* synthetic */ j f386c;

    r(View view, j jVar) {
        this.f385b = view;
        this.f386c = jVar;
    }

    @Override // android.view.View.OnApplyWindowInsetsListener
    public WindowInsets onApplyWindowInsets(View view, WindowInsets windowInsets) {
        w r = w.r(windowInsets, view);
        this.f384a = r;
        return this.f386c.a(view, r).p();
    }
}
