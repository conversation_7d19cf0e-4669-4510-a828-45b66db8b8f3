package a.i.a;

import android.database.Cursor;
import android.widget.Filter;

/* loaded from: classes.dex */
class b extends Filter {

    /* renamed from: a, reason: collision with root package name */
    a f423a;

    interface a {
        CharSequence a(Cursor cursor);

        void b(Cursor cursor);

        Cursor c(CharSequence charSequence);
    }

    b(a aVar) {
        this.f423a = aVar;
    }

    @Override // android.widget.Filter
    public CharSequence convertResultToString(Object obj) {
        return this.f423a.a((Cursor) obj);
    }

    @Override // android.widget.Filter
    protected Filter.FilterResults performFiltering(CharSequence charSequence) {
        Cursor c2 = this.f423a.c(charSequence);
        Filter.FilterResults filterResults = new Filter.FilterResults();
        if (c2 != null) {
            filterResults.count = c2.getCount();
        } else {
            filterResults.count = 0;
            c2 = null;
        }
        filterResults.values = c2;
        return filterResults;
    }

    @Override // android.widget.Filter
    protected void publishResults(CharSequence charSequence, Filter.FilterResults filterResults) {
        a aVar = this.f423a;
        Cursor cursor = ((a.i.a.a) aVar).f419c;
        Object obj = filterResults.values;
        if (obj == null || obj == cursor) {
            return;
        }
        aVar.b((Cursor) obj);
    }
}
