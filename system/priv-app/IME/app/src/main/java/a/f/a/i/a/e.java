package a.f.a.i.a;

import androidx.constraintlayout.motion.widget.MotionLayout;
import java.lang.reflect.Array;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;

/* loaded from: classes.dex */
public abstract class e {

    /* renamed from: a, reason: collision with root package name */
    private b f144a;

    /* renamed from: b, reason: collision with root package name */
    private String f145b;

    /* renamed from: c, reason: collision with root package name */
    private int f146c = 0;

    /* renamed from: d, reason: collision with root package name */
    private String f147d = null;
    public int e = 0;
    ArrayList<c> f = new ArrayList<>();

    class a implements Comparator<c> {
        a(e eVar) {
        }

        @Override // java.util.Comparator
        public int compare(c cVar, c cVar2) {
            return Integer.compare(cVar.f152a, cVar2.f152a);
        }
    }

    static class b {

        /* renamed from: a, reason: collision with root package name */
        h f148a;

        /* renamed from: b, reason: collision with root package name */
        float[] f149b;

        /* renamed from: c, reason: collision with root package name */
        double[] f150c;

        /* renamed from: d, reason: collision with root package name */
        float[] f151d;
        float[] e;
        float[] f;
        a.f.a.i.a.b g;
        double[] h;
        double[] i;

        b(int i, String str, int i2, int i3) {
            long j;
            char c2;
            h hVar = new h();
            this.f148a = hVar;
            hVar.f = i;
            hVar.f166d = str;
            if (str != null) {
                double[] dArr = new double[str.length() / 2];
                int indexOf = str.indexOf(40) + 1;
                int indexOf2 = str.indexOf(44, indexOf);
                char c3 = 0;
                int i4 = 0;
                while (indexOf2 != -1) {
                    dArr[i4] = Double.parseDouble(str.substring(indexOf, indexOf2).trim());
                    indexOf = indexOf2 + 1;
                    indexOf2 = str.indexOf(44, indexOf);
                    i4++;
                }
                dArr[i4] = Double.parseDouble(str.substring(indexOf, str.indexOf(41, indexOf)).trim());
                double[] copyOf = Arrays.copyOf(dArr, i4 + 1);
                int length = (copyOf.length * 3) - 2;
                int length2 = copyOf.length - 1;
                double d2 = 1.0d / length2;
                double[][] dArr2 = (double[][]) Array.newInstance((Class<?>) double.class, length, 1);
                double[] dArr3 = new double[length];
                int i5 = 0;
                while (i5 < copyOf.length) {
                    double d3 = copyOf[i5];
                    int i6 = i5 + length2;
                    dArr2[i6][c3] = d3;
                    double d4 = i5 * d2;
                    dArr3[i6] = d4;
                    if (i5 > 0) {
                        int i7 = (length2 * 2) + i5;
                        j = 4607182418800017408L;
                        c2 = 0;
                        dArr2[i7][0] = d3 + 1.0d;
                        dArr3[i7] = d4 + 1.0d;
                        int i8 = i5 - 1;
                        dArr2[i8][0] = (d3 - 1.0d) - d2;
                        dArr3[i8] = (d4 - 1.0d) - d2;
                    } else {
                        j = 4607182418800017408L;
                        c2 = 0;
                    }
                    i5++;
                    c3 = c2;
                }
                hVar.e = new g(dArr3, dArr2);
            }
            this.f149b = new float[i3];
            this.f150c = new double[i3];
            this.f151d = new float[i3];
            this.e = new float[i3];
            this.f = new float[i3];
            float[] fArr = new float[i3];
        }
    }

    static class c {

        /* renamed from: a, reason: collision with root package name */
        int f152a;

        /* renamed from: b, reason: collision with root package name */
        float f153b;

        /* renamed from: c, reason: collision with root package name */
        float f154c;

        /* renamed from: d, reason: collision with root package name */
        float f155d;
        float e;

        public c(int i, float f, float f2, float f3, float f4) {
            this.f152a = i;
            this.f153b = f4;
            this.f154c = f2;
            this.f155d = f;
            this.e = f3;
        }
    }

    public float a(float f) {
        b bVar = this.f144a;
        a.f.a.i.a.b bVar2 = bVar.g;
        if (bVar2 != null) {
            bVar2.c(f, bVar.h);
        } else {
            double[] dArr = bVar.h;
            dArr[0] = bVar.e[0];
            dArr[1] = bVar.f[0];
            dArr[2] = bVar.f149b[0];
        }
        double[] dArr2 = bVar.h;
        return (float) ((bVar.f148a.c(f, dArr2[1]) * bVar.h[2]) + dArr2[0]);
    }

    public float b(float f) {
        double d2;
        double d3;
        double d4;
        double signum;
        b bVar = this.f144a;
        a.f.a.i.a.b bVar2 = bVar.g;
        if (bVar2 != null) {
            double d5 = f;
            bVar2.f(d5, bVar.i);
            bVar.g.c(d5, bVar.h);
        } else {
            double[] dArr = bVar.i;
            dArr[0] = 0.0d;
            dArr[1] = 0.0d;
            dArr[2] = 0.0d;
        }
        double d6 = f;
        double c2 = bVar.f148a.c(d6, bVar.h[1]);
        h hVar = bVar.f148a;
        double d7 = bVar.h[1];
        double d8 = bVar.i[1];
        double b2 = d7 + hVar.b(d6);
        if (d6 <= 0.0d) {
            d6 = 1.0E-5d;
        } else if (d6 >= 1.0d) {
            d6 = 0.999999d;
        }
        int binarySearch = Arrays.binarySearch(hVar.f164b, d6);
        if (binarySearch > 0) {
            d2 = 0.0d;
        } else if (binarySearch != 0) {
            int i = (-binarySearch) - 1;
            float[] fArr = hVar.f163a;
            int i2 = i - 1;
            double d9 = fArr[i] - fArr[i2];
            double[] dArr2 = hVar.f164b;
            double d10 = d9 / (dArr2[i] - dArr2[i2]);
            d2 = (fArr[i2] - (d10 * dArr2[i2])) + (d6 * d10);
        } else {
            d2 = 0.0d;
        }
        double d11 = d2 + d8;
        switch (hVar.f) {
            case 1:
                d3 = 0.0d;
                break;
            case 2:
                d4 = d11 * 4.0d;
                signum = Math.signum((((b2 * 4.0d) + 3.0d) % 4.0d) - 2.0d);
                d3 = signum * d4;
                break;
            case 3:
                d3 = d11 * 2.0d;
                break;
            case 4:
                d3 = (-d11) * 2.0d;
                break;
            case 5:
                d4 = d11 * (-6.283185307179586d);
                signum = Math.sin(b2 * 6.283185307179586d);
                d3 = signum * d4;
                break;
            case 6:
                d3 = ((((b2 * 4.0d) + 2.0d) % 4.0d) - 2.0d) * d11 * 4.0d;
                break;
            case MotionLayout.TOUCH_UP_NEVER_TO_END /* 7 */:
                d3 = hVar.e.e(b2 % 1.0d, 0);
                break;
            default:
                d4 = d11 * 6.283185307179586d;
                signum = Math.cos(b2 * 6.283185307179586d);
                d3 = signum * d4;
                break;
        }
        double[] dArr3 = bVar.i;
        return (float) ((d3 * bVar.h[2]) + (c2 * dArr3[2]) + dArr3[0]);
    }

    protected void c(Object obj) {
    }

    public void d(int i, int i2, String str, int i3, float f, float f2, float f3, float f4) {
        this.f.add(new c(i, f, f2, f3, f4));
        if (i3 != -1) {
            this.e = i3;
        }
        this.f146c = i2;
        this.f147d = str;
    }

    public void e(int i, int i2, String str, int i3, float f, float f2, float f3, float f4, Object obj) {
        this.f.add(new c(i, f, f2, f3, f4));
        if (i3 != -1) {
            this.e = i3;
        }
        this.f146c = i2;
        c(obj);
        this.f147d = str;
    }

    public void f(String str) {
        this.f145b = str;
    }

    public void g(float f) {
        int i;
        a.f.a.i.a.b bVar;
        int size = this.f.size();
        if (size == 0) {
            return;
        }
        Collections.sort(this.f, new a(this));
        double[] dArr = new double[size];
        char c2 = 2;
        char c3 = 0;
        double[][] dArr2 = (double[][]) Array.newInstance((Class<?>) double.class, size, 3);
        this.f144a = new b(this.f146c, this.f147d, this.e, size);
        Iterator<c> it = this.f.iterator();
        int i2 = 0;
        while (it.hasNext()) {
            c next = it.next();
            float f2 = next.f155d;
            dArr[i2] = f2 * 0.01d;
            double[] dArr3 = dArr2[i2];
            float f3 = next.f153b;
            dArr3[c3] = f3;
            double[] dArr4 = dArr2[i2];
            float f4 = next.f154c;
            dArr4[1] = f4;
            double[] dArr5 = dArr2[i2];
            float f5 = next.e;
            Iterator<c> it2 = it;
            dArr5[c2] = f5;
            b bVar2 = this.f144a;
            bVar2.f150c[i2] = next.f152a / 100.0d;
            bVar2.f151d[i2] = f2;
            bVar2.e[i2] = f4;
            bVar2.f[i2] = f5;
            bVar2.f149b[i2] = f3;
            i2++;
            dArr = dArr;
            it = it2;
            dArr2 = dArr2;
            c2 = 2;
            c3 = 0;
        }
        double[] dArr6 = dArr;
        double[][] dArr7 = dArr2;
        b bVar3 = this.f144a;
        double[][] dArr8 = (double[][]) Array.newInstance((Class<?>) double.class, bVar3.f150c.length, 3);
        float[] fArr = bVar3.f149b;
        bVar3.h = new double[fArr.length + 2];
        bVar3.i = new double[fArr.length + 2];
        if (bVar3.f150c[0] > 0.0d) {
            bVar3.f148a.a(0.0d, bVar3.f151d[0]);
        }
        double[] dArr9 = bVar3.f150c;
        int length = dArr9.length - 1;
        if (dArr9[length] < 1.0d) {
            bVar3.f148a.a(1.0d, bVar3.f151d[length]);
        }
        for (int i3 = 0; i3 < dArr8.length; i3++) {
            dArr8[i3][0] = bVar3.e[i3];
            dArr8[i3][1] = bVar3.f[i3];
            dArr8[i3][2] = bVar3.f149b[i3];
            bVar3.f148a.a(bVar3.f150c[i3], bVar3.f151d[i3]);
        }
        h hVar = bVar3.f148a;
        double d2 = 0.0d;
        int i4 = 0;
        while (true) {
            if (i4 >= hVar.f163a.length) {
                break;
            }
            d2 += r9[i4];
            i4++;
        }
        int i5 = 1;
        double d3 = 0.0d;
        while (true) {
            float[] fArr2 = hVar.f163a;
            if (i5 >= fArr2.length) {
                break;
            }
            int i6 = i5 - 1;
            float f6 = (fArr2[i6] + fArr2[i5]) / 2.0f;
            double[] dArr10 = hVar.f164b;
            d3 = ((dArr10[i5] - dArr10[i6]) * f6) + d3;
            i5++;
        }
        int i7 = 0;
        while (true) {
            float[] fArr3 = hVar.f163a;
            if (i7 >= fArr3.length) {
                break;
            }
            fArr3[i7] = (float) ((d2 / d3) * fArr3[i7]);
            i7++;
        }
        hVar.f165c[0] = 0.0d;
        int i8 = 1;
        while (true) {
            float[] fArr4 = hVar.f163a;
            if (i8 >= fArr4.length) {
                break;
            }
            int i9 = i8 - 1;
            float f7 = (fArr4[i9] + fArr4[i8]) / 2.0f;
            double[] dArr11 = hVar.f164b;
            double d4 = dArr11[i8] - dArr11[i9];
            double[] dArr12 = hVar.f165c;
            dArr12[i8] = (d4 * f7) + dArr12[i9];
            i8++;
        }
        double[] dArr13 = bVar3.f150c;
        if (dArr13.length > 1) {
            i = 0;
            bVar = a.f.a.i.a.b.a(0, dArr13, dArr8);
        } else {
            i = 0;
            bVar = null;
        }
        bVar3.g = bVar;
        a.f.a.i.a.b.a(i, dArr6, dArr7);
    }

    public String toString() {
        String str = this.f145b;
        DecimalFormat decimalFormat = new DecimalFormat("##.##");
        Iterator<c> it = this.f.iterator();
        while (it.hasNext()) {
            str = str + "[" + it.next().f152a + " , " + decimalFormat.format(r2.f153b) + "] ";
        }
        return str;
    }
}
