package a.f.a;

/* JADX WARN: Unexpected interfaces in signature: [a.f.a.f<T>] */
/* loaded from: classes.dex */
class f<T> {

    /* renamed from: a, reason: collision with root package name */
    private final Object[] f119a;

    /* renamed from: b, reason: collision with root package name */
    private int f120b;

    f(int i) {
        if (i <= 0) {
            throw new IllegalArgumentException("The max pool size must be > 0");
        }
        this.f119a = new Object[i];
    }

    public T a() {
        int i = this.f120b;
        if (i <= 0) {
            return null;
        }
        int i2 = i - 1;
        Object[] objArr = this.f119a;
        T t = (T) objArr[i2];
        objArr[i2] = null;
        this.f120b = i - 1;
        return t;
    }

    public boolean b(T t) {
        int i = this.f120b;
        Object[] objArr = this.f119a;
        if (i >= objArr.length) {
            return false;
        }
        objArr[i] = t;
        this.f120b = i + 1;
        return true;
    }

    public void c(T[] tArr, int i) {
        if (i > tArr.length) {
            i = tArr.length;
        }
        for (int i2 = 0; i2 < i; i2++) {
            T t = tArr[i2];
            int i3 = this.f120b;
            Object[] objArr = this.f119a;
            if (i3 < objArr.length) {
                objArr[i3] = t;
                this.f120b = i3 + 1;
            }
        }
    }
}
