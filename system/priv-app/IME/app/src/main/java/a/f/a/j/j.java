package a.f.a.j;

import a.f.a.j.o.o;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;

/* loaded from: classes.dex */
public class j extends e implements i {
    public e[] N0 = new e[4];
    public int O0 = 0;

    public void Y0(ArrayList<o> arrayList, int i, o oVar) {
        for (int i2 = 0; i2 < this.O0; i2++) {
            oVar.a(this.N0[i2]);
        }
        for (int i3 = 0; i3 < this.O0; i3++) {
            a.f.a.j.o.i.a(this.N0[i3], i, arrayList, oVar);
        }
    }

    @Override // a.f.a.j.i
    public void a() {
        this.O0 = 0;
        Arrays.fill(this.N0, (Object) null);
    }

    @Override // a.f.a.j.i
    public void b(f fVar) {
    }

    @Override // a.f.a.j.i
    public void c(e eVar) {
        if (eVar == this || eVar == null) {
            return;
        }
        int i = this.O0 + 1;
        e[] eVarArr = this.N0;
        if (i > eVarArr.length) {
            this.N0 = (e[]) Arrays.copyOf(eVarArr, eVarArr.length * 2);
        }
        e[] eVarArr2 = this.N0;
        int i2 = this.O0;
        eVarArr2[i2] = eVar;
        this.O0 = i2 + 1;
    }

    @Override // a.f.a.j.e
    public void l(e eVar, HashMap<e, e> hashMap) {
        super.l(eVar, hashMap);
        j jVar = (j) eVar;
        this.O0 = 0;
        int i = jVar.O0;
        for (int i2 = 0; i2 < i; i2++) {
            c(hashMap.get(jVar.N0[i2]));
        }
    }
}
