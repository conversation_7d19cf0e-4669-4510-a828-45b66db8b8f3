package a.f.a.i.a;

import java.io.PrintStream;
import java.lang.reflect.Array;
import java.text.DecimalFormat;

/* loaded from: classes.dex */
public abstract class o {

    /* renamed from: a, reason: collision with root package name */
    protected b f181a;
    protected int e;
    protected String f;
    protected long i;

    /* renamed from: b, reason: collision with root package name */
    protected int f182b = 0;

    /* renamed from: c, reason: collision with root package name */
    protected int[] f183c = new int[10];

    /* renamed from: d, reason: collision with root package name */
    protected float[][] f184d = (float[][]) Array.newInstance((Class<?>) float.class, 10, 3);
    protected float[] g = new float[3];
    protected boolean h = false;
    protected float j = Float.NaN;

    protected float a(float f) {
        switch (this.f182b) {
            case 1:
                return Math.signum(f * 6.2831855f);
            case 2:
                return 1.0f - Math.abs(f);
            case 3:
                return (((f * 2.0f) + 1.0f) % 2.0f) - 1.0f;
            case 4:
                return 1.0f - (((f * 2.0f) + 1.0f) % 2.0f);
            case 5:
                return (float) Math.cos(f * 6.2831855f);
            case 6:
                float abs = 1.0f - Math.abs(((f * 4.0f) % 4.0f) - 2.0f);
                return 1.0f - (abs * abs);
            default:
                return (float) Math.sin(f * 6.2831855f);
        }
    }

    public void b(int i, float f, float f2, int i2, float f3) {
        int[] iArr = this.f183c;
        int i3 = this.e;
        iArr[i3] = i;
        float[][] fArr = this.f184d;
        fArr[i3][0] = f;
        fArr[i3][1] = f2;
        fArr[i3][2] = f3;
        this.f182b = Math.max(this.f182b, i2);
        this.e++;
    }

    protected void c(long j) {
        this.i = j;
    }

    public void d(String str) {
        this.f = str;
    }

    public void e(int i) {
        int i2;
        int i3 = this.e;
        if (i3 == 0) {
            PrintStream printStream = System.err;
            StringBuilder j = b.b.a.a.a.j("Error no points added to ");
            j.append(this.f);
            printStream.println(j.toString());
            return;
        }
        int[] iArr = this.f183c;
        float[][] fArr = this.f184d;
        int[] iArr2 = new int[iArr.length + 10];
        iArr2[0] = i3 - 1;
        iArr2[1] = 0;
        int i4 = 2;
        while (i4 > 0) {
            int i5 = i4 - 1;
            int i6 = iArr2[i5];
            i4 = i5 - 1;
            int i7 = iArr2[i4];
            if (i6 < i7) {
                int i8 = iArr[i7];
                int i9 = i6;
                int i10 = i9;
                while (i9 < i7) {
                    if (iArr[i9] <= i8) {
                        int i11 = iArr[i10];
                        iArr[i10] = iArr[i9];
                        iArr[i9] = i11;
                        float[] fArr2 = fArr[i10];
                        fArr[i10] = fArr[i9];
                        fArr[i9] = fArr2;
                        i10++;
                    }
                    i9++;
                }
                int i12 = iArr[i10];
                iArr[i10] = iArr[i7];
                iArr[i7] = i12;
                float[] fArr3 = fArr[i10];
                fArr[i10] = fArr[i7];
                fArr[i7] = fArr3;
                int i13 = i4 + 1;
                iArr2[i4] = i10 - 1;
                int i14 = i13 + 1;
                iArr2[i13] = i6;
                int i15 = i14 + 1;
                iArr2[i14] = i7;
                i4 = i15 + 1;
                iArr2[i15] = i10 + 1;
            }
        }
        int i16 = 1;
        int i17 = 0;
        while (true) {
            int[] iArr3 = this.f183c;
            if (i16 >= iArr3.length) {
                break;
            }
            if (iArr3[i16] != iArr3[i16 - 1]) {
                i17++;
            }
            i16++;
        }
        if (i17 == 0) {
            i17 = 1;
        }
        double[] dArr = new double[i17];
        double[][] dArr2 = (double[][]) Array.newInstance((Class<?>) double.class, i17, 3);
        int i18 = 0;
        for (0; i2 < this.e; i2 + 1) {
            if (i2 > 0) {
                int[] iArr4 = this.f183c;
                i2 = iArr4[i2] == iArr4[i2 - 1] ? i2 + 1 : 0;
            }
            dArr[i18] = this.f183c[i2] * 0.01d;
            double[] dArr3 = dArr2[i18];
            float[][] fArr4 = this.f184d;
            dArr3[0] = fArr4[i2][0];
            dArr2[i18][1] = fArr4[i2][1];
            dArr2[i18][2] = fArr4[i2][2];
            i18++;
        }
        this.f181a = b.a(i, dArr, dArr2);
    }

    public String toString() {
        String str = this.f;
        DecimalFormat decimalFormat = new DecimalFormat("##.##");
        for (int i = 0; i < this.e; i++) {
            str = str + "[" + this.f183c[i] + " , " + decimalFormat.format(this.f184d[i]) + "] ";
        }
        return str;
    }
}
