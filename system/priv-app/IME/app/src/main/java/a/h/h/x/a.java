package a.h.h.x;

import android.os.Bundle;
import android.text.style.ClickableSpan;
import android.view.View;

/* loaded from: classes.dex */
public final class a extends ClickableSpan {

    /* renamed from: a, reason: collision with root package name */
    private final int f402a;

    /* renamed from: b, reason: collision with root package name */
    private final b f403b;

    /* renamed from: c, reason: collision with root package name */
    private final int f404c;

    public a(int i, b bVar, int i2) {
        this.f402a = i;
        this.f403b = bVar;
        this.f404c = i2;
    }

    @Override // android.text.style.ClickableSpan
    public void onClick(View view) {
        Bundle bundle = new Bundle();
        bundle.putInt("ACCESSIBILITY_CLICKABLE_SPAN_ID", this.f402a);
        this.f403b.F(this.f404c, bundle);
    }
}
