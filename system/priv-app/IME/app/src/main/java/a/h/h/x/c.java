package a.h.h.x;

import android.os.Bundle;
import android.view.accessibility.AccessibilityNodeInfo;
import android.view.accessibility.AccessibilityNodeProvider;
import java.util.List;
import java.util.Objects;

/* loaded from: classes.dex */
public class c {

    /* renamed from: a, reason: collision with root package name */
    private final Object f415a;

    static class a extends AccessibilityNodeProvider {

        /* renamed from: a, reason: collision with root package name */
        final c f416a;

        a(c cVar) {
            this.f416a = cVar;
        }

        @Override // android.view.accessibility.AccessibilityNodeProvider
        public AccessibilityNodeInfo createAccessibilityNodeInfo(int i) {
            a.h.h.x.b a2 = this.f416a.a(i);
            if (a2 == null) {
                return null;
            }
            return a2.r0();
        }

        @Override // android.view.accessibility.AccessibilityNodeProvider
        public List<AccessibilityNodeInfo> findAccessibilityNodeInfosByText(String str, int i) {
            Objects.requireNonNull(this.f416a);
            return null;
        }

        @Override // android.view.accessibility.AccessibilityNodeProvider
        public boolean performAction(int i, int i2, Bundle bundle) {
            return this.f416a.d(i, i2, bundle);
        }
    }

    static class b extends a {
        b(c cVar) {
            super(cVar);
        }

        @Override // android.view.accessibility.AccessibilityNodeProvider
        public AccessibilityNodeInfo findFocus(int i) {
            a.h.h.x.b b2 = this.f416a.b(i);
            if (b2 == null) {
                return null;
            }
            return b2.r0();
        }
    }

    /* renamed from: a.h.h.x.c$c, reason: collision with other inner class name */
    static class C0012c extends b {
        C0012c(c cVar) {
            super(cVar);
        }

        @Override // android.view.accessibility.AccessibilityNodeProvider
        public void addExtraDataToAccessibilityNodeInfo(int i, AccessibilityNodeInfo accessibilityNodeInfo, String str, Bundle bundle) {
            Objects.requireNonNull(this.f416a);
        }
    }

    public c() {
        this.f415a = new C0012c(this);
    }

    public c(Object obj) {
        this.f415a = obj;
    }

    public a.h.h.x.b a(int i) {
        return null;
    }

    public a.h.h.x.b b(int i) {
        return null;
    }

    public Object c() {
        return this.f415a;
    }

    public boolean d(int i, int i2, Bundle bundle) {
        return false;
    }
}
