package a.f.b.a;

import android.util.Log;
import android.view.View;
import androidx.constraintlayout.motion.widget.MotionLayout;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/* loaded from: classes.dex */
public abstract class c extends a.f.a.i.a.e {

    static class a extends c {
        a() {
        }

        @Override // a.f.b.a.c
        public void i(View view, float f) {
            view.setAlpha(a(f));
        }
    }

    static class b extends c {
        float[] g = new float[1];
        protected androidx.constraintlayout.widget.a h;

        b() {
        }

        @Override // a.f.a.i.a.e
        protected void c(Object obj) {
            this.h = (androidx.constraintlayout.widget.a) obj;
        }

        @Override // a.f.b.a.c
        public void i(View view, float f) {
            this.g[0] = a(f);
            a.f.b.a.a.b(this.h, view, this.g);
        }
    }

    /* renamed from: a.f.b.a.c$c, reason: collision with other inner class name */
    static class C0004c extends c {
        C0004c() {
        }

        @Override // a.f.b.a.c
        public void i(View view, float f) {
            view.setElevation(a(f));
        }
    }

    public static class d extends c {
        @Override // a.f.b.a.c
        public void i(View view, float f) {
        }
    }

    static class e extends c {
        boolean g = false;

        e() {
        }

        @Override // a.f.b.a.c
        public void i(View view, float f) {
            if (view instanceof MotionLayout) {
                ((MotionLayout) view).setProgress(a(f));
                return;
            }
            if (this.g) {
                return;
            }
            Method method = null;
            try {
                method = view.getClass().getMethod("setProgress", Float.TYPE);
            } catch (NoSuchMethodException unused) {
                this.g = true;
            }
            if (method != null) {
                try {
                    method.invoke(view, Float.valueOf(a(f)));
                } catch (IllegalAccessException | InvocationTargetException e) {
                    Log.e("ViewOscillator", "unable to setProgress", e);
                }
            }
        }
    }

    static class f extends c {
        f() {
        }

        @Override // a.f.b.a.c
        public void i(View view, float f) {
            view.setRotation(a(f));
        }
    }

    static class g extends c {
        g() {
        }

        @Override // a.f.b.a.c
        public void i(View view, float f) {
            view.setRotationX(a(f));
        }
    }

    static class h extends c {
        h() {
        }

        @Override // a.f.b.a.c
        public void i(View view, float f) {
            view.setRotationY(a(f));
        }
    }

    static class i extends c {
        i() {
        }

        @Override // a.f.b.a.c
        public void i(View view, float f) {
            view.setScaleX(a(f));
        }
    }

    static class j extends c {
        j() {
        }

        @Override // a.f.b.a.c
        public void i(View view, float f) {
            view.setScaleY(a(f));
        }
    }

    static class k extends c {
        k() {
        }

        @Override // a.f.b.a.c
        public void i(View view, float f) {
            view.setTranslationX(a(f));
        }
    }

    static class l extends c {
        l() {
        }

        @Override // a.f.b.a.c
        public void i(View view, float f) {
            view.setTranslationY(a(f));
        }
    }

    static class m extends c {
        m() {
        }

        @Override // a.f.b.a.c
        public void i(View view, float f) {
            view.setTranslationZ(a(f));
        }
    }

    public static c h(String str) {
        if (str.startsWith("CUSTOM")) {
            return new b();
        }
        switch (str) {
            case "rotationX":
                return new g();
            case "rotationY":
                return new h();
            case "translationX":
                return new k();
            case "translationY":
                return new l();
            case "translationZ":
                return new m();
            case "progress":
                return new e();
            case "scaleX":
                return new i();
            case "scaleY":
                return new j();
            case "waveVariesBy":
                return new a();
            case "rotation":
                return new f();
            case "elevation":
                return new C0004c();
            case "transitionPathRotate":
                return new d();
            case "alpha":
                return new a();
            case "waveOffset":
                return new a();
            default:
                return null;
        }
    }

    public abstract void i(View view, float f2);
}
