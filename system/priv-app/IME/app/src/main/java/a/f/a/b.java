package a.f.a;

import a.f.a.d;
import a.f.a.h;
import java.util.ArrayList;

/* loaded from: classes.dex */
public class b implements d.a {

    /* renamed from: d, reason: collision with root package name */
    public a f110d;

    /* renamed from: a, reason: collision with root package name */
    h f107a = null;

    /* renamed from: b, reason: collision with root package name */
    float f108b = 0.0f;

    /* renamed from: c, reason: collision with root package name */
    ArrayList<h> f109c = new ArrayList<>();
    boolean e = false;

    public interface a {
        float a(int i);

        float b(h hVar);

        float c(h hVar, boolean z);

        void clear();

        boolean d(h hVar);

        float e(b bVar, boolean z);

        void f(h hVar, float f);

        h g(int i);

        void h(h hVar, float f, boolean z);

        void i(float f);

        void j();

        int k();
    }

    public b() {
    }

    public b(c cVar) {
        this.f110d = new a.f.a.a(this, cVar);
    }

    private boolean j(h hVar) {
        return hVar.l <= 1;
    }

    private h l(boolean[] zArr, h hVar) {
        h.a aVar;
        int k = this.f110d.k();
        h hVar2 = null;
        float f = 0.0f;
        for (int i = 0; i < k; i++) {
            float a2 = this.f110d.a(i);
            if (a2 < 0.0f) {
                h g = this.f110d.g(i);
                if ((zArr == null || !zArr[g.f124b]) && g != hVar && (((aVar = g.i) == h.a.f129c || aVar == h.a.f130d) && a2 < f)) {
                    f = a2;
                    hVar2 = g;
                }
            }
        }
        return hVar2;
    }

    @Override // a.f.a.d.a
    public h a(d dVar, boolean[] zArr) {
        return l(zArr, null);
    }

    @Override // a.f.a.d.a
    public void b(h hVar) {
        float f;
        int i = hVar.f126d;
        if (i != 1) {
            if (i == 2) {
                f = 1000.0f;
            } else if (i == 3) {
                f = 1000000.0f;
            } else if (i == 4) {
                f = 1.0E9f;
            } else if (i == 5) {
                f = 1.0E12f;
            }
            this.f110d.f(hVar, f);
        }
        f = 1.0f;
        this.f110d.f(hVar, f);
    }

    public b c(d dVar, int i) {
        this.f110d.f(dVar.k(i, "ep"), 1.0f);
        this.f110d.f(dVar.k(i, "em"), -1.0f);
        return this;
    }

    @Override // a.f.a.d.a
    public void clear() {
        this.f110d.clear();
        this.f107a = null;
        this.f108b = 0.0f;
    }

    boolean d(d dVar) {
        int k = this.f110d.k();
        h hVar = null;
        boolean z = false;
        h hVar2 = null;
        float f = 0.0f;
        float f2 = 0.0f;
        boolean z2 = false;
        boolean z3 = false;
        for (int i = 0; i < k; i++) {
            float a2 = this.f110d.a(i);
            h g = this.f110d.g(i);
            if (g.i == h.a.f127a) {
                if (hVar == null || f > a2) {
                    z2 = j(g);
                } else if (!z2 && j(g)) {
                    z2 = true;
                }
                f = a2;
                hVar = g;
            } else if (hVar == null && a2 < 0.0f) {
                if (hVar2 == null || f2 > a2) {
                    z3 = j(g);
                } else if (!z3 && j(g)) {
                    z3 = true;
                }
                f2 = a2;
                hVar2 = g;
            }
        }
        if (hVar == null) {
            hVar = hVar2;
        }
        if (hVar == null) {
            z = true;
        } else {
            m(hVar);
        }
        if (this.f110d.k() == 0) {
            this.e = true;
        }
        return z;
    }

    public b e(h hVar, h hVar2, h hVar3, h hVar4, float f) {
        this.f110d.f(hVar, -1.0f);
        this.f110d.f(hVar2, 1.0f);
        this.f110d.f(hVar3, f);
        this.f110d.f(hVar4, -f);
        return this;
    }

    public b f(float f, float f2, float f3, h hVar, h hVar2, h hVar3, h hVar4) {
        this.f108b = 0.0f;
        if (f2 == 0.0f || f == f3) {
            this.f110d.f(hVar, 1.0f);
            this.f110d.f(hVar2, -1.0f);
            this.f110d.f(hVar4, 1.0f);
            this.f110d.f(hVar3, -1.0f);
        } else if (f == 0.0f) {
            this.f110d.f(hVar, 1.0f);
            this.f110d.f(hVar2, -1.0f);
        } else if (f3 == 0.0f) {
            this.f110d.f(hVar3, 1.0f);
            this.f110d.f(hVar4, -1.0f);
        } else {
            float f4 = (f / f2) / (f3 / f2);
            this.f110d.f(hVar, 1.0f);
            this.f110d.f(hVar2, -1.0f);
            this.f110d.f(hVar4, f4);
            this.f110d.f(hVar3, -f4);
        }
        return this;
    }

    public b g(h hVar, h hVar2, h hVar3, int i) {
        boolean z = false;
        if (i != 0) {
            if (i < 0) {
                i *= -1;
                z = true;
            }
            this.f108b = i;
        }
        if (z) {
            this.f110d.f(hVar, 1.0f);
            this.f110d.f(hVar2, -1.0f);
            this.f110d.f(hVar3, -1.0f);
        } else {
            this.f110d.f(hVar, -1.0f);
            this.f110d.f(hVar2, 1.0f);
            this.f110d.f(hVar3, 1.0f);
        }
        return this;
    }

    public b h(h hVar, h hVar2, h hVar3, int i) {
        boolean z = false;
        if (i != 0) {
            if (i < 0) {
                i *= -1;
                z = true;
            }
            this.f108b = i;
        }
        if (z) {
            this.f110d.f(hVar, 1.0f);
            this.f110d.f(hVar2, -1.0f);
            this.f110d.f(hVar3, 1.0f);
        } else {
            this.f110d.f(hVar, -1.0f);
            this.f110d.f(hVar2, 1.0f);
            this.f110d.f(hVar3, -1.0f);
        }
        return this;
    }

    public b i(h hVar, h hVar2, h hVar3, h hVar4, float f) {
        this.f110d.f(hVar3, 0.5f);
        this.f110d.f(hVar4, 0.5f);
        this.f110d.f(hVar, -0.5f);
        this.f110d.f(hVar2, -0.5f);
        this.f108b = -f;
        return this;
    }

    @Override // a.f.a.d.a
    public boolean isEmpty() {
        return this.f107a == null && this.f108b == 0.0f && this.f110d.k() == 0;
    }

    public h k(h hVar) {
        return l(null, hVar);
    }

    void m(h hVar) {
        h hVar2 = this.f107a;
        if (hVar2 != null) {
            this.f110d.f(hVar2, -1.0f);
            this.f107a.f125c = -1;
            this.f107a = null;
        }
        float c2 = this.f110d.c(hVar, true) * (-1.0f);
        this.f107a = hVar;
        if (c2 == 1.0f) {
            return;
        }
        this.f108b /= c2;
        this.f110d.i(c2);
    }

    public void n(d dVar, h hVar, boolean z) {
        if (hVar.f) {
            float b2 = this.f110d.b(hVar);
            this.f108b = (hVar.e * b2) + this.f108b;
            this.f110d.c(hVar, z);
            if (z) {
                hVar.c(this);
            }
            if (this.f110d.k() == 0) {
                this.e = true;
                dVar.f115a = true;
            }
        }
    }

    public void o(d dVar, b bVar, boolean z) {
        float e = this.f110d.e(bVar, z);
        this.f108b = (bVar.f108b * e) + this.f108b;
        if (z) {
            bVar.f107a.c(this);
        }
        if (this.f107a == null || this.f110d.k() != 0) {
            return;
        }
        this.e = true;
        dVar.f115a = true;
    }

    /* JADX WARN: Removed duplicated region for block: B:21:0x0084  */
    /* JADX WARN: Removed duplicated region for block: B:25:0x008a  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public java.lang.String toString() {
        /*
            r10 = this;
            a.f.a.h r0 = r10.f107a
            if (r0 != 0) goto L7
            java.lang.String r0 = "0"
            goto L16
        L7:
            java.lang.String r0 = ""
            java.lang.StringBuilder r0 = b.b.a.a.a.j(r0)
            a.f.a.h r1 = r10.f107a
            r0.append(r1)
            java.lang.String r0 = r0.toString()
        L16:
            java.lang.String r1 = " = "
            java.lang.String r0 = b.b.a.a.a.g(r0, r1)
            float r1 = r10.f108b
            r2 = 0
            int r1 = (r1 > r2 ? 1 : (r1 == r2 ? 0 : -1))
            r3 = 0
            r4 = 1
            if (r1 == 0) goto L34
            java.lang.StringBuilder r0 = b.b.a.a.a.j(r0)
            float r1 = r10.f108b
            r0.append(r1)
            java.lang.String r0 = r0.toString()
            r1 = r4
            goto L35
        L34:
            r1 = r3
        L35:
            a.f.a.b$a r5 = r10.f110d
            int r5 = r5.k()
        L3b:
            if (r3 >= r5) goto La5
            a.f.a.b$a r6 = r10.f110d
            a.f.a.h r6 = r6.g(r3)
            if (r6 != 0) goto L46
            goto La2
        L46:
            a.f.a.b$a r7 = r10.f110d
            float r7 = r7.a(r3)
            int r8 = (r7 > r2 ? 1 : (r7 == r2 ? 0 : -1))
            if (r8 != 0) goto L51
            goto La2
        L51:
            java.lang.String r6 = r6.toString()
            r9 = -1082130432(0xffffffffbf800000, float:-1.0)
            if (r1 != 0) goto L64
            int r1 = (r7 > r2 ? 1 : (r7 == r2 ? 0 : -1))
            if (r1 >= 0) goto L7e
            java.lang.StringBuilder r0 = b.b.a.a.a.j(r0)
            java.lang.String r1 = "- "
            goto L76
        L64:
            java.lang.StringBuilder r0 = b.b.a.a.a.j(r0)
            if (r8 <= 0) goto L74
            java.lang.String r1 = " + "
            r0.append(r1)
            java.lang.String r0 = r0.toString()
            goto L7e
        L74:
            java.lang.String r1 = " - "
        L76:
            r0.append(r1)
            java.lang.String r0 = r0.toString()
            float r7 = r7 * r9
        L7e:
            r1 = 1065353216(0x3f800000, float:1.0)
            int r1 = (r7 > r1 ? 1 : (r7 == r1 ? 0 : -1))
            if (r1 != 0) goto L8a
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r1.<init>()
            goto L97
        L8a:
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r1.<init>()
            r1.append(r0)
            r1.append(r7)
            java.lang.String r0 = " "
        L97:
            r1.append(r0)
            r1.append(r6)
            java.lang.String r0 = r1.toString()
            r1 = r4
        La2:
            int r3 = r3 + 1
            goto L3b
        La5:
            if (r1 != 0) goto Lad
            java.lang.String r10 = "0.0"
            java.lang.String r0 = b.b.a.a.a.g(r0, r10)
        Lad:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: a.f.a.b.toString():java.lang.String");
    }
}
