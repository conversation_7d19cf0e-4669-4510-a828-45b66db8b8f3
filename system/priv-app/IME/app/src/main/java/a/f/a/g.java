package a.f.a;

import a.f.a.b;
import java.util.Arrays;
import java.util.Comparator;

/* loaded from: classes.dex */
public class g extends a.f.a.b {
    private int f;
    private h[] g;
    private h[] h;
    private int i;
    b j;

    class a implements Comparator<h> {
        a(g gVar) {
        }

        @Override // java.util.Comparator
        public int compare(h hVar, h hVar2) {
            return hVar.f124b - hVar2.f124b;
        }
    }

    class b {

        /* renamed from: a, reason: collision with root package name */
        h f121a;

        public b(g gVar) {
        }

        public String toString() {
            String str = "[ ";
            if (this.f121a != null) {
                for (int i = 0; i < 9; i++) {
                    StringBuilder j = b.b.a.a.a.j(str);
                    j.append(this.f121a.h[i]);
                    j.append(" ");
                    str = j.toString();
                }
            }
            return str + "] " + this.f121a;
        }
    }

    public g(c cVar) {
        super(cVar);
        this.f = 128;
        this.g = new h[128];
        this.h = new h[128];
        this.i = 0;
        this.j = new b(this);
    }

    private final void q(h hVar) {
        int i;
        int i2 = this.i + 1;
        h[] hVarArr = this.g;
        if (i2 > hVarArr.length) {
            h[] hVarArr2 = (h[]) Arrays.copyOf(hVarArr, hVarArr.length * 2);
            this.g = hVarArr2;
            this.h = (h[]) Arrays.copyOf(hVarArr2, hVarArr2.length * 2);
        }
        h[] hVarArr3 = this.g;
        int i3 = this.i;
        hVarArr3[i3] = hVar;
        int i4 = i3 + 1;
        this.i = i4;
        if (i4 > 1 && hVarArr3[i4 - 1].f124b > hVar.f124b) {
            int i5 = 0;
            while (true) {
                i = this.i;
                if (i5 >= i) {
                    break;
                }
                this.h[i5] = this.g[i5];
                i5++;
            }
            Arrays.sort(this.h, 0, i, new a(this));
            for (int i6 = 0; i6 < this.i; i6++) {
                this.g[i6] = this.h[i6];
            }
        }
        hVar.f123a = true;
        hVar.a(this);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final void r(h hVar) {
        int i = 0;
        while (i < this.i) {
            if (this.g[i] == hVar) {
                while (true) {
                    int i2 = this.i;
                    if (i >= i2 - 1) {
                        this.i = i2 - 1;
                        hVar.f123a = false;
                        return;
                    } else {
                        h[] hVarArr = this.g;
                        int i3 = i + 1;
                        hVarArr[i] = hVarArr[i3];
                        i = i3;
                    }
                }
            } else {
                i++;
            }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:31:0x0053, code lost:
    
        if (r8 < r7) goto L31;
     */
    @Override // a.f.a.b, a.f.a.d.a
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public a.f.a.h a(a.f.a.d r11, boolean[] r12) {
        /*
            r10 = this;
            r11 = 0
            r0 = -1
            r1 = r11
            r2 = r0
        L4:
            int r3 = r10.i
            if (r1 >= r3) goto L5d
            a.f.a.h[] r3 = r10.g
            r4 = r3[r1]
            int r5 = r4.f124b
            boolean r5 = r12[r5]
            if (r5 == 0) goto L13
            goto L5a
        L13:
            a.f.a.g$b r5 = r10.j
            r5.f121a = r4
            r4 = 8
            r6 = 1
            if (r2 != r0) goto L39
            java.util.Objects.requireNonNull(r5)
        L1f:
            if (r4 < 0) goto L35
            a.f.a.h r3 = r5.f121a
            float[] r3 = r3.h
            r3 = r3[r4]
            r7 = 0
            int r8 = (r3 > r7 ? 1 : (r3 == r7 ? 0 : -1))
            if (r8 <= 0) goto L2d
            goto L35
        L2d:
            int r3 = (r3 > r7 ? 1 : (r3 == r7 ? 0 : -1))
            if (r3 >= 0) goto L32
            goto L36
        L32:
            int r4 = r4 + (-1)
            goto L1f
        L35:
            r6 = r11
        L36:
            if (r6 == 0) goto L5a
            goto L59
        L39:
            r3 = r3[r2]
            java.util.Objects.requireNonNull(r5)
        L3e:
            if (r4 < 0) goto L56
            float[] r7 = r3.h
            r7 = r7[r4]
            a.f.a.h r8 = r5.f121a
            float[] r8 = r8.h
            r8 = r8[r4]
            int r9 = (r8 > r7 ? 1 : (r8 == r7 ? 0 : -1))
            if (r9 != 0) goto L51
            int r4 = r4 + (-1)
            goto L3e
        L51:
            int r3 = (r8 > r7 ? 1 : (r8 == r7 ? 0 : -1))
            if (r3 >= 0) goto L56
            goto L57
        L56:
            r6 = r11
        L57:
            if (r6 == 0) goto L5a
        L59:
            r2 = r1
        L5a:
            int r1 = r1 + 1
            goto L4
        L5d:
            if (r2 != r0) goto L61
            r10 = 0
            return r10
        L61:
            a.f.a.h[] r10 = r10.g
            r10 = r10[r2]
            return r10
        */
        throw new UnsupportedOperationException("Method not decompiled: a.f.a.g.a(a.f.a.d, boolean[]):a.f.a.h");
    }

    @Override // a.f.a.b, a.f.a.d.a
    public void b(h hVar) {
        this.j.f121a = hVar;
        Arrays.fill(hVar.h, 0.0f);
        hVar.h[hVar.f126d] = 1.0f;
        q(hVar);
    }

    @Override // a.f.a.b, a.f.a.d.a
    public void clear() {
        this.i = 0;
        this.f108b = 0.0f;
    }

    @Override // a.f.a.b, a.f.a.d.a
    public boolean isEmpty() {
        return this.i == 0;
    }

    @Override // a.f.a.b
    public void o(d dVar, a.f.a.b bVar, boolean z) {
        h hVar = bVar.f107a;
        if (hVar == null) {
            return;
        }
        b.a aVar = bVar.f110d;
        int k = aVar.k();
        for (int i = 0; i < k; i++) {
            h g = aVar.g(i);
            float a2 = aVar.a(i);
            b bVar2 = this.j;
            bVar2.f121a = g;
            boolean z2 = true;
            if (g.f123a) {
                for (int i2 = 0; i2 < 9; i2++) {
                    float[] fArr = bVar2.f121a.h;
                    fArr[i2] = (hVar.h[i2] * a2) + fArr[i2];
                    if (Math.abs(fArr[i2]) < 1.0E-4f) {
                        bVar2.f121a.h[i2] = 0.0f;
                    } else {
                        z2 = false;
                    }
                }
                if (z2) {
                    g.this.r(bVar2.f121a);
                }
                z2 = false;
            } else {
                for (int i3 = 0; i3 < 9; i3++) {
                    float f = hVar.h[i3];
                    if (f != 0.0f) {
                        float f2 = f * a2;
                        if (Math.abs(f2) < 1.0E-4f) {
                            f2 = 0.0f;
                        }
                        bVar2.f121a.h[i3] = f2;
                    } else {
                        bVar2.f121a.h[i3] = 0.0f;
                    }
                }
            }
            if (z2) {
                q(g);
            }
            this.f108b = (bVar.f108b * a2) + this.f108b;
        }
        r(hVar);
    }

    @Override // a.f.a.b
    public String toString() {
        String str = " goal -> (" + this.f108b + ") : ";
        for (int i = 0; i < this.i; i++) {
            this.j.f121a = this.g[i];
            StringBuilder j = b.b.a.a.a.j(str);
            j.append(this.j);
            j.append(" ");
            str = j.toString();
        }
        return str;
    }
}
