package a.f.a.j.o;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/* loaded from: classes.dex */
public class f implements d {

    /* renamed from: d, reason: collision with root package name */
    p f228d;
    int f;
    public int g;

    /* renamed from: a, reason: collision with root package name */
    public d f225a = null;

    /* renamed from: b, reason: collision with root package name */
    public boolean f226b = false;

    /* renamed from: c, reason: collision with root package name */
    public boolean f227c = false;
    a e = a.f229a;
    int h = 1;
    g i = null;
    public boolean j = false;
    List<d> k = new ArrayList();
    List<f> l = new ArrayList();

    /* JADX WARN: $VALUES field not found */
    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    static final class a {

        /* renamed from: a, reason: collision with root package name */
        public static final a f229a = new a("UNKNOWN", 0);

        /* renamed from: b, reason: collision with root package name */
        public static final a f230b = new a("HORIZONTAL_DIMENSION", 1);

        /* renamed from: c, reason: collision with root package name */
        public static final a f231c = new a("VERTICAL_DIMENSION", 2);

        /* renamed from: d, reason: collision with root package name */
        public static final a f232d = new a("LEFT", 3);
        public static final a e = new a("RIGHT", 4);
        public static final a f = new a("TOP", 5);
        public static final a g = new a("BOTTOM", 6);
        public static final a h = new a("BASELINE", 7);

        private a(String str, int i) {
        }
    }

    public f(p pVar) {
        this.f228d = pVar;
    }

    @Override // a.f.a.j.o.d
    public void a(d dVar) {
        Iterator<f> it = this.l.iterator();
        while (it.hasNext()) {
            if (!it.next().j) {
                return;
            }
        }
        this.f227c = true;
        d dVar2 = this.f225a;
        if (dVar2 != null) {
            dVar2.a(this);
        }
        if (this.f226b) {
            this.f228d.a(this);
            return;
        }
        f fVar = null;
        int i = 0;
        for (f fVar2 : this.l) {
            if (!(fVar2 instanceof g)) {
                i++;
                fVar = fVar2;
            }
        }
        if (fVar != null && i == 1 && fVar.j) {
            g gVar = this.i;
            if (gVar != null) {
                if (!gVar.j) {
                    return;
                } else {
                    this.f = this.h * gVar.g;
                }
            }
            c(fVar.g + this.f);
        }
        d dVar3 = this.f225a;
        if (dVar3 != null) {
            dVar3.a(this);
        }
    }

    public void b() {
        this.l.clear();
        this.k.clear();
        this.j = false;
        this.g = 0;
        this.f227c = false;
        this.f226b = false;
    }

    public void c(int i) {
        if (this.j) {
            return;
        }
        this.j = true;
        this.g = i;
        for (d dVar : this.k) {
            dVar.a(dVar);
        }
    }

    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(this.f228d.f244b.t());
        sb.append(":");
        sb.append(this.e);
        sb.append("(");
        sb.append(this.j ? Integer.valueOf(this.g) : "unresolved");
        sb.append(") <t=");
        sb.append(this.l.size());
        sb.append(":d=");
        sb.append(this.k.size());
        sb.append(">");
        return sb.toString();
    }
}
