package a.l.a.a;

import android.view.animation.Interpolator;

/* JADX INFO: Access modifiers changed from: package-private */
/* loaded from: classes.dex */
public abstract class d implements Interpolator {

    /* renamed from: a, reason: collision with root package name */
    private final float[] f459a;

    /* renamed from: b, reason: collision with root package name */
    private final float f460b;

    protected d(float[] fArr) {
        this.f459a = fArr;
        this.f460b = 1.0f / (fArr.length - 1);
    }

    @Override // android.animation.TimeInterpolator
    public float getInterpolation(float f) {
        if (f >= 1.0f) {
            return 1.0f;
        }
        if (f <= 0.0f) {
            return 0.0f;
        }
        float[] fArr = this.f459a;
        int min = Math.min((int) ((fArr.length - 1) * f), fArr.length - 2);
        float f2 = this.f460b;
        float f3 = (f - (min * f2)) / f2;
        float[] fArr2 = this.f459a;
        return b.b.a.a.a.a(fArr2[min + 1], fArr2[min], f3, fArr2[min]);
    }
}
