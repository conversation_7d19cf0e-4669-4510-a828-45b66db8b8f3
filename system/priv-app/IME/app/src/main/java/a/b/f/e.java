package a.b.f;

import a.b.f.b;
import android.content.Context;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import androidx.appcompat.view.menu.g;
import androidx.appcompat.widget.ActionBarContextView;
import java.lang.ref.WeakReference;

/* loaded from: classes.dex */
public class e extends b implements g.a {

    /* renamed from: c, reason: collision with root package name */
    private Context f17c;

    /* renamed from: d, reason: collision with root package name */
    private ActionBarContextView f18d;
    private b.a e;
    private WeakReference<View> f;
    private boolean g;
    private androidx.appcompat.view.menu.g h;

    public e(Context context, ActionBarContextView actionBarContextView, b.a aVar, boolean z) {
        this.f17c = context;
        this.f18d = actionBarContextView;
        this.e = aVar;
        androidx.appcompat.view.menu.g gVar = new androidx.appcompat.view.menu.g(actionBarContextView.getContext());
        gVar.H(1);
        this.h = gVar;
        gVar.G(this);
    }

    @Override // androidx.appcompat.view.menu.g.a
    public boolean a(androidx.appcompat.view.menu.g gVar, MenuItem menuItem) {
        return this.e.b(this, menuItem);
    }

    @Override // androidx.appcompat.view.menu.g.a
    public void b(androidx.appcompat.view.menu.g gVar) {
        k();
        this.f18d.showOverflowMenu();
    }

    @Override // a.b.f.b
    public void c() {
        if (this.g) {
            return;
        }
        this.g = true;
        this.f18d.sendAccessibilityEvent(32);
        this.e.d(this);
    }

    @Override // a.b.f.b
    public View d() {
        WeakReference<View> weakReference = this.f;
        if (weakReference != null) {
            return weakReference.get();
        }
        return null;
    }

    @Override // a.b.f.b
    public Menu e() {
        return this.h;
    }

    @Override // a.b.f.b
    public MenuInflater f() {
        return new g(this.f18d.getContext());
    }

    @Override // a.b.f.b
    public CharSequence g() {
        return this.f18d.getSubtitle();
    }

    @Override // a.b.f.b
    public CharSequence i() {
        return this.f18d.getTitle();
    }

    @Override // a.b.f.b
    public void k() {
        this.e.a(this, this.h);
    }

    @Override // a.b.f.b
    public boolean l() {
        return this.f18d.isTitleOptional();
    }

    @Override // a.b.f.b
    public void m(View view) {
        this.f18d.setCustomView(view);
        this.f = view != null ? new WeakReference<>(view) : null;
    }

    @Override // a.b.f.b
    public void n(int i) {
        this.f18d.setSubtitle(this.f17c.getString(i));
    }

    @Override // a.b.f.b
    public void o(CharSequence charSequence) {
        this.f18d.setSubtitle(charSequence);
    }

    @Override // a.b.f.b
    public void q(int i) {
        this.f18d.setTitle(this.f17c.getString(i));
    }

    @Override // a.b.f.b
    public void r(CharSequence charSequence) {
        this.f18d.setTitle(charSequence);
    }

    @Override // a.b.f.b
    public void s(boolean z) {
        super.s(z);
        this.f18d.setTitleOptional(z);
    }
}
