package a.h.h;

import a.h.h.q;
import android.text.TextUtils;
import android.view.View;

/* loaded from: classes.dex */
class n extends q.b<CharSequence> {
    n(int i, Class cls, int i2, int i3) {
        super(i, cls, i2, i3);
    }

    @Override // a.h.h.q.b
    CharSequence b(View view) {
        return view.getAccessibilityPaneTitle();
    }

    @Override // a.h.h.q.b
    void c(View view, CharSequence charSequence) {
        view.setAccessibilityPaneTitle(charSequence);
    }

    @Override // a.h.h.q.b
    boolean f(CharSequence charSequence, CharSequence charSequence2) {
        return !TextUtils.equals(charSequence, charSequence2);
    }
}
