package a.f.a.i.a;

/* loaded from: classes.dex */
public class i extends c {

    /* renamed from: d, reason: collision with root package name */
    double f167d;
    double e;

    i(String str) {
        this.f141a = str;
        int indexOf = str.indexOf(40);
        int indexOf2 = str.indexOf(44, indexOf);
        this.f167d = Double.parseDouble(str.substring(indexOf + 1, indexOf2).trim());
        int i = indexOf2 + 1;
        this.e = Double.parseDouble(str.substring(i, str.indexOf(44, i)).trim());
    }

    @Override // a.f.a.i.a.c
    public double a(double d2) {
        double d3 = this.e;
        if (d2 < d3) {
            return (d3 * d2) / (((d3 - d2) * this.f167d) + d2);
        }
        return ((d2 - 1.0d) * (1.0d - d3)) / ((1.0d - d2) - ((d3 - d2) * this.f167d));
    }

    @Override // a.f.a.i.a.c
    public double b(double d2) {
        double d3 = this.e;
        if (d2 < d3) {
            double d4 = this.f167d;
            double d5 = d4 * d3 * d3;
            double d6 = ((d3 - d2) * d4) + d2;
            return d5 / (d6 * d6);
        }
        double d7 = this.f167d;
        double d8 = d3 - 1.0d;
        double d9 = (((d3 - d2) * (-d7)) - d2) + 1.0d;
        return ((d8 * d7) * d8) / (d9 * d9);
    }
}
