package a.f.a.j.o;

import a.f.a.j.d;
import a.f.a.j.e;
import a.f.a.j.o.b;
import java.util.ArrayList;
import java.util.Iterator;

/* loaded from: classes.dex */
public class h {

    /* renamed from: a, reason: collision with root package name */
    private static b.a f233a = new b.a();

    /* renamed from: b, reason: collision with root package name */
    private static int f234b;

    /* renamed from: c, reason: collision with root package name */
    private static int f235c;

    private static boolean a(a.f.a.j.e eVar) {
        e.a aVar = e.a.MATCH_CONSTRAINT;
        e.a aVar2 = e.a.WRAP_CONTENT;
        e.a aVar3 = e.a.FIXED;
        e.a z = eVar.z();
        e.a O = eVar.O();
        a.f.a.j.e eVar2 = eVar.X;
        a.f.a.j.f fVar = eVar2 != null ? (a.f.a.j.f) eVar2 : null;
        if (fVar != null) {
            fVar.z();
        }
        if (fVar != null) {
            fVar.O();
        }
        boolean z2 = z == aVar3 || eVar.f0() || z == aVar2 || (z == aVar && eVar.r == 0 && eVar.a0 == 0.0f && eVar.U(0)) || (z == aVar && eVar.r == 1 && eVar.W(0, eVar.Q()));
        boolean z3 = O == aVar3 || eVar.g0() || O == aVar2 || (O == aVar && eVar.s == 0 && eVar.a0 == 0.0f && eVar.U(1)) || (O == aVar && eVar.s == 1 && eVar.W(1, eVar.w()));
        if (eVar.a0 <= 0.0f || !(z2 || z3)) {
            return z2 && z3;
        }
        return true;
    }

    private static void b(int i, a.f.a.j.e eVar, b.InterfaceC0003b interfaceC0003b, boolean z) {
        boolean z2;
        a.f.a.j.d dVar;
        a.f.a.j.d dVar2;
        a.f.a.j.d dVar3;
        a.f.a.j.d dVar4;
        e.a aVar = e.a.MATCH_CONSTRAINT;
        if (eVar.Y()) {
            return;
        }
        f234b++;
        int i2 = 0;
        if (!(eVar instanceof a.f.a.j.f) && eVar.e0() && a(eVar)) {
            a.f.a.j.f.n1(eVar, interfaceC0003b, new b.a(), 0);
        }
        a.f.a.j.d o = eVar.o(d.a.LEFT);
        a.f.a.j.d o2 = eVar.o(d.a.RIGHT);
        int e = o.e();
        int e2 = o2.e();
        if (o.d() != null && o.k()) {
            Iterator<a.f.a.j.d> it = o.d().iterator();
            while (it.hasNext()) {
                a.f.a.j.d next = it.next();
                a.f.a.j.e eVar2 = next.f196d;
                int i3 = i + 1;
                boolean a2 = a(eVar2);
                if (eVar2.e0() && a2) {
                    a.f.a.j.f.n1(eVar2, interfaceC0003b, new b.a(), i2);
                }
                int i4 = ((next == eVar2.L && (dVar4 = eVar2.N.f) != null && dVar4.k()) || (next == eVar2.N && (dVar3 = eVar2.L.f) != null && dVar3.k())) ? 1 : i2;
                if (eVar2.z() != aVar || a2) {
                    if (!eVar2.e0()) {
                        a.f.a.j.d dVar5 = eVar2.L;
                        if (next == dVar5 && eVar2.N.f == null) {
                            int f = dVar5.f() + e;
                            eVar2.v0(f, eVar2.Q() + f);
                        } else {
                            a.f.a.j.d dVar6 = eVar2.N;
                            if (next == dVar6 && dVar5.f == null) {
                                int f2 = e - dVar6.f();
                                eVar2.v0(f2 - eVar2.Q(), f2);
                            } else if (i4 != 0 && !eVar2.a0()) {
                                c(i3, interfaceC0003b, eVar2, z);
                            }
                        }
                        b(i3, eVar2, interfaceC0003b, z);
                    }
                } else if (eVar2.z() == aVar && eVar2.v >= 0 && eVar2.u >= 0 && ((eVar2.P() == 8 || (eVar2.r == 0 && eVar2.a0 == 0.0f)) && !eVar2.a0() && !eVar2.d0() && i4 != 0 && !eVar2.a0())) {
                    d(i3, eVar, interfaceC0003b, eVar2, z);
                }
                i2 = 0;
            }
        }
        if (eVar instanceof a.f.a.j.h) {
            return;
        }
        if (o2.d() != null && o2.k()) {
            Iterator<a.f.a.j.d> it2 = o2.d().iterator();
            while (it2.hasNext()) {
                a.f.a.j.d next2 = it2.next();
                a.f.a.j.e eVar3 = next2.f196d;
                int i5 = i + 1;
                boolean a3 = a(eVar3);
                if (eVar3.e0() && a3) {
                    z2 = false;
                    a.f.a.j.f.n1(eVar3, interfaceC0003b, new b.a(), 0);
                } else {
                    z2 = false;
                }
                boolean z3 = ((next2 == eVar3.L && (dVar2 = eVar3.N.f) != null && dVar2.k()) || (next2 == eVar3.N && (dVar = eVar3.L.f) != null && dVar.k())) ? true : z2;
                if (eVar3.z() != aVar || a3) {
                    if (!eVar3.e0()) {
                        a.f.a.j.d dVar7 = eVar3.L;
                        if (next2 == dVar7 && eVar3.N.f == null) {
                            int f3 = dVar7.f() + e2;
                            eVar3.v0(f3, eVar3.Q() + f3);
                        } else {
                            a.f.a.j.d dVar8 = eVar3.N;
                            if (next2 == dVar8 && dVar7.f == null) {
                                int f4 = e2 - dVar8.f();
                                eVar3.v0(f4 - eVar3.Q(), f4);
                            } else if (z3 && !eVar3.a0()) {
                                c(i5, interfaceC0003b, eVar3, z);
                            }
                        }
                        b(i5, eVar3, interfaceC0003b, z);
                    }
                } else if (eVar3.z() == aVar && eVar3.v >= 0 && eVar3.u >= 0 && (eVar3.P() == 8 || (eVar3.r == 0 && eVar3.a0 == 0.0f))) {
                    if (!eVar3.a0() && !eVar3.d0() && z3 && !eVar3.a0()) {
                        d(i5, eVar, interfaceC0003b, eVar3, z);
                    }
                }
            }
        }
        eVar.i0();
    }

    private static void c(int i, b.InterfaceC0003b interfaceC0003b, a.f.a.j.e eVar, boolean z) {
        float x = eVar.x();
        int e = eVar.L.f.e();
        int e2 = eVar.N.f.e();
        int f = eVar.L.f() + e;
        int f2 = e2 - eVar.N.f();
        if (e == e2) {
            x = 0.5f;
        } else {
            e = f;
            e2 = f2;
        }
        int Q = eVar.Q();
        int i2 = (e2 - e) - Q;
        if (e > e2) {
            i2 = (e - e2) - Q;
        }
        int i3 = ((int) (i2 > 0 ? (x * i2) + 0.5f : x * i2)) + e;
        int i4 = i3 + Q;
        if (e > e2) {
            i4 = i3 - Q;
        }
        eVar.v0(i3, i4);
        b(i + 1, eVar, interfaceC0003b, z);
    }

    private static void d(int i, a.f.a.j.e eVar, b.InterfaceC0003b interfaceC0003b, a.f.a.j.e eVar2, boolean z) {
        float x = eVar2.x();
        int f = eVar2.L.f() + eVar2.L.f.e();
        int e = eVar2.N.f.e() - eVar2.N.f();
        if (e >= f) {
            int Q = eVar2.Q();
            if (eVar2.P() != 8) {
                int i2 = eVar2.r;
                if (i2 == 2) {
                    if (!(eVar instanceof a.f.a.j.f)) {
                        eVar = eVar.X;
                    }
                    Q = (int) (eVar2.x() * 0.5f * eVar.Q());
                } else if (i2 == 0) {
                    Q = e - f;
                }
                Q = Math.max(eVar2.u, Q);
                int i3 = eVar2.v;
                if (i3 > 0) {
                    Q = Math.min(i3, Q);
                }
            }
            int i4 = f + ((int) ((x * ((e - f) - Q)) + 0.5f));
            eVar2.v0(i4, Q + i4);
            b(i + 1, eVar2, interfaceC0003b, z);
        }
    }

    private static void e(int i, b.InterfaceC0003b interfaceC0003b, a.f.a.j.e eVar) {
        float M = eVar.M();
        int e = eVar.M.f.e();
        int e2 = eVar.O.f.e();
        int f = eVar.M.f() + e;
        int f2 = e2 - eVar.O.f();
        if (e == e2) {
            M = 0.5f;
        } else {
            e = f;
            e2 = f2;
        }
        int w = eVar.w();
        int i2 = (e2 - e) - w;
        if (e > e2) {
            i2 = (e - e2) - w;
        }
        int i3 = (int) (i2 > 0 ? (M * i2) + 0.5f : M * i2);
        int i4 = e + i3;
        int i5 = i4 + w;
        if (e > e2) {
            i4 = e - i3;
            i5 = i4 - w;
        }
        eVar.y0(i4, i5);
        h(i + 1, eVar, interfaceC0003b);
    }

    private static void f(int i, a.f.a.j.e eVar, b.InterfaceC0003b interfaceC0003b, a.f.a.j.e eVar2) {
        float M = eVar2.M();
        int f = eVar2.M.f() + eVar2.M.f.e();
        int e = eVar2.O.f.e() - eVar2.O.f();
        if (e >= f) {
            int w = eVar2.w();
            if (eVar2.P() != 8) {
                int i2 = eVar2.s;
                if (i2 == 2) {
                    if (!(eVar instanceof a.f.a.j.f)) {
                        eVar = eVar.X;
                    }
                    w = (int) (M * 0.5f * eVar.w());
                } else if (i2 == 0) {
                    w = e - f;
                }
                w = Math.max(eVar2.x, w);
                int i3 = eVar2.y;
                if (i3 > 0) {
                    w = Math.min(i3, w);
                }
            }
            int i4 = f + ((int) ((M * ((e - f) - w)) + 0.5f));
            eVar2.y0(i4, w + i4);
            h(i + 1, eVar2, interfaceC0003b);
        }
    }

    public static void g(a.f.a.j.f fVar, b.InterfaceC0003b interfaceC0003b) {
        int c1;
        int c12;
        e.a aVar = e.a.FIXED;
        e.a z = fVar.z();
        e.a O = fVar.O();
        f234b = 0;
        f235c = 0;
        fVar.m0();
        ArrayList<a.f.a.j.e> arrayList = fVar.N0;
        int size = arrayList.size();
        for (int i = 0; i < size; i++) {
            arrayList.get(i).m0();
        }
        boolean k1 = fVar.k1();
        if (z == aVar) {
            fVar.v0(0, fVar.Q());
        } else {
            fVar.w0(0);
        }
        boolean z2 = false;
        boolean z3 = false;
        for (int i2 = 0; i2 < size; i2++) {
            a.f.a.j.e eVar = arrayList.get(i2);
            if (eVar instanceof a.f.a.j.h) {
                a.f.a.j.h hVar = (a.f.a.j.h) eVar;
                if (hVar.Z0() == 1) {
                    if (hVar.a1() != -1) {
                        c12 = hVar.a1();
                    } else if (hVar.b1() == -1 || !fVar.f0()) {
                        if (fVar.f0()) {
                            c12 = (int) ((hVar.c1() * fVar.Q()) + 0.5f);
                        }
                        z2 = true;
                    } else {
                        c12 = fVar.Q() - hVar.b1();
                    }
                    hVar.d1(c12);
                    z2 = true;
                }
            } else if ((eVar instanceof a.f.a.j.a) && ((a.f.a.j.a) eVar).d1() == 0) {
                z3 = true;
            }
        }
        if (z2) {
            for (int i3 = 0; i3 < size; i3++) {
                a.f.a.j.e eVar2 = arrayList.get(i3);
                if (eVar2 instanceof a.f.a.j.h) {
                    a.f.a.j.h hVar2 = (a.f.a.j.h) eVar2;
                    if (hVar2.Z0() == 1) {
                        b(0, hVar2, interfaceC0003b, k1);
                    }
                }
            }
        }
        b(0, fVar, interfaceC0003b, k1);
        if (z3) {
            for (int i4 = 0; i4 < size; i4++) {
                a.f.a.j.e eVar3 = arrayList.get(i4);
                if (eVar3 instanceof a.f.a.j.a) {
                    a.f.a.j.a aVar2 = (a.f.a.j.a) eVar3;
                    if (aVar2.d1() == 0 && aVar2.Z0()) {
                        b(1, aVar2, interfaceC0003b, k1);
                    }
                }
            }
        }
        if (O == aVar) {
            fVar.y0(0, fVar.w());
        } else {
            fVar.x0(0);
        }
        boolean z4 = false;
        boolean z5 = false;
        for (int i5 = 0; i5 < size; i5++) {
            a.f.a.j.e eVar4 = arrayList.get(i5);
            if (eVar4 instanceof a.f.a.j.h) {
                a.f.a.j.h hVar3 = (a.f.a.j.h) eVar4;
                if (hVar3.Z0() == 0) {
                    if (hVar3.a1() != -1) {
                        c1 = hVar3.a1();
                    } else if (hVar3.b1() == -1 || !fVar.g0()) {
                        if (fVar.g0()) {
                            c1 = (int) ((hVar3.c1() * fVar.w()) + 0.5f);
                        }
                        z4 = true;
                    } else {
                        c1 = fVar.w() - hVar3.b1();
                    }
                    hVar3.d1(c1);
                    z4 = true;
                }
            } else if ((eVar4 instanceof a.f.a.j.a) && ((a.f.a.j.a) eVar4).d1() == 1) {
                z5 = true;
            }
        }
        if (z4) {
            for (int i6 = 0; i6 < size; i6++) {
                a.f.a.j.e eVar5 = arrayList.get(i6);
                if (eVar5 instanceof a.f.a.j.h) {
                    a.f.a.j.h hVar4 = (a.f.a.j.h) eVar5;
                    if (hVar4.Z0() == 0) {
                        h(1, hVar4, interfaceC0003b);
                    }
                }
            }
        }
        h(0, fVar, interfaceC0003b);
        if (z5) {
            for (int i7 = 0; i7 < size; i7++) {
                a.f.a.j.e eVar6 = arrayList.get(i7);
                if (eVar6 instanceof a.f.a.j.a) {
                    a.f.a.j.a aVar3 = (a.f.a.j.a) eVar6;
                    if (aVar3.d1() == 1 && aVar3.Z0()) {
                        h(1, aVar3, interfaceC0003b);
                    }
                }
            }
        }
        for (int i8 = 0; i8 < size; i8++) {
            a.f.a.j.e eVar7 = arrayList.get(i8);
            if (eVar7.e0() && a(eVar7)) {
                a.f.a.j.f.n1(eVar7, interfaceC0003b, f233a, 0);
                if (!(eVar7 instanceof a.f.a.j.h)) {
                    b(0, eVar7, interfaceC0003b, k1);
                } else if (((a.f.a.j.h) eVar7).Z0() != 0) {
                    b(0, eVar7, interfaceC0003b, k1);
                }
                h(0, eVar7, interfaceC0003b);
            }
        }
    }

    private static void h(int i, a.f.a.j.e eVar, b.InterfaceC0003b interfaceC0003b) {
        a.f.a.j.d dVar;
        a.f.a.j.d dVar2;
        a.f.a.j.d dVar3;
        a.f.a.j.d dVar4;
        e.a aVar = e.a.MATCH_CONSTRAINT;
        if (eVar.h0()) {
            return;
        }
        boolean z = true;
        f235c++;
        if (!(eVar instanceof a.f.a.j.f) && eVar.e0() && a(eVar)) {
            a.f.a.j.f.n1(eVar, interfaceC0003b, new b.a(), 0);
        }
        a.f.a.j.d o = eVar.o(d.a.TOP);
        a.f.a.j.d o2 = eVar.o(d.a.BOTTOM);
        int e = o.e();
        int e2 = o2.e();
        if (o.d() != null && o.k()) {
            Iterator<a.f.a.j.d> it = o.d().iterator();
            while (it.hasNext()) {
                a.f.a.j.d next = it.next();
                a.f.a.j.e eVar2 = next.f196d;
                int i2 = i + 1;
                boolean a2 = a(eVar2);
                if (eVar2.e0() && a2) {
                    a.f.a.j.f.n1(eVar2, interfaceC0003b, new b.a(), 0);
                }
                boolean z2 = ((next == eVar2.M && (dVar4 = eVar2.O.f) != null && dVar4.k()) || (next == eVar2.O && (dVar3 = eVar2.M.f) != null && dVar3.k())) ? z : false;
                if (eVar2.O() != aVar || a2) {
                    if (!eVar2.e0()) {
                        a.f.a.j.d dVar5 = eVar2.M;
                        if (next == dVar5 && eVar2.O.f == null) {
                            int f = dVar5.f() + e;
                            eVar2.y0(f, eVar2.w() + f);
                        } else {
                            a.f.a.j.d dVar6 = eVar2.O;
                            if (next == dVar6 && dVar5.f == null) {
                                int f2 = e - dVar6.f();
                                eVar2.y0(f2 - eVar2.w(), f2);
                            } else if (z2 && !eVar2.c0()) {
                                e(i2, interfaceC0003b, eVar2);
                            }
                        }
                        h(i2, eVar2, interfaceC0003b);
                    }
                } else if (eVar2.O() == aVar && eVar2.y >= 0 && eVar2.x >= 0 && ((eVar2.P() == 8 || (eVar2.s == 0 && eVar2.a0 == 0.0f)) && !eVar2.c0() && !eVar2.d0() && z2 && !eVar2.c0())) {
                    f(i2, eVar, interfaceC0003b, eVar2);
                }
                z = true;
            }
        }
        if (eVar instanceof a.f.a.j.h) {
            return;
        }
        if (o2.d() != null && o2.k()) {
            Iterator<a.f.a.j.d> it2 = o2.d().iterator();
            while (it2.hasNext()) {
                a.f.a.j.d next2 = it2.next();
                a.f.a.j.e eVar3 = next2.f196d;
                int i3 = i + 1;
                boolean a3 = a(eVar3);
                if (eVar3.e0() && a3) {
                    a.f.a.j.f.n1(eVar3, interfaceC0003b, new b.a(), 0);
                }
                boolean z3 = (next2 == eVar3.M && (dVar2 = eVar3.O.f) != null && dVar2.k()) || (next2 == eVar3.O && (dVar = eVar3.M.f) != null && dVar.k());
                if (eVar3.O() != aVar || a3) {
                    if (!eVar3.e0()) {
                        a.f.a.j.d dVar7 = eVar3.M;
                        if (next2 == dVar7 && eVar3.O.f == null) {
                            int f3 = dVar7.f() + e2;
                            eVar3.y0(f3, eVar3.w() + f3);
                        } else {
                            a.f.a.j.d dVar8 = eVar3.O;
                            if (next2 == dVar8 && dVar7.f == null) {
                                int f4 = e2 - dVar8.f();
                                eVar3.y0(f4 - eVar3.w(), f4);
                            } else if (z3 && !eVar3.c0()) {
                                e(i3, interfaceC0003b, eVar3);
                            }
                        }
                        h(i3, eVar3, interfaceC0003b);
                    }
                } else if (eVar3.O() == aVar && eVar3.y >= 0 && eVar3.x >= 0 && (eVar3.P() == 8 || (eVar3.s == 0 && eVar3.a0 == 0.0f))) {
                    if (!eVar3.c0() && !eVar3.d0() && z3 && !eVar3.c0()) {
                        f(i3, eVar, interfaceC0003b, eVar3);
                    }
                }
            }
        }
        a.f.a.j.d o3 = eVar.o(d.a.BASELINE);
        if (o3.d() != null && o3.k()) {
            int e3 = o3.e();
            Iterator<a.f.a.j.d> it3 = o3.d().iterator();
            while (it3.hasNext()) {
                a.f.a.j.d next3 = it3.next();
                a.f.a.j.e eVar4 = next3.f196d;
                int i4 = i + 1;
                boolean a4 = a(eVar4);
                if (eVar4.e0() && a4) {
                    a.f.a.j.f.n1(eVar4, interfaceC0003b, new b.a(), 0);
                }
                if (eVar4.O() != aVar || a4) {
                    if (!eVar4.e0() && next3 == eVar4.P) {
                        eVar4.u0(next3.f() + e3);
                        h(i4, eVar4, interfaceC0003b);
                    }
                }
            }
        }
        eVar.j0();
    }
}
