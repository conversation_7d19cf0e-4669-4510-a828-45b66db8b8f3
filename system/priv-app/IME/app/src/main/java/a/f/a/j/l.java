package a.f.a.j;

import a.f.a.j.d;

/* loaded from: classes.dex */
public class l extends m {
    @Override // a.f.a.j.e
    public void f(a.f.a.d dVar, boolean z) {
        super.f(dVar, z);
        if (this.O0 > 0) {
            e eVar = this.N0[0];
            eVar.l0();
            eVar.m0 = 0.5f;
            eVar.l0 = 0.5f;
            d.a aVar = d.a.LEFT;
            eVar.i(aVar, this, aVar, 0);
            d.a aVar2 = d.a.RIGHT;
            eVar.i(aVar2, this, aVar2, 0);
            d.a aVar3 = d.a.TOP;
            eVar.i(aVar3, this, aVar3, 0);
            d.a aVar4 = d.a.BOTTOM;
            eVar.i(aVar4, this, aVar4, 0);
        }
    }

    @Override // a.f.a.j.m
    public void h1(int i, int i2, int i3, int i4) {
        int e1 = e1() + f1() + 0;
        int g1 = g1() + d1() + 0;
        if (this.O0 > 0) {
            e1 += this.N0[0].Q();
            g1 += this.N0[0].w();
        }
        int max = Math.max(this.j0, e1);
        int max2 = Math.max(this.k0, g1);
        if (i != 1073741824) {
            i2 = i == Integer.MIN_VALUE ? Math.min(max, i2) : i == 0 ? max : 0;
        }
        if (i3 != 1073741824) {
            i4 = i3 == Integer.MIN_VALUE ? Math.min(max2, i4) : i3 == 0 ? max2 : 0;
        }
        l1(i2, i4);
        S0(i2);
        A0(i4);
        k1(this.O0 > 0);
    }
}
