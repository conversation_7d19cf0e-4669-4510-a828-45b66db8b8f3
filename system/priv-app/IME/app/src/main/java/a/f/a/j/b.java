package a.f.a.j;

/* loaded from: classes.dex */
public class b {
    /* JADX WARN: Code restructure failed: missing block: B:22:0x006e, code lost:
    
        if (r14 == r9) goto L37;
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x008e, code lost:
    
        r14 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:332:0x008c, code lost:
    
        r14 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:341:0x008a, code lost:
    
        if (r14 == 2) goto L37;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:101:0x01e8  */
    /* JADX WARN: Removed duplicated region for block: B:104:0x0207  */
    /* JADX WARN: Removed duplicated region for block: B:113:0x0223  */
    /* JADX WARN: Removed duplicated region for block: B:134:0x02ad A[ADDED_TO_REGION] */
    /* JADX WARN: Removed duplicated region for block: B:154:0x0552 A[ADDED_TO_REGION] */
    /* JADX WARN: Removed duplicated region for block: B:160:0x055e  */
    /* JADX WARN: Removed duplicated region for block: B:163:0x056b  */
    /* JADX WARN: Removed duplicated region for block: B:166:0x0574  */
    /* JADX WARN: Removed duplicated region for block: B:168:0x057b  */
    /* JADX WARN: Removed duplicated region for block: B:172:0x0589  */
    /* JADX WARN: Removed duplicated region for block: B:174:0x058f A[ADDED_TO_REGION] */
    /* JADX WARN: Removed duplicated region for block: B:178:0x05ae A[ADDED_TO_REGION, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:180:0x0577  */
    /* JADX WARN: Removed duplicated region for block: B:181:0x056e  */
    /* JADX WARN: Removed duplicated region for block: B:182:0x0560  */
    /* JADX WARN: Removed duplicated region for block: B:191:0x0324 A[ADDED_TO_REGION] */
    /* JADX WARN: Removed duplicated region for block: B:223:0x0399  */
    /* JADX WARN: Removed duplicated region for block: B:227:0x03bb  */
    /* JADX WARN: Removed duplicated region for block: B:230:0x03cb A[ADDED_TO_REGION] */
    /* JADX WARN: Removed duplicated region for block: B:243:0x03a0  */
    /* JADX WARN: Removed duplicated region for block: B:258:0x0436 A[ADDED_TO_REGION] */
    /* JADX WARN: Removed duplicated region for block: B:266:0x0449  */
    /* JADX WARN: Removed duplicated region for block: B:283:0x04a3  */
    /* JADX WARN: Removed duplicated region for block: B:286:0x04b8  */
    /* JADX WARN: Removed duplicated region for block: B:301:0x04bb  */
    /* JADX WARN: Removed duplicated region for block: B:302:0x04ab  */
    /* JADX WARN: Removed duplicated region for block: B:315:0x050e  */
    /* JADX WARN: Removed duplicated region for block: B:321:0x0541 A[ADDED_TO_REGION] */
    /* JADX WARN: Type inference failed for: r1v62, types: [a.f.a.j.e] */
    /* JADX WARN: Type inference failed for: r9v3 */
    /* JADX WARN: Type inference failed for: r9v34 */
    /* JADX WARN: Type inference failed for: r9v35 */
    /* JADX WARN: Type inference failed for: r9v4, types: [a.f.a.j.e] */
    /* JADX WARN: Type inference failed for: r9v40 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static void a(a.f.a.j.f r42, a.f.a.d r43, java.util.ArrayList<a.f.a.j.e> r44, int r45) {
        /*
            Method dump skipped, instructions count: 1465
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: a.f.a.j.b.a(a.f.a.j.f, a.f.a.d, java.util.ArrayList, int):void");
    }
}
