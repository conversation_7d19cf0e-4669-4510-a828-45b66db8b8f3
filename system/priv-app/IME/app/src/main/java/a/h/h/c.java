package a.h.h;

import java.util.Objects;

/* loaded from: classes.dex */
public final class c {

    /* renamed from: a, reason: collision with root package name */
    private final Object f364a;

    private c(Object obj) {
        this.f364a = obj;
    }

    static c a(Object obj) {
        if (obj == null) {
            return null;
        }
        return new c(obj);
    }

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || c.class != obj.getClass()) {
            return false;
        }
        return Objects.equals(this.f364a, ((c) obj).f364a);
    }

    public int hashCode() {
        Object obj = this.f364a;
        if (obj == null) {
            return 0;
        }
        return obj.hashCode();
    }

    public String toString() {
        StringBuilder j = b.b.a.a.a.j("DisplayCutoutCompat{");
        j.append(this.f364a);
        j.append("}");
        return j.toString();
    }
}
