package a.h.e;

import android.os.Handler;
import java.util.concurrent.Callable;

/* loaded from: classes.dex */
class i<T> implements Runnable {

    /* renamed from: a, reason: collision with root package name */
    private Callable<T> f324a;

    /* renamed from: b, reason: collision with root package name */
    private a.h.g.a<T> f325b;

    /* renamed from: c, reason: collision with root package name */
    private Handler f326c;

    class a implements Runnable {

        /* renamed from: a, reason: collision with root package name */
        final /* synthetic */ a.h.g.a f327a;

        /* renamed from: b, reason: collision with root package name */
        final /* synthetic */ Object f328b;

        a(i iVar, a.h.g.a aVar, Object obj) {
            this.f327a = aVar;
            this.f328b = obj;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // java.lang.Runnable
        public void run() {
            this.f327a.a(this.f328b);
        }
    }

    i(Handler handler, Callable<T> callable, a.h.g.a<T> aVar) {
        this.f324a = callable;
        this.f325b = aVar;
        this.f326c = handler;
    }

    @Override // java.lang.Runnable
    public void run() {
        T t;
        try {
            t = this.f324a.call();
        } catch (Exception unused) {
            t = null;
        }
        this.f326c.post(new a(this, this.f325b, t));
    }
}
