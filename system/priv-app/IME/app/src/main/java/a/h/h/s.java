package a.h.h;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.view.View;
import android.view.animation.Interpolator;
import java.lang.ref.WeakReference;

/* loaded from: classes.dex */
public final class s {

    /* renamed from: a, reason: collision with root package name */
    private WeakReference<View> f387a;

    /* renamed from: b, reason: collision with root package name */
    int f388b = -1;

    class a extends AnimatorListenerAdapter {

        /* renamed from: a, reason: collision with root package name */
        final /* synthetic */ t f389a;

        /* renamed from: b, reason: collision with root package name */
        final /* synthetic */ View f390b;

        a(s sVar, t tVar, View view) {
            this.f389a = tVar;
            this.f390b = view;
        }

        @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
        public void onAnimationCancel(Animator animator) {
            this.f389a.c(this.f390b);
        }

        @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
        public void onAnimationEnd(Animator animator) {
            this.f389a.a(this.f390b);
        }

        @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
        public void onAnimationStart(Animator animator) {
            this.f389a.b(this.f390b);
        }
    }

    class b implements ValueAnimator.AnimatorUpdateListener {

        /* renamed from: a, reason: collision with root package name */
        final /* synthetic */ v f391a;

        /* renamed from: b, reason: collision with root package name */
        final /* synthetic */ View f392b;

        b(s sVar, v vVar, View view) {
            this.f391a = vVar;
            this.f392b = view;
        }

        @Override // android.animation.ValueAnimator.AnimatorUpdateListener
        public void onAnimationUpdate(ValueAnimator valueAnimator) {
            this.f391a.a(this.f392b);
        }
    }

    s(View view) {
        this.f387a = new WeakReference<>(view);
    }

    private void g(View view, t tVar) {
        if (tVar != null) {
            view.animate().setListener(new a(this, tVar, view));
        } else {
            view.animate().setListener(null);
        }
    }

    public s a(float f) {
        View view = this.f387a.get();
        if (view != null) {
            view.animate().alpha(f);
        }
        return this;
    }

    public void b() {
        View view = this.f387a.get();
        if (view != null) {
            view.animate().cancel();
        }
    }

    public long c() {
        View view = this.f387a.get();
        if (view != null) {
            return view.animate().getDuration();
        }
        return 0L;
    }

    public s d(long j) {
        View view = this.f387a.get();
        if (view != null) {
            view.animate().setDuration(j);
        }
        return this;
    }

    public s e(Interpolator interpolator) {
        View view = this.f387a.get();
        if (view != null) {
            view.animate().setInterpolator(interpolator);
        }
        return this;
    }

    public s f(t tVar) {
        View view = this.f387a.get();
        if (view != null) {
            g(view, tVar);
        }
        return this;
    }

    public s h(long j) {
        View view = this.f387a.get();
        if (view != null) {
            view.animate().setStartDelay(j);
        }
        return this;
    }

    public s i(v vVar) {
        View view = this.f387a.get();
        if (view != null) {
            view.animate().setUpdateListener(vVar != null ? new b(this, vVar, view) : null);
        }
        return this;
    }

    public void j() {
        View view = this.f387a.get();
        if (view != null) {
            view.animate().start();
        }
    }

    public s k(float f) {
        View view = this.f387a.get();
        if (view != null) {
            view.animate().translationY(f);
        }
        return this;
    }
}
