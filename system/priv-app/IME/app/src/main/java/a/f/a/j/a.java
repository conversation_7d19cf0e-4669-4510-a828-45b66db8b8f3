package a.f.a.j;

import a.f.a.j.d;
import a.f.a.j.e;
import java.util.HashMap;

/* loaded from: classes.dex */
public class a extends j {
    private int P0 = 0;
    private boolean Q0 = true;
    private int R0 = 0;
    boolean S0 = false;

    public boolean Z0() {
        int i;
        d o;
        d o2;
        d o3;
        int i2;
        int i3;
        d.a aVar = d.a.BOTTOM;
        d.a aVar2 = d.a.TOP;
        d.a aVar3 = d.a.RIGHT;
        d.a aVar4 = d.a.LEFT;
        int i4 = 0;
        boolean z = true;
        while (true) {
            i = this.O0;
            if (i4 >= i) {
                break;
            }
            e eVar = this.N0[i4];
            if ((this.Q0 || eVar.g()) && ((((i2 = this.P0) == 0 || i2 == 1) && !eVar.f0()) || (((i3 = this.P0) == 2 || i3 == 3) && !eVar.g0()))) {
                z = false;
            }
            i4++;
        }
        if (!z || i <= 0) {
            return false;
        }
        int i5 = 0;
        boolean z2 = false;
        for (int i6 = 0; i6 < this.O0; i6++) {
            e eVar2 = this.N0[i6];
            if (this.Q0 || eVar2.g()) {
                if (!z2) {
                    int i7 = this.P0;
                    if (i7 == 0) {
                        o3 = eVar2.o(aVar4);
                    } else if (i7 == 1) {
                        o3 = eVar2.o(aVar3);
                    } else if (i7 == 2) {
                        o3 = eVar2.o(aVar2);
                    } else {
                        if (i7 == 3) {
                            o3 = eVar2.o(aVar);
                        }
                        z2 = true;
                    }
                    i5 = o3.e();
                    z2 = true;
                }
                int i8 = this.P0;
                if (i8 == 0) {
                    o2 = eVar2.o(aVar4);
                } else {
                    if (i8 == 1) {
                        o = eVar2.o(aVar3);
                    } else if (i8 == 2) {
                        o2 = eVar2.o(aVar2);
                    } else if (i8 == 3) {
                        o = eVar2.o(aVar);
                    }
                    i5 = Math.max(i5, o.e());
                }
                i5 = Math.min(i5, o2.e());
            }
        }
        int i9 = i5 + this.R0;
        int i10 = this.P0;
        if (i10 == 0 || i10 == 1) {
            v0(i9, i9);
        } else {
            y0(i9, i9);
        }
        this.S0 = true;
        return true;
    }

    public boolean a1() {
        return this.Q0;
    }

    public int b1() {
        return this.P0;
    }

    public int c1() {
        return this.R0;
    }

    public int d1() {
        int i = this.P0;
        if (i == 0 || i == 1) {
            return 0;
        }
        return (i == 2 || i == 3) ? 1 : -1;
    }

    protected void e1() {
        for (int i = 0; i < this.O0; i++) {
            e eVar = this.N0[i];
            if (this.Q0 || eVar.g()) {
                int i2 = this.P0;
                if (i2 == 0 || i2 == 1) {
                    eVar.E0(0, true);
                } else if (i2 == 2 || i2 == 3) {
                    eVar.E0(1, true);
                }
            }
        }
    }

    @Override // a.f.a.j.e
    public void f(a.f.a.d dVar, boolean z) {
        d[] dVarArr;
        boolean z2;
        a.f.a.h hVar;
        d dVar2;
        int i;
        int i2;
        int i3;
        a.f.a.h hVar2;
        int i4;
        e.a aVar = e.a.MATCH_CONSTRAINT;
        d[] dVarArr2 = this.T;
        dVarArr2[0] = this.L;
        dVarArr2[2] = this.M;
        dVarArr2[1] = this.N;
        dVarArr2[3] = this.O;
        int i5 = 0;
        while (true) {
            dVarArr = this.T;
            if (i5 >= dVarArr.length) {
                break;
            }
            dVarArr[i5].i = dVar.l(dVarArr[i5]);
            i5++;
        }
        int i6 = this.P0;
        if (i6 < 0 || i6 >= 4) {
            return;
        }
        d dVar3 = dVarArr[i6];
        if (!this.S0) {
            Z0();
        }
        if (this.S0) {
            this.S0 = false;
            int i7 = this.P0;
            if (i7 == 0 || i7 == 1) {
                dVar.e(this.L.i, this.c0);
                hVar2 = this.N.i;
                i4 = this.c0;
            } else {
                if (i7 != 2 && i7 != 3) {
                    return;
                }
                dVar.e(this.M.i, this.d0);
                hVar2 = this.O.i;
                i4 = this.d0;
            }
            dVar.e(hVar2, i4);
            return;
        }
        for (int i8 = 0; i8 < this.O0; i8++) {
            e eVar = this.N0[i8];
            if ((this.Q0 || eVar.g()) && ((((i2 = this.P0) == 0 || i2 == 1) && eVar.z() == aVar && eVar.L.f != null && eVar.N.f != null) || (((i3 = this.P0) == 2 || i3 == 3) && eVar.O() == aVar && eVar.M.f != null && eVar.O.f != null))) {
                z2 = true;
                break;
            }
        }
        z2 = false;
        boolean z3 = this.L.i() || this.N.i();
        boolean z4 = this.M.i() || this.O.i();
        int i9 = !z2 && (((i = this.P0) == 0 && z3) || ((i == 2 && z4) || ((i == 1 && z3) || (i == 3 && z4)))) ? 5 : 4;
        for (int i10 = 0; i10 < this.O0; i10++) {
            e eVar2 = this.N0[i10];
            if (this.Q0 || eVar2.g()) {
                a.f.a.h l = dVar.l(eVar2.T[this.P0]);
                d[] dVarArr3 = eVar2.T;
                int i11 = this.P0;
                dVarArr3[i11].i = l;
                int i12 = (dVarArr3[i11].f == null || dVarArr3[i11].f.f196d != this) ? 0 : dVarArr3[i11].g + 0;
                if (i11 == 0 || i11 == 2) {
                    a.f.a.h hVar3 = dVar3.i;
                    int i13 = this.R0 - i12;
                    a.f.a.b m = dVar.m();
                    a.f.a.h n = dVar.n();
                    n.f126d = 0;
                    m.h(hVar3, l, n, i13);
                    dVar.c(m);
                } else {
                    a.f.a.h hVar4 = dVar3.i;
                    int i14 = this.R0 + i12;
                    a.f.a.b m2 = dVar.m();
                    a.f.a.h n2 = dVar.n();
                    n2.f126d = 0;
                    m2.g(hVar4, l, n2, i14);
                    dVar.c(m2);
                }
                dVar.d(dVar3.i, l, this.R0 + i12, i9);
            }
        }
        int i15 = this.P0;
        if (i15 == 0) {
            dVar.d(this.N.i, this.L.i, 0, 8);
            dVar.d(this.L.i, this.X.N.i, 0, 4);
            hVar = this.L.i;
            dVar2 = this.X.L;
        } else if (i15 == 1) {
            dVar.d(this.L.i, this.N.i, 0, 8);
            dVar.d(this.L.i, this.X.L.i, 0, 4);
            hVar = this.L.i;
            dVar2 = this.X.N;
        } else if (i15 == 2) {
            dVar.d(this.O.i, this.M.i, 0, 8);
            dVar.d(this.M.i, this.X.O.i, 0, 4);
            hVar = this.M.i;
            dVar2 = this.X.M;
        } else {
            if (i15 != 3) {
                return;
            }
            dVar.d(this.M.i, this.O.i, 0, 8);
            dVar.d(this.M.i, this.X.M.i, 0, 4);
            hVar = this.M.i;
            dVar2 = this.X.O;
        }
        dVar.d(hVar, dVar2.i, 0, 0);
    }

    @Override // a.f.a.j.e
    public boolean f0() {
        return this.S0;
    }

    public void f1(boolean z) {
        this.Q0 = z;
    }

    @Override // a.f.a.j.e
    public boolean g() {
        return true;
    }

    @Override // a.f.a.j.e
    public boolean g0() {
        return this.S0;
    }

    public void g1(int i) {
        this.P0 = i;
    }

    public void h1(int i) {
        this.R0 = i;
    }

    @Override // a.f.a.j.j, a.f.a.j.e
    public void l(e eVar, HashMap<e, e> hashMap) {
        super.l(eVar, hashMap);
        a aVar = (a) eVar;
        this.P0 = aVar.P0;
        this.Q0 = aVar.Q0;
        this.R0 = aVar.R0;
    }

    @Override // a.f.a.j.e
    public String toString() {
        StringBuilder j = b.b.a.a.a.j("[Barrier] ");
        j.append(t());
        j.append(" {");
        String sb = j.toString();
        for (int i = 0; i < this.O0; i++) {
            e eVar = this.N0[i];
            if (i > 0) {
                sb = b.b.a.a.a.g(sb, ", ");
            }
            StringBuilder j2 = b.b.a.a.a.j(sb);
            j2.append(eVar.t());
            sb = j2.toString();
        }
        return b.b.a.a.a.g(sb, "}");
    }
}
