package a.h.e;

import android.os.Process;
import java.util.concurrent.ThreadFactory;

/* loaded from: classes.dex */
class h implements ThreadFactory {

    /* renamed from: a, reason: collision with root package name */
    private String f321a;

    /* renamed from: b, reason: collision with root package name */
    private int f322b;

    private static class a extends Thread {

        /* renamed from: a, reason: collision with root package name */
        private final int f323a;

        a(Runnable runnable, String str, int i) {
            super(runnable, str);
            this.f323a = i;
        }

        @Override // java.lang.Thread, java.lang.Runnable
        public void run() {
            Process.setThreadPriority(this.f323a);
            super.run();
        }
    }

    h(String str, int i) {
        this.f321a = str;
        this.f322b = i;
    }

    @Override // java.util.concurrent.ThreadFactory
    public Thread newThread(Runnable runnable) {
        return new a(runnable, this.f321a, this.f322b);
    }
}
