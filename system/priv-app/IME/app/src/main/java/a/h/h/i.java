package a.h.h;

import android.view.View;

/* loaded from: classes.dex */
public class i {

    /* renamed from: a, reason: collision with root package name */
    private int f369a;

    /* renamed from: b, reason: collision with root package name */
    private int f370b;

    public int a() {
        return this.f370b | this.f369a;
    }

    public void b(int i, int i2) {
        if (i2 == 1) {
            this.f370b = i;
        } else {
            this.f369a = i;
        }
    }

    public void c(View view, View view2, int i) {
        this.f369a = i;
    }

    public void d(int i) {
        if (i == 1) {
            this.f370b = 0;
        } else {
            this.f369a = 0;
        }
    }
}
