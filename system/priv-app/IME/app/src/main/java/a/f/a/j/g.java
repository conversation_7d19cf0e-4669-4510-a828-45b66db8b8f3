package a.f.a.j;

import a.f.a.j.e;
import java.util.ArrayList;
import java.util.HashMap;

/* loaded from: classes.dex */
public class g extends m {
    private e[] x1;
    private int a1 = -1;
    private int b1 = -1;
    private int c1 = -1;
    private int d1 = -1;
    private int e1 = -1;
    private int f1 = -1;
    private float g1 = 0.5f;
    private float h1 = 0.5f;
    private float i1 = 0.5f;
    private float j1 = 0.5f;
    private float k1 = 0.5f;
    private float l1 = 0.5f;
    private int m1 = 0;
    private int n1 = 0;
    private int o1 = 2;
    private int p1 = 2;
    private int q1 = 0;
    private int r1 = -1;
    private int s1 = 0;
    private ArrayList<a> t1 = new ArrayList<>();
    private e[] u1 = null;
    private e[] v1 = null;
    private int[] w1 = null;
    private int y1 = 0;

    private class a {

        /* renamed from: a, reason: collision with root package name */
        private int f209a;

        /* renamed from: d, reason: collision with root package name */
        private d f212d;
        private d e;
        private d f;
        private d g;
        private int h;
        private int i;
        private int j;
        private int k;
        private int q;

        /* renamed from: b, reason: collision with root package name */
        private e f210b = null;

        /* renamed from: c, reason: collision with root package name */
        int f211c = 0;
        private int l = 0;
        private int m = 0;
        private int n = 0;
        private int o = 0;
        private int p = 0;

        public a(int i, d dVar, d dVar2, d dVar3, d dVar4, int i2) {
            this.f209a = 0;
            this.h = 0;
            this.i = 0;
            this.j = 0;
            this.k = 0;
            this.q = 0;
            this.f209a = i;
            this.f212d = dVar;
            this.e = dVar2;
            this.f = dVar3;
            this.g = dVar4;
            this.h = g.this.e1();
            this.i = g.this.g1();
            this.j = g.this.f1();
            this.k = g.this.d1();
            this.q = i2;
        }

        public void b(e eVar) {
            e.a aVar = e.a.MATCH_CONSTRAINT;
            if (this.f209a == 0) {
                int O1 = g.this.O1(eVar, this.q);
                if (eVar.z() == aVar) {
                    this.p++;
                    O1 = 0;
                }
                this.l = O1 + (eVar.P() != 8 ? g.this.m1 : 0) + this.l;
                int N1 = g.this.N1(eVar, this.q);
                if (this.f210b == null || this.f211c < N1) {
                    this.f210b = eVar;
                    this.f211c = N1;
                    this.m = N1;
                }
            } else {
                int O12 = g.this.O1(eVar, this.q);
                int N12 = g.this.N1(eVar, this.q);
                if (eVar.O() == aVar) {
                    this.p++;
                    N12 = 0;
                }
                this.m = N12 + (eVar.P() != 8 ? g.this.n1 : 0) + this.m;
                if (this.f210b == null || this.f211c < O12) {
                    this.f210b = eVar;
                    this.f211c = O12;
                    this.l = O12;
                }
            }
            this.o++;
        }

        public void c() {
            this.f211c = 0;
            this.f210b = null;
            this.l = 0;
            this.m = 0;
            this.n = 0;
            this.o = 0;
            this.p = 0;
        }

        /* JADX WARN: Removed duplicated region for block: B:155:0x026b  */
        /* JADX WARN: Removed duplicated region for block: B:158:0x026e  */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public void d(boolean r17, int r18, boolean r19) {
            /*
                Method dump skipped, instructions count: 846
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: a.f.a.j.g.a.d(boolean, int, boolean):void");
        }

        public int e() {
            return this.f209a == 1 ? this.m - g.this.n1 : this.m;
        }

        public int f() {
            return this.f209a == 0 ? this.l - g.this.m1 : this.l;
        }

        public void g(int i) {
            g gVar;
            e.a z;
            int Q;
            e.a aVar;
            int i2;
            e.a aVar2 = e.a.FIXED;
            e.a aVar3 = e.a.MATCH_CONSTRAINT;
            int i3 = this.p;
            if (i3 == 0) {
                return;
            }
            int i4 = this.o;
            int i5 = i / i3;
            for (int i6 = 0; i6 < i4 && this.n + i6 < g.this.y1; i6++) {
                e eVar = g.this.x1[this.n + i6];
                if (this.f209a == 0) {
                    if (eVar != null && eVar.z() == aVar3 && eVar.r == 0) {
                        gVar = g.this;
                        aVar = eVar.O();
                        i2 = eVar.w();
                        z = aVar2;
                        Q = i5;
                        gVar.i1(eVar, z, Q, aVar, i2);
                    }
                } else {
                    if (eVar != null && eVar.O() == aVar3 && eVar.s == 0) {
                        gVar = g.this;
                        z = eVar.z();
                        Q = eVar.Q();
                        aVar = aVar2;
                        i2 = i5;
                        gVar.i1(eVar, z, Q, aVar, i2);
                    }
                }
            }
            this.l = 0;
            this.m = 0;
            this.f210b = null;
            this.f211c = 0;
            int i7 = this.o;
            for (int i8 = 0; i8 < i7 && this.n + i8 < g.this.y1; i8++) {
                e eVar2 = g.this.x1[this.n + i8];
                if (this.f209a == 0) {
                    int Q2 = eVar2.Q();
                    int i9 = g.this.m1;
                    if (eVar2.P() == 8) {
                        i9 = 0;
                    }
                    this.l = Q2 + i9 + this.l;
                    int N1 = g.this.N1(eVar2, this.q);
                    if (this.f210b == null || this.f211c < N1) {
                        this.f210b = eVar2;
                        this.f211c = N1;
                        this.m = N1;
                    }
                } else {
                    int O1 = g.this.O1(eVar2, this.q);
                    int N12 = g.this.N1(eVar2, this.q);
                    int i10 = g.this.n1;
                    if (eVar2.P() == 8) {
                        i10 = 0;
                    }
                    this.m = N12 + i10 + this.m;
                    if (this.f210b == null || this.f211c < O1) {
                        this.f210b = eVar2;
                        this.f211c = O1;
                        this.l = O1;
                    }
                }
            }
        }

        public void h(int i) {
            this.n = i;
        }

        public void i(int i, d dVar, d dVar2, d dVar3, d dVar4, int i2, int i3, int i4, int i5, int i6) {
            this.f209a = i;
            this.f212d = dVar;
            this.e = dVar2;
            this.f = dVar3;
            this.g = dVar4;
            this.h = i2;
            this.i = i3;
            this.j = i4;
            this.k = i5;
            this.q = i6;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final int N1(e eVar, int i) {
        if (eVar == null) {
            return 0;
        }
        if (eVar.O() == e.a.MATCH_CONSTRAINT) {
            int i2 = eVar.s;
            if (i2 == 0) {
                return 0;
            }
            if (i2 == 2) {
                int i3 = (int) (eVar.z * i);
                if (i3 != eVar.w()) {
                    eVar.K0(true);
                    i1(eVar, eVar.z(), eVar.Q(), e.a.FIXED, i3);
                }
                return i3;
            }
            if (i2 == 1) {
                return eVar.w();
            }
            if (i2 == 3) {
                return (int) ((eVar.Q() * eVar.a0) + 0.5f);
            }
        }
        return eVar.w();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final int O1(e eVar, int i) {
        if (eVar == null) {
            return 0;
        }
        if (eVar.z() == e.a.MATCH_CONSTRAINT) {
            int i2 = eVar.r;
            if (i2 == 0) {
                return 0;
            }
            if (i2 == 2) {
                int i3 = (int) (eVar.w * i);
                if (i3 != eVar.Q()) {
                    eVar.K0(true);
                    i1(eVar, e.a.FIXED, i3, eVar.O(), eVar.w());
                }
                return i3;
            }
            if (i2 == 1) {
                return eVar.Q();
            }
            if (i2 == 3) {
                return (int) ((eVar.w() * eVar.a0) + 0.5f);
            }
        }
        return eVar.Q();
    }

    public void P1(float f) {
        this.i1 = f;
    }

    public void Q1(int i) {
        this.c1 = i;
    }

    public void R1(float f) {
        this.j1 = f;
    }

    public void S1(int i) {
        this.d1 = i;
    }

    public void T1(int i) {
        this.o1 = i;
    }

    public void U1(float f) {
        this.g1 = f;
    }

    public void V1(int i) {
        this.m1 = i;
    }

    public void W1(int i) {
        this.a1 = i;
    }

    public void X1(float f) {
        this.k1 = f;
    }

    public void Y1(int i) {
        this.e1 = i;
    }

    public void Z1(float f) {
        this.l1 = f;
    }

    public void a2(int i) {
        this.f1 = i;
    }

    public void b2(int i) {
        this.r1 = i;
    }

    public void c2(int i) {
        this.s1 = i;
    }

    public void d2(int i) {
        this.p1 = i;
    }

    public void e2(float f) {
        this.h1 = f;
    }

    @Override // a.f.a.j.e
    public void f(a.f.a.d dVar, boolean z) {
        e eVar;
        float f;
        int i;
        super.f(dVar, z);
        e eVar2 = this.X;
        boolean z2 = eVar2 != null && ((f) eVar2).k1();
        int i2 = this.q1;
        if (i2 != 0) {
            if (i2 == 1) {
                int size = this.t1.size();
                int i3 = 0;
                while (i3 < size) {
                    this.t1.get(i3).d(z2, i3, i3 == size + (-1));
                    i3++;
                }
            } else if (i2 != 2) {
                if (i2 == 3) {
                    int size2 = this.t1.size();
                    int i4 = 0;
                    while (i4 < size2) {
                        this.t1.get(i4).d(z2, i4, i4 == size2 + (-1));
                        i4++;
                    }
                }
            } else if (this.w1 != null && this.v1 != null && this.u1 != null) {
                for (int i5 = 0; i5 < this.y1; i5++) {
                    this.x1[i5].l0();
                }
                int[] iArr = this.w1;
                int i6 = iArr[0];
                int i7 = iArr[1];
                e eVar3 = null;
                float f2 = this.g1;
                int i8 = 0;
                while (i8 < i6) {
                    if (z2) {
                        i = (i6 - i8) - 1;
                        f = 1.0f - this.g1;
                    } else {
                        f = f2;
                        i = i8;
                    }
                    e eVar4 = this.v1[i];
                    if (eVar4 != null && eVar4.P() != 8) {
                        if (i8 == 0) {
                            eVar4.j(eVar4.L, this.L, e1());
                            eVar4.C0 = this.a1;
                            eVar4.l0 = f;
                        }
                        if (i8 == i6 - 1) {
                            eVar4.j(eVar4.N, this.N, f1());
                        }
                        if (i8 > 0 && eVar3 != null) {
                            eVar4.j(eVar4.L, eVar3.N, this.m1);
                            eVar3.j(eVar3.N, eVar4.L, 0);
                        }
                        eVar3 = eVar4;
                    }
                    i8++;
                    f2 = f;
                }
                for (int i9 = 0; i9 < i7; i9++) {
                    e eVar5 = this.u1[i9];
                    if (eVar5 != null && eVar5.P() != 8) {
                        if (i9 == 0) {
                            eVar5.j(eVar5.M, this.M, g1());
                            eVar5.D0 = this.b1;
                            eVar5.m0 = this.h1;
                        }
                        if (i9 == i7 - 1) {
                            eVar5.j(eVar5.O, this.O, d1());
                        }
                        if (i9 > 0 && eVar3 != null) {
                            eVar5.j(eVar5.M, eVar3.O, this.n1);
                            eVar3.j(eVar3.O, eVar5.M, 0);
                        }
                        eVar3 = eVar5;
                    }
                }
                for (int i10 = 0; i10 < i6; i10++) {
                    for (int i11 = 0; i11 < i7; i11++) {
                        int i12 = (i11 * i6) + i10;
                        if (this.s1 == 1) {
                            i12 = (i10 * i7) + i11;
                        }
                        e[] eVarArr = this.x1;
                        if (i12 < eVarArr.length && (eVar = eVarArr[i12]) != null && eVar.P() != 8) {
                            e eVar6 = this.v1[i10];
                            e eVar7 = this.u1[i11];
                            if (eVar != eVar6) {
                                eVar.j(eVar.L, eVar6.L, 0);
                                eVar.j(eVar.N, eVar6.N, 0);
                            }
                            if (eVar != eVar7) {
                                eVar.j(eVar.M, eVar7.M, 0);
                                eVar.j(eVar.O, eVar7.O, 0);
                            }
                        }
                    }
                }
            }
        } else if (this.t1.size() > 0) {
            this.t1.get(0).d(z2, 0, true);
        }
        k1(false);
    }

    public void f2(int i) {
        this.n1 = i;
    }

    public void g2(int i) {
        this.b1 = i;
    }

    /* JADX WARN: Code restructure failed: missing block: B:462:0x00c7, code lost:
    
        r36.b1 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:467:0x00c5, code lost:
    
        if (r36.b1 == (-1)) goto L56;
     */
    /* JADX WARN: Code restructure failed: missing block: B:51:0x00ba, code lost:
    
        if (r36.b1 == (-1)) goto L56;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:239:0x03d3  */
    /* JADX WARN: Type inference failed for: r2v7 */
    /* JADX WARN: Type inference failed for: r2v75 */
    /* JADX WARN: Type inference failed for: r2v8 */
    /* JADX WARN: Type inference failed for: r2v9 */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:264:0x048a -> B:209:0x049a). Please report as a decompilation issue!!! */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:265:0x048c -> B:209:0x049a). Please report as a decompilation issue!!! */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:267:0x0492 -> B:209:0x049a). Please report as a decompilation issue!!! */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:268:0x0494 -> B:209:0x049a). Please report as a decompilation issue!!! */
    @Override // a.f.a.j.m
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void h1(int r37, int r38, int r39, int r40) {
        /*
            Method dump skipped, instructions count: 1927
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: a.f.a.j.g.h1(int, int, int, int):void");
    }

    public void h2(int i) {
        this.q1 = i;
    }

    @Override // a.f.a.j.j, a.f.a.j.e
    public void l(e eVar, HashMap<e, e> hashMap) {
        super.l(eVar, hashMap);
        g gVar = (g) eVar;
        this.a1 = gVar.a1;
        this.b1 = gVar.b1;
        this.c1 = gVar.c1;
        this.d1 = gVar.d1;
        this.e1 = gVar.e1;
        this.f1 = gVar.f1;
        this.g1 = gVar.g1;
        this.h1 = gVar.h1;
        this.i1 = gVar.i1;
        this.j1 = gVar.j1;
        this.k1 = gVar.k1;
        this.l1 = gVar.l1;
        this.m1 = gVar.m1;
        this.n1 = gVar.n1;
        this.o1 = gVar.o1;
        this.p1 = gVar.p1;
        this.q1 = gVar.q1;
        this.r1 = gVar.r1;
        this.s1 = gVar.s1;
    }
}
