package a.e;

import a.e.g;
import java.lang.reflect.Array;
import java.util.Collection;
import java.util.Iterator;
import java.util.Set;

/* loaded from: classes.dex */
public final class c<E> implements Collection<E>, Set<E> {
    private static final int[] e = new int[0];
    private static final Object[] f = new Object[0];
    private static Object[] g;
    private static int h;
    private static Object[] i;
    private static int j;

    /* renamed from: a, reason: collision with root package name */
    private int[] f66a = e;

    /* renamed from: b, reason: collision with root package name */
    Object[] f67b = f;

    /* renamed from: c, reason: collision with root package name */
    int f68c = 0;

    /* renamed from: d, reason: collision with root package name */
    private g<E, E> f69d;

    private void a(int i2) {
        if (i2 == 8) {
            synchronized (c.class) {
                Object[] objArr = i;
                if (objArr != null) {
                    this.f67b = objArr;
                    i = (Object[]) objArr[0];
                    this.f66a = (int[]) objArr[1];
                    objArr[1] = null;
                    objArr[0] = null;
                    j--;
                    return;
                }
            }
        } else if (i2 == 4) {
            synchronized (c.class) {
                Object[] objArr2 = g;
                if (objArr2 != null) {
                    this.f67b = objArr2;
                    g = (Object[]) objArr2[0];
                    this.f66a = (int[]) objArr2[1];
                    objArr2[1] = null;
                    objArr2[0] = null;
                    h--;
                    return;
                }
            }
        }
        this.f66a = new int[i2];
        this.f67b = new Object[i2];
    }

    private static void b(int[] iArr, Object[] objArr, int i2) {
        if (iArr.length == 8) {
            synchronized (c.class) {
                if (j < 10) {
                    objArr[0] = i;
                    objArr[1] = iArr;
                    for (int i3 = i2 - 1; i3 >= 2; i3--) {
                        objArr[i3] = null;
                    }
                    i = objArr;
                    j++;
                }
            }
            return;
        }
        if (iArr.length == 4) {
            synchronized (c.class) {
                if (h < 10) {
                    objArr[0] = g;
                    objArr[1] = iArr;
                    for (int i4 = i2 - 1; i4 >= 2; i4--) {
                        objArr[i4] = null;
                    }
                    g = objArr;
                    h++;
                }
            }
        }
    }

    private int d(Object obj, int i2) {
        int i3 = this.f68c;
        if (i3 == 0) {
            return -1;
        }
        int a2 = d.a(this.f66a, i3, i2);
        if (a2 < 0 || obj.equals(this.f67b[a2])) {
            return a2;
        }
        int i4 = a2 + 1;
        while (i4 < i3 && this.f66a[i4] == i2) {
            if (obj.equals(this.f67b[i4])) {
                return i4;
            }
            i4++;
        }
        for (int i5 = a2 - 1; i5 >= 0 && this.f66a[i5] == i2; i5--) {
            if (obj.equals(this.f67b[i5])) {
                return i5;
            }
        }
        return ~i4;
    }

    private int e() {
        int i2 = this.f68c;
        if (i2 == 0) {
            return -1;
        }
        int a2 = d.a(this.f66a, i2, 0);
        if (a2 < 0 || this.f67b[a2] == null) {
            return a2;
        }
        int i3 = a2 + 1;
        while (i3 < i2 && this.f66a[i3] == 0) {
            if (this.f67b[i3] == null) {
                return i3;
            }
            i3++;
        }
        for (int i4 = a2 - 1; i4 >= 0 && this.f66a[i4] == 0; i4--) {
            if (this.f67b[i4] == null) {
                return i4;
            }
        }
        return ~i3;
    }

    @Override // java.util.Collection, java.util.Set
    public boolean add(E e2) {
        int i2;
        int d2;
        if (e2 == null) {
            d2 = e();
            i2 = 0;
        } else {
            int hashCode = e2.hashCode();
            i2 = hashCode;
            d2 = d(e2, hashCode);
        }
        if (d2 >= 0) {
            return false;
        }
        int i3 = ~d2;
        int i4 = this.f68c;
        int[] iArr = this.f66a;
        if (i4 >= iArr.length) {
            int i5 = 4;
            if (i4 >= 8) {
                i5 = (i4 >> 1) + i4;
            } else if (i4 >= 4) {
                i5 = 8;
            }
            Object[] objArr = this.f67b;
            a(i5);
            int[] iArr2 = this.f66a;
            if (iArr2.length > 0) {
                System.arraycopy(iArr, 0, iArr2, 0, iArr.length);
                System.arraycopy(objArr, 0, this.f67b, 0, objArr.length);
            }
            b(iArr, objArr, this.f68c);
        }
        int i6 = this.f68c;
        if (i3 < i6) {
            int[] iArr3 = this.f66a;
            int i7 = i3 + 1;
            System.arraycopy(iArr3, i3, iArr3, i7, i6 - i3);
            Object[] objArr2 = this.f67b;
            System.arraycopy(objArr2, i3, objArr2, i7, this.f68c - i3);
        }
        this.f66a[i3] = i2;
        this.f67b[i3] = e2;
        this.f68c++;
        return true;
    }

    @Override // java.util.Collection, java.util.Set
    public boolean addAll(Collection<? extends E> collection) {
        int size = collection.size() + this.f68c;
        int[] iArr = this.f66a;
        boolean z = false;
        if (iArr.length < size) {
            Object[] objArr = this.f67b;
            a(size);
            int i2 = this.f68c;
            if (i2 > 0) {
                System.arraycopy(iArr, 0, this.f66a, 0, i2);
                System.arraycopy(objArr, 0, this.f67b, 0, this.f68c);
            }
            b(iArr, objArr, this.f68c);
        }
        Iterator<? extends E> it = collection.iterator();
        while (it.hasNext()) {
            z |= add(it.next());
        }
        return z;
    }

    public int c(Object obj) {
        return obj == null ? e() : d(obj, obj.hashCode());
    }

    @Override // java.util.Collection, java.util.Set
    public void clear() {
        int i2 = this.f68c;
        if (i2 != 0) {
            b(this.f66a, this.f67b, i2);
            this.f66a = e;
            this.f67b = f;
            this.f68c = 0;
        }
    }

    @Override // java.util.Collection, java.util.Set
    public boolean contains(Object obj) {
        return c(obj) >= 0;
    }

    @Override // java.util.Collection, java.util.Set
    public boolean containsAll(Collection<?> collection) {
        Iterator<?> it = collection.iterator();
        while (it.hasNext()) {
            if (!contains(it.next())) {
                return false;
            }
        }
        return true;
    }

    @Override // java.util.Collection, java.util.Set
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj instanceof Set) {
            Set set = (Set) obj;
            if (this.f68c != set.size()) {
                return false;
            }
            for (int i2 = 0; i2 < this.f68c; i2++) {
                try {
                    if (!set.contains(this.f67b[i2])) {
                        return false;
                    }
                } catch (ClassCastException | NullPointerException unused) {
                }
            }
            return true;
        }
        return false;
    }

    public E f(int i2) {
        Object[] objArr = this.f67b;
        E e2 = (E) objArr[i2];
        int i3 = this.f68c;
        if (i3 <= 1) {
            b(this.f66a, objArr, i3);
            this.f66a = e;
            this.f67b = f;
            this.f68c = 0;
        } else {
            int[] iArr = this.f66a;
            if (iArr.length <= 8 || i3 >= iArr.length / 3) {
                int i4 = i3 - 1;
                this.f68c = i4;
                if (i2 < i4) {
                    int i5 = i2 + 1;
                    System.arraycopy(iArr, i5, iArr, i2, i4 - i2);
                    Object[] objArr2 = this.f67b;
                    System.arraycopy(objArr2, i5, objArr2, i2, this.f68c - i2);
                }
                this.f67b[this.f68c] = null;
            } else {
                a(i3 > 8 ? i3 + (i3 >> 1) : 8);
                this.f68c--;
                if (i2 > 0) {
                    System.arraycopy(iArr, 0, this.f66a, 0, i2);
                    System.arraycopy(objArr, 0, this.f67b, 0, i2);
                }
                int i6 = this.f68c;
                if (i2 < i6) {
                    int i7 = i2 + 1;
                    System.arraycopy(iArr, i7, this.f66a, i2, i6 - i2);
                    System.arraycopy(objArr, i7, this.f67b, i2, this.f68c - i2);
                }
            }
        }
        return e2;
    }

    public E g(int i2) {
        return (E) this.f67b[i2];
    }

    @Override // java.util.Collection, java.util.Set
    public int hashCode() {
        int[] iArr = this.f66a;
        int i2 = this.f68c;
        int i3 = 0;
        for (int i4 = 0; i4 < i2; i4++) {
            i3 += iArr[i4];
        }
        return i3;
    }

    @Override // java.util.Collection, java.util.Set
    public boolean isEmpty() {
        return this.f68c <= 0;
    }

    @Override // java.util.Collection, java.lang.Iterable, java.util.Set
    public Iterator<E> iterator() {
        if (this.f69d == null) {
            this.f69d = new b(this);
        }
        g<E, E> gVar = this.f69d;
        if (gVar.f82b == null) {
            gVar.f82b = new g.c();
        }
        return gVar.f82b.iterator();
    }

    @Override // java.util.Collection, java.util.Set
    public boolean remove(Object obj) {
        int c2 = c(obj);
        if (c2 < 0) {
            return false;
        }
        f(c2);
        return true;
    }

    @Override // java.util.Collection, java.util.Set
    public boolean removeAll(Collection<?> collection) {
        Iterator<?> it = collection.iterator();
        boolean z = false;
        while (it.hasNext()) {
            z |= remove(it.next());
        }
        return z;
    }

    @Override // java.util.Collection, java.util.Set
    public boolean retainAll(Collection<?> collection) {
        boolean z = false;
        for (int i2 = this.f68c - 1; i2 >= 0; i2--) {
            if (!collection.contains(this.f67b[i2])) {
                f(i2);
                z = true;
            }
        }
        return z;
    }

    @Override // java.util.Collection, java.util.Set
    public int size() {
        return this.f68c;
    }

    @Override // java.util.Collection, java.util.Set
    public Object[] toArray() {
        int i2 = this.f68c;
        Object[] objArr = new Object[i2];
        System.arraycopy(this.f67b, 0, objArr, 0, i2);
        return objArr;
    }

    @Override // java.util.Collection, java.util.Set
    public <T> T[] toArray(T[] tArr) {
        if (tArr.length < this.f68c) {
            tArr = (T[]) ((Object[]) Array.newInstance(tArr.getClass().getComponentType(), this.f68c));
        }
        System.arraycopy(this.f67b, 0, tArr, 0, this.f68c);
        int length = tArr.length;
        int i2 = this.f68c;
        if (length > i2) {
            tArr[i2] = null;
        }
        return tArr;
    }

    public String toString() {
        if (isEmpty()) {
            return "{}";
        }
        StringBuilder sb = new StringBuilder(this.f68c * 14);
        sb.append('{');
        for (int i2 = 0; i2 < this.f68c; i2++) {
            if (i2 > 0) {
                sb.append(", ");
            }
            Object obj = this.f67b[i2];
            if (obj != this) {
                sb.append(obj);
            } else {
                sb.append("(this Set)");
            }
        }
        sb.append('}');
        return sb.toString();
    }
}
