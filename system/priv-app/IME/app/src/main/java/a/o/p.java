package a.o;

import android.view.View;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/* loaded from: classes.dex */
public class p {

    /* renamed from: b, reason: collision with root package name */
    public View f513b;

    /* renamed from: a, reason: collision with root package name */
    public final Map<String, Object> f512a = new HashMap();

    /* renamed from: c, reason: collision with root package name */
    final ArrayList<i> f514c = new ArrayList<>();

    @Deprecated
    public p() {
    }

    public p(View view) {
        this.f513b = view;
    }

    public boolean equals(Object obj) {
        if (!(obj instanceof p)) {
            return false;
        }
        p pVar = (p) obj;
        return this.f513b == pVar.f513b && this.f512a.equals(pVar.f512a);
    }

    public int hashCode() {
        return this.f512a.hashCode() + (this.f513b.hashCode() * 31);
    }

    public String toString() {
        StringBuilder j = b.b.a.a.a.j("TransitionValues@");
        j.append(Integer.toHexString(hashCode()));
        j.append(":\n");
        String g = b.b.a.a.a.g(j.toString() + "    view = " + this.f513b + "\n", "    values:");
        for (String str : this.f512a.keySet()) {
            g = g + "    " + str + ": " + this.f512a.get(str) + "\n";
        }
        return g;
    }
}
