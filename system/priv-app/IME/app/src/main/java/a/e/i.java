package a.e;

/* loaded from: classes.dex */
public class i<E> implements Cloneable {
    private static final Object e = new Object();

    /* renamed from: a, reason: collision with root package name */
    private boolean f99a;

    /* renamed from: b, reason: collision with root package name */
    private int[] f100b;

    /* renamed from: c, reason: collision with root package name */
    private Object[] f101c;

    /* renamed from: d, reason: collision with root package name */
    private int f102d;

    public i() {
        this(10);
    }

    public i(int i) {
        this.f99a = false;
        if (i == 0) {
            this.f100b = d.f70a;
            this.f101c = d.f72c;
        } else {
            int e2 = d.e(i);
            this.f100b = new int[e2];
            this.f101c = new Object[e2];
        }
    }

    private void d() {
        int i = this.f102d;
        int[] iArr = this.f100b;
        Object[] objArr = this.f101c;
        int i2 = 0;
        for (int i3 = 0; i3 < i; i3++) {
            Object obj = objArr[i3];
            if (obj != e) {
                if (i3 != i2) {
                    iArr[i2] = iArr[i3];
                    objArr[i2] = obj;
                    objArr[i3] = null;
                }
                i2++;
            }
        }
        this.f99a = false;
        this.f102d = i2;
    }

    public void a(int i, E e2) {
        int i2 = this.f102d;
        if (i2 != 0 && i <= this.f100b[i2 - 1]) {
            i(i, e2);
            return;
        }
        if (this.f99a && i2 >= this.f100b.length) {
            d();
        }
        int i3 = this.f102d;
        if (i3 >= this.f100b.length) {
            int e3 = d.e(i3 + 1);
            int[] iArr = new int[e3];
            Object[] objArr = new Object[e3];
            int[] iArr2 = this.f100b;
            System.arraycopy(iArr2, 0, iArr, 0, iArr2.length);
            Object[] objArr2 = this.f101c;
            System.arraycopy(objArr2, 0, objArr, 0, objArr2.length);
            this.f100b = iArr;
            this.f101c = objArr;
        }
        this.f100b[i3] = i;
        this.f101c[i3] = e2;
        this.f102d = i3 + 1;
    }

    public void b() {
        int i = this.f102d;
        Object[] objArr = this.f101c;
        for (int i2 = 0; i2 < i; i2++) {
            objArr[i2] = null;
        }
        this.f102d = 0;
        this.f99a = false;
    }

    /* renamed from: c, reason: merged with bridge method [inline-methods] */
    public i<E> clone() {
        try {
            i<E> iVar = (i) super.clone();
            iVar.f100b = (int[]) this.f100b.clone();
            iVar.f101c = (Object[]) this.f101c.clone();
            return iVar;
        } catch (CloneNotSupportedException e2) {
            throw new AssertionError(e2);
        }
    }

    public E e(int i) {
        return f(i, null);
    }

    public E f(int i, E e2) {
        int a2 = d.a(this.f100b, this.f102d, i);
        if (a2 >= 0) {
            Object[] objArr = this.f101c;
            if (objArr[a2] != e) {
                return (E) objArr[a2];
            }
        }
        return e2;
    }

    public int g(E e2) {
        if (this.f99a) {
            d();
        }
        for (int i = 0; i < this.f102d; i++) {
            if (this.f101c[i] == e2) {
                return i;
            }
        }
        return -1;
    }

    public int h(int i) {
        if (this.f99a) {
            d();
        }
        return this.f100b[i];
    }

    public void i(int i, E e2) {
        int a2 = d.a(this.f100b, this.f102d, i);
        if (a2 >= 0) {
            this.f101c[a2] = e2;
            return;
        }
        int i2 = ~a2;
        int i3 = this.f102d;
        if (i2 < i3) {
            Object[] objArr = this.f101c;
            if (objArr[i2] == e) {
                this.f100b[i2] = i;
                objArr[i2] = e2;
                return;
            }
        }
        if (this.f99a && i3 >= this.f100b.length) {
            d();
            i2 = ~d.a(this.f100b, this.f102d, i);
        }
        int i4 = this.f102d;
        if (i4 >= this.f100b.length) {
            int e3 = d.e(i4 + 1);
            int[] iArr = new int[e3];
            Object[] objArr2 = new Object[e3];
            int[] iArr2 = this.f100b;
            System.arraycopy(iArr2, 0, iArr, 0, iArr2.length);
            Object[] objArr3 = this.f101c;
            System.arraycopy(objArr3, 0, objArr2, 0, objArr3.length);
            this.f100b = iArr;
            this.f101c = objArr2;
        }
        int i5 = this.f102d;
        if (i5 - i2 != 0) {
            int[] iArr3 = this.f100b;
            int i6 = i2 + 1;
            System.arraycopy(iArr3, i2, iArr3, i6, i5 - i2);
            Object[] objArr4 = this.f101c;
            System.arraycopy(objArr4, i2, objArr4, i6, this.f102d - i2);
        }
        this.f100b[i2] = i;
        this.f101c[i2] = e2;
        this.f102d++;
    }

    public void j(int i) {
        int a2 = d.a(this.f100b, this.f102d, i);
        if (a2 >= 0) {
            Object[] objArr = this.f101c;
            Object obj = objArr[a2];
            Object obj2 = e;
            if (obj != obj2) {
                objArr[a2] = obj2;
                this.f99a = true;
            }
        }
    }

    public int k() {
        if (this.f99a) {
            d();
        }
        return this.f102d;
    }

    public E l(int i) {
        if (this.f99a) {
            d();
        }
        return (E) this.f101c[i];
    }

    public String toString() {
        if (k() <= 0) {
            return "{}";
        }
        StringBuilder sb = new StringBuilder(this.f102d * 28);
        sb.append('{');
        for (int i = 0; i < this.f102d; i++) {
            if (i > 0) {
                sb.append(", ");
            }
            sb.append(h(i));
            sb.append('=');
            E l = l(i);
            if (l != this) {
                sb.append(l);
            } else {
                sb.append("(this Map)");
            }
        }
        sb.append('}');
        return sb.toString();
    }
}
