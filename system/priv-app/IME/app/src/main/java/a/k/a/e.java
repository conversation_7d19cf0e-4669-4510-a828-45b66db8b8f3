package a.k.a;

import a.k.a.b;

/* loaded from: classes.dex */
public final class e {

    /* renamed from: a, reason: collision with root package name */
    double f452a;

    /* renamed from: b, reason: collision with root package name */
    double f453b;

    /* renamed from: c, reason: collision with root package name */
    private boolean f454c;

    /* renamed from: d, reason: collision with root package name */
    private double f455d;
    private double e;
    private double f;
    private double g;
    private double h;
    private double i;
    private final b.h j;

    public e() {
        this.f452a = Math.sqrt(1500.0d);
        this.f453b = 0.5d;
        this.f454c = false;
        this.i = Double.MAX_VALUE;
        this.j = new b.h();
    }

    public e(float f) {
        this.f452a = Math.sqrt(1500.0d);
        this.f453b = 0.5d;
        this.f454c = false;
        this.i = Double.MAX_VALUE;
        this.j = new b.h();
        this.i = f;
    }

    public float a() {
        return (float) this.i;
    }

    public boolean b(float f, float f2) {
        return ((double) Math.abs(f2)) < this.e && ((double) Math.abs(f - ((float) this.i))) < this.f455d;
    }

    public e c(float f) {
        if (f < 0.0f) {
            throw new IllegalArgumentException("Damping ratio must be non-negative");
        }
        this.f453b = f;
        this.f454c = false;
        return this;
    }

    public e d(float f) {
        this.i = f;
        return this;
    }

    public e e(float f) {
        if (f <= 0.0f) {
            throw new IllegalArgumentException("Spring stiffness constant must be positive.");
        }
        this.f452a = Math.sqrt(f);
        this.f454c = false;
        return this;
    }

    void f(double d2) {
        double abs = Math.abs(d2);
        this.f455d = abs;
        this.e = abs * 62.5d;
    }

    b.h g(double d2, double d3, long j) {
        double cos;
        double d4;
        if (!this.f454c) {
            if (this.i == Double.MAX_VALUE) {
                throw new IllegalStateException("Error: Final position of the spring must be set before the animation starts");
            }
            double d5 = this.f453b;
            if (d5 > 1.0d) {
                double d6 = this.f452a;
                this.f = (Math.sqrt((d5 * d5) - 1.0d) * d6) + ((-d5) * d6);
                double d7 = this.f453b;
                double d8 = this.f452a;
                this.g = ((-d7) * d8) - (Math.sqrt((d7 * d7) - 1.0d) * d8);
            } else if (d5 >= 0.0d && d5 < 1.0d) {
                this.h = Math.sqrt(1.0d - (d5 * d5)) * this.f452a;
            }
            this.f454c = true;
        }
        double d9 = j / 1000.0d;
        double d10 = d2 - this.i;
        double d11 = this.f453b;
        if (d11 > 1.0d) {
            double d12 = this.g;
            double d13 = this.f;
            double d14 = d10 - (((d12 * d10) - d3) / (d12 - d13));
            double d15 = ((d10 * d12) - d3) / (d12 - d13);
            d4 = (Math.pow(2.718281828459045d, this.f * d9) * d15) + (Math.pow(2.718281828459045d, d12 * d9) * d14);
            double d16 = this.g;
            double pow = Math.pow(2.718281828459045d, d16 * d9) * d14 * d16;
            double d17 = this.f;
            cos = (Math.pow(2.718281828459045d, d17 * d9) * d15 * d17) + pow;
        } else if (d11 == 1.0d) {
            double d18 = this.f452a;
            double d19 = (d18 * d10) + d3;
            double d20 = (d19 * d9) + d10;
            double pow2 = Math.pow(2.718281828459045d, (-d18) * d9) * d20;
            double pow3 = Math.pow(2.718281828459045d, (-this.f452a) * d9) * d20;
            double d21 = this.f452a;
            cos = (Math.pow(2.718281828459045d, (-d21) * d9) * d19) + (pow3 * (-d21));
            d4 = pow2;
        } else {
            double d22 = 1.0d / this.h;
            double d23 = this.f452a;
            double d24 = ((d11 * d23 * d10) + d3) * d22;
            double sin = ((Math.sin(this.h * d9) * d24) + (Math.cos(this.h * d9) * d10)) * Math.pow(2.718281828459045d, (-d11) * d23 * d9);
            double d25 = this.f452a;
            double d26 = this.f453b;
            double d27 = (-d25) * sin * d26;
            double pow4 = Math.pow(2.718281828459045d, (-d26) * d25 * d9);
            double d28 = this.h;
            double sin2 = Math.sin(d28 * d9) * (-d28) * d10;
            double d29 = this.h;
            cos = (((Math.cos(d29 * d9) * d24 * d29) + sin2) * pow4) + d27;
            d4 = sin;
        }
        b.h hVar = this.j;
        hVar.f450a = (float) (d4 + this.i);
        hVar.f451b = (float) cos;
        return hVar;
    }
}
