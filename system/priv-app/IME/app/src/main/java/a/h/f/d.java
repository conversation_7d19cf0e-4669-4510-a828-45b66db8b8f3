package a.h.f;

import com.google.android.material.button.MaterialButton;

/* loaded from: classes.dex */
public final class d {

    /* renamed from: a, reason: collision with root package name */
    public static final a.h.f.c f344a = new C0008d(null, false);

    /* renamed from: b, reason: collision with root package name */
    public static final a.h.f.c f345b = new C0008d(null, true);

    /* renamed from: c, reason: collision with root package name */
    public static final a.h.f.c f346c;

    /* renamed from: d, reason: collision with root package name */
    public static final a.h.f.c f347d;

    private static class a implements b {

        /* renamed from: a, reason: collision with root package name */
        static final a f348a = new a();

        private a() {
        }

        @Override // a.h.f.d.b
        public int a(CharSequence charSequence, int i, int i2) {
            int i3 = i2 + i;
            int i4 = 2;
            while (i < i3 && i4 == 2) {
                byte directionality = Character.getDirectionality(charSequence.charAt(i));
                a.h.f.c cVar = d.f344a;
                if (directionality != 0) {
                    if (directionality != 1 && directionality != 2) {
                        switch (directionality) {
                            case 14:
                            case 15:
                                break;
                            case MaterialButton.ICON_GRAVITY_TOP /* 16 */:
                            case 17:
                                break;
                            default:
                                i4 = 2;
                                break;
                        }
                        i++;
                    }
                    i4 = 0;
                    i++;
                }
                i4 = 1;
                i++;
            }
            return i4;
        }
    }

    private interface b {
        int a(CharSequence charSequence, int i, int i2);
    }

    /* JADX INFO: Access modifiers changed from: private */
    static abstract class c implements a.h.f.c {

        /* renamed from: a, reason: collision with root package name */
        private final b f349a;

        c(b bVar) {
            this.f349a = bVar;
        }

        @Override // a.h.f.c
        public boolean a(CharSequence charSequence, int i, int i2) {
            if (i < 0 || i2 < 0 || charSequence.length() - i2 < i) {
                throw new IllegalArgumentException();
            }
            b bVar = this.f349a;
            if (bVar == null) {
                return b();
            }
            int a2 = bVar.a(charSequence, i, i2);
            if (a2 == 0) {
                return true;
            }
            if (a2 != 1) {
                return b();
            }
            return false;
        }

        protected abstract boolean b();
    }

    /* renamed from: a.h.f.d$d, reason: collision with other inner class name */
    private static class C0008d extends c {

        /* renamed from: b, reason: collision with root package name */
        private final boolean f350b;

        C0008d(b bVar, boolean z) {
            super(bVar);
            this.f350b = z;
        }

        @Override // a.h.f.d.c
        protected boolean b() {
            return this.f350b;
        }
    }

    static {
        a aVar = a.f348a;
        f346c = new C0008d(aVar, false);
        f347d = new C0008d(aVar, true);
    }
}
