package a.f.a.j.o;

import a.f.a.j.e;
import a.f.a.j.o.f;

/* loaded from: classes.dex */
public class n extends p {
    public f k;
    g l;

    public n(a.f.a.j.e eVar) {
        super(eVar);
        f fVar = new f(this);
        this.k = fVar;
        this.l = null;
        this.h.e = f.a.f;
        this.i.e = f.a.g;
        fVar.e = f.a.h;
        this.f = 1;
    }

    @Override // a.f.a.j.o.p, a.f.a.j.o.d
    public void a(d dVar) {
        a.f.a.j.e eVar;
        int i;
        float f;
        int i2;
        e.a aVar = e.a.MATCH_CONSTRAINT;
        int ordinal = this.j.ordinal();
        if (ordinal != 1 && ordinal != 2 && ordinal == 3) {
            a.f.a.j.e eVar2 = this.f244b;
            m(eVar2.M, eVar2.O, 1);
            return;
        }
        g gVar = this.e;
        if (gVar.f227c && !gVar.j && this.f246d == aVar) {
            a.f.a.j.e eVar3 = this.f244b;
            int i3 = eVar3.s;
            if (i3 == 2) {
                a.f.a.j.e eVar4 = eVar3.X;
                if (eVar4 != null) {
                    if (eVar4.e.e.j) {
                        gVar.c((int) ((r1.g * eVar3.z) + 0.5f));
                    }
                }
            } else if (i3 == 3 && eVar3.f204d.e.j) {
                int v = eVar3.v();
                if (v == -1) {
                    eVar = this.f244b;
                    i = eVar.f204d.e.g;
                } else if (v == 0) {
                    f = r0.f204d.e.g * this.f244b.a0;
                    i2 = (int) (f + 0.5f);
                    this.e.c(i2);
                } else if (v != 1) {
                    i2 = 0;
                    this.e.c(i2);
                } else {
                    eVar = this.f244b;
                    i = eVar.f204d.e.g;
                }
                f = i / eVar.a0;
                i2 = (int) (f + 0.5f);
                this.e.c(i2);
            }
        }
        f fVar = this.h;
        if (fVar.f227c) {
            f fVar2 = this.i;
            if (fVar2.f227c) {
                if (fVar.j && fVar2.j && this.e.j) {
                    return;
                }
                if (!this.e.j && this.f246d == aVar) {
                    a.f.a.j.e eVar5 = this.f244b;
                    if (eVar5.r == 0 && !eVar5.c0()) {
                        f fVar3 = this.h.l.get(0);
                        f fVar4 = this.i.l.get(0);
                        int i4 = fVar3.g;
                        f fVar5 = this.h;
                        int i5 = i4 + fVar5.f;
                        int i6 = fVar4.g + this.i.f;
                        fVar5.c(i5);
                        this.i.c(i6);
                        this.e.c(i6 - i5);
                        return;
                    }
                }
                if (!this.e.j && this.f246d == aVar && this.f243a == 1 && this.h.l.size() > 0 && this.i.l.size() > 0) {
                    f fVar6 = this.h.l.get(0);
                    int i7 = (this.i.l.get(0).g + this.i.f) - (fVar6.g + this.h.f);
                    g gVar2 = this.e;
                    int i8 = gVar2.m;
                    if (i7 < i8) {
                        gVar2.c(i7);
                    } else {
                        gVar2.c(i8);
                    }
                }
                if (this.e.j && this.h.l.size() > 0 && this.i.l.size() > 0) {
                    f fVar7 = this.h.l.get(0);
                    f fVar8 = this.i.l.get(0);
                    int i9 = fVar7.g + this.h.f;
                    int i10 = fVar8.g + this.i.f;
                    float M = this.f244b.M();
                    if (fVar7 == fVar8) {
                        i9 = fVar7.g;
                        i10 = fVar8.g;
                        M = 0.5f;
                    }
                    this.h.c((int) ((((i10 - i9) - this.e.g) * M) + i9 + 0.5f));
                    this.i.c(this.h.g + this.e.g);
                }
            }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:100:0x0323, code lost:
    
        if (r10.f244b.T() != false) goto L121;
     */
    /* JADX WARN: Code restructure failed: missing block: B:101:0x0325, code lost:
    
        r0 = r10.k;
        r1 = r10.h;
        r2 = r10.l;
     */
    /* JADX WARN: Code restructure failed: missing block: B:130:0x0381, code lost:
    
        if (r0.f246d == r2) goto L162;
     */
    /* JADX WARN: Code restructure failed: missing block: B:131:0x042e, code lost:
    
        r0.e.k.add(r10.e);
        r10.e.l.add(r10.f244b.f204d.e);
        r10.e.f225a = r10;
     */
    /* JADX WARN: Code restructure failed: missing block: B:137:0x03bc, code lost:
    
        if (r10.f244b.T() != false) goto L121;
     */
    /* JADX WARN: Code restructure failed: missing block: B:155:0x042c, code lost:
    
        if (r0.f246d == r2) goto L162;
     */
    /* JADX WARN: Removed duplicated region for block: B:105:0x0452  */
    /* JADX WARN: Removed duplicated region for block: B:107:? A[RETURN, SYNTHETIC] */
    @Override // a.f.a.j.o.p
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    void d() {
        /*
            Method dump skipped, instructions count: 1111
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: a.f.a.j.o.n.d():void");
    }

    @Override // a.f.a.j.o.p
    public void e() {
        f fVar = this.h;
        if (fVar.j) {
            this.f244b.V0(fVar.g);
        }
    }

    @Override // a.f.a.j.o.p
    void f() {
        this.f245c = null;
        this.h.b();
        this.i.b();
        this.k.b();
        this.e.b();
        this.g = false;
    }

    @Override // a.f.a.j.o.p
    boolean l() {
        return this.f246d != e.a.MATCH_CONSTRAINT || this.f244b.s == 0;
    }

    void n() {
        this.g = false;
        this.h.b();
        this.h.j = false;
        this.i.b();
        this.i.j = false;
        this.k.b();
        this.k.j = false;
        this.e.j = false;
    }

    public String toString() {
        StringBuilder j = b.b.a.a.a.j("VerticalRun ");
        j.append(this.f244b.t());
        return j.toString();
    }
}
