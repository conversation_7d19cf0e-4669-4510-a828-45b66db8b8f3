package a.b.f;

import android.content.Context;
import android.content.ContextWrapper;
import android.content.res.AssetManager;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.view.LayoutInflater;
import org.libpag.R;

/* loaded from: classes.dex */
public class d extends ContextWrapper {

    /* renamed from: a, reason: collision with root package name */
    private int f13a;

    /* renamed from: b, reason: collision with root package name */
    private Resources.Theme f14b;

    /* renamed from: c, reason: collision with root package name */
    private LayoutInflater f15c;

    /* renamed from: d, reason: collision with root package name */
    private Configuration f16d;
    private Resources e;

    public d() {
        super(null);
    }

    public d(Context context, int i) {
        super(context);
        this.f13a = i;
    }

    public d(Context context, Resources.Theme theme) {
        super(context);
        this.f14b = theme;
    }

    private void b() {
        if (this.f14b == null) {
            this.f14b = getResources().newTheme();
            Resources.Theme theme = getBaseContext().getTheme();
            if (theme != null) {
                this.f14b.setTo(theme);
            }
        }
        this.f14b.applyStyle(this.f13a, true);
    }

    public void a(Configuration configuration) {
        if (this.e != null) {
            throw new IllegalStateException("getResources() or getAssets() has already been called");
        }
        if (this.f16d != null) {
            throw new IllegalStateException("Override configuration has already been set");
        }
        this.f16d = new Configuration(configuration);
    }

    @Override // android.content.ContextWrapper
    protected void attachBaseContext(Context context) {
        super.attachBaseContext(context);
    }

    @Override // android.content.ContextWrapper, android.content.Context
    public AssetManager getAssets() {
        return getResources().getAssets();
    }

    @Override // android.content.ContextWrapper, android.content.Context
    public Resources getResources() {
        if (this.e == null) {
            Configuration configuration = this.f16d;
            this.e = configuration == null ? super.getResources() : createConfigurationContext(configuration).getResources();
        }
        return this.e;
    }

    @Override // android.content.ContextWrapper, android.content.Context
    public Object getSystemService(String str) {
        if (!"layout_inflater".equals(str)) {
            return getBaseContext().getSystemService(str);
        }
        if (this.f15c == null) {
            this.f15c = LayoutInflater.from(getBaseContext()).cloneInContext(this);
        }
        return this.f15c;
    }

    @Override // android.content.ContextWrapper, android.content.Context
    public Resources.Theme getTheme() {
        Resources.Theme theme = this.f14b;
        if (theme != null) {
            return theme;
        }
        if (this.f13a == 0) {
            this.f13a = R.style.Theme_AppCompat_Light;
        }
        b();
        return this.f14b;
    }

    public int getThemeResId() {
        return this.f13a;
    }

    @Override // android.content.ContextWrapper, android.content.Context
    public void setTheme(int i) {
        if (this.f13a != i) {
            this.f13a = i;
            b();
        }
    }
}
