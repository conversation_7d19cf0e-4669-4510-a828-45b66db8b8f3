package a.h.f;

import android.annotation.SuppressLint;
import android.text.PrecomputedText;
import android.text.Spannable;
import android.text.TextDirectionHeuristic;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.MetricAffectingSpan;
import java.util.Objects;

/* loaded from: classes.dex */
public class b implements Spannable {

    public static final class a {

        /* renamed from: a, reason: collision with root package name */
        private final TextPaint f340a;

        /* renamed from: b, reason: collision with root package name */
        private final TextDirectionHeuristic f341b;

        /* renamed from: c, reason: collision with root package name */
        private final int f342c;

        /* renamed from: d, reason: collision with root package name */
        private final int f343d;

        public a(PrecomputedText.Params params) {
            this.f340a = params.getTextPaint();
            this.f341b = params.getTextDirection();
            this.f342c = params.getBreakStrategy();
            this.f343d = params.getHyphenationFrequency();
        }

        public boolean a(a aVar) {
            if (this.f342c == aVar.f342c && this.f343d == aVar.f343d && this.f340a.getTextSize() == aVar.f340a.getTextSize() && this.f340a.getTextScaleX() == aVar.f340a.getTextScaleX() && this.f340a.getTextSkewX() == aVar.f340a.getTextSkewX() && this.f340a.getLetterSpacing() == aVar.f340a.getLetterSpacing() && TextUtils.equals(this.f340a.getFontFeatureSettings(), aVar.f340a.getFontFeatureSettings()) && this.f340a.getFlags() == aVar.f340a.getFlags() && this.f340a.getTextLocales().equals(aVar.f340a.getTextLocales())) {
                return this.f340a.getTypeface() == null ? aVar.f340a.getTypeface() == null : this.f340a.getTypeface().equals(aVar.f340a.getTypeface());
            }
            return false;
        }

        public int b() {
            return this.f342c;
        }

        public int c() {
            return this.f343d;
        }

        public TextDirectionHeuristic d() {
            return this.f341b;
        }

        public TextPaint e() {
            return this.f340a;
        }

        public boolean equals(Object obj) {
            if (obj == this) {
                return true;
            }
            if (!(obj instanceof a)) {
                return false;
            }
            a aVar = (a) obj;
            return a(aVar) && this.f341b == aVar.f341b;
        }

        public int hashCode() {
            return Objects.hash(Float.valueOf(this.f340a.getTextSize()), Float.valueOf(this.f340a.getTextScaleX()), Float.valueOf(this.f340a.getTextSkewX()), Float.valueOf(this.f340a.getLetterSpacing()), Integer.valueOf(this.f340a.getFlags()), this.f340a.getTextLocales(), this.f340a.getTypeface(), Boolean.valueOf(this.f340a.isElegantTextHeight()), this.f341b, Integer.valueOf(this.f342c), Integer.valueOf(this.f343d));
        }

        public String toString() {
            StringBuilder sb = new StringBuilder("{");
            StringBuilder j = b.b.a.a.a.j("textSize=");
            j.append(this.f340a.getTextSize());
            sb.append(j.toString());
            sb.append(", textScaleX=" + this.f340a.getTextScaleX());
            sb.append(", textSkewX=" + this.f340a.getTextSkewX());
            sb.append(", letterSpacing=" + this.f340a.getLetterSpacing());
            sb.append(", elegantTextHeight=" + this.f340a.isElegantTextHeight());
            sb.append(", textLocale=" + this.f340a.getTextLocales());
            sb.append(", typeface=" + this.f340a.getTypeface());
            sb.append(", variationSettings=" + this.f340a.getFontVariationSettings());
            sb.append(", textDir=" + this.f341b);
            sb.append(", breakStrategy=" + this.f342c);
            sb.append(", hyphenationFrequency=" + this.f343d);
            sb.append("}");
            return sb.toString();
        }
    }

    @Override // java.lang.CharSequence
    public char charAt(int i) {
        throw null;
    }

    @Override // android.text.Spanned
    public int getSpanEnd(Object obj) {
        throw null;
    }

    @Override // android.text.Spanned
    public int getSpanFlags(Object obj) {
        throw null;
    }

    @Override // android.text.Spanned
    public int getSpanStart(Object obj) {
        throw null;
    }

    @Override // android.text.Spanned
    @SuppressLint({"NewApi"})
    public <T> T[] getSpans(int i, int i2, Class<T> cls) {
        throw null;
    }

    @Override // java.lang.CharSequence
    public int length() {
        throw null;
    }

    @Override // android.text.Spanned
    public int nextSpanTransition(int i, int i2, Class cls) {
        throw null;
    }

    @Override // android.text.Spannable
    @SuppressLint({"NewApi"})
    public void removeSpan(Object obj) {
        if (!(obj instanceof MetricAffectingSpan)) {
            throw null;
        }
        throw new IllegalArgumentException("MetricAffectingSpan can not be removed from PrecomputedText.");
    }

    @Override // android.text.Spannable
    @SuppressLint({"NewApi"})
    public void setSpan(Object obj, int i, int i2, int i3) {
        if (!(obj instanceof MetricAffectingSpan)) {
            throw null;
        }
        throw new IllegalArgumentException("MetricAffectingSpan can not be set to PrecomputedText.");
    }

    @Override // java.lang.CharSequence
    public CharSequence subSequence(int i, int i2) {
        throw null;
    }

    @Override // java.lang.CharSequence
    public String toString() {
        throw null;
    }
}
