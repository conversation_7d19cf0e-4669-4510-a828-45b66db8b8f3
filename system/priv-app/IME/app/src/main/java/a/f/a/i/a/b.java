package a.f.a.i.a;

/* loaded from: classes.dex */
public abstract class b {

    static class a extends b {

        /* renamed from: a, reason: collision with root package name */
        double f137a;

        /* renamed from: b, reason: collision with root package name */
        double[] f138b;

        a(double d2, double[] dArr) {
            this.f137a = d2;
            this.f138b = dArr;
        }

        @Override // a.f.a.i.a.b
        public double b(double d2, int i) {
            return this.f138b[i];
        }

        @Override // a.f.a.i.a.b
        public void c(double d2, double[] dArr) {
            double[] dArr2 = this.f138b;
            System.arraycopy(dArr2, 0, dArr, 0, dArr2.length);
        }

        @Override // a.f.a.i.a.b
        public void d(double d2, float[] fArr) {
            int i = 0;
            while (true) {
                double[] dArr = this.f138b;
                if (i >= dArr.length) {
                    return;
                }
                fArr[i] = (float) dArr[i];
                i++;
            }
        }

        @Override // a.f.a.i.a.b
        public double e(double d2, int i) {
            return 0.0d;
        }

        @Override // a.f.a.i.a.b
        public void f(double d2, double[] dArr) {
            for (int i = 0; i < this.f138b.length; i++) {
                dArr[i] = 0.0d;
            }
        }

        @Override // a.f.a.i.a.b
        public double[] g() {
            return new double[]{this.f137a};
        }
    }

    public static b a(int i, double[] dArr, double[][] dArr2) {
        if (dArr.length == 1) {
            i = 2;
        }
        return i != 0 ? i != 2 ? new f(dArr, dArr2) : new a(dArr[0], dArr2[0]) : new g(dArr, dArr2);
    }

    public abstract double b(double d2, int i);

    public abstract void c(double d2, double[] dArr);

    public abstract void d(double d2, float[] fArr);

    public abstract double e(double d2, int i);

    public abstract void f(double d2, double[] dArr);

    public abstract double[] g();
}
