package a.f.a.j;

import java.util.ArrayList;

/* loaded from: classes.dex */
public class n extends e {
    public ArrayList<e> N0 = new ArrayList<>();

    public void Y0() {
        ArrayList<e> arrayList = this.N0;
        if (arrayList == null) {
            return;
        }
        int size = arrayList.size();
        for (int i = 0; i < size; i++) {
            e eVar = this.N0.get(i);
            if (eVar instanceof n) {
                ((n) eVar).Y0();
            }
        }
    }

    @Override // a.f.a.j.e
    public void k0() {
        this.N0.clear();
        super.k0();
    }

    @Override // a.f.a.j.e
    public void n0(a.f.a.c cVar) {
        super.n0(cVar);
        int size = this.N0.size();
        for (int i = 0; i < size; i++) {
            this.N0.get(i).n0(cVar);
        }
    }
}
