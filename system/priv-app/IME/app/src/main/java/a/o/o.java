package a.o;

import a.o.i;
import android.animation.TimeInterpolator;
import android.util.AndroidRuntimeException;
import android.view.View;
import android.view.ViewGroup;
import java.util.ArrayList;
import java.util.Iterator;

/* loaded from: classes.dex */
public class o extends i {
    int z;
    private ArrayList<i> x = new ArrayList<>();
    private boolean y = true;
    boolean A = false;
    private int B = 0;

    class a extends l {

        /* renamed from: a, reason: collision with root package name */
        final /* synthetic */ i f510a;

        a(o oVar, i iVar) {
            this.f510a = iVar;
        }

        @Override // a.o.i.d
        public void e(i iVar) {
            this.f510a.E();
            iVar.B(this);
        }
    }

    static class b extends l {

        /* renamed from: a, reason: collision with root package name */
        o f511a;

        b(o oVar) {
            this.f511a = oVar;
        }

        @Override // a.o.l, a.o.i.d
        public void c(i iVar) {
            o oVar = this.f511a;
            if (oVar.A) {
                return;
            }
            oVar.L();
            this.f511a.A = true;
        }

        @Override // a.o.i.d
        public void e(i iVar) {
            o oVar = this.f511a;
            int i = oVar.z - 1;
            oVar.z = i;
            if (i == 0) {
                oVar.A = false;
                oVar.n();
            }
            iVar.B(this);
        }
    }

    @Override // a.o.i
    public i B(i.d dVar) {
        super.B(dVar);
        return this;
    }

    @Override // a.o.i
    public i C(View view) {
        for (int i = 0; i < this.x.size(); i++) {
            this.x.get(i).C(view);
        }
        this.f.remove(view);
        return this;
    }

    @Override // a.o.i
    public void D(View view) {
        super.D(view);
        int size = this.x.size();
        for (int i = 0; i < size; i++) {
            this.x.get(i).D(view);
        }
    }

    @Override // a.o.i
    protected void E() {
        if (this.x.isEmpty()) {
            L();
            n();
            return;
        }
        b bVar = new b(this);
        Iterator<i> it = this.x.iterator();
        while (it.hasNext()) {
            it.next().a(bVar);
        }
        this.z = this.x.size();
        if (this.y) {
            Iterator<i> it2 = this.x.iterator();
            while (it2.hasNext()) {
                it2.next().E();
            }
            return;
        }
        for (int i = 1; i < this.x.size(); i++) {
            this.x.get(i - 1).a(new a(this, this.x.get(i)));
        }
        i iVar = this.x.get(0);
        if (iVar != null) {
            iVar.E();
        }
    }

    @Override // a.o.i
    public /* bridge */ /* synthetic */ i F(long j) {
        Q(j);
        return this;
    }

    @Override // a.o.i
    public void G(i.c cVar) {
        super.G(cVar);
        this.B |= 8;
        int size = this.x.size();
        for (int i = 0; i < size; i++) {
            this.x.get(i).G(cVar);
        }
    }

    @Override // a.o.i
    public void I(f fVar) {
        super.I(fVar);
        this.B |= 4;
        if (this.x != null) {
            for (int i = 0; i < this.x.size(); i++) {
                this.x.get(i).I(fVar);
            }
        }
    }

    @Override // a.o.i
    public void J(n nVar) {
        this.B |= 2;
        int size = this.x.size();
        for (int i = 0; i < size; i++) {
            this.x.get(i).J(nVar);
        }
    }

    @Override // a.o.i
    public i K(long j) {
        super.K(j);
        return this;
    }

    @Override // a.o.i
    String M(String str) {
        String M = super.M(str);
        for (int i = 0; i < this.x.size(); i++) {
            M = M + "\n" + this.x.get(i).M(b.b.a.a.a.g(str, "  "));
        }
        return M;
    }

    public o N(i iVar) {
        this.x.add(iVar);
        iVar.i = this;
        long j = this.f494c;
        if (j >= 0) {
            iVar.F(j);
        }
        if ((this.B & 1) != 0) {
            iVar.H(p());
        }
        if ((this.B & 2) != 0) {
            iVar.J(null);
        }
        if ((this.B & 4) != 0) {
            iVar.I(r());
        }
        if ((this.B & 8) != 0) {
            iVar.G(o());
        }
        return this;
    }

    public i O(int i) {
        if (i < 0 || i >= this.x.size()) {
            return null;
        }
        return this.x.get(i);
    }

    public int P() {
        return this.x.size();
    }

    public o Q(long j) {
        ArrayList<i> arrayList;
        this.f494c = j;
        if (j >= 0 && (arrayList = this.x) != null) {
            int size = arrayList.size();
            for (int i = 0; i < size; i++) {
                this.x.get(i).F(j);
            }
        }
        return this;
    }

    @Override // a.o.i
    /* renamed from: R, reason: merged with bridge method [inline-methods] */
    public o H(TimeInterpolator timeInterpolator) {
        this.B |= 1;
        ArrayList<i> arrayList = this.x;
        if (arrayList != null) {
            int size = arrayList.size();
            for (int i = 0; i < size; i++) {
                this.x.get(i).H(timeInterpolator);
            }
        }
        super.H(timeInterpolator);
        return this;
    }

    public o S(int i) {
        if (i == 0) {
            this.y = true;
        } else {
            if (i != 1) {
                throw new AndroidRuntimeException(b.b.a.a.a.e("Invalid parameter for TransitionSet ordering: ", i));
            }
            this.y = false;
        }
        return this;
    }

    @Override // a.o.i
    public i a(i.d dVar) {
        super.a(dVar);
        return this;
    }

    @Override // a.o.i
    public i b(View view) {
        for (int i = 0; i < this.x.size(); i++) {
            this.x.get(i).b(view);
        }
        this.f.add(view);
        return this;
    }

    @Override // a.o.i
    public void e(p pVar) {
        if (x(pVar.f513b)) {
            Iterator<i> it = this.x.iterator();
            while (it.hasNext()) {
                i next = it.next();
                if (next.x(pVar.f513b)) {
                    next.e(pVar);
                    pVar.f514c.add(next);
                }
            }
        }
    }

    @Override // a.o.i
    void g(p pVar) {
        int size = this.x.size();
        for (int i = 0; i < size; i++) {
            this.x.get(i).g(pVar);
        }
    }

    @Override // a.o.i
    public void h(p pVar) {
        if (x(pVar.f513b)) {
            Iterator<i> it = this.x.iterator();
            while (it.hasNext()) {
                i next = it.next();
                if (next.x(pVar.f513b)) {
                    next.h(pVar);
                    pVar.f514c.add(next);
                }
            }
        }
    }

    @Override // a.o.i
    /* renamed from: k */
    public i clone() {
        o oVar = (o) super.clone();
        oVar.x = new ArrayList<>();
        int size = this.x.size();
        for (int i = 0; i < size; i++) {
            i clone = this.x.get(i).clone();
            oVar.x.add(clone);
            clone.i = oVar;
        }
        return oVar;
    }

    @Override // a.o.i
    protected void m(ViewGroup viewGroup, q qVar, q qVar2, ArrayList<p> arrayList, ArrayList<p> arrayList2) {
        long t = t();
        int size = this.x.size();
        for (int i = 0; i < size; i++) {
            i iVar = this.x.get(i);
            if (t > 0 && (this.y || i == 0)) {
                long t2 = iVar.t();
                if (t2 > 0) {
                    iVar.K(t2 + t);
                } else {
                    iVar.K(t);
                }
            }
            iVar.m(viewGroup, qVar, qVar2, arrayList, arrayList2);
        }
    }

    @Override // a.o.i
    public void z(View view) {
        super.z(view);
        int size = this.x.size();
        for (int i = 0; i < size; i++) {
            this.x.get(i).z(view);
        }
    }
}
