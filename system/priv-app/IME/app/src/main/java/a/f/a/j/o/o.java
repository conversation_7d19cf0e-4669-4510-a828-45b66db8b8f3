package a.f.a.j.o;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Iterator;

/* loaded from: classes.dex */
public class o {
    static int f;

    /* renamed from: b, reason: collision with root package name */
    int f240b;

    /* renamed from: c, reason: collision with root package name */
    int f241c;

    /* renamed from: a, reason: collision with root package name */
    ArrayList<a.f.a.j.e> f239a = new ArrayList<>();

    /* renamed from: d, reason: collision with root package name */
    ArrayList<a> f242d = null;
    private int e = -1;

    class a {
        public a(o oVar, a.f.a.j.e eVar, a.f.a.d dVar, int i) {
            new WeakReference(eVar);
            dVar.p(eVar.L);
            dVar.p(eVar.M);
            dVar.p(eVar.N);
            dVar.p(eVar.O);
            dVar.p(eVar.P);
        }
    }

    public o(int i) {
        this.f240b = -1;
        this.f241c = 0;
        int i2 = f;
        f = i2 + 1;
        this.f240b = i2;
        this.f241c = i;
    }

    public boolean a(a.f.a.j.e eVar) {
        if (this.f239a.contains(eVar)) {
            return false;
        }
        this.f239a.add(eVar);
        return true;
    }

    public void b(ArrayList<o> arrayList) {
        int size = this.f239a.size();
        if (this.e != -1 && size > 0) {
            for (int i = 0; i < arrayList.size(); i++) {
                o oVar = arrayList.get(i);
                if (this.e == oVar.f240b) {
                    d(this.f241c, oVar);
                }
            }
        }
        if (size == 0) {
            arrayList.remove(this);
        }
    }

    public int c(a.f.a.d dVar, int i) {
        int p;
        a.f.a.j.d dVar2;
        if (this.f239a.size() == 0) {
            return 0;
        }
        ArrayList<a.f.a.j.e> arrayList = this.f239a;
        a.f.a.j.f fVar = (a.f.a.j.f) arrayList.get(0).X;
        dVar.v();
        fVar.f(dVar, false);
        for (int i2 = 0; i2 < arrayList.size(); i2++) {
            arrayList.get(i2).f(dVar, false);
        }
        if (i == 0 && fVar.W0 > 0) {
            a.f.a.j.b.a(fVar, dVar, arrayList, 0);
        }
        if (i == 1 && fVar.X0 > 0) {
            a.f.a.j.b.a(fVar, dVar, arrayList, 1);
        }
        try {
            dVar.r();
        } catch (Exception e) {
            e.printStackTrace();
        }
        this.f242d = new ArrayList<>();
        for (int i3 = 0; i3 < arrayList.size(); i3++) {
            this.f242d.add(new a(this, arrayList.get(i3), dVar, i));
        }
        if (i == 0) {
            p = dVar.p(fVar.L);
            dVar2 = fVar.N;
        } else {
            p = dVar.p(fVar.M);
            dVar2 = fVar.O;
        }
        int p2 = dVar.p(dVar2);
        dVar.v();
        return p2 - p;
    }

    public void d(int i, o oVar) {
        Iterator<a.f.a.j.e> it = this.f239a.iterator();
        while (it.hasNext()) {
            a.f.a.j.e next = it.next();
            oVar.a(next);
            if (i == 0) {
                next.L0 = oVar.f240b;
            } else {
                next.M0 = oVar.f240b;
            }
        }
        this.e = oVar.f240b;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder();
        int i = this.f241c;
        sb.append(i == 0 ? "Horizontal" : i == 1 ? "Vertical" : i == 2 ? "Both" : "Unknown");
        sb.append(" [");
        sb.append(this.f240b);
        sb.append("] <");
        String sb2 = sb.toString();
        Iterator<a.f.a.j.e> it = this.f239a.iterator();
        while (it.hasNext()) {
            sb2 = sb2 + " " + it.next().t();
        }
        return b.b.a.a.a.g(sb2, " >");
    }
}
