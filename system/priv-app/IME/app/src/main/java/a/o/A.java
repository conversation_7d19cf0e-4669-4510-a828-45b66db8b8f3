package a.o;

import a.o.i;
import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.util.Property;
import android.view.View;
import android.view.ViewGroup;

/* loaded from: classes.dex */
public abstract class A extends i {
    private static final String[] y = {"android:visibility:visibility", "android:visibility:parent"};
    private int x = 3;

    private static class a extends AnimatorListenerAdapter implements i.d {

        /* renamed from: a, reason: collision with root package name */
        private final View f466a;

        /* renamed from: b, reason: collision with root package name */
        private final int f467b;

        /* renamed from: c, reason: collision with root package name */
        private final ViewGroup f468c;

        /* renamed from: d, reason: collision with root package name */
        private final boolean f469d;
        private boolean e;
        boolean f = false;

        a(View view, int i, boolean z) {
            this.f466a = view;
            this.f467b = i;
            this.f468c = (ViewGroup) view.getParent();
            this.f469d = z;
            g(true);
        }

        private void f() {
            if (!this.f) {
                View view = this.f466a;
                int i = this.f467b;
                Property<View, Float> property = s.f521b;
                view.setTransitionVisibility(i);
                ViewGroup viewGroup = this.f468c;
                if (viewGroup != null) {
                    viewGroup.invalidate();
                }
            }
            g(false);
        }

        private void g(boolean z) {
            ViewGroup viewGroup;
            if (!this.f469d || this.e == z || (viewGroup = this.f468c) == null) {
                return;
            }
            this.e = z;
            viewGroup.suppressLayout(z);
        }

        @Override // a.o.i.d
        public void a(i iVar) {
            g(false);
        }

        @Override // a.o.i.d
        public void b(i iVar) {
            g(true);
        }

        @Override // a.o.i.d
        public void c(i iVar) {
        }

        @Override // a.o.i.d
        public void d(i iVar) {
        }

        @Override // a.o.i.d
        public void e(i iVar) {
            f();
            iVar.B(this);
        }

        @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
        public void onAnimationCancel(Animator animator) {
            this.f = true;
        }

        @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
        public void onAnimationEnd(Animator animator) {
            f();
        }

        @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorPauseListener
        public void onAnimationPause(Animator animator) {
            if (this.f) {
                return;
            }
            View view = this.f466a;
            int i = this.f467b;
            Property<View, Float> property = s.f521b;
            view.setTransitionVisibility(i);
        }

        @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
        public void onAnimationRepeat(Animator animator) {
        }

        @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorPauseListener
        public void onAnimationResume(Animator animator) {
            if (this.f) {
                return;
            }
            View view = this.f466a;
            Property<View, Float> property = s.f521b;
            view.setTransitionVisibility(0);
        }

        @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
        public void onAnimationStart(Animator animator) {
        }
    }

    private static class b {

        /* renamed from: a, reason: collision with root package name */
        boolean f470a;

        /* renamed from: b, reason: collision with root package name */
        boolean f471b;

        /* renamed from: c, reason: collision with root package name */
        int f472c;

        /* renamed from: d, reason: collision with root package name */
        int f473d;
        ViewGroup e;
        ViewGroup f;

        b() {
        }
    }

    private void N(p pVar) {
        pVar.f512a.put("android:visibility:visibility", Integer.valueOf(pVar.f513b.getVisibility()));
        pVar.f512a.put("android:visibility:parent", pVar.f513b.getParent());
        int[] iArr = new int[2];
        pVar.f513b.getLocationOnScreen(iArr);
        pVar.f512a.put("android:visibility:screenLocation", iArr);
    }

    /* JADX WARN: Code restructure failed: missing block: B:21:0x0075, code lost:
    
        if (r8 == 0) goto L35;
     */
    /* JADX WARN: Code restructure failed: missing block: B:29:0x007f, code lost:
    
        if (r6.e == null) goto L35;
     */
    /* JADX WARN: Code restructure failed: missing block: B:35:0x0091, code lost:
    
        if (r6.f472c == 0) goto L40;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private a.o.A.b O(a.o.p r7, a.o.p r8) {
        /*
            r6 = this;
            a.o.A$b r6 = new a.o.A$b
            r6.<init>()
            r0 = 0
            r6.f470a = r0
            r6.f471b = r0
            java.lang.String r1 = "android:visibility:parent"
            r2 = 0
            r3 = -1
            java.lang.String r4 = "android:visibility:visibility"
            if (r7 == 0) goto L33
            java.util.Map<java.lang.String, java.lang.Object> r5 = r7.f512a
            boolean r5 = r5.containsKey(r4)
            if (r5 == 0) goto L33
            java.util.Map<java.lang.String, java.lang.Object> r5 = r7.f512a
            java.lang.Object r5 = r5.get(r4)
            java.lang.Integer r5 = (java.lang.Integer) r5
            int r5 = r5.intValue()
            r6.f472c = r5
            java.util.Map<java.lang.String, java.lang.Object> r5 = r7.f512a
            java.lang.Object r5 = r5.get(r1)
            android.view.ViewGroup r5 = (android.view.ViewGroup) r5
            r6.e = r5
            goto L37
        L33:
            r6.f472c = r3
            r6.e = r2
        L37:
            if (r8 == 0) goto L5a
            java.util.Map<java.lang.String, java.lang.Object> r5 = r8.f512a
            boolean r5 = r5.containsKey(r4)
            if (r5 == 0) goto L5a
            java.util.Map<java.lang.String, java.lang.Object> r2 = r8.f512a
            java.lang.Object r2 = r2.get(r4)
            java.lang.Integer r2 = (java.lang.Integer) r2
            int r2 = r2.intValue()
            r6.f473d = r2
            java.util.Map<java.lang.String, java.lang.Object> r2 = r8.f512a
            java.lang.Object r1 = r2.get(r1)
            android.view.ViewGroup r1 = (android.view.ViewGroup) r1
            r6.f = r1
            goto L5e
        L5a:
            r6.f473d = r3
            r6.f = r2
        L5e:
            r1 = 1
            if (r7 == 0) goto L82
            if (r8 == 0) goto L82
            int r7 = r6.f472c
            int r8 = r6.f473d
            if (r7 != r8) goto L70
            android.view.ViewGroup r2 = r6.e
            android.view.ViewGroup r3 = r6.f
            if (r2 != r3) goto L70
            return r6
        L70:
            if (r7 == r8) goto L78
            if (r7 != 0) goto L75
            goto L93
        L75:
            if (r8 != 0) goto L96
            goto L88
        L78:
            android.view.ViewGroup r7 = r6.f
            if (r7 != 0) goto L7d
            goto L93
        L7d:
            android.view.ViewGroup r7 = r6.e
            if (r7 != 0) goto L96
            goto L88
        L82:
            if (r7 != 0) goto L8d
            int r7 = r6.f473d
            if (r7 != 0) goto L8d
        L88:
            r6.f471b = r1
        L8a:
            r6.f470a = r1
            goto L96
        L8d:
            if (r8 != 0) goto L96
            int r7 = r6.f472c
            if (r7 != 0) goto L96
        L93:
            r6.f471b = r0
            goto L8a
        L96:
            return r6
        */
        throw new UnsupportedOperationException("Method not decompiled: a.o.A.O(a.o.p, a.o.p):a.o.A$b");
    }

    public abstract Animator P(ViewGroup viewGroup, View view, p pVar, p pVar2);

    public abstract Animator Q(ViewGroup viewGroup, View view, p pVar, p pVar2);

    public void R(int i) {
        if ((i & (-4)) != 0) {
            throw new IllegalArgumentException("Only MODE_IN and MODE_OUT flags are allowed");
        }
        this.x = i;
    }

    @Override // a.o.i
    public void e(p pVar) {
        N(pVar);
    }

    @Override // a.o.i
    public void h(p pVar) {
        N(pVar);
    }

    /* JADX WARN: Code restructure failed: missing block: B:16:0x003e, code lost:
    
        if (O(q(r4, false), v(r4, false)).f470a != false) goto L19;
     */
    /* JADX WARN: Removed duplicated region for block: B:59:0x008c  */
    /* JADX WARN: Removed duplicated region for block: B:71:0x01a2  */
    @Override // a.o.i
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public android.animation.Animator l(android.view.ViewGroup r21, a.o.p r22, a.o.p r23) {
        /*
            Method dump skipped, instructions count: 610
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: a.o.A.l(android.view.ViewGroup, a.o.p, a.o.p):android.animation.Animator");
    }

    @Override // a.o.i
    public String[] u() {
        return y;
    }

    @Override // a.o.i
    public boolean w(p pVar, p pVar2) {
        if (pVar == null && pVar2 == null) {
            return false;
        }
        if (pVar != null && pVar2 != null && pVar2.f512a.containsKey("android:visibility:visibility") != pVar.f512a.containsKey("android:visibility:visibility")) {
            return false;
        }
        b O = O(pVar, pVar2);
        if (O.f470a) {
            return O.f472c == 0 || O.f473d == 0;
        }
        return false;
    }
}
