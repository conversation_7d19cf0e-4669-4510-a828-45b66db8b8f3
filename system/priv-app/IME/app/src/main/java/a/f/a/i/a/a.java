package a.f.a.i.a;

import java.util.Arrays;

/* loaded from: classes.dex */
public class a extends b {

    /* renamed from: a, reason: collision with root package name */
    private final double[] f131a;

    /* renamed from: b, reason: collision with root package name */
    C0002a[] f132b;

    /* renamed from: a.f.a.i.a.a$a, reason: collision with other inner class name */
    private static class C0002a {
        private static double[] s = new double[91];

        /* renamed from: a, reason: collision with root package name */
        double[] f133a;

        /* renamed from: b, reason: collision with root package name */
        double f134b;

        /* renamed from: c, reason: collision with root package name */
        double f135c;

        /* renamed from: d, reason: collision with root package name */
        double f136d;
        double e;
        double f;
        double g;
        double h;
        double i;
        double j;
        double k;
        double l;
        double m;
        double n;
        double o;
        double p;
        boolean q;
        boolean r;

        C0002a(int i, double d2, double d3, double d4, double d5, double d6, double d7) {
            double d8 = d4;
            this.r = false;
            this.q = i == 1;
            this.f135c = d2;
            this.f136d = d3;
            this.i = 1.0d / (d3 - d2);
            if (3 == i) {
                this.r = true;
            }
            double d9 = d6 - d8;
            double d10 = d7 - d5;
            if (this.r || Math.abs(d9) < 0.001d || Math.abs(d10) < 0.001d) {
                this.r = true;
                this.e = d8;
                this.f = d6;
                this.g = d5;
                this.h = d7;
                double hypot = Math.hypot(d10, d9);
                this.f134b = hypot;
                this.n = hypot * this.i;
                double d11 = this.f136d;
                double d12 = this.f135c;
                this.l = d9 / (d11 - d12);
                this.m = d10 / (d11 - d12);
                return;
            }
            this.f133a = new double[101];
            boolean z = this.q;
            this.j = (z ? -1 : 1) * d9;
            this.k = d10 * (z ? 1 : -1);
            this.l = z ? d6 : d8;
            this.m = z ? d5 : d7;
            double d13 = d5 - d7;
            int i2 = 0;
            double d14 = 0.0d;
            double d15 = 0.0d;
            double d16 = 0.0d;
            while (true) {
                if (i2 >= s.length) {
                    break;
                }
                double radians = Math.toRadians((i2 * 90.0d) / (r14.length - 1));
                double sin = Math.sin(radians) * d9;
                double cos = Math.cos(radians) * d13;
                if (i2 > 0) {
                    d14 += Math.hypot(sin - d15, cos - d16);
                    s[i2] = d14;
                }
                i2++;
                d16 = cos;
                d15 = sin;
            }
            this.f134b = d14;
            int i3 = 0;
            while (true) {
                double[] dArr = s;
                if (i3 >= dArr.length) {
                    break;
                }
                dArr[i3] = dArr[i3] / d14;
                i3++;
            }
            int i4 = 0;
            while (true) {
                if (i4 >= this.f133a.length) {
                    this.n = this.f134b * this.i;
                    return;
                }
                double length = i4 / (r1.length - 1);
                int binarySearch = Arrays.binarySearch(s, length);
                if (binarySearch >= 0) {
                    this.f133a[i4] = binarySearch / (s.length - 1);
                } else if (binarySearch == -1) {
                    this.f133a[i4] = 0.0d;
                } else {
                    int i5 = -binarySearch;
                    int i6 = i5 - 2;
                    double[] dArr2 = s;
                    this.f133a[i4] = (((length - dArr2[i6]) / (dArr2[i5 - 1] - dArr2[i6])) + i6) / (dArr2.length - 1);
                }
                i4++;
            }
        }

        double a() {
            double d2 = this.j * this.p;
            double hypot = this.n / Math.hypot(d2, (-this.k) * this.o);
            if (this.q) {
                d2 = -d2;
            }
            return d2 * hypot;
        }

        double b() {
            double d2 = this.j * this.p;
            double d3 = (-this.k) * this.o;
            double hypot = this.n / Math.hypot(d2, d3);
            return this.q ? (-d3) * hypot : d3 * hypot;
        }

        public double c(double d2) {
            double d3 = (d2 - this.f135c) * this.i;
            double d4 = this.e;
            return ((this.f - d4) * d3) + d4;
        }

        public double d(double d2) {
            double d3 = (d2 - this.f135c) * this.i;
            double d4 = this.g;
            return ((this.h - d4) * d3) + d4;
        }

        double e() {
            return (this.j * this.o) + this.l;
        }

        double f() {
            return (this.k * this.p) + this.m;
        }

        void g(double d2) {
            double d3 = (this.q ? this.f136d - d2 : d2 - this.f135c) * this.i;
            double d4 = 0.0d;
            if (d3 > 0.0d) {
                d4 = 1.0d;
                if (d3 < 1.0d) {
                    double[] dArr = this.f133a;
                    double length = d3 * (dArr.length - 1);
                    int i = (int) length;
                    d4 = ((dArr[i + 1] - dArr[i]) * (length - i)) + dArr[i];
                }
            }
            double d5 = d4 * 1.5707963267948966d;
            this.o = Math.sin(d5);
            this.p = Math.cos(d5);
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:9:0x0026, code lost:
    
        if (r5 == 1) goto L12;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public a(int[] r25, double[] r26, double[][] r27) {
        /*
            r24 = this;
            r0 = r24
            r1 = r26
            r24.<init>()
            r0.f131a = r1
            int r2 = r1.length
            r3 = 1
            int r2 = r2 - r3
            a.f.a.i.a.a$a[] r2 = new a.f.a.i.a.a.C0002a[r2]
            r0.f132b = r2
            r2 = 0
            r4 = r2
            r5 = r3
            r6 = r5
        L14:
            a.f.a.i.a.a$a[] r7 = r0.f132b
            int r8 = r7.length
            if (r4 >= r8) goto L51
            r8 = r25[r4]
            r9 = 3
            r10 = 2
            if (r8 == 0) goto L2d
            if (r8 == r3) goto L2a
            if (r8 == r10) goto L28
            if (r8 == r9) goto L26
            goto L2e
        L26:
            if (r5 != r3) goto L2a
        L28:
            r5 = r10
            goto L2b
        L2a:
            r5 = r3
        L2b:
            r6 = r5
            goto L2e
        L2d:
            r6 = r9
        L2e:
            a.f.a.i.a.a$a r22 = new a.f.a.i.a.a$a
            r10 = r1[r4]
            int r23 = r4 + 1
            r12 = r1[r23]
            r8 = r27[r4]
            r14 = r8[r2]
            r8 = r27[r4]
            r16 = r8[r3]
            r8 = r27[r23]
            r18 = r8[r2]
            r8 = r27[r23]
            r20 = r8[r3]
            r8 = r22
            r9 = r6
            r8.<init>(r9, r10, r12, r14, r16, r18, r20)
            r7[r4] = r22
            r4 = r23
            goto L14
        L51:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: a.f.a.i.a.a.<init>(int[], double[], double[][]):void");
    }

    @Override // a.f.a.i.a.b
    public double b(double d2, int i) {
        C0002a[] c0002aArr = this.f132b;
        int i2 = 0;
        if (d2 < c0002aArr[0].f135c) {
            double d3 = c0002aArr[0].f135c;
            double d4 = d2 - c0002aArr[0].f135c;
            if (c0002aArr[0].r) {
                if (i == 0) {
                    return (d4 * this.f132b[0].l) + c0002aArr[0].c(d3);
                }
                return (d4 * this.f132b[0].m) + c0002aArr[0].d(d3);
            }
            c0002aArr[0].g(d3);
            if (i == 0) {
                return (d4 * this.f132b[0].a()) + this.f132b[0].e();
            }
            return (d4 * this.f132b[0].b()) + this.f132b[0].f();
        }
        if (d2 > c0002aArr[c0002aArr.length - 1].f136d) {
            double d5 = c0002aArr[c0002aArr.length - 1].f136d;
            double d6 = d2 - d5;
            int length = c0002aArr.length - 1;
            if (i == 0) {
                return (d6 * this.f132b[length].l) + c0002aArr[length].c(d5);
            }
            return (d6 * this.f132b[length].m) + c0002aArr[length].d(d5);
        }
        while (true) {
            C0002a[] c0002aArr2 = this.f132b;
            if (i2 >= c0002aArr2.length) {
                return Double.NaN;
            }
            if (d2 <= c0002aArr2[i2].f136d) {
                if (c0002aArr2[i2].r) {
                    return i == 0 ? c0002aArr2[i2].c(d2) : c0002aArr2[i2].d(d2);
                }
                c0002aArr2[i2].g(d2);
                C0002a[] c0002aArr3 = this.f132b;
                return i == 0 ? c0002aArr3[i2].e() : c0002aArr3[i2].f();
            }
            i2++;
        }
    }

    @Override // a.f.a.i.a.b
    public void c(double d2, double[] dArr) {
        C0002a[] c0002aArr = this.f132b;
        if (d2 < c0002aArr[0].f135c) {
            double d3 = c0002aArr[0].f135c;
            double d4 = d2 - c0002aArr[0].f135c;
            if (c0002aArr[0].r) {
                double c2 = c0002aArr[0].c(d3);
                C0002a[] c0002aArr2 = this.f132b;
                dArr[0] = (c0002aArr2[0].l * d4) + c2;
                dArr[1] = (d4 * this.f132b[0].m) + c0002aArr2[0].d(d3);
                return;
            }
            c0002aArr[0].g(d3);
            dArr[0] = (this.f132b[0].a() * d4) + this.f132b[0].e();
            dArr[1] = (d4 * this.f132b[0].b()) + this.f132b[0].f();
            return;
        }
        if (d2 > c0002aArr[c0002aArr.length - 1].f136d) {
            double d5 = c0002aArr[c0002aArr.length - 1].f136d;
            double d6 = d2 - d5;
            int length = c0002aArr.length - 1;
            if (c0002aArr[length].r) {
                double c3 = c0002aArr[length].c(d5);
                C0002a[] c0002aArr3 = this.f132b;
                dArr[0] = (c0002aArr3[length].l * d6) + c3;
                dArr[1] = (d6 * this.f132b[length].m) + c0002aArr3[length].d(d5);
                return;
            }
            c0002aArr[length].g(d2);
            dArr[0] = (this.f132b[length].a() * d6) + this.f132b[length].e();
            dArr[1] = (d6 * this.f132b[length].b()) + this.f132b[length].f();
            return;
        }
        int i = 0;
        while (true) {
            C0002a[] c0002aArr4 = this.f132b;
            if (i >= c0002aArr4.length) {
                return;
            }
            if (d2 <= c0002aArr4[i].f136d) {
                if (c0002aArr4[i].r) {
                    dArr[0] = c0002aArr4[i].c(d2);
                    dArr[1] = this.f132b[i].d(d2);
                    return;
                } else {
                    c0002aArr4[i].g(d2);
                    dArr[0] = this.f132b[i].e();
                    dArr[1] = this.f132b[i].f();
                    return;
                }
            }
            i++;
        }
    }

    @Override // a.f.a.i.a.b
    public void d(double d2, float[] fArr) {
        C0002a[] c0002aArr = this.f132b;
        if (d2 < c0002aArr[0].f135c) {
            double d3 = c0002aArr[0].f135c;
            double d4 = d2 - c0002aArr[0].f135c;
            if (c0002aArr[0].r) {
                double c2 = c0002aArr[0].c(d3);
                C0002a[] c0002aArr2 = this.f132b;
                fArr[0] = (float) ((c0002aArr2[0].l * d4) + c2);
                fArr[1] = (float) ((d4 * this.f132b[0].m) + c0002aArr2[0].d(d3));
                return;
            }
            c0002aArr[0].g(d3);
            fArr[0] = (float) ((this.f132b[0].a() * d4) + this.f132b[0].e());
            fArr[1] = (float) ((d4 * this.f132b[0].b()) + this.f132b[0].f());
            return;
        }
        if (d2 > c0002aArr[c0002aArr.length - 1].f136d) {
            double d5 = c0002aArr[c0002aArr.length - 1].f136d;
            double d6 = d2 - d5;
            int length = c0002aArr.length - 1;
            if (!c0002aArr[length].r) {
                c0002aArr[length].g(d2);
                fArr[0] = (float) this.f132b[length].e();
                fArr[1] = (float) this.f132b[length].f();
                return;
            } else {
                double c3 = c0002aArr[length].c(d5);
                C0002a[] c0002aArr3 = this.f132b;
                fArr[0] = (float) ((c0002aArr3[length].l * d6) + c3);
                fArr[1] = (float) ((d6 * this.f132b[length].m) + c0002aArr3[length].d(d5));
                return;
            }
        }
        int i = 0;
        while (true) {
            C0002a[] c0002aArr4 = this.f132b;
            if (i >= c0002aArr4.length) {
                return;
            }
            if (d2 <= c0002aArr4[i].f136d) {
                if (c0002aArr4[i].r) {
                    fArr[0] = (float) c0002aArr4[i].c(d2);
                    fArr[1] = (float) this.f132b[i].d(d2);
                    return;
                } else {
                    c0002aArr4[i].g(d2);
                    fArr[0] = (float) this.f132b[i].e();
                    fArr[1] = (float) this.f132b[i].f();
                    return;
                }
            }
            i++;
        }
    }

    @Override // a.f.a.i.a.b
    public double e(double d2, int i) {
        C0002a[] c0002aArr = this.f132b;
        int i2 = 0;
        if (d2 < c0002aArr[0].f135c) {
            d2 = c0002aArr[0].f135c;
        }
        if (d2 > c0002aArr[c0002aArr.length - 1].f136d) {
            d2 = c0002aArr[c0002aArr.length - 1].f136d;
        }
        while (true) {
            C0002a[] c0002aArr2 = this.f132b;
            if (i2 >= c0002aArr2.length) {
                return Double.NaN;
            }
            if (d2 <= c0002aArr2[i2].f136d) {
                if (c0002aArr2[i2].r) {
                    return i == 0 ? c0002aArr2[i2].l : c0002aArr2[i2].m;
                }
                c0002aArr2[i2].g(d2);
                C0002a[] c0002aArr3 = this.f132b;
                return i == 0 ? c0002aArr3[i2].a() : c0002aArr3[i2].b();
            }
            i2++;
        }
    }

    @Override // a.f.a.i.a.b
    public void f(double d2, double[] dArr) {
        C0002a[] c0002aArr = this.f132b;
        if (d2 < c0002aArr[0].f135c) {
            d2 = c0002aArr[0].f135c;
        } else if (d2 > c0002aArr[c0002aArr.length - 1].f136d) {
            d2 = c0002aArr[c0002aArr.length - 1].f136d;
        }
        int i = 0;
        while (true) {
            C0002a[] c0002aArr2 = this.f132b;
            if (i >= c0002aArr2.length) {
                return;
            }
            if (d2 <= c0002aArr2[i].f136d) {
                if (c0002aArr2[i].r) {
                    dArr[0] = c0002aArr2[i].l;
                    dArr[1] = c0002aArr2[i].m;
                    return;
                } else {
                    c0002aArr2[i].g(d2);
                    dArr[0] = this.f132b[i].a();
                    dArr[1] = this.f132b[i].b();
                    return;
                }
            }
            i++;
        }
    }

    @Override // a.f.a.i.a.b
    public double[] g() {
        return this.f131a;
    }
}
