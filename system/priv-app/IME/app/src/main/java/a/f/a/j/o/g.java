package a.f.a.j.o;

import a.f.a.j.o.f;

/* loaded from: classes.dex */
class g extends f {
    public int m;

    public g(p pVar) {
        super(pVar);
        this.e = pVar instanceof l ? f.a.f230b : f.a.f231c;
    }

    @Override // a.f.a.j.o.f
    public void c(int i) {
        if (this.j) {
            return;
        }
        this.j = true;
        this.g = i;
        for (d dVar : this.k) {
            dVar.a(dVar);
        }
    }
}
