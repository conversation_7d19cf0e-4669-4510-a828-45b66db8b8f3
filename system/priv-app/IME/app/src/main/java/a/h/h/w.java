package a.h.h;

import android.graphics.Insets;
import android.view.View;
import android.view.WindowInsets;
import java.util.Objects;

/* loaded from: classes.dex */
public class w {

    /* renamed from: b, reason: collision with root package name */
    public static final w f393b = i.i;

    /* renamed from: a, reason: collision with root package name */
    private final j f394a;

    public static final class a {

        /* renamed from: a, reason: collision with root package name */
        private final d f395a;

        public a() {
            this.f395a = new c();
        }

        public a(w wVar) {
            this.f395a = new c(wVar);
        }

        public w a() {
            return this.f395a.a();
        }

        @Deprecated
        public a b(a.h.c.b bVar) {
            this.f395a.b(bVar);
            return this;
        }

        @Deprecated
        public a c(a.h.c.b bVar) {
            this.f395a.c(bVar);
            return this;
        }
    }

    private static class b extends d {

        /* renamed from: b, reason: collision with root package name */
        final WindowInsets.Builder f396b;

        b() {
            this.f396b = new WindowInsets.Builder();
        }

        b(w wVar) {
            WindowInsets p = wVar.p();
            this.f396b = p != null ? new WindowInsets.Builder(p) : new WindowInsets.Builder();
        }

        @Override // a.h.h.w.d
        w a() {
            w q = w.q(this.f396b.build());
            q.n(null);
            return q;
        }

        @Override // a.h.h.w.d
        void b(a.h.c.b bVar) {
            this.f396b.setStableInsets(bVar.b());
        }

        @Override // a.h.h.w.d
        void c(a.h.c.b bVar) {
            this.f396b.setSystemWindowInsets(bVar.b());
        }
    }

    private static class c extends b {
        c() {
        }

        c(w wVar) {
            super(wVar);
        }
    }

    private static class d {

        /* renamed from: a, reason: collision with root package name */
        private final w f397a = new w((w) null);

        d() {
        }

        w a() {
            throw null;
        }

        void b(a.h.c.b bVar) {
            throw null;
        }

        void c(a.h.c.b bVar) {
            throw null;
        }
    }

    private static class e extends j {

        /* renamed from: c, reason: collision with root package name */
        final WindowInsets f398c;

        /* renamed from: d, reason: collision with root package name */
        private a.h.c.b f399d;
        private w e;
        a.h.c.b f;

        e(w wVar, WindowInsets windowInsets) {
            super(wVar);
            this.f399d = null;
            this.f398c = windowInsets;
        }

        @Override // a.h.h.w.j
        void d(View view) {
            throw new UnsupportedOperationException("getVisibleInsets() should not be called on API >= 30. Use WindowInsets.isVisible() instead.");
        }

        @Override // a.h.h.w.j
        public boolean equals(Object obj) {
            if (super.equals(obj)) {
                return Objects.equals(this.f, ((e) obj).f);
            }
            return false;
        }

        @Override // a.h.h.w.j
        final a.h.c.b h() {
            if (this.f399d == null) {
                this.f399d = a.h.c.b.a(this.f398c.getSystemWindowInsetLeft(), this.f398c.getSystemWindowInsetTop(), this.f398c.getSystemWindowInsetRight(), this.f398c.getSystemWindowInsetBottom());
            }
            return this.f399d;
        }

        @Override // a.h.h.w.j
        w i(int i, int i2, int i3, int i4) {
            a aVar = new a(w.q(this.f398c));
            aVar.c(w.l(h(), i, i2, i3, i4));
            aVar.b(w.l(g(), i, i2, i3, i4));
            return aVar.a();
        }

        @Override // a.h.h.w.j
        boolean k() {
            return this.f398c.isRound();
        }

        @Override // a.h.h.w.j
        public void l(a.h.c.b[] bVarArr) {
        }

        @Override // a.h.h.w.j
        void m(w wVar) {
            this.e = wVar;
        }
    }

    private static class f extends e {
        private a.h.c.b g;

        f(w wVar, WindowInsets windowInsets) {
            super(wVar, windowInsets);
            this.g = null;
        }

        @Override // a.h.h.w.j
        w b() {
            return w.q(this.f398c.consumeStableInsets());
        }

        @Override // a.h.h.w.j
        w c() {
            return w.q(this.f398c.consumeSystemWindowInsets());
        }

        @Override // a.h.h.w.j
        final a.h.c.b g() {
            if (this.g == null) {
                this.g = a.h.c.b.a(this.f398c.getStableInsetLeft(), this.f398c.getStableInsetTop(), this.f398c.getStableInsetRight(), this.f398c.getStableInsetBottom());
            }
            return this.g;
        }

        @Override // a.h.h.w.j
        boolean j() {
            return this.f398c.isConsumed();
        }
    }

    private static class g extends f {
        g(w wVar, WindowInsets windowInsets) {
            super(wVar, windowInsets);
        }

        @Override // a.h.h.w.j
        w a() {
            return w.q(this.f398c.consumeDisplayCutout());
        }

        @Override // a.h.h.w.j
        a.h.h.c e() {
            return a.h.h.c.a(this.f398c.getDisplayCutout());
        }

        @Override // a.h.h.w.e, a.h.h.w.j
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (!(obj instanceof g)) {
                return false;
            }
            g gVar = (g) obj;
            return Objects.equals(this.f398c, gVar.f398c) && Objects.equals(this.f, gVar.f);
        }

        @Override // a.h.h.w.j
        public int hashCode() {
            return this.f398c.hashCode();
        }
    }

    private static class h extends g {
        private a.h.c.b h;

        h(w wVar, WindowInsets windowInsets) {
            super(wVar, windowInsets);
            this.h = null;
        }

        @Override // a.h.h.w.j
        a.h.c.b f() {
            if (this.h == null) {
                Insets mandatorySystemGestureInsets = this.f398c.getMandatorySystemGestureInsets();
                this.h = a.h.c.b.a(mandatorySystemGestureInsets.left, mandatorySystemGestureInsets.top, mandatorySystemGestureInsets.right, mandatorySystemGestureInsets.bottom);
            }
            return this.h;
        }

        @Override // a.h.h.w.e, a.h.h.w.j
        w i(int i, int i2, int i3, int i4) {
            return w.q(this.f398c.inset(i, i2, i3, i4));
        }
    }

    private static class i extends h {
        static final w i = w.q(WindowInsets.CONSUMED);

        i(w wVar, WindowInsets windowInsets) {
            super(wVar, windowInsets);
        }

        @Override // a.h.h.w.e, a.h.h.w.j
        final void d(View view) {
        }
    }

    private static class j {

        /* renamed from: b, reason: collision with root package name */
        static final w f400b = new a().a().a().b().c();

        /* renamed from: a, reason: collision with root package name */
        final w f401a;

        j(w wVar) {
            this.f401a = wVar;
        }

        w a() {
            return this.f401a;
        }

        w b() {
            return this.f401a;
        }

        w c() {
            return this.f401a;
        }

        void d(View view) {
        }

        a.h.h.c e() {
            return null;
        }

        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (!(obj instanceof j)) {
                return false;
            }
            j jVar = (j) obj;
            return k() == jVar.k() && j() == jVar.j() && Objects.equals(h(), jVar.h()) && Objects.equals(g(), jVar.g()) && Objects.equals(e(), jVar.e());
        }

        a.h.c.b f() {
            return h();
        }

        a.h.c.b g() {
            return a.h.c.b.e;
        }

        a.h.c.b h() {
            return a.h.c.b.e;
        }

        public int hashCode() {
            return Objects.hash(Boolean.valueOf(k()), Boolean.valueOf(j()), h(), g(), e());
        }

        w i(int i, int i2, int i3, int i4) {
            return f400b;
        }

        boolean j() {
            return false;
        }

        boolean k() {
            return false;
        }

        public void l(a.h.c.b[] bVarArr) {
        }

        void m(w wVar) {
        }
    }

    public w(w wVar) {
        this.f394a = new j(this);
    }

    private w(WindowInsets windowInsets) {
        this.f394a = new i(this, windowInsets);
    }

    static a.h.c.b l(a.h.c.b bVar, int i2, int i3, int i4, int i5) {
        int max = Math.max(0, bVar.f279a - i2);
        int max2 = Math.max(0, bVar.f280b - i3);
        int max3 = Math.max(0, bVar.f281c - i4);
        int max4 = Math.max(0, bVar.f282d - i5);
        return (max == i2 && max2 == i3 && max3 == i4 && max4 == i5) ? bVar : a.h.c.b.a(max, max2, max3, max4);
    }

    public static w q(WindowInsets windowInsets) {
        return r(windowInsets, null);
    }

    public static w r(WindowInsets windowInsets, View view) {
        Objects.requireNonNull(windowInsets);
        w wVar = new w(windowInsets);
        if (view != null && view.isAttachedToWindow()) {
            wVar.f394a.m(q.h(view));
            wVar.f394a.d(view.getRootView());
        }
        return wVar;
    }

    @Deprecated
    public w a() {
        return this.f394a.a();
    }

    @Deprecated
    public w b() {
        return this.f394a.b();
    }

    @Deprecated
    public w c() {
        return this.f394a.c();
    }

    void d(View view) {
        this.f394a.d(view);
    }

    @Deprecated
    public a.h.c.b e() {
        return this.f394a.f();
    }

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj instanceof w) {
            return Objects.equals(this.f394a, ((w) obj).f394a);
        }
        return false;
    }

    @Deprecated
    public int f() {
        return this.f394a.h().f282d;
    }

    @Deprecated
    public int g() {
        return this.f394a.h().f279a;
    }

    @Deprecated
    public int h() {
        return this.f394a.h().f281c;
    }

    public int hashCode() {
        j jVar = this.f394a;
        if (jVar == null) {
            return 0;
        }
        return jVar.hashCode();
    }

    @Deprecated
    public int i() {
        return this.f394a.h().f280b;
    }

    @Deprecated
    public boolean j() {
        return !this.f394a.h().equals(a.h.c.b.e);
    }

    public w k(int i2, int i3, int i4, int i5) {
        return this.f394a.i(i2, i3, i4, i5);
    }

    public boolean m() {
        return this.f394a.j();
    }

    void n(a.h.c.b[] bVarArr) {
        this.f394a.l(null);
    }

    void o(w wVar) {
        this.f394a.m(wVar);
    }

    public WindowInsets p() {
        j jVar = this.f394a;
        if (jVar instanceof e) {
            return ((e) jVar).f398c;
        }
        return null;
    }
}
