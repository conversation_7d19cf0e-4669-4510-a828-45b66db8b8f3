package a.o;

import android.graphics.Rect;
import android.util.Property;
import android.view.View;

/* loaded from: classes.dex */
class s {

    /* renamed from: a, reason: collision with root package name */
    private static final y f520a = new x();

    /* renamed from: b, reason: collision with root package name */
    static final Property<View, Float> f521b = new a(Float.class, "translationAlpha");

    static class a extends Property<View, Float> {
        a(Class cls, String str) {
            super(cls, str);
        }

        @Override // android.util.Property
        public Float get(View view) {
            return Float.valueOf(view.getTransitionAlpha());
        }

        @Override // android.util.Property
        public void set(View view, Float f) {
            view.setTransitionAlpha(f.floatValue());
        }
    }

    static class b extends Property<View, Rect> {
        b(Class cls, String str) {
            super(cls, str);
        }

        @Override // android.util.Property
        public Rect get(View view) {
            int i = a.h.h.q.e;
            return view.getClipBounds();
        }

        @Override // android.util.Property
        public void set(View view, Rect rect) {
            int i = a.h.h.q.e;
            view.setClipBounds(rect);
        }
    }

    static {
        new b(Rect.class, "clipBounds");
    }
}
