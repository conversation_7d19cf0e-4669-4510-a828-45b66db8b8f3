package a.f.a.j.o;

import a.f.a.j.e;
import a.f.a.j.o.b;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;

/* loaded from: classes.dex */
public class e {

    /* renamed from: a, reason: collision with root package name */
    private a.f.a.j.f f221a;

    /* renamed from: d, reason: collision with root package name */
    private a.f.a.j.f f224d;
    private b.InterfaceC0003b f;
    private b.a g;
    ArrayList<m> h;

    /* renamed from: b, reason: collision with root package name */
    private boolean f222b = true;

    /* renamed from: c, reason: collision with root package name */
    private boolean f223c = true;
    private ArrayList<p> e = new ArrayList<>();

    public e(a.f.a.j.f fVar) {
        new ArrayList();
        this.f = null;
        this.g = new b.a();
        this.h = new ArrayList<>();
        this.f221a = fVar;
        this.f224d = fVar;
    }

    private void a(f fVar, int i, int i2, f fVar2, ArrayList<m> arrayList, m mVar) {
        p pVar = fVar.f228d;
        if (pVar.f245c == null) {
            a.f.a.j.f fVar3 = this.f221a;
            if (pVar == fVar3.f204d || pVar == fVar3.e) {
                return;
            }
            if (mVar == null) {
                mVar = new m(pVar, i2);
                arrayList.add(mVar);
            }
            pVar.f245c = mVar;
            mVar.f238b.add(pVar);
            for (d dVar : pVar.h.k) {
                if (dVar instanceof f) {
                    a((f) dVar, i, 0, fVar2, arrayList, mVar);
                }
            }
            for (d dVar2 : pVar.i.k) {
                if (dVar2 instanceof f) {
                    a((f) dVar2, i, 1, fVar2, arrayList, mVar);
                }
            }
            if (i == 1 && (pVar instanceof n)) {
                for (d dVar3 : ((n) pVar).k.k) {
                    if (dVar3 instanceof f) {
                        a((f) dVar3, i, 2, fVar2, arrayList, mVar);
                    }
                }
            }
            Iterator<f> it = pVar.h.l.iterator();
            while (it.hasNext()) {
                a(it.next(), i, 0, fVar2, arrayList, mVar);
            }
            Iterator<f> it2 = pVar.i.l.iterator();
            while (it2.hasNext()) {
                a(it2.next(), i, 1, fVar2, arrayList, mVar);
            }
            if (i == 1 && (pVar instanceof n)) {
                Iterator<f> it3 = ((n) pVar).k.l.iterator();
                while (it3.hasNext()) {
                    a(it3.next(), i, 2, fVar2, arrayList, mVar);
                }
            }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:30:0x006b, code lost:
    
        if (r15.s == 0) goto L25;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private boolean b(a.f.a.j.f r20) {
        /*
            Method dump skipped, instructions count: 698
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: a.f.a.j.o.e.b(a.f.a.j.f):boolean");
    }

    private int d(a.f.a.j.f fVar, int i) {
        int size = this.h.size();
        long j = 0;
        for (int i2 = 0; i2 < size; i2++) {
            j = Math.max(j, this.h.get(i2).a(fVar, i));
        }
        return (int) j;
    }

    private void h(p pVar, int i, ArrayList<m> arrayList) {
        for (d dVar : pVar.h.k) {
            if (dVar instanceof f) {
                a((f) dVar, i, 0, pVar.i, arrayList, null);
            } else if (dVar instanceof p) {
                a(((p) dVar).h, i, 0, pVar.i, arrayList, null);
            }
        }
        for (d dVar2 : pVar.i.k) {
            if (dVar2 instanceof f) {
                a((f) dVar2, i, 1, pVar.h, arrayList, null);
            } else if (dVar2 instanceof p) {
                a(((p) dVar2).i, i, 1, pVar.h, arrayList, null);
            }
        }
        if (i == 1) {
            for (d dVar3 : ((n) pVar).k.k) {
                if (dVar3 instanceof f) {
                    a((f) dVar3, i, 2, null, arrayList, null);
                }
            }
        }
    }

    private void k(a.f.a.j.e eVar, e.a aVar, int i, e.a aVar2, int i2) {
        b.a aVar3 = this.g;
        aVar3.f217a = aVar;
        aVar3.f218b = aVar2;
        aVar3.f219c = i;
        aVar3.f220d = i2;
        this.f.b(eVar, aVar3);
        eVar.S0(this.g.e);
        eVar.A0(this.g.f);
        eVar.z0(this.g.h);
        eVar.q0(this.g.g);
    }

    public void c() {
        p jVar;
        ArrayList<p> arrayList = this.e;
        arrayList.clear();
        this.f224d.f204d.f();
        this.f224d.e.f();
        arrayList.add(this.f224d.f204d);
        arrayList.add(this.f224d.e);
        Iterator<a.f.a.j.e> it = this.f224d.N0.iterator();
        HashSet hashSet = null;
        while (it.hasNext()) {
            a.f.a.j.e next = it.next();
            if (next instanceof a.f.a.j.h) {
                jVar = new j(next);
            } else {
                if (next.a0()) {
                    if (next.f202b == null) {
                        next.f202b = new c(next, 0);
                    }
                    if (hashSet == null) {
                        hashSet = new HashSet();
                    }
                    hashSet.add(next.f202b);
                } else {
                    arrayList.add(next.f204d);
                }
                if (next.c0()) {
                    if (next.f203c == null) {
                        next.f203c = new c(next, 1);
                    }
                    if (hashSet == null) {
                        hashSet = new HashSet();
                    }
                    hashSet.add(next.f203c);
                } else {
                    arrayList.add(next.e);
                }
                if (next instanceof a.f.a.j.j) {
                    jVar = new k(next);
                }
            }
            arrayList.add(jVar);
        }
        if (hashSet != null) {
            arrayList.addAll(hashSet);
        }
        Iterator<p> it2 = arrayList.iterator();
        while (it2.hasNext()) {
            it2.next().f();
        }
        Iterator<p> it3 = arrayList.iterator();
        while (it3.hasNext()) {
            p next2 = it3.next();
            if (next2.f244b != this.f224d) {
                next2.d();
            }
        }
        this.h.clear();
        m.f236c = 0;
        h(this.f221a.f204d, 0, this.h);
        h(this.f221a.e, 1, this.h);
        this.f222b = false;
    }

    public boolean e(boolean z) {
        boolean z2;
        e.a aVar = e.a.MATCH_PARENT;
        e.a aVar2 = e.a.FIXED;
        e.a aVar3 = e.a.WRAP_CONTENT;
        boolean z3 = true;
        boolean z4 = z & true;
        if (this.f222b || this.f223c) {
            Iterator<a.f.a.j.e> it = this.f221a.N0.iterator();
            while (it.hasNext()) {
                a.f.a.j.e next = it.next();
                next.n();
                next.f201a = false;
                next.f204d.o();
                next.e.n();
            }
            this.f221a.n();
            a.f.a.j.f fVar = this.f221a;
            fVar.f201a = false;
            fVar.f204d.o();
            this.f221a.e.n();
            this.f223c = false;
        }
        b(this.f224d);
        this.f221a.U0(0);
        this.f221a.V0(0);
        e.a u = this.f221a.u(0);
        e.a u2 = this.f221a.u(1);
        if (this.f222b) {
            c();
        }
        int R = this.f221a.R();
        int S = this.f221a.S();
        this.f221a.f204d.h.c(R);
        this.f221a.e.h.c(S);
        l();
        if (u == aVar3 || u2 == aVar3) {
            if (z4) {
                Iterator<p> it2 = this.e.iterator();
                while (true) {
                    if (!it2.hasNext()) {
                        break;
                    }
                    if (!it2.next().l()) {
                        z4 = false;
                        break;
                    }
                }
            }
            if (z4 && u == aVar3) {
                a.f.a.j.f fVar2 = this.f221a;
                fVar2.W[0] = aVar2;
                fVar2.S0(d(fVar2, 0));
                a.f.a.j.f fVar3 = this.f221a;
                fVar3.f204d.e.c(fVar3.Q());
            }
            if (z4 && u2 == aVar3) {
                a.f.a.j.f fVar4 = this.f221a;
                fVar4.W[1] = aVar2;
                fVar4.A0(d(fVar4, 1));
                a.f.a.j.f fVar5 = this.f221a;
                fVar5.e.e.c(fVar5.w());
            }
        }
        a.f.a.j.f fVar6 = this.f221a;
        e.a[] aVarArr = fVar6.W;
        if (aVarArr[0] == aVar2 || aVarArr[0] == aVar) {
            int Q = fVar6.Q() + R;
            this.f221a.f204d.i.c(Q);
            this.f221a.f204d.e.c(Q - R);
            l();
            a.f.a.j.f fVar7 = this.f221a;
            e.a[] aVarArr2 = fVar7.W;
            if (aVarArr2[1] == aVar2 || aVarArr2[1] == aVar) {
                int w = fVar7.w() + S;
                this.f221a.e.i.c(w);
                this.f221a.e.e.c(w - S);
            }
            l();
            z2 = true;
        } else {
            z2 = false;
        }
        Iterator<p> it3 = this.e.iterator();
        while (it3.hasNext()) {
            p next2 = it3.next();
            if (next2.f244b != this.f221a || next2.g) {
                next2.e();
            }
        }
        Iterator<p> it4 = this.e.iterator();
        while (it4.hasNext()) {
            p next3 = it4.next();
            if (z2 || next3.f244b != this.f221a) {
                if (!next3.h.j || ((!next3.i.j && !(next3 instanceof j)) || (!next3.e.j && !(next3 instanceof c) && !(next3 instanceof j)))) {
                    z3 = false;
                    break;
                }
            }
        }
        this.f221a.D0(u);
        this.f221a.Q0(u2);
        return z3;
    }

    public boolean f() {
        if (this.f222b) {
            Iterator<a.f.a.j.e> it = this.f221a.N0.iterator();
            while (it.hasNext()) {
                a.f.a.j.e next = it.next();
                next.n();
                next.f201a = false;
                l lVar = next.f204d;
                lVar.e.j = false;
                lVar.g = false;
                lVar.o();
                n nVar = next.e;
                nVar.e.j = false;
                nVar.g = false;
                nVar.n();
            }
            this.f221a.n();
            a.f.a.j.f fVar = this.f221a;
            fVar.f201a = false;
            l lVar2 = fVar.f204d;
            lVar2.e.j = false;
            lVar2.g = false;
            lVar2.o();
            n nVar2 = this.f221a.e;
            nVar2.e.j = false;
            nVar2.g = false;
            nVar2.n();
            c();
        }
        b(this.f224d);
        this.f221a.U0(0);
        this.f221a.V0(0);
        this.f221a.f204d.h.c(0);
        this.f221a.e.h.c(0);
        return true;
    }

    public boolean g(boolean z, int i) {
        g gVar;
        int i2;
        boolean z2;
        g gVar2;
        int w;
        e.a aVar = e.a.MATCH_PARENT;
        e.a aVar2 = e.a.WRAP_CONTENT;
        e.a aVar3 = e.a.FIXED;
        boolean z3 = true;
        boolean z4 = z & true;
        e.a u = this.f221a.u(0);
        e.a u2 = this.f221a.u(1);
        int R = this.f221a.R();
        int S = this.f221a.S();
        if (z4 && (u == aVar2 || u2 == aVar2)) {
            Iterator<p> it = this.e.iterator();
            while (true) {
                if (!it.hasNext()) {
                    break;
                }
                p next = it.next();
                if (next.f == i && !next.l()) {
                    z4 = false;
                    break;
                }
            }
            if (i == 0) {
                if (z4 && u == aVar2) {
                    a.f.a.j.f fVar = this.f221a;
                    fVar.W[0] = aVar3;
                    fVar.S0(d(fVar, 0));
                    a.f.a.j.f fVar2 = this.f221a;
                    gVar2 = fVar2.f204d.e;
                    w = fVar2.Q();
                    gVar2.c(w);
                }
            } else if (z4 && u2 == aVar2) {
                a.f.a.j.f fVar3 = this.f221a;
                fVar3.W[1] = aVar3;
                fVar3.A0(d(fVar3, 1));
                a.f.a.j.f fVar4 = this.f221a;
                gVar2 = fVar4.e.e;
                w = fVar4.w();
                gVar2.c(w);
            }
        }
        a.f.a.j.f fVar5 = this.f221a;
        e.a[] aVarArr = fVar5.W;
        if (i == 0) {
            if (aVarArr[0] == aVar3 || aVarArr[0] == aVar) {
                int Q = fVar5.Q() + R;
                this.f221a.f204d.i.c(Q);
                gVar = this.f221a.f204d.e;
                i2 = Q - R;
                gVar.c(i2);
                z2 = true;
            }
            z2 = false;
        } else {
            if (aVarArr[1] == aVar3 || aVarArr[1] == aVar) {
                int w2 = fVar5.w() + S;
                this.f221a.e.i.c(w2);
                gVar = this.f221a.e.e;
                i2 = w2 - S;
                gVar.c(i2);
                z2 = true;
            }
            z2 = false;
        }
        l();
        Iterator<p> it2 = this.e.iterator();
        while (it2.hasNext()) {
            p next2 = it2.next();
            if (next2.f == i && (next2.f244b != this.f221a || next2.g)) {
                next2.e();
            }
        }
        Iterator<p> it3 = this.e.iterator();
        while (it3.hasNext()) {
            p next3 = it3.next();
            if (next3.f == i && (z2 || next3.f244b != this.f221a)) {
                if (!next3.h.j || !next3.i.j || (!(next3 instanceof c) && !next3.e.j)) {
                    z3 = false;
                    break;
                }
            }
        }
        this.f221a.D0(u);
        this.f221a.Q0(u2);
        return z3;
    }

    public void i() {
        this.f222b = true;
    }

    public void j() {
        this.f223c = true;
    }

    /* JADX WARN: Code restructure failed: missing block: B:34:0x0071, code lost:
    
        if (r12 == r7) goto L28;
     */
    /* JADX WARN: Code restructure failed: missing block: B:35:0x0073, code lost:
    
        r0.m = r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:36:0x0076, code lost:
    
        r0.c(r1);
     */
    /* JADX WARN: Code restructure failed: missing block: B:40:0x0091, code lost:
    
        if (r10 == r7) goto L28;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void l() {
        /*
            r14 = this;
            a.f.a.j.e$a r6 = a.f.a.j.e.a.FIXED
            a.f.a.j.e$a r7 = a.f.a.j.e.a.MATCH_CONSTRAINT
            a.f.a.j.f r0 = r14.f221a
            java.util.ArrayList<a.f.a.j.e> r0 = r0.N0
            java.util.Iterator r8 = r0.iterator()
        Lc:
            boolean r0 = r8.hasNext()
            if (r0 == 0) goto La7
            java.lang.Object r0 = r8.next()
            r9 = r0
            a.f.a.j.e r9 = (a.f.a.j.e) r9
            boolean r0 = r9.f201a
            if (r0 == 0) goto L1e
            goto Lc
        L1e:
            a.f.a.j.e$a[] r0 = r9.W
            r1 = 0
            r10 = r0[r1]
            r11 = 1
            r12 = r0[r11]
            int r0 = r9.r
            int r2 = r9.s
            a.f.a.j.e$a r4 = a.f.a.j.e.a.WRAP_CONTENT
            if (r10 == r4) goto L35
            if (r10 != r7) goto L33
            if (r0 != r11) goto L33
            goto L35
        L33:
            r0 = r1
            goto L36
        L35:
            r0 = r11
        L36:
            if (r12 == r4) goto L3c
            if (r12 != r7) goto L3d
            if (r2 != r11) goto L3d
        L3c:
            r1 = r11
        L3d:
            a.f.a.j.o.l r2 = r9.f204d
            a.f.a.j.o.g r2 = r2.e
            boolean r3 = r2.j
            a.f.a.j.o.n r5 = r9.e
            a.f.a.j.o.g r5 = r5.e
            boolean r13 = r5.j
            if (r3 == 0) goto L5b
            if (r13 == 0) goto L5b
            int r3 = r2.g
            int r5 = r5.g
            r0 = r14
            r1 = r9
            r2 = r6
            r4 = r6
            r0.k(r1, r2, r3, r4, r5)
        L58:
            r9.f201a = r11
            goto L94
        L5b:
            if (r3 == 0) goto L7a
            if (r1 == 0) goto L7a
            int r3 = r2.g
            int r5 = r5.g
            r0 = r14
            r1 = r9
            r2 = r6
            r0.k(r1, r2, r3, r4, r5)
            a.f.a.j.o.n r0 = r9.e
            a.f.a.j.o.g r0 = r0.e
            int r1 = r9.w()
            if (r12 != r7) goto L76
        L73:
            r0.m = r1
            goto L94
        L76:
            r0.c(r1)
            goto L58
        L7a:
            if (r13 == 0) goto L94
            if (r0 == 0) goto L94
            int r3 = r2.g
            int r5 = r5.g
            r0 = r14
            r1 = r9
            r2 = r4
            r4 = r6
            r0.k(r1, r2, r3, r4, r5)
            a.f.a.j.o.l r0 = r9.f204d
            a.f.a.j.o.g r0 = r0.e
            int r1 = r9.Q()
            if (r10 != r7) goto L76
            goto L73
        L94:
            boolean r0 = r9.f201a
            if (r0 == 0) goto Lc
            a.f.a.j.o.n r0 = r9.e
            a.f.a.j.o.g r0 = r0.l
            if (r0 == 0) goto Lc
            int r1 = r9.p()
            r0.c(r1)
            goto Lc
        La7:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: a.f.a.j.o.e.l():void");
    }

    public void m(b.InterfaceC0003b interfaceC0003b) {
        this.f = interfaceC0003b;
    }
}
