package androidx.appcompat.app;

import a.b.f.b;
import a.h.h.q;
import a.h.h.s;
import a.h.h.t;
import a.h.h.u;
import a.h.h.v;
import android.R;
import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.res.Configuration;
import android.content.res.TypedArray;
import android.util.TypedValue;
import android.view.ContextThemeWrapper;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.view.animation.Interpolator;
import androidx.appcompat.app.a;
import androidx.appcompat.view.menu.g;
import androidx.appcompat.widget.ActionBarContainer;
import androidx.appcompat.widget.ActionBarContextView;
import androidx.appcompat.widget.ActionBarOverlayLayout;
import androidx.appcompat.widget.Toolbar;
import java.lang.ref.WeakReference;
import java.util.ArrayList;

/* loaded from: classes.dex */
public class p extends androidx.appcompat.app.a implements ActionBarOverlayLayout.d {

    /* renamed from: a, reason: collision with root package name */
    Context f613a;

    /* renamed from: b, reason: collision with root package name */
    private Context f614b;

    /* renamed from: c, reason: collision with root package name */
    ActionBarOverlayLayout f615c;

    /* renamed from: d, reason: collision with root package name */
    ActionBarContainer f616d;
    androidx.appcompat.widget.p e;
    ActionBarContextView f;
    View g;
    private boolean h;
    d i;
    a.b.f.b j;
    b.a k;
    private boolean l;
    private ArrayList<a.b> m;
    private boolean n;
    private int o;
    boolean p;
    boolean q;
    private boolean r;
    private boolean s;
    a.b.f.h t;
    private boolean u;
    boolean v;
    final t w;
    final t x;
    final v y;
    private static final Interpolator z = new AccelerateInterpolator();
    private static final Interpolator A = new DecelerateInterpolator();

    class a extends u {
        a() {
        }

        @Override // a.h.h.t
        public void a(View view) {
            View view2;
            p pVar = p.this;
            if (pVar.p && (view2 = pVar.g) != null) {
                view2.setTranslationY(0.0f);
                p.this.f616d.setTranslationY(0.0f);
            }
            p.this.f616d.setVisibility(8);
            p.this.f616d.setTransitioning(false);
            p pVar2 = p.this;
            pVar2.t = null;
            b.a aVar = pVar2.k;
            if (aVar != null) {
                aVar.d(pVar2.j);
                pVar2.j = null;
                pVar2.k = null;
            }
            ActionBarOverlayLayout actionBarOverlayLayout = p.this.f615c;
            if (actionBarOverlayLayout != null) {
                int i = q.e;
                actionBarOverlayLayout.requestApplyInsets();
            }
        }
    }

    class b extends u {
        b() {
        }

        @Override // a.h.h.t
        public void a(View view) {
            p pVar = p.this;
            pVar.t = null;
            pVar.f616d.requestLayout();
        }
    }

    class c implements v {
        c() {
        }

        @Override // a.h.h.v
        public void a(View view) {
            ((View) p.this.f616d.getParent()).invalidate();
        }
    }

    public class d extends a.b.f.b implements g.a {

        /* renamed from: c, reason: collision with root package name */
        private final Context f620c;

        /* renamed from: d, reason: collision with root package name */
        private final androidx.appcompat.view.menu.g f621d;
        private b.a e;
        private WeakReference<View> f;

        public d(Context context, b.a aVar) {
            this.f620c = context;
            this.e = aVar;
            androidx.appcompat.view.menu.g gVar = new androidx.appcompat.view.menu.g(context);
            gVar.H(1);
            this.f621d = gVar;
            gVar.G(this);
        }

        @Override // androidx.appcompat.view.menu.g.a
        public boolean a(androidx.appcompat.view.menu.g gVar, MenuItem menuItem) {
            b.a aVar = this.e;
            if (aVar != null) {
                return aVar.b(this, menuItem);
            }
            return false;
        }

        @Override // androidx.appcompat.view.menu.g.a
        public void b(androidx.appcompat.view.menu.g gVar) {
            if (this.e == null) {
                return;
            }
            k();
            p.this.f.showOverflowMenu();
        }

        @Override // a.b.f.b
        public void c() {
            p pVar = p.this;
            if (pVar.i != this) {
                return;
            }
            if (!pVar.q) {
                this.e.d(this);
            } else {
                pVar.j = this;
                pVar.k = this.e;
            }
            this.e = null;
            p.this.f(false);
            p.this.f.closeMode();
            p.this.e.g().sendAccessibilityEvent(32);
            p pVar2 = p.this;
            pVar2.f615c.setHideOnContentScrollEnabled(pVar2.v);
            p.this.i = null;
        }

        @Override // a.b.f.b
        public View d() {
            WeakReference<View> weakReference = this.f;
            if (weakReference != null) {
                return weakReference.get();
            }
            return null;
        }

        @Override // a.b.f.b
        public Menu e() {
            return this.f621d;
        }

        @Override // a.b.f.b
        public MenuInflater f() {
            return new a.b.f.g(this.f620c);
        }

        @Override // a.b.f.b
        public CharSequence g() {
            return p.this.f.getSubtitle();
        }

        @Override // a.b.f.b
        public CharSequence i() {
            return p.this.f.getTitle();
        }

        @Override // a.b.f.b
        public void k() {
            if (p.this.i != this) {
                return;
            }
            this.f621d.R();
            try {
                this.e.a(this, this.f621d);
            } finally {
                this.f621d.Q();
            }
        }

        @Override // a.b.f.b
        public boolean l() {
            return p.this.f.isTitleOptional();
        }

        @Override // a.b.f.b
        public void m(View view) {
            p.this.f.setCustomView(view);
            this.f = new WeakReference<>(view);
        }

        @Override // a.b.f.b
        public void n(int i) {
            p.this.f.setSubtitle(p.this.f613a.getResources().getString(i));
        }

        @Override // a.b.f.b
        public void o(CharSequence charSequence) {
            p.this.f.setSubtitle(charSequence);
        }

        @Override // a.b.f.b
        public void q(int i) {
            p.this.f.setTitle(p.this.f613a.getResources().getString(i));
        }

        @Override // a.b.f.b
        public void r(CharSequence charSequence) {
            p.this.f.setTitle(charSequence);
        }

        @Override // a.b.f.b
        public void s(boolean z) {
            super.s(z);
            p.this.f.setTitleOptional(z);
        }

        public boolean t() {
            this.f621d.R();
            try {
                return this.e.c(this, this.f621d);
            } finally {
                this.f621d.Q();
            }
        }
    }

    public p(Activity activity, boolean z2) {
        new ArrayList();
        this.m = new ArrayList<>();
        this.o = 0;
        this.p = true;
        this.s = true;
        this.w = new a();
        this.x = new b();
        this.y = new c();
        View decorView = activity.getWindow().getDecorView();
        i(decorView);
        if (z2) {
            return;
        }
        this.g = decorView.findViewById(R.id.content);
    }

    public p(Dialog dialog) {
        new ArrayList();
        this.m = new ArrayList<>();
        this.o = 0;
        this.p = true;
        this.s = true;
        this.w = new a();
        this.x = new b();
        this.y = new c();
        i(dialog.getWindow().getDecorView());
    }

    private void i(View view) {
        androidx.appcompat.widget.p wrapper;
        ActionBarOverlayLayout actionBarOverlayLayout = (ActionBarOverlayLayout) view.findViewById(org.libpag.R.id.decor_content_parent);
        this.f615c = actionBarOverlayLayout;
        if (actionBarOverlayLayout != null) {
            actionBarOverlayLayout.setActionBarVisibilityCallback(this);
        }
        KeyEvent.Callback findViewById = view.findViewById(org.libpag.R.id.action_bar);
        if (findViewById instanceof androidx.appcompat.widget.p) {
            wrapper = (androidx.appcompat.widget.p) findViewById;
        } else {
            if (!(findViewById instanceof Toolbar)) {
                StringBuilder j = b.b.a.a.a.j("Can't make a decor toolbar out of ");
                j.append(findViewById != null ? findViewById.getClass().getSimpleName() : "null");
                throw new IllegalStateException(j.toString());
            }
            wrapper = ((Toolbar) findViewById).getWrapper();
        }
        this.e = wrapper;
        this.f = (ActionBarContextView) view.findViewById(org.libpag.R.id.action_context_bar);
        ActionBarContainer actionBarContainer = (ActionBarContainer) view.findViewById(org.libpag.R.id.action_bar_container);
        this.f616d = actionBarContainer;
        androidx.appcompat.widget.p pVar = this.e;
        if (pVar == null || this.f == null || actionBarContainer == null) {
            throw new IllegalStateException(p.class.getSimpleName() + " can only be used with a compatible window decor layout");
        }
        this.f613a = pVar.i();
        boolean z2 = (this.e.b() & 4) != 0;
        if (z2) {
            this.h = true;
        }
        a.b.f.a b2 = a.b.f.a.b(this.f613a);
        this.e.h(b2.a() || z2);
        l(b2.g());
        TypedArray obtainStyledAttributes = this.f613a.obtainStyledAttributes(null, a.b.b.f0a, org.libpag.R.attr.actionBarStyle, 0);
        if (obtainStyledAttributes.getBoolean(14, false)) {
            if (!this.f615c.isInOverlayMode()) {
                throw new IllegalStateException("Action bar must be in overlay mode (Window.FEATURE_OVERLAY_ACTION_BAR) to enable hide on content scroll");
            }
            this.v = true;
            this.f615c.setHideOnContentScrollEnabled(true);
        }
        int dimensionPixelSize = obtainStyledAttributes.getDimensionPixelSize(12, 0);
        if (dimensionPixelSize != 0) {
            ActionBarContainer actionBarContainer2 = this.f616d;
            int i = q.e;
            actionBarContainer2.setElevation(dimensionPixelSize);
        }
        obtainStyledAttributes.recycle();
    }

    private void l(boolean z2) {
        this.n = z2;
        if (z2) {
            this.f616d.setTabContainer(null);
            this.e.f(null);
        } else {
            this.e.f(null);
            this.f616d.setTabContainer(null);
        }
        boolean z3 = this.e.j() == 2;
        this.e.r(!this.n && z3);
        this.f615c.setHasNonEmbeddedTabs(!this.n && z3);
    }

    private void n(boolean z2) {
        View view;
        View view2;
        View view3;
        if (!(this.r || !this.q)) {
            if (this.s) {
                this.s = false;
                a.b.f.h hVar = this.t;
                if (hVar != null) {
                    hVar.a();
                }
                if (this.o != 0 || (!this.u && !z2)) {
                    this.w.a(null);
                    return;
                }
                this.f616d.setAlpha(1.0f);
                this.f616d.setTransitioning(true);
                a.b.f.h hVar2 = new a.b.f.h();
                float f = -this.f616d.getHeight();
                if (z2) {
                    this.f616d.getLocationInWindow(new int[]{0, 0});
                    f -= r9[1];
                }
                s c2 = q.c(this.f616d);
                c2.k(f);
                c2.i(this.y);
                hVar2.c(c2);
                if (this.p && (view = this.g) != null) {
                    s c3 = q.c(view);
                    c3.k(f);
                    hVar2.c(c3);
                }
                hVar2.f(z);
                hVar2.e(250L);
                hVar2.g(this.w);
                this.t = hVar2;
                hVar2.h();
                return;
            }
            return;
        }
        if (this.s) {
            return;
        }
        this.s = true;
        a.b.f.h hVar3 = this.t;
        if (hVar3 != null) {
            hVar3.a();
        }
        this.f616d.setVisibility(0);
        if (this.o == 0 && (this.u || z2)) {
            this.f616d.setTranslationY(0.0f);
            float f2 = -this.f616d.getHeight();
            if (z2) {
                this.f616d.getLocationInWindow(new int[]{0, 0});
                f2 -= r9[1];
            }
            this.f616d.setTranslationY(f2);
            a.b.f.h hVar4 = new a.b.f.h();
            s c4 = q.c(this.f616d);
            c4.k(0.0f);
            c4.i(this.y);
            hVar4.c(c4);
            if (this.p && (view3 = this.g) != null) {
                view3.setTranslationY(f2);
                s c5 = q.c(this.g);
                c5.k(0.0f);
                hVar4.c(c5);
            }
            hVar4.f(A);
            hVar4.e(250L);
            hVar4.g(this.x);
            this.t = hVar4;
            hVar4.h();
        } else {
            this.f616d.setAlpha(1.0f);
            this.f616d.setTranslationY(0.0f);
            if (this.p && (view2 = this.g) != null) {
                view2.setTranslationY(0.0f);
            }
            this.x.a(null);
        }
        ActionBarOverlayLayout actionBarOverlayLayout = this.f615c;
        if (actionBarOverlayLayout != null) {
            int i = q.e;
            actionBarOverlayLayout.requestApplyInsets();
        }
    }

    @Override // androidx.appcompat.app.a
    public void a(boolean z2) {
        if (z2 == this.l) {
            return;
        }
        this.l = z2;
        int size = this.m.size();
        for (int i = 0; i < size; i++) {
            this.m.get(i).a(z2);
        }
    }

    @Override // androidx.appcompat.app.a
    public Context b() {
        if (this.f614b == null) {
            TypedValue typedValue = new TypedValue();
            this.f613a.getTheme().resolveAttribute(org.libpag.R.attr.actionBarWidgetTheme, typedValue, true);
            int i = typedValue.resourceId;
            if (i != 0) {
                this.f614b = new ContextThemeWrapper(this.f613a, i);
            } else {
                this.f614b = this.f613a;
            }
        }
        return this.f614b;
    }

    @Override // androidx.appcompat.app.a
    public void c(Configuration configuration) {
        l(a.b.f.a.b(this.f613a).g());
    }

    @Override // androidx.appcompat.app.a
    public void d(boolean z2) {
        if (this.h) {
            return;
        }
        int i = z2 ? 4 : 0;
        int b2 = this.e.b();
        this.h = true;
        this.e.s((i & 4) | (b2 & (-5)));
    }

    @Override // androidx.appcompat.app.a
    public void e(boolean z2) {
        a.b.f.h hVar;
        this.u = z2;
        if (z2 || (hVar = this.t) == null) {
            return;
        }
        hVar.a();
    }

    public void f(boolean z2) {
        s sVar;
        s sVar2;
        if (z2) {
            if (!this.r) {
                this.r = true;
                ActionBarOverlayLayout actionBarOverlayLayout = this.f615c;
                if (actionBarOverlayLayout != null) {
                    actionBarOverlayLayout.setShowingForActionMode(true);
                }
                n(false);
            }
        } else if (this.r) {
            this.r = false;
            ActionBarOverlayLayout actionBarOverlayLayout2 = this.f615c;
            if (actionBarOverlayLayout2 != null) {
                actionBarOverlayLayout2.setShowingForActionMode(false);
            }
            n(false);
        }
        ActionBarContainer actionBarContainer = this.f616d;
        int i = q.e;
        if (!actionBarContainer.isLaidOut()) {
            if (z2) {
                this.e.c(4);
                this.f.setVisibility(0);
                return;
            } else {
                this.e.c(0);
                this.f.setVisibility(8);
                return;
            }
        }
        if (z2) {
            sVar = this.e.l(4, 100L);
            sVar2 = this.f.setupAnimatorToVisibility(0, 200L);
        } else {
            s l = this.e.l(0, 200L);
            sVar = this.f.setupAnimatorToVisibility(8, 100L);
            sVar2 = l;
        }
        a.b.f.h hVar = new a.b.f.h();
        hVar.d(sVar, sVar2);
        hVar.h();
    }

    public void g(boolean z2) {
        this.p = z2;
    }

    public void h() {
        if (this.q) {
            return;
        }
        this.q = true;
        n(true);
    }

    public void j() {
        a.b.f.h hVar = this.t;
        if (hVar != null) {
            hVar.a();
            this.t = null;
        }
    }

    public void k(int i) {
        this.o = i;
    }

    public void m() {
        if (this.q) {
            this.q = false;
            n(true);
        }
    }
}
