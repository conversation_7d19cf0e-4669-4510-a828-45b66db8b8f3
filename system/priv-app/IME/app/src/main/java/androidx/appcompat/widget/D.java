package androidx.appcompat.widget;

import android.content.Context;
import android.content.ContextWrapper;

/* loaded from: classes.dex */
public class D extends ContextWrapper {

    /* renamed from: a, reason: collision with root package name */
    private static final Object f723a = new Object();

    public static Context a(Context context) {
        if (!(context instanceof D) && !(context.getResources() instanceof F)) {
            context.getResources();
        }
        return context;
    }
}
