package androidx.appcompat.view.menu;

import android.content.Context;
import android.os.Bundle;
import android.os.Parcelable;
import android.util.SparseArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.ListAdapter;
import androidx.appcompat.view.menu.m;
import androidx.appcompat.view.menu.n;
import java.util.ArrayList;
import java.util.Objects;
import org.libpag.R;

/* loaded from: classes.dex */
public class e implements m, AdapterView.OnItemClickListener {

    /* renamed from: a, reason: collision with root package name */
    Context f646a;

    /* renamed from: b, reason: collision with root package name */
    LayoutInflater f647b;

    /* renamed from: c, reason: collision with root package name */
    g f648c;

    /* renamed from: d, reason: collision with root package name */
    ExpandedMenuView f649d;
    private m.a e;
    a f;

    /* JADX INFO: Access modifiers changed from: private */
    class a extends BaseAdapter {

        /* renamed from: a, reason: collision with root package name */
        private int f650a = -1;

        public a() {
            a();
        }

        void a() {
            i o = e.this.f648c.o();
            if (o != null) {
                ArrayList<i> p = e.this.f648c.p();
                int size = p.size();
                for (int i = 0; i < size; i++) {
                    if (p.get(i) == o) {
                        this.f650a = i;
                        return;
                    }
                }
            }
            this.f650a = -1;
        }

        @Override // android.widget.Adapter
        /* renamed from: b, reason: merged with bridge method [inline-methods] */
        public i getItem(int i) {
            ArrayList<i> p = e.this.f648c.p();
            Objects.requireNonNull(e.this);
            int i2 = i + 0;
            int i3 = this.f650a;
            if (i3 >= 0 && i2 >= i3) {
                i2++;
            }
            return p.get(i2);
        }

        @Override // android.widget.Adapter
        public int getCount() {
            int size = e.this.f648c.p().size();
            Objects.requireNonNull(e.this);
            int i = size + 0;
            return this.f650a < 0 ? i : i - 1;
        }

        @Override // android.widget.Adapter
        public long getItemId(int i) {
            return i;
        }

        @Override // android.widget.Adapter
        public View getView(int i, View view, ViewGroup viewGroup) {
            if (view == null) {
                view = e.this.f647b.inflate(R.layout.abc_list_menu_item_layout, viewGroup, false);
            }
            ((n.a) view).initialize(getItem(i), 0);
            return view;
        }

        @Override // android.widget.BaseAdapter
        public void notifyDataSetChanged() {
            a();
            super.notifyDataSetChanged();
        }
    }

    public e(Context context, int i) {
        this.f646a = context;
        this.f647b = LayoutInflater.from(context);
    }

    @Override // androidx.appcompat.view.menu.m
    public void a(g gVar, boolean z) {
        m.a aVar = this.e;
        if (aVar != null) {
            aVar.a(gVar, z);
        }
    }

    public ListAdapter b() {
        if (this.f == null) {
            this.f = new a();
        }
        return this.f;
    }

    @Override // androidx.appcompat.view.menu.m
    public int c() {
        return 0;
    }

    @Override // androidx.appcompat.view.menu.m
    public boolean d() {
        return false;
    }

    @Override // androidx.appcompat.view.menu.m
    public Parcelable e() {
        if (this.f649d == null) {
            return null;
        }
        Bundle bundle = new Bundle();
        SparseArray<Parcelable> sparseArray = new SparseArray<>();
        ExpandedMenuView expandedMenuView = this.f649d;
        if (expandedMenuView != null) {
            expandedMenuView.saveHierarchyState(sparseArray);
        }
        bundle.putSparseParcelableArray("android:menu:list", sparseArray);
        return bundle;
    }

    public n f(ViewGroup viewGroup) {
        if (this.f649d == null) {
            this.f649d = (ExpandedMenuView) this.f647b.inflate(R.layout.abc_expanded_menu_layout, viewGroup, false);
            if (this.f == null) {
                this.f = new a();
            }
            this.f649d.setAdapter((ListAdapter) this.f);
            this.f649d.setOnItemClickListener(this);
        }
        return this.f649d;
    }

    @Override // androidx.appcompat.view.menu.m
    public void g(Context context, g gVar) {
        if (this.f646a != null) {
            this.f646a = context;
            if (this.f647b == null) {
                this.f647b = LayoutInflater.from(context);
            }
        }
        this.f648c = gVar;
        a aVar = this.f;
        if (aVar != null) {
            aVar.notifyDataSetChanged();
        }
    }

    @Override // androidx.appcompat.view.menu.m
    public void h(Parcelable parcelable) {
        SparseArray<Parcelable> sparseParcelableArray = ((Bundle) parcelable).getSparseParcelableArray("android:menu:list");
        if (sparseParcelableArray != null) {
            this.f649d.restoreHierarchyState(sparseParcelableArray);
        }
    }

    @Override // androidx.appcompat.view.menu.m
    public boolean i(g gVar, i iVar) {
        return false;
    }

    @Override // androidx.appcompat.view.menu.m
    public boolean j(g gVar, i iVar) {
        return false;
    }

    @Override // androidx.appcompat.view.menu.m
    public void l(m.a aVar) {
        this.e = aVar;
    }

    @Override // androidx.appcompat.view.menu.m
    public boolean m(r rVar) {
        if (!rVar.hasVisibleItems()) {
            return false;
        }
        new h(rVar).c(null);
        m.a aVar = this.e;
        if (aVar == null) {
            return true;
        }
        aVar.b(rVar);
        return true;
    }

    @Override // androidx.appcompat.view.menu.m
    public void n(boolean z) {
        a aVar = this.f;
        if (aVar != null) {
            aVar.notifyDataSetChanged();
        }
    }

    @Override // android.widget.AdapterView.OnItemClickListener
    public void onItemClick(AdapterView<?> adapterView, View view, int i, long j) {
        this.f648c.z(this.f.getItem(i), this, 0);
    }
}
