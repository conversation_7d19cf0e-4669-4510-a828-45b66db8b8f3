package androidx.appcompat.view.menu;

import android.content.Context;
import android.content.res.Resources;
import android.os.Parcelable;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.AdapterView;
import android.widget.FrameLayout;
import android.widget.ListView;
import android.widget.PopupWindow;
import android.widget.TextView;
import androidx.appcompat.view.menu.m;
import androidx.appcompat.widget.MenuPopupWindow;
import org.libpag.R;

/* loaded from: classes.dex */
final class q extends k implements PopupWindow.OnDismissListener, AdapterView.OnItemClickListener, m, View.OnKeyListener {

    /* renamed from: b, reason: collision with root package name */
    private final Context f683b;

    /* renamed from: c, reason: collision with root package name */
    private final g f684c;

    /* renamed from: d, reason: collision with root package name */
    private final f f685d;
    private final boolean e;
    private final int f;
    private final int g;
    private final int h;
    final MenuPopupWindow i;
    private PopupWindow.OnDismissListener l;
    private View m;
    View n;
    private m.a o;
    ViewTreeObserver p;
    private boolean q;
    private boolean r;
    private int s;
    private boolean u;
    final ViewTreeObserver.OnGlobalLayoutListener j = new a();
    private final View.OnAttachStateChangeListener k = new b();
    private int t = 0;

    class a implements ViewTreeObserver.OnGlobalLayoutListener {
        a() {
        }

        @Override // android.view.ViewTreeObserver.OnGlobalLayoutListener
        public void onGlobalLayout() {
            if (!q.this.b() || q.this.i.w()) {
                return;
            }
            View view = q.this.n;
            if (view == null || !view.isShown()) {
                q.this.dismiss();
            } else {
                q.this.i.f();
            }
        }
    }

    class b implements View.OnAttachStateChangeListener {
        b() {
        }

        @Override // android.view.View.OnAttachStateChangeListener
        public void onViewAttachedToWindow(View view) {
        }

        @Override // android.view.View.OnAttachStateChangeListener
        public void onViewDetachedFromWindow(View view) {
            ViewTreeObserver viewTreeObserver = q.this.p;
            if (viewTreeObserver != null) {
                if (!viewTreeObserver.isAlive()) {
                    q.this.p = view.getViewTreeObserver();
                }
                q qVar = q.this;
                qVar.p.removeGlobalOnLayoutListener(qVar.j);
            }
            view.removeOnAttachStateChangeListener(this);
        }
    }

    public q(Context context, g gVar, View view, int i, int i2, boolean z) {
        this.f683b = context;
        this.f684c = gVar;
        this.e = z;
        this.f685d = new f(gVar, LayoutInflater.from(context), z, R.layout.abc_popup_menu_item_layout);
        this.g = i;
        this.h = i2;
        Resources resources = context.getResources();
        this.f = Math.max(resources.getDisplayMetrics().widthPixels / 2, resources.getDimensionPixelSize(R.dimen.abc_config_prefDialogWidth));
        this.m = view;
        this.i = new MenuPopupWindow(context, null, i, i2);
        gVar.c(this, context);
    }

    @Override // androidx.appcompat.view.menu.m
    public void a(g gVar, boolean z) {
        if (gVar != this.f684c) {
            return;
        }
        dismiss();
        m.a aVar = this.o;
        if (aVar != null) {
            aVar.a(gVar, z);
        }
    }

    @Override // androidx.appcompat.view.menu.p
    public boolean b() {
        return !this.q && this.i.b();
    }

    @Override // androidx.appcompat.view.menu.m
    public boolean d() {
        return false;
    }

    @Override // androidx.appcompat.view.menu.p
    public void dismiss() {
        if (b()) {
            this.i.dismiss();
        }
    }

    @Override // androidx.appcompat.view.menu.m
    public Parcelable e() {
        return null;
    }

    @Override // androidx.appcompat.view.menu.p
    public void f() {
        View view;
        boolean z = true;
        if (!b()) {
            if (this.q || (view = this.m) == null) {
                z = false;
            } else {
                this.n = view;
                this.i.E(this);
                this.i.F(this);
                this.i.D(true);
                View view2 = this.n;
                boolean z2 = this.p == null;
                ViewTreeObserver viewTreeObserver = view2.getViewTreeObserver();
                this.p = viewTreeObserver;
                if (z2) {
                    viewTreeObserver.addOnGlobalLayoutListener(this.j);
                }
                view2.addOnAttachStateChangeListener(this.k);
                this.i.x(view2);
                this.i.A(this.t);
                if (!this.r) {
                    this.s = k.r(this.f685d, null, this.f683b, this.f);
                    this.r = true;
                }
                this.i.z(this.s);
                this.i.C(2);
                this.i.B(q());
                this.i.f();
                ListView k = this.i.k();
                k.setOnKeyListener(this);
                if (this.u && this.f684c.m != null) {
                    FrameLayout frameLayout = (FrameLayout) LayoutInflater.from(this.f683b).inflate(R.layout.abc_popup_menu_header_item_layout, (ViewGroup) k, false);
                    TextView textView = (TextView) frameLayout.findViewById(android.R.id.title);
                    if (textView != null) {
                        textView.setText(this.f684c.m);
                    }
                    frameLayout.setEnabled(false);
                    k.addHeaderView(frameLayout, null, false);
                }
                this.i.o(this.f685d);
                this.i.f();
            }
        }
        if (!z) {
            throw new IllegalStateException("StandardMenuPopup cannot be used without an anchor");
        }
    }

    @Override // androidx.appcompat.view.menu.m
    public void h(Parcelable parcelable) {
    }

    @Override // androidx.appcompat.view.menu.p
    public ListView k() {
        return this.i.k();
    }

    @Override // androidx.appcompat.view.menu.m
    public void l(m.a aVar) {
        this.o = aVar;
    }

    @Override // androidx.appcompat.view.menu.m
    public boolean m(r rVar) {
        if (rVar.hasVisibleItems()) {
            l lVar = new l(this.f683b, rVar, this.n, this.e, this.g, this.h);
            lVar.i(this.o);
            lVar.f(k.A(rVar));
            lVar.h(this.l);
            this.l = null;
            this.f684c.e(false);
            int c2 = this.i.c();
            int g = this.i.g();
            int i = this.t;
            View view = this.m;
            int i2 = a.h.h.q.e;
            if ((Gravity.getAbsoluteGravity(i, view.getLayoutDirection()) & 7) == 5) {
                c2 += this.m.getWidth();
            }
            if (lVar.l(c2, g)) {
                m.a aVar = this.o;
                if (aVar == null) {
                    return true;
                }
                aVar.b(rVar);
                return true;
            }
        }
        return false;
    }

    @Override // androidx.appcompat.view.menu.m
    public void n(boolean z) {
        this.r = false;
        f fVar = this.f685d;
        if (fVar != null) {
            fVar.notifyDataSetChanged();
        }
    }

    @Override // androidx.appcompat.view.menu.k
    public void o(g gVar) {
    }

    @Override // android.widget.PopupWindow.OnDismissListener
    public void onDismiss() {
        this.q = true;
        this.f684c.e(true);
        ViewTreeObserver viewTreeObserver = this.p;
        if (viewTreeObserver != null) {
            if (!viewTreeObserver.isAlive()) {
                this.p = this.n.getViewTreeObserver();
            }
            this.p.removeGlobalOnLayoutListener(this.j);
            this.p = null;
        }
        this.n.removeOnAttachStateChangeListener(this.k);
        PopupWindow.OnDismissListener onDismissListener = this.l;
        if (onDismissListener != null) {
            onDismissListener.onDismiss();
        }
    }

    @Override // android.view.View.OnKeyListener
    public boolean onKey(View view, int i, KeyEvent keyEvent) {
        if (keyEvent.getAction() != 1 || i != 82) {
            return false;
        }
        dismiss();
        return true;
    }

    @Override // androidx.appcompat.view.menu.k
    public void s(View view) {
        this.m = view;
    }

    @Override // androidx.appcompat.view.menu.k
    public void u(boolean z) {
        this.f685d.d(z);
    }

    @Override // androidx.appcompat.view.menu.k
    public void v(int i) {
        this.t = i;
    }

    @Override // androidx.appcompat.view.menu.k
    public void w(int i) {
        this.i.a(i);
    }

    @Override // androidx.appcompat.view.menu.k
    public void x(PopupWindow.OnDismissListener onDismissListener) {
        this.l = onDismissListener;
    }

    @Override // androidx.appcompat.view.menu.k
    public void y(boolean z) {
        this.u = z;
    }

    @Override // androidx.appcompat.view.menu.k
    public void z(int i) {
        this.i.n(i);
    }
}
