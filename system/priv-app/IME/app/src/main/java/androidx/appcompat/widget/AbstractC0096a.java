package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.Configuration;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.ContextThemeWrapper;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import androidx.recyclerview.widget.RecyclerView;
import org.libpag.R;

/* renamed from: androidx.appcompat.widget.a, reason: case insensitive filesystem */
/* loaded from: classes.dex */
abstract class AbstractC0096a extends ViewGroup {
    private static final int FADE_DURATION = 200;
    protected C0098c mActionMenuPresenter;
    protected int mContentHeight;
    private boolean mEatingHover;
    private boolean mEatingTouch;
    protected ActionMenuView mMenuView;
    protected final Context mPopupContext;
    protected final b mVisAnimListener;
    protected a.h.h.s mVisibilityAnim;

    /* renamed from: androidx.appcompat.widget.a$a, reason: collision with other inner class name */
    class RunnableC0027a implements Runnable {
        RunnableC0027a() {
        }

        @Override // java.lang.Runnable
        public void run() {
            AbstractC0096a.this.showOverflowMenu();
        }
    }

    /* renamed from: androidx.appcompat.widget.a$b */
    protected class b implements a.h.h.t {

        /* renamed from: a, reason: collision with root package name */
        private boolean f781a = false;

        /* renamed from: b, reason: collision with root package name */
        int f782b;

        protected b() {
        }

        @Override // a.h.h.t
        public void a(View view) {
            if (this.f781a) {
                return;
            }
            AbstractC0096a abstractC0096a = AbstractC0096a.this;
            abstractC0096a.mVisibilityAnim = null;
            AbstractC0096a.super.setVisibility(this.f782b);
        }

        @Override // a.h.h.t
        public void b(View view) {
            AbstractC0096a.super.setVisibility(0);
            this.f781a = false;
        }

        @Override // a.h.h.t
        public void c(View view) {
            this.f781a = true;
        }
    }

    AbstractC0096a(Context context) {
        this(context, null);
    }

    AbstractC0096a(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, 0);
    }

    AbstractC0096a(Context context, AttributeSet attributeSet, int i) {
        super(context, attributeSet, i);
        int i2;
        this.mVisAnimListener = new b();
        TypedValue typedValue = new TypedValue();
        if (!context.getTheme().resolveAttribute(R.attr.actionBarPopupTheme, typedValue, true) || (i2 = typedValue.resourceId) == 0) {
            this.mPopupContext = context;
        } else {
            this.mPopupContext = new ContextThemeWrapper(context, i2);
        }
    }

    protected static int next(int i, int i2, boolean z) {
        return z ? i - i2 : i + i2;
    }

    public void animateToVisibility(int i) {
        setupAnimatorToVisibility(i, 200L).j();
    }

    public boolean canShowOverflowMenu() {
        return isOverflowReserved() && getVisibility() == 0;
    }

    public void dismissPopupMenus() {
        C0098c c0098c = this.mActionMenuPresenter;
        if (c0098c != null) {
            c0098c.A();
        }
    }

    public int getAnimatedVisibility() {
        return this.mVisibilityAnim != null ? this.mVisAnimListener.f782b : getVisibility();
    }

    public int getContentHeight() {
        return this.mContentHeight;
    }

    public abstract boolean hideOverflowMenu();

    public boolean isOverflowMenuShowPending() {
        C0098c c0098c = this.mActionMenuPresenter;
        return c0098c != null && (c0098c.w != null || c0098c.D());
    }

    public abstract boolean isOverflowMenuShowing();

    public boolean isOverflowReserved() {
        C0098c c0098c = this.mActionMenuPresenter;
        return c0098c != null && c0098c.E();
    }

    protected int measureChildView(View view, int i, int i2, int i3) {
        view.measure(View.MeasureSpec.makeMeasureSpec(i, RecyclerView.UNDEFINED_DURATION), i2);
        return Math.max(0, (i - view.getMeasuredWidth()) - i3);
    }

    @Override // android.view.View
    protected void onConfigurationChanged(Configuration configuration) {
        super.onConfigurationChanged(configuration);
        TypedArray obtainStyledAttributes = getContext().obtainStyledAttributes(null, a.b.b.f0a, R.attr.actionBarStyle, 0);
        setContentHeight(obtainStyledAttributes.getLayoutDimension(13, 0));
        obtainStyledAttributes.recycle();
        C0098c c0098c = this.mActionMenuPresenter;
        if (c0098c != null) {
            c0098c.F();
        }
    }

    @Override // android.view.View
    public boolean onHoverEvent(MotionEvent motionEvent) {
        int actionMasked = motionEvent.getActionMasked();
        if (actionMasked == 9) {
            this.mEatingHover = false;
        }
        if (!this.mEatingHover) {
            boolean onHoverEvent = super.onHoverEvent(motionEvent);
            if (actionMasked == 9 && !onHoverEvent) {
                this.mEatingHover = true;
            }
        }
        if (actionMasked == 10 || actionMasked == 3) {
            this.mEatingHover = false;
        }
        return true;
    }

    @Override // android.view.View
    public boolean onTouchEvent(MotionEvent motionEvent) {
        int actionMasked = motionEvent.getActionMasked();
        if (actionMasked == 0) {
            this.mEatingTouch = false;
        }
        if (!this.mEatingTouch) {
            boolean onTouchEvent = super.onTouchEvent(motionEvent);
            if (actionMasked == 0 && !onTouchEvent) {
                this.mEatingTouch = true;
            }
        }
        if (actionMasked == 1 || actionMasked == 3) {
            this.mEatingTouch = false;
        }
        return true;
    }

    protected int positionChild(View view, int i, int i2, int i3, boolean z) {
        int measuredWidth = view.getMeasuredWidth();
        int measuredHeight = view.getMeasuredHeight();
        int i4 = ((i3 - measuredHeight) / 2) + i2;
        if (z) {
            view.layout(i - measuredWidth, i4, i, measuredHeight + i4);
        } else {
            view.layout(i, i4, i + measuredWidth, measuredHeight + i4);
        }
        return z ? -measuredWidth : measuredWidth;
    }

    public void postShowOverflowMenu() {
        post(new RunnableC0027a());
    }

    public abstract void setContentHeight(int i);

    @Override // android.view.View
    public void setVisibility(int i) {
        if (i != getVisibility()) {
            a.h.h.s sVar = this.mVisibilityAnim;
            if (sVar != null) {
                sVar.b();
            }
            super.setVisibility(i);
        }
    }

    public a.h.h.s setupAnimatorToVisibility(int i, long j) {
        a.h.h.s sVar = this.mVisibilityAnim;
        if (sVar != null) {
            sVar.b();
        }
        if (i != 0) {
            a.h.h.s c2 = a.h.h.q.c(this);
            c2.a(0.0f);
            c2.d(j);
            b bVar = this.mVisAnimListener;
            AbstractC0096a.this.mVisibilityAnim = c2;
            bVar.f782b = i;
            c2.f(bVar);
            return c2;
        }
        if (getVisibility() != 0) {
            setAlpha(0.0f);
        }
        a.h.h.s c3 = a.h.h.q.c(this);
        c3.a(1.0f);
        c3.d(j);
        b bVar2 = this.mVisAnimListener;
        AbstractC0096a.this.mVisibilityAnim = c3;
        bVar2.f782b = i;
        c3.f(bVar2);
        return c3;
    }

    public abstract boolean showOverflowMenu();
}
