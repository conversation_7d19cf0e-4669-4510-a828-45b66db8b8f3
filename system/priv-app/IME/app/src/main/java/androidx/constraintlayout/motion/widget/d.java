package androidx.constraintlayout.motion.widget;

import android.content.Context;
import android.util.AttributeSet;
import java.util.HashMap;
import java.util.HashSet;

/* loaded from: classes.dex */
public abstract class d {

    /* renamed from: a, reason: collision with root package name */
    int f897a = -1;

    /* renamed from: b, reason: collision with root package name */
    int f898b = -1;

    /* renamed from: c, reason: collision with root package name */
    String f899c = null;

    /* renamed from: d, reason: collision with root package name */
    protected int f900d;
    HashMap<String, androidx.constraintlayout.widget.a> e;

    public abstract void a(HashMap<String, a.f.b.a.d> hashMap);

    @Override // 
    /* renamed from: b, reason: merged with bridge method [inline-methods] */
    public abstract d clone();

    public d c(d dVar) {
        this.f897a = dVar.f897a;
        this.f898b = dVar.f898b;
        this.f899c = dVar.f899c;
        this.f900d = dVar.f900d;
        this.e = dVar.e;
        return this;
    }

    abstract void d(HashSet<String> hashSet);

    abstract void e(Context context, AttributeSet attributeSet);

    public void f(int i) {
        this.f897a = i;
    }

    public void g(HashMap<String, Integer> hashMap) {
    }

    float h(Object obj) {
        return obj instanceof Float ? ((Float) obj).floatValue() : Float.parseFloat(obj.toString());
    }

    int i(Object obj) {
        return obj instanceof Integer ? ((Integer) obj).intValue() : Integer.parseInt(obj.toString());
    }
}
