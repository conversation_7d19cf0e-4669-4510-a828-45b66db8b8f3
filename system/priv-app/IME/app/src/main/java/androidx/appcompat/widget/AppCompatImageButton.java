package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Bitmap;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.util.AttributeSet;
import android.widget.ImageButton;
import org.libpag.R;

/* loaded from: classes.dex */
public class AppCompatImageButton extends ImageButton {
    private final C0100e mBackgroundTintHelper;
    private final C0103h mImageHelper;

    public AppCompatImageButton(Context context) {
        this(context, null);
    }

    public AppCompatImageButton(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, R.attr.imageButtonStyle);
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public AppCompatImageButton(Context context, AttributeSet attributeSet, int i) {
        super(context, attributeSet, i);
        D.a(context);
        B.a(this, getContext());
        C0100e c0100e = new C0100e(this);
        this.mBackgroundTintHelper = c0100e;
        c0100e.d(attributeSet, i);
        C0103h c0103h = new C0103h(this);
        this.mImageHelper = c0103h;
        c0103h.e(attributeSet, i);
    }

    @Override // android.widget.ImageView, android.view.View
    protected void drawableStateChanged() {
        super.drawableStateChanged();
        C0100e c0100e = this.mBackgroundTintHelper;
        if (c0100e != null) {
            c0100e.a();
        }
        C0103h c0103h = this.mImageHelper;
        if (c0103h != null) {
            c0103h.a();
        }
    }

    public ColorStateList getSupportBackgroundTintList() {
        C0100e c0100e = this.mBackgroundTintHelper;
        if (c0100e != null) {
            return c0100e.b();
        }
        return null;
    }

    public PorterDuff.Mode getSupportBackgroundTintMode() {
        C0100e c0100e = this.mBackgroundTintHelper;
        if (c0100e != null) {
            return c0100e.c();
        }
        return null;
    }

    public ColorStateList getSupportImageTintList() {
        C0103h c0103h = this.mImageHelper;
        if (c0103h != null) {
            return c0103h.b();
        }
        return null;
    }

    public PorterDuff.Mode getSupportImageTintMode() {
        C0103h c0103h = this.mImageHelper;
        if (c0103h != null) {
            return c0103h.c();
        }
        return null;
    }

    @Override // android.widget.ImageView, android.view.View
    public boolean hasOverlappingRendering() {
        return this.mImageHelper.d() && super.hasOverlappingRendering();
    }

    @Override // android.view.View
    public void setBackgroundDrawable(Drawable drawable) {
        super.setBackgroundDrawable(drawable);
        C0100e c0100e = this.mBackgroundTintHelper;
        if (c0100e != null) {
            c0100e.e();
        }
    }

    @Override // android.view.View
    public void setBackgroundResource(int i) {
        super.setBackgroundResource(i);
        C0100e c0100e = this.mBackgroundTintHelper;
        if (c0100e != null) {
            c0100e.f(i);
        }
    }

    @Override // android.widget.ImageView
    public void setImageBitmap(Bitmap bitmap) {
        super.setImageBitmap(bitmap);
        C0103h c0103h = this.mImageHelper;
        if (c0103h != null) {
            c0103h.a();
        }
    }

    @Override // android.widget.ImageView
    public void setImageDrawable(Drawable drawable) {
        super.setImageDrawable(drawable);
        C0103h c0103h = this.mImageHelper;
        if (c0103h != null) {
            c0103h.a();
        }
    }

    @Override // android.widget.ImageView
    public void setImageResource(int i) {
        this.mImageHelper.f(i);
    }

    @Override // android.widget.ImageView
    public void setImageURI(Uri uri) {
        super.setImageURI(uri);
        C0103h c0103h = this.mImageHelper;
        if (c0103h != null) {
            c0103h.a();
        }
    }

    public void setSupportBackgroundTintList(ColorStateList colorStateList) {
        C0100e c0100e = this.mBackgroundTintHelper;
        if (c0100e != null) {
            c0100e.h(colorStateList);
        }
    }

    public void setSupportBackgroundTintMode(PorterDuff.Mode mode) {
        C0100e c0100e = this.mBackgroundTintHelper;
        if (c0100e != null) {
            c0100e.i(mode);
        }
    }

    public void setSupportImageTintList(ColorStateList colorStateList) {
        C0103h c0103h = this.mImageHelper;
        if (c0103h != null) {
            c0103h.g(colorStateList);
        }
    }

    public void setSupportImageTintMode(PorterDuff.Mode mode) {
        C0103h c0103h = this.mImageHelper;
        if (c0103h != null) {
            c0103h.h(mode);
        }
    }
}
