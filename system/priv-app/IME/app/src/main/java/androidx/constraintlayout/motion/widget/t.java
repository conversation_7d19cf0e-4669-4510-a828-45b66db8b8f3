package androidx.constraintlayout.motion.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Rect;
import android.util.Log;
import android.util.Xml;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.AnimationUtils;
import android.view.animation.AnticipateInterpolator;
import android.view.animation.BounceInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.view.animation.Interpolator;
import android.view.animation.OvershootInterpolator;
import androidx.constraintlayout.motion.widget.q;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.c;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import org.libpag.R;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

/* loaded from: classes.dex */
public class t {

    /* renamed from: a, reason: collision with root package name */
    private int f938a;
    int e;
    g f;
    c.a g;
    private int j;
    private String k;
    Context o;

    /* renamed from: b, reason: collision with root package name */
    private int f939b = -1;

    /* renamed from: c, reason: collision with root package name */
    private boolean f940c = false;

    /* renamed from: d, reason: collision with root package name */
    private int f941d = 0;
    private int h = -1;
    private int i = -1;
    private int l = 0;
    private String m = null;
    private int n = -1;
    private int p = -1;
    private int q = -1;
    private int r = -1;
    private int s = -1;
    private int t = -1;
    private int u = -1;

    static class a {

        /* renamed from: a, reason: collision with root package name */
        private final int f942a;

        /* renamed from: b, reason: collision with root package name */
        private final int f943b;

        /* renamed from: c, reason: collision with root package name */
        long f944c;

        /* renamed from: d, reason: collision with root package name */
        n f945d;
        int e;
        u g;
        Interpolator h;
        float j;
        float k;
        long l;
        boolean n;
        a.f.a.i.a.d f = new a.f.a.i.a.d();
        boolean i = false;
        Rect m = new Rect();

        a(u uVar, n nVar, int i, int i2, int i3, Interpolator interpolator, int i4, int i5) {
            this.n = false;
            this.g = uVar;
            this.f945d = nVar;
            this.e = i2;
            long nanoTime = System.nanoTime();
            this.f944c = nanoTime;
            this.l = nanoTime;
            u uVar2 = this.g;
            if (uVar2.e == null) {
                uVar2.e = new ArrayList<>();
            }
            uVar2.e.add(this);
            this.h = interpolator;
            this.f942a = i4;
            this.f943b = i5;
            if (i3 == 3) {
                this.n = true;
            }
            this.k = i == 0 ? Float.MAX_VALUE : 1.0f / i;
            a();
        }

        void a() {
            if (this.i) {
                long nanoTime = System.nanoTime();
                long j = nanoTime - this.l;
                this.l = nanoTime;
                float f = this.j - (((float) (j * 1.0E-6d)) * this.k);
                this.j = f;
                if (f < 0.0f) {
                    this.j = 0.0f;
                }
                Interpolator interpolator = this.h;
                float interpolation = interpolator == null ? this.j : interpolator.getInterpolation(this.j);
                n nVar = this.f945d;
                boolean r = nVar.r(nVar.f914b, interpolation, nanoTime, this.f);
                if (this.j <= 0.0f) {
                    int i = this.f942a;
                    if (i != -1) {
                        this.f945d.f914b.setTag(i, Long.valueOf(System.nanoTime()));
                    }
                    int i2 = this.f943b;
                    if (i2 != -1) {
                        this.f945d.f914b.setTag(i2, null);
                    }
                    this.g.f.add(this);
                }
                if (this.j > 0.0f || r) {
                    this.g.d();
                    return;
                }
                return;
            }
            long nanoTime2 = System.nanoTime();
            long j2 = nanoTime2 - this.l;
            this.l = nanoTime2;
            float f2 = (((float) (j2 * 1.0E-6d)) * this.k) + this.j;
            this.j = f2;
            if (f2 >= 1.0f) {
                this.j = 1.0f;
            }
            Interpolator interpolator2 = this.h;
            float interpolation2 = interpolator2 == null ? this.j : interpolator2.getInterpolation(this.j);
            n nVar2 = this.f945d;
            boolean r2 = nVar2.r(nVar2.f914b, interpolation2, nanoTime2, this.f);
            if (this.j >= 1.0f) {
                int i3 = this.f942a;
                if (i3 != -1) {
                    this.f945d.f914b.setTag(i3, Long.valueOf(System.nanoTime()));
                }
                int i4 = this.f943b;
                if (i4 != -1) {
                    this.f945d.f914b.setTag(i4, null);
                }
                if (!this.n) {
                    this.g.f.add(this);
                }
            }
            if (this.j < 1.0f || r2) {
                this.g.d();
            }
        }

        void b(boolean z) {
            int i;
            this.i = z;
            if (z && (i = this.e) != -1) {
                this.k = i == 0 ? Float.MAX_VALUE : 1.0f / i;
            }
            this.g.d();
            this.l = System.nanoTime();
        }
    }

    t(Context context, XmlPullParser xmlPullParser) {
        char c2;
        this.o = context;
        try {
            int eventType = xmlPullParser.getEventType();
            while (eventType != 1) {
                if (eventType == 2) {
                    String name = xmlPullParser.getName();
                    switch (name.hashCode()) {
                        case -1962203927:
                            if (name.equals("ConstraintOverride")) {
                                c2 = 2;
                                break;
                            }
                            c2 = 65535;
                            break;
                        case -1239391468:
                            if (name.equals("KeyFrameSet")) {
                                c2 = 1;
                                break;
                            }
                            c2 = 65535;
                            break;
                        case 61998586:
                            if (name.equals("ViewTransition")) {
                                c2 = 0;
                                break;
                            }
                            c2 = 65535;
                            break;
                        case 366511058:
                            if (name.equals("CustomMethod")) {
                                c2 = 4;
                                break;
                            }
                            c2 = 65535;
                            break;
                        case 1791837707:
                            if (name.equals("CustomAttribute")) {
                                c2 = 3;
                                break;
                            }
                            c2 = 65535;
                            break;
                        default:
                            c2 = 65535;
                            break;
                    }
                    if (c2 == 0) {
                        j(context, xmlPullParser);
                    } else if (c2 == 1) {
                        this.f = new g(context, xmlPullParser);
                    } else if (c2 == 2) {
                        this.g = androidx.constraintlayout.widget.c.h(context, xmlPullParser);
                    } else if (c2 == 3 || c2 == 4) {
                        androidx.constraintlayout.widget.a.h(context, xmlPullParser, this.g.g);
                    } else {
                        Log.e("ViewTransition", a.b.a.a() + " unknown tag " + name);
                        StringBuilder sb = new StringBuilder();
                        sb.append(".xml:");
                        sb.append(xmlPullParser.getLineNumber());
                        Log.e("ViewTransition", sb.toString());
                    }
                } else if (eventType != 3) {
                    continue;
                } else if ("ViewTransition".equals(xmlPullParser.getName())) {
                    return;
                }
                eventType = xmlPullParser.next();
            }
        } catch (IOException e) {
            e.printStackTrace();
        } catch (XmlPullParserException e2) {
            e2.printStackTrace();
        }
    }

    private void j(Context context, XmlPullParser xmlPullParser) {
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(Xml.asAttributeSet(xmlPullParser), androidx.constraintlayout.widget.f.F);
        int indexCount = obtainStyledAttributes.getIndexCount();
        for (int i = 0; i < indexCount; i++) {
            int index = obtainStyledAttributes.getIndex(i);
            if (index == 0) {
                this.f938a = obtainStyledAttributes.getResourceId(index, this.f938a);
            } else if (index == 8) {
                if (MotionLayout.IS_IN_EDIT_MODE) {
                    int resourceId = obtainStyledAttributes.getResourceId(index, this.j);
                    this.j = resourceId;
                    if (resourceId != -1) {
                    }
                    this.k = obtainStyledAttributes.getString(index);
                } else {
                    if (obtainStyledAttributes.peekValue(index).type != 3) {
                        this.j = obtainStyledAttributes.getResourceId(index, this.j);
                    }
                    this.k = obtainStyledAttributes.getString(index);
                }
            } else if (index == 9) {
                this.f939b = obtainStyledAttributes.getInt(index, this.f939b);
            } else if (index == 12) {
                this.f940c = obtainStyledAttributes.getBoolean(index, this.f940c);
            } else if (index == 10) {
                this.f941d = obtainStyledAttributes.getInt(index, this.f941d);
            } else if (index == 4) {
                this.h = obtainStyledAttributes.getInt(index, this.h);
            } else if (index == 13) {
                this.i = obtainStyledAttributes.getInt(index, this.i);
            } else if (index == 14) {
                this.e = obtainStyledAttributes.getInt(index, this.e);
            } else if (index == 7) {
                int i2 = obtainStyledAttributes.peekValue(index).type;
                if (i2 == 1) {
                    int resourceId2 = obtainStyledAttributes.getResourceId(index, -1);
                    this.n = resourceId2;
                    if (resourceId2 == -1) {
                    }
                    this.l = -2;
                } else if (i2 == 3) {
                    String string = obtainStyledAttributes.getString(index);
                    this.m = string;
                    if (string == null || string.indexOf("/") <= 0) {
                        this.l = -1;
                    } else {
                        this.n = obtainStyledAttributes.getResourceId(index, -1);
                        this.l = -2;
                    }
                } else {
                    this.l = obtainStyledAttributes.getInteger(index, this.l);
                }
            } else if (index == 11) {
                this.p = obtainStyledAttributes.getResourceId(index, this.p);
            } else if (index == 3) {
                this.q = obtainStyledAttributes.getResourceId(index, this.q);
            } else if (index == 6) {
                this.r = obtainStyledAttributes.getResourceId(index, this.r);
            } else if (index == 5) {
                this.s = obtainStyledAttributes.getResourceId(index, this.s);
            } else if (index == 2) {
                this.u = obtainStyledAttributes.getResourceId(index, this.u);
            } else if (index == 1) {
                this.t = obtainStyledAttributes.getInteger(index, this.t);
            }
        }
        obtainStyledAttributes.recycle();
    }

    void a(u uVar, MotionLayout motionLayout, int i, androidx.constraintlayout.widget.c cVar, final View... viewArr) {
        Interpolator loadInterpolator;
        Interpolator interpolator;
        if (this.f940c) {
            return;
        }
        int i2 = this.e;
        if (i2 == 2) {
            View view = viewArr[0];
            n nVar = new n(view);
            nVar.u(view);
            this.f.a(nVar);
            nVar.A(motionLayout.getWidth(), motionLayout.getHeight(), System.nanoTime());
            int i3 = this.h;
            int i4 = this.i;
            int i5 = this.f939b;
            Context context = motionLayout.getContext();
            int i6 = this.l;
            if (i6 == -2) {
                loadInterpolator = AnimationUtils.loadInterpolator(context, this.n);
            } else {
                if (i6 == -1) {
                    interpolator = new s(this, a.f.a.i.a.c.c(this.m));
                    new a(uVar, nVar, i3, i4, i5, interpolator, this.p, this.q);
                    return;
                }
                loadInterpolator = i6 != 0 ? i6 != 1 ? i6 != 2 ? i6 != 4 ? i6 != 5 ? i6 != 6 ? null : new AnticipateInterpolator() : new OvershootInterpolator() : new BounceInterpolator() : new DecelerateInterpolator() : new AccelerateInterpolator() : new AccelerateDecelerateInterpolator();
            }
            interpolator = loadInterpolator;
            new a(uVar, nVar, i3, i4, i5, interpolator, this.p, this.q);
            return;
        }
        if (i2 == 1) {
            for (int i7 : motionLayout.getConstraintSetIds()) {
                if (i7 != i) {
                    androidx.constraintlayout.widget.c constraintSet = motionLayout.getConstraintSet(i7);
                    for (View view2 : viewArr) {
                        c.a q = constraintSet.q(view2.getId());
                        c.a aVar = this.g;
                        if (aVar != null) {
                            aVar.d(q);
                            q.g.putAll(this.g.g);
                        }
                    }
                }
            }
        }
        androidx.constraintlayout.widget.c cVar2 = new androidx.constraintlayout.widget.c();
        cVar2.k(cVar);
        for (View view3 : viewArr) {
            c.a q2 = cVar2.q(view3.getId());
            c.a aVar2 = this.g;
            if (aVar2 != null) {
                aVar2.d(q2);
                q2.g.putAll(this.g.g);
            }
        }
        motionLayout.updateState(i, cVar2);
        motionLayout.updateState(R.id.view_transition, cVar);
        motionLayout.setState(R.id.view_transition, -1, -1);
        q.b bVar = new q.b(-1, motionLayout.mScene, R.id.view_transition, i);
        for (View view4 : viewArr) {
            int i8 = this.h;
            if (i8 != -1) {
                bVar.C(i8);
            }
            bVar.G(this.f941d);
            bVar.E(this.l, this.m, this.n);
            int id = view4.getId();
            g gVar = this.f;
            if (gVar != null) {
                ArrayList<d> d2 = gVar.d(-1);
                g gVar2 = new g();
                Iterator<d> it = d2.iterator();
                while (it.hasNext()) {
                    d clone = it.next().clone();
                    clone.f898b = id;
                    gVar2.c(clone);
                }
                bVar.t(gVar2);
            }
        }
        motionLayout.setTransition(bVar);
        motionLayout.transitionToEnd(new Runnable() { // from class: androidx.constraintlayout.motion.widget.a
            @Override // java.lang.Runnable
            public final void run() {
                t.this.h(viewArr);
            }
        });
    }

    boolean b(View view) {
        int i = this.r;
        boolean z = i == -1 || view.getTag(i) != null;
        int i2 = this.s;
        return z && (i2 == -1 || view.getTag(i2) == null);
    }

    int c() {
        return this.f938a;
    }

    public int d() {
        return this.t;
    }

    public int e() {
        return this.u;
    }

    public int f() {
        return this.f939b;
    }

    boolean g() {
        return !this.f940c;
    }

    public /* synthetic */ void h(View[] viewArr) {
        if (this.p != -1) {
            for (View view : viewArr) {
                view.setTag(this.p, Long.valueOf(System.nanoTime()));
            }
        }
        if (this.q != -1) {
            for (View view2 : viewArr) {
                view2.setTag(this.q, null);
            }
        }
    }

    boolean i(View view) {
        String str;
        if (view == null) {
            return false;
        }
        if ((this.j == -1 && this.k == null) || !b(view)) {
            return false;
        }
        if (view.getId() == this.j) {
            return true;
        }
        return this.k != null && (view.getLayoutParams() instanceof ConstraintLayout.a) && (str = ((ConstraintLayout.a) view.getLayoutParams()).Y) != null && str.matches(this.k);
    }

    void k(boolean z) {
        this.f940c = !z;
    }

    boolean l(int i) {
        int i2 = this.f939b;
        return i2 == 1 ? i == 0 : i2 == 2 ? i == 1 : i2 == 3 && i == 0;
    }

    public String toString() {
        StringBuilder j = b.b.a.a.a.j("ViewTransition(");
        j.append(a.b.a.c(this.o, this.f938a));
        j.append(")");
        return j.toString();
    }
}
