package androidx.recyclerview.widget;

import a.h.h.x.b;
import android.R;
import android.animation.LayoutTransition;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.database.Observable;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.PointF;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.StateListDrawable;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.os.SystemClock;
import android.os.Trace;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseArray;
import android.view.Display;
import android.view.FocusFinder;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityManager;
import android.view.animation.Interpolator;
import android.widget.EdgeEffect;
import android.widget.OverScroller;
import androidx.recyclerview.widget.a;
import androidx.recyclerview.widget.b;
import androidx.recyclerview.widget.j;
import androidx.recyclerview.widget.q;
import androidx.recyclerview.widget.u;
import androidx.recyclerview.widget.v;
import java.lang.ref.WeakReference;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/* loaded from: classes.dex */
public class RecyclerView extends ViewGroup implements a.h.h.e {
    static final boolean DEBUG = false;
    static final int DEFAULT_ORIENTATION = 1;
    static final boolean DISPATCH_TEMP_DETACH = false;
    static final long FOREVER_NS = Long.MAX_VALUE;
    public static final int HORIZONTAL = 0;
    private static final int INVALID_POINTER = -1;
    public static final int INVALID_TYPE = -1;
    private static final Class<?>[] LAYOUT_MANAGER_CONSTRUCTOR_SIGNATURE;
    static final int MAX_SCROLL_DURATION = 2000;
    public static final long NO_ID = -1;
    public static final int NO_POSITION = -1;
    public static final int SCROLL_STATE_DRAGGING = 1;
    public static final int SCROLL_STATE_IDLE = 0;
    public static final int SCROLL_STATE_SETTLING = 2;
    static final String TAG = "RecyclerView";
    public static final int TOUCH_SLOP_DEFAULT = 0;
    public static final int TOUCH_SLOP_PAGING = 1;
    static final String TRACE_BIND_VIEW_TAG = "RV OnBindView";
    static final String TRACE_CREATE_VIEW_TAG = "RV CreateView";
    private static final String TRACE_HANDLE_ADAPTER_UPDATES_TAG = "RV PartialInvalidate";
    static final String TRACE_NESTED_PREFETCH_TAG = "RV Nested Prefetch";
    private static final String TRACE_ON_DATA_SET_CHANGE_LAYOUT_TAG = "RV FullInvalidate";
    private static final String TRACE_ON_LAYOUT_TAG = "RV OnLayout";
    static final String TRACE_PREFETCH_TAG = "RV Prefetch";
    static final String TRACE_SCROLL_TAG = "RV Scroll";
    public static final int UNDEFINED_DURATION = Integer.MIN_VALUE;
    static final boolean VERBOSE_TRACING = false;
    public static final int VERTICAL = 1;
    static final Interpolator sQuinticInterpolator;
    androidx.recyclerview.widget.q mAccessibilityDelegate;
    private final AccessibilityManager mAccessibilityManager;
    g mAdapter;
    a mAdapterHelper;
    boolean mAdapterUpdateDuringMeasure;
    private EdgeEffect mBottomGlow;
    private j mChildDrawingOrderCallback;
    b mChildHelper;
    boolean mClipToPadding;
    boolean mDataSetHasChangedAfterLayout;
    boolean mDispatchItemsChangedEvent;
    private int mDispatchScrollCounter;
    private int mEatenAccessibilityChangeFlags;
    private k mEdgeEffectFactory;
    boolean mEnableFastScroller;
    boolean mFirstLayoutComplete;
    androidx.recyclerview.widget.j mGapWorker;
    boolean mHasFixedSize;
    private boolean mIgnoreMotionEventTillDown;
    private int mInitialTouchX;
    private int mInitialTouchY;
    private int mInterceptRequestLayoutDepth;
    private s mInterceptingOnItemTouchListener;
    boolean mIsAttached;
    l mItemAnimator;
    private l.b mItemAnimatorListener;
    private Runnable mItemAnimatorRunner;
    final ArrayList<n> mItemDecorations;
    boolean mItemsAddedOrRemoved;
    boolean mItemsChanged;
    private int mLastTouchX;
    private int mLastTouchY;
    o mLayout;
    private int mLayoutOrScrollCounter;
    boolean mLayoutSuppressed;
    boolean mLayoutWasDefered;
    private EdgeEffect mLeftGlow;
    private final int mMaxFlingVelocity;
    private final int mMinFlingVelocity;
    private final int[] mMinMaxLayoutPositions;
    private final int[] mNestedOffsets;
    private final x mObserver;
    private List<q> mOnChildAttachStateListeners;
    private r mOnFlingListener;
    private final ArrayList<s> mOnItemTouchListeners;
    final List<D> mPendingAccessibilityImportanceChange;
    private y mPendingSavedState;
    boolean mPostedAnimatorRunner;
    j.b mPrefetchRegistry;
    private boolean mPreserveFocusAfterLayout;
    final v mRecycler;
    w mRecyclerListener;
    final int[] mReusableIntPair;
    private EdgeEffect mRightGlow;
    private float mScaledHorizontalScrollFactor;
    private float mScaledVerticalScrollFactor;
    private t mScrollListener;
    private List<t> mScrollListeners;
    private final int[] mScrollOffset;
    private int mScrollPointerId;
    private int mScrollState;
    private a.h.h.f mScrollingChildHelper;
    final A mState;
    final Rect mTempRect;
    private final Rect mTempRect2;
    final RectF mTempRectF;
    private EdgeEffect mTopGlow;
    private int mTouchSlop;
    final Runnable mUpdateChildViewsRunnable;
    private VelocityTracker mVelocityTracker;
    final C mViewFlinger;
    private final v.b mViewInfoProcessCallback;
    final androidx.recyclerview.widget.v mViewInfoStore;
    private static final int[] NESTED_SCROLLING_ATTRS = {R.attr.nestedScrollingEnabled};
    static final boolean FORCE_INVALIDATE_DISPLAY_LIST = false;
    static final boolean ALLOW_SIZE_IN_UNSPECIFIED_SPEC = true;
    static final boolean POST_UPDATES_ON_ANIMATION = true;
    static final boolean ALLOW_THREAD_GAP_WORK = true;
    private static final boolean FORCE_ABS_FOCUS_SEARCH_DIRECTION = false;
    private static final boolean IGNORE_DETACHED_FOCUSED_CHILD = false;

    public static class A {

        /* renamed from: a, reason: collision with root package name */
        int f1249a = -1;

        /* renamed from: b, reason: collision with root package name */
        int f1250b = 0;

        /* renamed from: c, reason: collision with root package name */
        int f1251c = 0;

        /* renamed from: d, reason: collision with root package name */
        int f1252d = 1;
        int e = 0;
        boolean f = false;
        boolean g = false;
        boolean h = false;
        boolean i = false;
        boolean j = false;
        boolean k = false;
        int l;
        long m;
        int n;

        void a(int i) {
            if ((this.f1252d & i) != 0) {
                return;
            }
            StringBuilder j = b.b.a.a.a.j("Layout state should be one of ");
            j.append(Integer.toBinaryString(i));
            j.append(" but it is ");
            j.append(Integer.toBinaryString(this.f1252d));
            throw new IllegalStateException(j.toString());
        }

        public int b() {
            return this.g ? this.f1250b - this.f1251c : this.e;
        }

        public String toString() {
            StringBuilder j = b.b.a.a.a.j("State{mTargetPosition=");
            j.append(this.f1249a);
            j.append(", mData=");
            j.append((Object) null);
            j.append(", mItemCount=");
            j.append(this.e);
            j.append(", mIsMeasuring=");
            j.append(this.i);
            j.append(", mPreviousLayoutItemCount=");
            j.append(this.f1250b);
            j.append(", mDeletedInvisibleItemCountSincePreviousLayout=");
            j.append(this.f1251c);
            j.append(", mStructureChanged=");
            j.append(this.f);
            j.append(", mInPreLayout=");
            j.append(this.g);
            j.append(", mRunSimpleAnimations=");
            j.append(this.j);
            j.append(", mRunPredictiveAnimations=");
            j.append(this.k);
            j.append('}');
            return j.toString();
        }
    }

    public static abstract class B {
    }

    class C implements Runnable {

        /* renamed from: a, reason: collision with root package name */
        private int f1253a;

        /* renamed from: b, reason: collision with root package name */
        private int f1254b;

        /* renamed from: c, reason: collision with root package name */
        OverScroller f1255c;

        /* renamed from: d, reason: collision with root package name */
        Interpolator f1256d;
        private boolean e;
        private boolean f;

        C() {
            Interpolator interpolator = RecyclerView.sQuinticInterpolator;
            this.f1256d = interpolator;
            this.e = false;
            this.f = false;
            this.f1255c = new OverScroller(RecyclerView.this.getContext(), interpolator);
        }

        public void a(int i, int i2) {
            RecyclerView.this.setScrollState(2);
            this.f1254b = 0;
            this.f1253a = 0;
            Interpolator interpolator = this.f1256d;
            Interpolator interpolator2 = RecyclerView.sQuinticInterpolator;
            if (interpolator != interpolator2) {
                this.f1256d = interpolator2;
                this.f1255c = new OverScroller(RecyclerView.this.getContext(), interpolator2);
            }
            this.f1255c.fling(0, 0, i, i2, RecyclerView.UNDEFINED_DURATION, Integer.MAX_VALUE, RecyclerView.UNDEFINED_DURATION, Integer.MAX_VALUE);
            b();
        }

        void b() {
            if (this.e) {
                this.f = true;
                return;
            }
            RecyclerView.this.removeCallbacks(this);
            RecyclerView recyclerView = RecyclerView.this;
            int i = a.h.h.q.e;
            recyclerView.postOnAnimation(this);
        }

        public void c(int i, int i2, int i3, Interpolator interpolator) {
            int i4;
            if (i3 == Integer.MIN_VALUE) {
                int abs = Math.abs(i);
                int abs2 = Math.abs(i2);
                boolean z = abs > abs2;
                int sqrt = (int) Math.sqrt(0);
                int sqrt2 = (int) Math.sqrt((i2 * i2) + (i * i));
                RecyclerView recyclerView = RecyclerView.this;
                int width = z ? recyclerView.getWidth() : recyclerView.getHeight();
                int i5 = width / 2;
                float f = width;
                float f2 = i5;
                float sin = (((float) Math.sin((Math.min(1.0f, (sqrt2 * 1.0f) / f) - 0.5f) * 0.47123894f)) * f2) + f2;
                if (sqrt > 0) {
                    i4 = Math.round(Math.abs(sin / sqrt) * 1000.0f) * 4;
                } else {
                    if (!z) {
                        abs = abs2;
                    }
                    i4 = (int) (((abs / f) + 1.0f) * 300.0f);
                }
                i3 = Math.min(i4, RecyclerView.MAX_SCROLL_DURATION);
            }
            int i6 = i3;
            if (interpolator == null) {
                interpolator = RecyclerView.sQuinticInterpolator;
            }
            if (this.f1256d != interpolator) {
                this.f1256d = interpolator;
                this.f1255c = new OverScroller(RecyclerView.this.getContext(), interpolator);
            }
            this.f1254b = 0;
            this.f1253a = 0;
            RecyclerView.this.setScrollState(2);
            this.f1255c.startScroll(0, 0, i, i2, i6);
            b();
        }

        public void d() {
            RecyclerView.this.removeCallbacks(this);
            this.f1255c.abortAnimation();
        }

        @Override // java.lang.Runnable
        public void run() {
            int i;
            int i2;
            RecyclerView recyclerView = RecyclerView.this;
            if (recyclerView.mLayout == null) {
                d();
                return;
            }
            this.f = false;
            this.e = true;
            recyclerView.consumePendingUpdateOperations();
            OverScroller overScroller = this.f1255c;
            if (overScroller.computeScrollOffset()) {
                int currX = overScroller.getCurrX();
                int currY = overScroller.getCurrY();
                int i3 = currX - this.f1253a;
                int i4 = currY - this.f1254b;
                this.f1253a = currX;
                this.f1254b = currY;
                RecyclerView recyclerView2 = RecyclerView.this;
                int[] iArr = recyclerView2.mReusableIntPair;
                iArr[0] = 0;
                iArr[1] = 0;
                if (recyclerView2.dispatchNestedPreScroll(i3, i4, iArr, null, 1)) {
                    int[] iArr2 = RecyclerView.this.mReusableIntPair;
                    i3 -= iArr2[0];
                    i4 -= iArr2[1];
                }
                if (RecyclerView.this.getOverScrollMode() != 2) {
                    RecyclerView.this.considerReleasingGlowsOnScroll(i3, i4);
                }
                RecyclerView recyclerView3 = RecyclerView.this;
                if (recyclerView3.mAdapter != null) {
                    int[] iArr3 = recyclerView3.mReusableIntPair;
                    iArr3[0] = 0;
                    iArr3[1] = 0;
                    recyclerView3.scrollStep(i3, i4, iArr3);
                    RecyclerView recyclerView4 = RecyclerView.this;
                    int[] iArr4 = recyclerView4.mReusableIntPair;
                    i2 = iArr4[0];
                    i = iArr4[1];
                    i3 -= i2;
                    i4 -= i;
                    z zVar = recyclerView4.mLayout.g;
                    if (zVar != null && !zVar.e() && zVar.f()) {
                        int b2 = RecyclerView.this.mState.b();
                        if (b2 == 0) {
                            zVar.l();
                        } else {
                            if (zVar.d() >= b2) {
                                zVar.j(b2 - 1);
                            }
                            zVar.g(i2, i);
                        }
                    }
                } else {
                    i = 0;
                    i2 = 0;
                }
                if (!RecyclerView.this.mItemDecorations.isEmpty()) {
                    RecyclerView.this.invalidate();
                }
                RecyclerView recyclerView5 = RecyclerView.this;
                int[] iArr5 = recyclerView5.mReusableIntPair;
                iArr5[0] = 0;
                iArr5[1] = 0;
                recyclerView5.dispatchNestedScroll(i2, i, i3, i4, null, 1, iArr5);
                RecyclerView recyclerView6 = RecyclerView.this;
                int[] iArr6 = recyclerView6.mReusableIntPair;
                int i5 = i3 - iArr6[0];
                int i6 = i4 - iArr6[1];
                if (i2 != 0 || i != 0) {
                    recyclerView6.dispatchOnScrolled(i2, i);
                }
                if (!RecyclerView.this.awakenScrollBars()) {
                    RecyclerView.this.invalidate();
                }
                boolean z = overScroller.isFinished() || (((overScroller.getCurrX() == overScroller.getFinalX()) || i5 != 0) && ((overScroller.getCurrY() == overScroller.getFinalY()) || i6 != 0));
                z zVar2 = RecyclerView.this.mLayout.g;
                if ((zVar2 != null && zVar2.e()) || !z) {
                    b();
                    RecyclerView recyclerView7 = RecyclerView.this;
                    androidx.recyclerview.widget.j jVar = recyclerView7.mGapWorker;
                    if (jVar != null) {
                        jVar.a(recyclerView7, i2, i);
                    }
                } else {
                    if (RecyclerView.this.getOverScrollMode() != 2) {
                        int currVelocity = (int) overScroller.getCurrVelocity();
                        int i7 = i5 < 0 ? -currVelocity : i5 > 0 ? currVelocity : 0;
                        if (i6 < 0) {
                            currVelocity = -currVelocity;
                        } else if (i6 <= 0) {
                            currVelocity = 0;
                        }
                        RecyclerView.this.absorbGlows(i7, currVelocity);
                    }
                    if (RecyclerView.ALLOW_THREAD_GAP_WORK) {
                        j.b bVar = RecyclerView.this.mPrefetchRegistry;
                        int[] iArr7 = bVar.f1390c;
                        if (iArr7 != null) {
                            Arrays.fill(iArr7, -1);
                        }
                        bVar.f1391d = 0;
                    }
                }
            }
            z zVar3 = RecyclerView.this.mLayout.g;
            if (zVar3 != null && zVar3.e()) {
                zVar3.g(0, 0);
            }
            this.e = false;
            if (!this.f) {
                RecyclerView.this.setScrollState(0);
                RecyclerView.this.stopNestedScroll(1);
            } else {
                RecyclerView.this.removeCallbacks(this);
                RecyclerView recyclerView8 = RecyclerView.this;
                int i8 = a.h.h.q.e;
                recyclerView8.postOnAnimation(this);
            }
        }
    }

    public static abstract class D {
        private static final List<Object> s = Collections.emptyList();

        /* renamed from: a, reason: collision with root package name */
        public final View f1257a;

        /* renamed from: b, reason: collision with root package name */
        WeakReference<RecyclerView> f1258b;
        int j;
        RecyclerView r;

        /* renamed from: c, reason: collision with root package name */
        int f1259c = -1;

        /* renamed from: d, reason: collision with root package name */
        int f1260d = -1;
        long e = -1;
        int f = -1;
        int g = -1;
        D h = null;
        D i = null;
        List<Object> k = null;
        List<Object> l = null;
        private int m = 0;
        v n = null;
        boolean o = false;
        private int p = 0;
        int q = -1;

        public D(View view) {
            if (view == null) {
                throw new IllegalArgumentException("itemView may not be null");
            }
            this.f1257a = view;
        }

        void a(Object obj) {
            if (obj == null) {
                b(1024);
                return;
            }
            if ((1024 & this.j) == 0) {
                if (this.k == null) {
                    ArrayList arrayList = new ArrayList();
                    this.k = arrayList;
                    this.l = Collections.unmodifiableList(arrayList);
                }
                this.k.add(obj);
            }
        }

        void b(int i) {
            this.j = i | this.j;
        }

        void c() {
            this.f1260d = -1;
            this.g = -1;
        }

        void d() {
            this.j &= -33;
        }

        public final int e() {
            RecyclerView recyclerView = this.r;
            if (recyclerView == null) {
                return -1;
            }
            return recyclerView.getAdapterPositionFor(this);
        }

        public final int f() {
            int i = this.g;
            return i == -1 ? this.f1259c : i;
        }

        List<Object> g() {
            if ((this.j & 1024) != 0) {
                return s;
            }
            List<Object> list = this.k;
            return (list == null || list.size() == 0) ? s : this.l;
        }

        boolean h(int i) {
            return (this.j & i) != 0;
        }

        boolean i() {
            return (this.f1257a.getParent() == null || this.f1257a.getParent() == this.r) ? false : true;
        }

        boolean j() {
            return (this.j & 1) != 0;
        }

        boolean k() {
            return (this.j & 4) != 0;
        }

        public final boolean l() {
            if ((this.j & 16) == 0) {
                View view = this.f1257a;
                int i = a.h.h.q.e;
                if (!view.hasTransientState()) {
                    return true;
                }
            }
            return false;
        }

        boolean m() {
            return (this.j & 8) != 0;
        }

        boolean n() {
            return this.n != null;
        }

        boolean o() {
            return (this.j & 256) != 0;
        }

        boolean p() {
            return (this.j & 2) != 0;
        }

        void q(int i, boolean z) {
            if (this.f1260d == -1) {
                this.f1260d = this.f1259c;
            }
            if (this.g == -1) {
                this.g = this.f1259c;
            }
            if (z) {
                this.g += i;
            }
            this.f1259c += i;
            if (this.f1257a.getLayoutParams() != null) {
                ((p) this.f1257a.getLayoutParams()).f1287c = true;
            }
        }

        void r(RecyclerView recyclerView) {
            int i = this.q;
            if (i == -1) {
                View view = this.f1257a;
                int i2 = a.h.h.q.e;
                i = view.getImportantForAccessibility();
            }
            this.p = i;
            recyclerView.setChildImportantForAccessibilityInternal(this, 4);
        }

        void s(RecyclerView recyclerView) {
            recyclerView.setChildImportantForAccessibilityInternal(this, this.p);
            this.p = 0;
        }

        void t() {
            this.j = 0;
            this.f1259c = -1;
            this.f1260d = -1;
            this.e = -1L;
            this.g = -1;
            this.m = 0;
            this.h = null;
            this.i = null;
            List<Object> list = this.k;
            if (list != null) {
                list.clear();
            }
            this.j &= -1025;
            this.p = 0;
            this.q = -1;
            RecyclerView.clearNestedRecyclerViewIfNotNested(this);
        }

        public String toString() {
            StringBuilder sb = new StringBuilder((getClass().isAnonymousClass() ? "ViewHolder" : getClass().getSimpleName()) + "{" + Integer.toHexString(hashCode()) + " position=" + this.f1259c + " id=" + this.e + ", oldPos=" + this.f1260d + ", pLpos:" + this.g);
            if (n()) {
                sb.append(" scrap ");
                sb.append(this.o ? "[changeScrap]" : "[attachedScrap]");
            }
            if (k()) {
                sb.append(" invalid");
            }
            if (!j()) {
                sb.append(" unbound");
            }
            boolean z = true;
            if ((this.j & 2) != 0) {
                sb.append(" update");
            }
            if (m()) {
                sb.append(" removed");
            }
            if (w()) {
                sb.append(" ignored");
            }
            if (o()) {
                sb.append(" tmpDetached");
            }
            if (!l()) {
                StringBuilder j = b.b.a.a.a.j(" not recyclable(");
                j.append(this.m);
                j.append(")");
                sb.append(j.toString());
            }
            if ((this.j & 512) == 0 && !k()) {
                z = false;
            }
            if (z) {
                sb.append(" undefined adapter position");
            }
            if (this.f1257a.getParent() == null) {
                sb.append(" no parent");
            }
            sb.append("}");
            return sb.toString();
        }

        void u(int i, int i2) {
            this.j = (i & i2) | (this.j & (~i2));
        }

        public final void v(boolean z) {
            int i;
            int i2 = this.m;
            int i3 = z ? i2 - 1 : i2 + 1;
            this.m = i3;
            if (i3 < 0) {
                this.m = 0;
                Log.e("View", "isRecyclable decremented below 0: unmatched pair of setIsRecyable() calls for " + this);
                return;
            }
            if (!z && i3 == 1) {
                i = this.j | 16;
            } else if (!z || i3 != 0) {
                return;
            } else {
                i = this.j & (-17);
            }
            this.j = i;
        }

        boolean w() {
            return (this.j & 128) != 0;
        }

        boolean x() {
            return (this.j & 32) != 0;
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$a, reason: case insensitive filesystem */
    class RunnableC0111a implements Runnable {
        RunnableC0111a() {
        }

        @Override // java.lang.Runnable
        public void run() {
            RecyclerView recyclerView = RecyclerView.this;
            if (!recyclerView.mFirstLayoutComplete || recyclerView.isLayoutRequested()) {
                return;
            }
            RecyclerView recyclerView2 = RecyclerView.this;
            if (!recyclerView2.mIsAttached) {
                recyclerView2.requestLayout();
            } else if (recyclerView2.mLayoutSuppressed) {
                recyclerView2.mLayoutWasDefered = true;
            } else {
                recyclerView2.consumePendingUpdateOperations();
            }
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$b, reason: case insensitive filesystem */
    class RunnableC0112b implements Runnable {
        RunnableC0112b() {
        }

        @Override // java.lang.Runnable
        public void run() {
            l lVar = RecyclerView.this.mItemAnimator;
            if (lVar != null) {
                lVar.m();
            }
            RecyclerView.this.mPostedAnimatorRunner = false;
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$c, reason: case insensitive filesystem */
    static class InterpolatorC0113c implements Interpolator {
        InterpolatorC0113c() {
        }

        @Override // android.animation.TimeInterpolator
        public float getInterpolation(float f) {
            float f2 = f - 1.0f;
            return (f2 * f2 * f2 * f2 * f2) + 1.0f;
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$d, reason: case insensitive filesystem */
    class C0114d implements v.b {
        C0114d() {
        }
    }

    class e implements b.InterfaceC0038b {
        e() {
        }

        public View a(int i) {
            return RecyclerView.this.getChildAt(i);
        }

        public int b() {
            return RecyclerView.this.getChildCount();
        }

        public void c(int i) {
            View childAt = RecyclerView.this.getChildAt(i);
            if (childAt != null) {
                RecyclerView.this.dispatchChildDetached(childAt);
                childAt.clearAnimation();
            }
            RecyclerView.this.removeViewAt(i);
        }
    }

    class f implements a.InterfaceC0037a {
        f() {
        }

        void a(a.b bVar) {
            int i = bVar.f1332a;
            if (i == 1) {
                RecyclerView recyclerView = RecyclerView.this;
                recyclerView.mLayout.B0(recyclerView, bVar.f1333b, bVar.f1335d);
                return;
            }
            if (i == 2) {
                RecyclerView recyclerView2 = RecyclerView.this;
                recyclerView2.mLayout.E0(recyclerView2, bVar.f1333b, bVar.f1335d);
            } else if (i == 4) {
                RecyclerView recyclerView3 = RecyclerView.this;
                recyclerView3.mLayout.G0(recyclerView3, bVar.f1333b, bVar.f1335d, bVar.f1334c);
            } else {
                if (i != 8) {
                    return;
                }
                RecyclerView recyclerView4 = RecyclerView.this;
                recyclerView4.mLayout.D0(recyclerView4, bVar.f1333b, bVar.f1335d, 1);
            }
        }

        public D b(int i) {
            D findViewHolderForPosition = RecyclerView.this.findViewHolderForPosition(i, true);
            if (findViewHolderForPosition == null || RecyclerView.this.mChildHelper.l(findViewHolderForPosition.f1257a)) {
                return null;
            }
            return findViewHolderForPosition;
        }

        public void c(int i, int i2, Object obj) {
            RecyclerView.this.viewRangeUpdate(i, i2, obj);
            RecyclerView.this.mItemsChanged = true;
        }
    }

    public static abstract class g<VH extends D> {

        /* renamed from: a, reason: collision with root package name */
        private final h f1266a = new h();

        /* renamed from: b, reason: collision with root package name */
        private boolean f1267b = false;

        public final void a(VH vh, int i) {
            vh.f1259c = i;
            if (this.f1267b) {
                vh.e = c(i);
            }
            vh.u(1, 519);
            Trace.beginSection(RecyclerView.TRACE_BIND_VIEW_TAG);
            vh.g();
            g(vh, i);
            List<Object> list = vh.k;
            if (list != null) {
                list.clear();
            }
            vh.j &= -1025;
            ViewGroup.LayoutParams layoutParams = vh.f1257a.getLayoutParams();
            if (layoutParams instanceof p) {
                ((p) layoutParams).f1287c = true;
            }
            Trace.endSection();
        }

        public abstract int b();

        public long c(int i) {
            return -1L;
        }

        public int d(int i) {
            return 0;
        }

        public final boolean e() {
            return this.f1267b;
        }

        public final void f() {
            this.f1266a.b();
        }

        public abstract void g(VH vh, int i);

        public abstract VH h(ViewGroup viewGroup, int i);

        public void i(VH vh) {
        }

        public void j(i iVar) {
            this.f1266a.registerObserver(iVar);
        }

        public void k(boolean z) {
            if (this.f1266a.a()) {
                throw new IllegalStateException("Cannot change whether this adapter has stable IDs while the adapter has registered observers.");
            }
            this.f1267b = z;
        }

        public void l(i iVar) {
            this.f1266a.unregisterObserver(iVar);
        }
    }

    static class h extends Observable<i> {
        h() {
        }

        public boolean a() {
            return !((Observable) this).mObservers.isEmpty();
        }

        public void b() {
            for (int size = ((Observable) this).mObservers.size() - 1; size >= 0; size--) {
                ((i) ((Observable) this).mObservers.get(size)).a();
            }
        }
    }

    public static abstract class i {
        public void a() {
        }
    }

    public interface j {
        int a(int i, int i2);
    }

    public static class k {
        protected EdgeEffect a(RecyclerView recyclerView) {
            return new EdgeEffect(recyclerView.getContext());
        }
    }

    public static abstract class l {

        /* renamed from: a, reason: collision with root package name */
        private b f1268a = null;

        /* renamed from: b, reason: collision with root package name */
        private ArrayList<a> f1269b = new ArrayList<>();

        /* renamed from: c, reason: collision with root package name */
        private long f1270c = 120;

        /* renamed from: d, reason: collision with root package name */
        private long f1271d = 120;
        private long e = 250;
        private long f = 250;

        public interface a {
            void a();
        }

        interface b {
        }

        public static class c {

            /* renamed from: a, reason: collision with root package name */
            public int f1272a;

            /* renamed from: b, reason: collision with root package name */
            public int f1273b;
        }

        static int b(D d2) {
            int i = d2.j & 14;
            if (d2.k()) {
                return 4;
            }
            if ((i & 4) != 0) {
                return i;
            }
            int i2 = d2.f1260d;
            int e = d2.e();
            return (i2 == -1 || e == -1 || i2 == e) ? i : i | 2048;
        }

        public abstract boolean a(D d2, D d3, c cVar, c cVar2);

        public final void c(D d2) {
            b bVar = this.f1268a;
            if (bVar != null) {
                m mVar = (m) bVar;
                Objects.requireNonNull(mVar);
                d2.v(true);
                if (d2.h != null && d2.i == null) {
                    d2.h = null;
                }
                d2.i = null;
                if (((d2.j & 16) != 0) || RecyclerView.this.removeAnimatingView(d2.f1257a) || !d2.o()) {
                    return;
                }
                RecyclerView.this.removeDetachedView(d2.f1257a, false);
            }
        }

        public final void d() {
            int size = this.f1269b.size();
            for (int i = 0; i < size; i++) {
                this.f1269b.get(i).a();
            }
            this.f1269b.clear();
        }

        public abstract void e(D d2);

        public abstract void f();

        public long g() {
            return this.f1270c;
        }

        public long h() {
            return this.f;
        }

        public long i() {
            return this.e;
        }

        public long j() {
            return this.f1271d;
        }

        public abstract boolean k();

        public c l(D d2) {
            c cVar = new c();
            View view = d2.f1257a;
            cVar.f1272a = view.getLeft();
            cVar.f1273b = view.getTop();
            view.getRight();
            view.getBottom();
            return cVar;
        }

        public abstract void m();

        void n(b bVar) {
            this.f1268a = bVar;
        }
    }

    private class m implements l.b {
        m() {
        }
    }

    public static abstract class n {
        public void d(Rect rect, View view, RecyclerView recyclerView, A a2) {
            ((p) view.getLayoutParams()).a();
            rect.set(0, 0, 0, 0);
        }

        public void e(Canvas canvas, RecyclerView recyclerView, A a2) {
        }

        public void f(Canvas canvas, RecyclerView recyclerView, A a2) {
        }
    }

    public static abstract class o {

        /* renamed from: a, reason: collision with root package name */
        androidx.recyclerview.widget.b f1275a;

        /* renamed from: b, reason: collision with root package name */
        RecyclerView f1276b;

        /* renamed from: c, reason: collision with root package name */
        private final u.b f1277c;

        /* renamed from: d, reason: collision with root package name */
        private final u.b f1278d;
        androidx.recyclerview.widget.u e;
        androidx.recyclerview.widget.u f;
        z g;
        boolean h;
        boolean i;
        private boolean j;
        private boolean k;
        int l;
        boolean m;
        private int n;
        private int o;
        private int p;
        private int q;

        class a implements u.b {
            a() {
            }

            @Override // androidx.recyclerview.widget.u.b
            public View a(int i) {
                androidx.recyclerview.widget.b bVar = o.this.f1275a;
                if (bVar != null) {
                    return bVar.d(i);
                }
                return null;
            }

            @Override // androidx.recyclerview.widget.u.b
            public int b() {
                return o.this.e0() - o.this.V();
            }

            @Override // androidx.recyclerview.widget.u.b
            public int c() {
                return o.this.U();
            }

            @Override // androidx.recyclerview.widget.u.b
            public int d(View view) {
                return o.this.J(view) + ((ViewGroup.MarginLayoutParams) ((p) view.getLayoutParams())).rightMargin;
            }

            @Override // androidx.recyclerview.widget.u.b
            public int e(View view) {
                return o.this.G(view) - ((ViewGroup.MarginLayoutParams) ((p) view.getLayoutParams())).leftMargin;
            }
        }

        class b implements u.b {
            b() {
            }

            @Override // androidx.recyclerview.widget.u.b
            public View a(int i) {
                androidx.recyclerview.widget.b bVar = o.this.f1275a;
                if (bVar != null) {
                    return bVar.d(i);
                }
                return null;
            }

            @Override // androidx.recyclerview.widget.u.b
            public int b() {
                return o.this.M() - o.this.T();
            }

            @Override // androidx.recyclerview.widget.u.b
            public int c() {
                return o.this.W();
            }

            @Override // androidx.recyclerview.widget.u.b
            public int d(View view) {
                return o.this.E(view) + ((ViewGroup.MarginLayoutParams) ((p) view.getLayoutParams())).bottomMargin;
            }

            @Override // androidx.recyclerview.widget.u.b
            public int e(View view) {
                return o.this.K(view) - ((ViewGroup.MarginLayoutParams) ((p) view.getLayoutParams())).topMargin;
            }
        }

        public interface c {
        }

        public static class d {

            /* renamed from: a, reason: collision with root package name */
            public int f1281a;

            /* renamed from: b, reason: collision with root package name */
            public int f1282b;

            /* renamed from: c, reason: collision with root package name */
            public boolean f1283c;

            /* renamed from: d, reason: collision with root package name */
            public boolean f1284d;
        }

        public o() {
            a aVar = new a();
            this.f1277c = aVar;
            b bVar = new b();
            this.f1278d = bVar;
            this.e = new androidx.recyclerview.widget.u(aVar);
            this.f = new androidx.recyclerview.widget.u(bVar);
            this.h = false;
            this.i = false;
            this.j = true;
            this.k = true;
        }

        /* JADX WARN: Code restructure failed: missing block: B:7:0x0017, code lost:
        
            if (r5 == 1073741824) goto L14;
         */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public static int C(int r4, int r5, int r6, int r7, boolean r8) {
            /*
                int r4 = r4 - r6
                r6 = 0
                int r4 = java.lang.Math.max(r6, r4)
                r0 = -2
                r1 = -1
                r2 = -2147483648(0xffffffff80000000, float:-0.0)
                r3 = 1073741824(0x40000000, float:2.0)
                if (r8 == 0) goto L1a
                if (r7 < 0) goto L11
                goto L1c
            L11:
                if (r7 != r1) goto L2f
                if (r5 == r2) goto L20
                if (r5 == 0) goto L2f
                if (r5 == r3) goto L20
                goto L2f
            L1a:
                if (r7 < 0) goto L1e
            L1c:
                r5 = r3
                goto L31
            L1e:
                if (r7 != r1) goto L22
            L20:
                r7 = r4
                goto L31
            L22:
                if (r7 != r0) goto L2f
                if (r5 == r2) goto L2c
                if (r5 != r3) goto L29
                goto L2c
            L29:
                r7 = r4
                r5 = r6
                goto L31
            L2c:
                r7 = r4
                r5 = r2
                goto L31
            L2f:
                r5 = r6
                r7 = r5
            L31:
                int r4 = android.view.View.MeasureSpec.makeMeasureSpec(r7, r5)
                return r4
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.o.C(int, int, int, int, boolean):int");
        }

        public static d Y(Context context, AttributeSet attributeSet, int i, int i2) {
            d dVar = new d();
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, a.n.a.f465a, i, i2);
            dVar.f1281a = obtainStyledAttributes.getInt(0, 1);
            dVar.f1282b = obtainStyledAttributes.getInt(10, 1);
            dVar.f1283c = obtainStyledAttributes.getBoolean(9, false);
            dVar.f1284d = obtainStyledAttributes.getBoolean(11, false);
            obtainStyledAttributes.recycle();
            return dVar;
        }

        private void f(View view, int i, boolean z) {
            D childViewHolderInt = RecyclerView.getChildViewHolderInt(view);
            if (z || childViewHolderInt.m()) {
                this.f1276b.mViewInfoStore.a(childViewHolderInt);
            } else {
                this.f1276b.mViewInfoStore.h(childViewHolderInt);
            }
            p pVar = (p) view.getLayoutParams();
            if (childViewHolderInt.x() || childViewHolderInt.n()) {
                if (childViewHolderInt.n()) {
                    childViewHolderInt.n.n(childViewHolderInt);
                } else {
                    childViewHolderInt.d();
                }
                this.f1275a.b(view, i, view.getLayoutParams(), false);
            } else if (view.getParent() == this.f1276b) {
                int k = this.f1275a.k(view);
                if (i == -1) {
                    i = this.f1275a.e();
                }
                if (k == -1) {
                    StringBuilder j = b.b.a.a.a.j("Added View has RecyclerView as parent but view is not a real child. Unfiltered index:");
                    j.append(this.f1276b.indexOfChild(view));
                    j.append(this.f1276b.exceptionLabel());
                    throw new IllegalStateException(j.toString());
                }
                if (k != i) {
                    o oVar = this.f1276b.mLayout;
                    androidx.recyclerview.widget.b bVar = oVar.f1275a;
                    View d2 = bVar != null ? bVar.d(k) : null;
                    if (d2 == null) {
                        throw new IllegalArgumentException("Cannot move a child from non-existing index:" + k + oVar.f1276b.toString());
                    }
                    androidx.recyclerview.widget.b bVar2 = oVar.f1275a;
                    if (bVar2 != null) {
                        bVar2.d(k);
                    }
                    oVar.f1275a.c(k);
                    p pVar2 = (p) d2.getLayoutParams();
                    D childViewHolderInt2 = RecyclerView.getChildViewHolderInt(d2);
                    if (childViewHolderInt2.m()) {
                        oVar.f1276b.mViewInfoStore.a(childViewHolderInt2);
                    } else {
                        oVar.f1276b.mViewInfoStore.h(childViewHolderInt2);
                    }
                    oVar.f1275a.b(d2, i, pVar2, childViewHolderInt2.m());
                }
            } else {
                this.f1275a.a(view, i, false);
                pVar.f1287c = true;
                z zVar = this.g;
                if (zVar != null && zVar.f()) {
                    this.g.h(view);
                }
            }
            if (pVar.f1288d) {
                childViewHolderInt.f1257a.invalidate();
                pVar.f1288d = false;
            }
        }

        private static boolean j0(int i, int i2, int i3) {
            int mode = View.MeasureSpec.getMode(i2);
            int size = View.MeasureSpec.getSize(i2);
            if (i3 > 0 && i != i3) {
                return false;
            }
            if (mode == Integer.MIN_VALUE) {
                return size >= i;
            }
            if (mode != 0) {
                return mode == 1073741824 && size == i;
            }
            return true;
        }

        public static int k(int i, int i2, int i3) {
            int mode = View.MeasureSpec.getMode(i);
            int size = View.MeasureSpec.getSize(i);
            return mode != Integer.MIN_VALUE ? mode != 1073741824 ? Math.max(i2, i3) : size : Math.min(size, Math.max(i2, i3));
        }

        public View A(int i) {
            androidx.recyclerview.widget.b bVar = this.f1275a;
            if (bVar != null) {
                return bVar.d(i);
            }
            return null;
        }

        public View A0() {
            return null;
        }

        public int B() {
            androidx.recyclerview.widget.b bVar = this.f1275a;
            if (bVar != null) {
                return bVar.e();
            }
            return 0;
        }

        public void B0(RecyclerView recyclerView, int i, int i2) {
        }

        public void C0(RecyclerView recyclerView) {
        }

        public int D(v vVar, A a2) {
            RecyclerView recyclerView = this.f1276b;
            if (recyclerView == null || recyclerView.mAdapter == null || !h()) {
                return 1;
            }
            return this.f1276b.mAdapter.b();
        }

        public void D0(RecyclerView recyclerView, int i, int i2, int i3) {
        }

        public int E(View view) {
            return view.getBottom() + ((p) view.getLayoutParams()).f1286b.bottom;
        }

        public void E0(RecyclerView recyclerView, int i, int i2) {
        }

        public void F(View view, Rect rect) {
            RecyclerView.getDecoratedBoundsWithMarginsInt(view, rect);
        }

        public void F0() {
        }

        public int G(View view) {
            return view.getLeft() - ((p) view.getLayoutParams()).f1286b.left;
        }

        public void G0(RecyclerView recyclerView, int i, int i2, Object obj) {
            F0();
        }

        public int H(View view) {
            Rect rect = ((p) view.getLayoutParams()).f1286b;
            return view.getMeasuredHeight() + rect.top + rect.bottom;
        }

        public void H0(v vVar, A a2) {
            Log.e(RecyclerView.TAG, "You must override onLayoutChildren(Recycler recycler, State state) ");
        }

        public int I(View view) {
            Rect rect = ((p) view.getLayoutParams()).f1286b;
            return view.getMeasuredWidth() + rect.left + rect.right;
        }

        public void I0(A a2) {
        }

        public int J(View view) {
            return view.getRight() + ((p) view.getLayoutParams()).f1286b.right;
        }

        public void J0(int i, int i2) {
            this.f1276b.defaultOnMeasure(i, i2);
        }

        public int K(View view) {
            return view.getTop() - ((p) view.getLayoutParams()).f1286b.top;
        }

        @Deprecated
        public boolean K0(RecyclerView recyclerView) {
            z zVar = this.g;
            return (zVar != null && zVar.f()) || recyclerView.isComputingLayout();
        }

        public View L() {
            View focusedChild;
            RecyclerView recyclerView = this.f1276b;
            if (recyclerView == null || (focusedChild = recyclerView.getFocusedChild()) == null || this.f1275a.f1338c.contains(focusedChild)) {
                return null;
            }
            return focusedChild;
        }

        public boolean L0(RecyclerView recyclerView, View view, View view2) {
            return K0(recyclerView);
        }

        public int M() {
            return this.q;
        }

        public void M0(Parcelable parcelable) {
        }

        public int N() {
            return this.o;
        }

        public Parcelable N0() {
            return null;
        }

        public int O() {
            RecyclerView recyclerView = this.f1276b;
            g adapter = recyclerView != null ? recyclerView.getAdapter() : null;
            if (adapter != null) {
                return adapter.b();
            }
            return 0;
        }

        public void O0(int i) {
        }

        public int P() {
            RecyclerView recyclerView = this.f1276b;
            int i = a.h.h.q.e;
            return recyclerView.getLayoutDirection();
        }

        public boolean P0(v vVar, A a2, int i, Bundle bundle) {
            int W;
            int U;
            int i2;
            int i3;
            RecyclerView recyclerView = this.f1276b;
            if (recyclerView == null) {
                return false;
            }
            if (i == 4096) {
                W = recyclerView.canScrollVertically(1) ? (this.q - W()) - T() : 0;
                if (this.f1276b.canScrollHorizontally(1)) {
                    U = (this.p - U()) - V();
                    i2 = W;
                    i3 = U;
                }
                i2 = W;
                i3 = 0;
            } else if (i != 8192) {
                i3 = 0;
                i2 = 0;
            } else {
                W = recyclerView.canScrollVertically(-1) ? -((this.q - W()) - T()) : 0;
                if (this.f1276b.canScrollHorizontally(-1)) {
                    U = -((this.p - U()) - V());
                    i2 = W;
                    i3 = U;
                }
                i2 = W;
                i3 = 0;
            }
            if (i2 == 0 && i3 == 0) {
                return false;
            }
            this.f1276b.smoothScrollBy(i3, i2, null, RecyclerView.UNDEFINED_DURATION, true);
            return true;
        }

        public int Q(View view) {
            return ((p) view.getLayoutParams()).f1286b.left;
        }

        public boolean Q0() {
            return false;
        }

        public int R() {
            RecyclerView recyclerView = this.f1276b;
            int i = a.h.h.q.e;
            return recyclerView.getMinimumHeight();
        }

        public void R0(v vVar) {
            for (int B = B() - 1; B >= 0; B--) {
                if (!RecyclerView.getChildViewHolderInt(A(B)).w()) {
                    U0(B, vVar);
                }
            }
        }

        public int S() {
            RecyclerView recyclerView = this.f1276b;
            int i = a.h.h.q.e;
            return recyclerView.getMinimumWidth();
        }

        void S0(v vVar) {
            int size = vVar.f1295a.size();
            for (int i = size - 1; i >= 0; i--) {
                View view = vVar.f1295a.get(i).f1257a;
                D childViewHolderInt = RecyclerView.getChildViewHolderInt(view);
                if (!childViewHolderInt.w()) {
                    childViewHolderInt.v(false);
                    if (childViewHolderInt.o()) {
                        this.f1276b.removeDetachedView(view, false);
                    }
                    l lVar = this.f1276b.mItemAnimator;
                    if (lVar != null) {
                        lVar.e(childViewHolderInt);
                    }
                    childViewHolderInt.v(true);
                    D childViewHolderInt2 = RecyclerView.getChildViewHolderInt(view);
                    childViewHolderInt2.n = null;
                    childViewHolderInt2.o = false;
                    childViewHolderInt2.d();
                    vVar.j(childViewHolderInt2);
                }
            }
            vVar.f1295a.clear();
            ArrayList<D> arrayList = vVar.f1296b;
            if (arrayList != null) {
                arrayList.clear();
            }
            if (size > 0) {
                this.f1276b.invalidate();
            }
        }

        public int T() {
            RecyclerView recyclerView = this.f1276b;
            if (recyclerView != null) {
                return recyclerView.getPaddingBottom();
            }
            return 0;
        }

        public void T0(View view, v vVar) {
            this.f1275a.m(view);
            vVar.i(view);
        }

        public int U() {
            RecyclerView recyclerView = this.f1276b;
            if (recyclerView != null) {
                return recyclerView.getPaddingLeft();
            }
            return 0;
        }

        public void U0(int i, v vVar) {
            androidx.recyclerview.widget.b bVar = this.f1275a;
            View d2 = bVar != null ? bVar.d(i) : null;
            androidx.recyclerview.widget.b bVar2 = this.f1275a;
            if ((bVar2 != null ? bVar2.d(i) : null) != null) {
                this.f1275a.n(i);
            }
            vVar.i(d2);
        }

        public int V() {
            RecyclerView recyclerView = this.f1276b;
            if (recyclerView != null) {
                return recyclerView.getPaddingRight();
            }
            return 0;
        }

        /* JADX WARN: Code restructure failed: missing block: B:12:0x00b3, code lost:
        
            if (r9 == false) goto L33;
         */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public boolean V0(androidx.recyclerview.widget.RecyclerView r10, android.view.View r11, android.graphics.Rect r12, boolean r13, boolean r14) {
            /*
                r9 = this;
                r0 = 2
                int[] r0 = new int[r0]
                int r1 = r9.U()
                int r2 = r9.W()
                int r3 = r9.p
                int r4 = r9.V()
                int r3 = r3 - r4
                int r4 = r9.q
                int r5 = r9.T()
                int r4 = r4 - r5
                int r5 = r11.getLeft()
                int r6 = r12.left
                int r5 = r5 + r6
                int r6 = r11.getScrollX()
                int r5 = r5 - r6
                int r6 = r11.getTop()
                int r7 = r12.top
                int r6 = r6 + r7
                int r11 = r11.getScrollY()
                int r6 = r6 - r11
                int r11 = r12.width()
                int r11 = r11 + r5
                int r12 = r12.height()
                int r12 = r12 + r6
                int r5 = r5 - r1
                r1 = 0
                int r7 = java.lang.Math.min(r1, r5)
                int r6 = r6 - r2
                int r2 = java.lang.Math.min(r1, r6)
                int r11 = r11 - r3
                int r3 = java.lang.Math.max(r1, r11)
                int r12 = r12 - r4
                int r12 = java.lang.Math.max(r1, r12)
                int r4 = r9.P()
                r8 = 1
                if (r4 != r8) goto L5f
                if (r3 == 0) goto L5a
                goto L67
            L5a:
                int r3 = java.lang.Math.max(r7, r11)
                goto L67
            L5f:
                if (r7 == 0) goto L62
                goto L66
            L62:
                int r7 = java.lang.Math.min(r5, r3)
            L66:
                r3 = r7
            L67:
                if (r2 == 0) goto L6a
                goto L6e
            L6a:
                int r2 = java.lang.Math.min(r6, r12)
            L6e:
                r0[r1] = r3
                r0[r8] = r2
                r11 = r0[r1]
                r12 = r0[r8]
                if (r14 == 0) goto Lb5
                android.view.View r14 = r10.getFocusedChild()
                if (r14 != 0) goto L80
            L7e:
                r9 = r1
                goto Lb3
            L80:
                int r0 = r9.U()
                int r2 = r9.W()
                int r3 = r9.p
                int r4 = r9.V()
                int r3 = r3 - r4
                int r4 = r9.q
                int r5 = r9.T()
                int r4 = r4 - r5
                androidx.recyclerview.widget.RecyclerView r9 = r9.f1276b
                android.graphics.Rect r9 = r9.mTempRect
                androidx.recyclerview.widget.RecyclerView.getDecoratedBoundsWithMarginsInt(r14, r9)
                int r14 = r9.left
                int r14 = r14 - r11
                if (r14 >= r3) goto L7e
                int r14 = r9.right
                int r14 = r14 - r11
                if (r14 <= r0) goto L7e
                int r14 = r9.top
                int r14 = r14 - r12
                if (r14 >= r4) goto L7e
                int r9 = r9.bottom
                int r9 = r9 - r12
                if (r9 > r2) goto Lb2
                goto L7e
            Lb2:
                r9 = r8
            Lb3:
                if (r9 == 0) goto Lba
            Lb5:
                if (r11 != 0) goto Lbb
                if (r12 == 0) goto Lba
                goto Lbb
            Lba:
                return r1
            Lbb:
                if (r13 == 0) goto Lc1
                r10.scrollBy(r11, r12)
                goto Lc4
            Lc1:
                r10.smoothScrollBy(r11, r12)
            Lc4:
                return r8
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.o.V0(androidx.recyclerview.widget.RecyclerView, android.view.View, android.graphics.Rect, boolean, boolean):boolean");
        }

        public int W() {
            RecyclerView recyclerView = this.f1276b;
            if (recyclerView != null) {
                return recyclerView.getPaddingTop();
            }
            return 0;
        }

        public void W0() {
            RecyclerView recyclerView = this.f1276b;
            if (recyclerView != null) {
                recyclerView.requestLayout();
            }
        }

        public int X(View view) {
            return ((p) view.getLayoutParams()).a();
        }

        public int X0(int i, v vVar, A a2) {
            return 0;
        }

        public void Y0(int i) {
        }

        public int Z(View view) {
            return ((p) view.getLayoutParams()).f1286b.right;
        }

        public int Z0(int i, v vVar, A a2) {
            return 0;
        }

        public int a0(v vVar, A a2) {
            RecyclerView recyclerView = this.f1276b;
            if (recyclerView == null || recyclerView.mAdapter == null || !i()) {
                return 1;
            }
            return this.f1276b.mAdapter.b();
        }

        void a1(RecyclerView recyclerView) {
            b1(View.MeasureSpec.makeMeasureSpec(recyclerView.getWidth(), 1073741824), View.MeasureSpec.makeMeasureSpec(recyclerView.getHeight(), 1073741824));
        }

        public void b(View view) {
            f(view, -1, true);
        }

        public int b0() {
            return 0;
        }

        void b1(int i, int i2) {
            this.p = View.MeasureSpec.getSize(i);
            int mode = View.MeasureSpec.getMode(i);
            this.n = mode;
            if (mode == 0 && !RecyclerView.ALLOW_SIZE_IN_UNSPECIFIED_SPEC) {
                this.p = 0;
            }
            this.q = View.MeasureSpec.getSize(i2);
            int mode2 = View.MeasureSpec.getMode(i2);
            this.o = mode2;
            if (mode2 != 0 || RecyclerView.ALLOW_SIZE_IN_UNSPECIFIED_SPEC) {
                return;
            }
            this.q = 0;
        }

        public void c(View view, int i) {
            f(view, i, true);
        }

        public int c0(View view) {
            return ((p) view.getLayoutParams()).f1286b.top;
        }

        public void c1(Rect rect, int i, int i2) {
            int V = V() + U() + rect.width();
            int T = T() + W() + rect.height();
            this.f1276b.setMeasuredDimension(k(i, V, S()), k(i2, T, R()));
        }

        public void d(View view) {
            f(view, -1, false);
        }

        public void d0(View view, boolean z, Rect rect) {
            Matrix matrix;
            if (z) {
                Rect rect2 = ((p) view.getLayoutParams()).f1286b;
                rect.set(-rect2.left, -rect2.top, view.getWidth() + rect2.right, view.getHeight() + rect2.bottom);
            } else {
                rect.set(0, 0, view.getWidth(), view.getHeight());
            }
            if (this.f1276b != null && (matrix = view.getMatrix()) != null && !matrix.isIdentity()) {
                RectF rectF = this.f1276b.mTempRectF;
                rectF.set(rect);
                matrix.mapRect(rectF);
                rect.set((int) Math.floor(rectF.left), (int) Math.floor(rectF.top), (int) Math.ceil(rectF.right), (int) Math.ceil(rectF.bottom));
            }
            rect.offset(view.getLeft(), view.getTop());
        }

        void d1(int i, int i2) {
            int B = B();
            if (B == 0) {
                this.f1276b.defaultOnMeasure(i, i2);
                return;
            }
            int i3 = RecyclerView.UNDEFINED_DURATION;
            int i4 = Integer.MAX_VALUE;
            int i5 = Integer.MAX_VALUE;
            int i6 = Integer.MIN_VALUE;
            for (int i7 = 0; i7 < B; i7++) {
                View A = A(i7);
                Rect rect = this.f1276b.mTempRect;
                RecyclerView.getDecoratedBoundsWithMarginsInt(A, rect);
                int i8 = rect.left;
                if (i8 < i4) {
                    i4 = i8;
                }
                int i9 = rect.right;
                if (i9 > i3) {
                    i3 = i9;
                }
                int i10 = rect.top;
                if (i10 < i5) {
                    i5 = i10;
                }
                int i11 = rect.bottom;
                if (i11 > i6) {
                    i6 = i11;
                }
            }
            this.f1276b.mTempRect.set(i4, i5, i3, i6);
            c1(this.f1276b.mTempRect, i, i2);
        }

        public void e(View view, int i) {
            f(view, i, false);
        }

        public int e0() {
            return this.p;
        }

        void e1(RecyclerView recyclerView) {
            int height;
            if (recyclerView == null) {
                this.f1276b = null;
                this.f1275a = null;
                height = 0;
                this.p = 0;
            } else {
                this.f1276b = recyclerView;
                this.f1275a = recyclerView.mChildHelper;
                this.p = recyclerView.getWidth();
                height = recyclerView.getHeight();
            }
            this.q = height;
            this.n = 1073741824;
            this.o = 1073741824;
        }

        public int f0() {
            return this.n;
        }

        boolean f1(View view, int i, int i2, p pVar) {
            return (!view.isLayoutRequested() && this.j && j0(view.getWidth(), i, ((ViewGroup.MarginLayoutParams) pVar).width) && j0(view.getHeight(), i2, ((ViewGroup.MarginLayoutParams) pVar).height)) ? false : true;
        }

        public void g(String str) {
            RecyclerView recyclerView = this.f1276b;
            if (recyclerView != null) {
                recyclerView.assertNotInLayoutOrScroll(str);
            }
        }

        public boolean g0() {
            return false;
        }

        boolean g1() {
            return false;
        }

        public boolean h() {
            return false;
        }

        public final boolean h0() {
            return this.k;
        }

        boolean h1(View view, int i, int i2, p pVar) {
            return (this.j && j0(view.getMeasuredWidth(), i, ((ViewGroup.MarginLayoutParams) pVar).width) && j0(view.getMeasuredHeight(), i2, ((ViewGroup.MarginLayoutParams) pVar).height)) ? false : true;
        }

        public boolean i() {
            return false;
        }

        public boolean i0() {
            return false;
        }

        public void i1(RecyclerView recyclerView, A a2, int i) {
            Log.e(RecyclerView.TAG, "You must override smoothScrollToPosition to support smooth scrolling");
        }

        public boolean j(p pVar) {
            return pVar != null;
        }

        public void j1(z zVar) {
            z zVar2 = this.g;
            if (zVar2 != null && zVar != zVar2 && zVar2.f()) {
                this.g.l();
            }
            this.g = zVar;
            zVar.k(this.f1276b, this);
        }

        public boolean k0(View view, boolean z) {
            boolean z2 = this.e.b(view, 24579) && this.f.b(view, 24579);
            return z ? z2 : !z2;
        }

        public boolean k1() {
            return false;
        }

        public void l(int i, int i2, A a2, c cVar) {
        }

        public void l0(View view, int i, int i2, int i3, int i4) {
            p pVar = (p) view.getLayoutParams();
            Rect rect = pVar.f1286b;
            view.layout(i + rect.left + ((ViewGroup.MarginLayoutParams) pVar).leftMargin, i2 + rect.top + ((ViewGroup.MarginLayoutParams) pVar).topMargin, (i3 - rect.right) - ((ViewGroup.MarginLayoutParams) pVar).rightMargin, (i4 - rect.bottom) - ((ViewGroup.MarginLayoutParams) pVar).bottomMargin);
        }

        public void m(int i, c cVar) {
        }

        public void m0(View view, int i, int i2) {
            p pVar = (p) view.getLayoutParams();
            Rect itemDecorInsetsForChild = this.f1276b.getItemDecorInsetsForChild(view);
            int i3 = itemDecorInsetsForChild.left + itemDecorInsetsForChild.right + i;
            int i4 = itemDecorInsetsForChild.top + itemDecorInsetsForChild.bottom + i2;
            int C = C(this.p, this.n, V() + U() + ((ViewGroup.MarginLayoutParams) pVar).leftMargin + ((ViewGroup.MarginLayoutParams) pVar).rightMargin + i3, ((ViewGroup.MarginLayoutParams) pVar).width, h());
            int C2 = C(this.q, this.o, T() + W() + ((ViewGroup.MarginLayoutParams) pVar).topMargin + ((ViewGroup.MarginLayoutParams) pVar).bottomMargin + i4, ((ViewGroup.MarginLayoutParams) pVar).height, i());
            if (f1(view, C, C2, pVar)) {
                view.measure(C, C2);
            }
        }

        public int n(A a2) {
            return 0;
        }

        public void n0(int i) {
            RecyclerView recyclerView = this.f1276b;
            if (recyclerView != null) {
                recyclerView.offsetChildrenHorizontal(i);
            }
        }

        public int o(A a2) {
            return 0;
        }

        public void o0(int i) {
            RecyclerView recyclerView = this.f1276b;
            if (recyclerView != null) {
                recyclerView.offsetChildrenVertical(i);
            }
        }

        public int p(A a2) {
            return 0;
        }

        public void p0() {
        }

        public int q(A a2) {
            return 0;
        }

        public boolean q0() {
            return false;
        }

        public int r(A a2) {
            return 0;
        }

        public void r0() {
        }

        public int s(A a2) {
            return 0;
        }

        @Deprecated
        public void s0() {
        }

        public void t(v vVar) {
            int B = B();
            while (true) {
                B--;
                if (B < 0) {
                    return;
                }
                View A = A(B);
                D childViewHolderInt = RecyclerView.getChildViewHolderInt(A);
                if (!childViewHolderInt.w()) {
                    if (!childViewHolderInt.k() || childViewHolderInt.m() || this.f1276b.mAdapter.e()) {
                        androidx.recyclerview.widget.b bVar = this.f1275a;
                        if (bVar != null) {
                            bVar.d(B);
                        }
                        this.f1275a.c(B);
                        vVar.k(A);
                        this.f1276b.mViewInfoStore.h(childViewHolderInt);
                    } else {
                        androidx.recyclerview.widget.b bVar2 = this.f1275a;
                        if ((bVar2 != null ? bVar2.d(B) : null) != null) {
                            this.f1275a.n(B);
                        }
                        vVar.j(childViewHolderInt);
                    }
                }
            }
        }

        public void t0(RecyclerView recyclerView, v vVar) {
            s0();
        }

        public View u(View view) {
            View findContainingItemView;
            RecyclerView recyclerView = this.f1276b;
            if (recyclerView == null || (findContainingItemView = recyclerView.findContainingItemView(view)) == null || this.f1275a.f1338c.contains(findContainingItemView)) {
                return null;
            }
            return findContainingItemView;
        }

        public View u0(View view, int i, v vVar, A a2) {
            return null;
        }

        public View v(int i) {
            int B = B();
            for (int i2 = 0; i2 < B; i2++) {
                View A = A(i2);
                D childViewHolderInt = RecyclerView.getChildViewHolderInt(A);
                if (childViewHolderInt != null && childViewHolderInt.f() == i && !childViewHolderInt.w() && (this.f1276b.mState.g || !childViewHolderInt.m())) {
                    return A;
                }
            }
            return null;
        }

        public void v0(AccessibilityEvent accessibilityEvent) {
            RecyclerView recyclerView = this.f1276b;
            v vVar = recyclerView.mRecycler;
            A a2 = recyclerView.mState;
            w0(accessibilityEvent);
        }

        public abstract p w();

        public void w0(AccessibilityEvent accessibilityEvent) {
            RecyclerView recyclerView = this.f1276b;
            if (recyclerView == null || accessibilityEvent == null) {
                return;
            }
            boolean z = true;
            if (!recyclerView.canScrollVertically(1) && !this.f1276b.canScrollVertically(-1) && !this.f1276b.canScrollHorizontally(-1) && !this.f1276b.canScrollHorizontally(1)) {
                z = false;
            }
            accessibilityEvent.setScrollable(z);
            g gVar = this.f1276b.mAdapter;
            if (gVar != null) {
                accessibilityEvent.setItemCount(gVar.b());
            }
        }

        public p x(Context context, AttributeSet attributeSet) {
            return new p(context, attributeSet);
        }

        public void x0(v vVar, A a2, a.h.h.x.b bVar) {
            if (this.f1276b.canScrollVertically(-1) || this.f1276b.canScrollHorizontally(-1)) {
                bVar.a(8192);
                bVar.i0(true);
            }
            if (this.f1276b.canScrollVertically(1) || this.f1276b.canScrollHorizontally(1)) {
                bVar.a(4096);
                bVar.i0(true);
            }
            bVar.Q(b.C0011b.b(a0(vVar, a2), D(vVar, a2), i0(), b0()));
        }

        public p y(ViewGroup.LayoutParams layoutParams) {
            return layoutParams instanceof p ? new p((p) layoutParams) : layoutParams instanceof ViewGroup.MarginLayoutParams ? new p((ViewGroup.MarginLayoutParams) layoutParams) : new p(layoutParams);
        }

        void y0(View view, a.h.h.x.b bVar) {
            D childViewHolderInt = RecyclerView.getChildViewHolderInt(view);
            if (childViewHolderInt == null || childViewHolderInt.m() || this.f1275a.l(childViewHolderInt.f1257a)) {
                return;
            }
            RecyclerView recyclerView = this.f1276b;
            z0(recyclerView.mRecycler, recyclerView.mState, view, bVar);
        }

        public int z(View view) {
            return ((p) view.getLayoutParams()).f1286b.bottom;
        }

        public void z0(v vVar, A a2, View view, a.h.h.x.b bVar) {
            bVar.R(b.c.a(i() ? X(view) : 0, 1, h() ? X(view) : 0, 1, false, false));
        }
    }

    public static class p extends ViewGroup.MarginLayoutParams {

        /* renamed from: a, reason: collision with root package name */
        D f1285a;

        /* renamed from: b, reason: collision with root package name */
        final Rect f1286b;

        /* renamed from: c, reason: collision with root package name */
        boolean f1287c;

        /* renamed from: d, reason: collision with root package name */
        boolean f1288d;

        public p(int i, int i2) {
            super(i, i2);
            this.f1286b = new Rect();
            this.f1287c = true;
            this.f1288d = false;
        }

        public p(Context context, AttributeSet attributeSet) {
            super(context, attributeSet);
            this.f1286b = new Rect();
            this.f1287c = true;
            this.f1288d = false;
        }

        public p(ViewGroup.LayoutParams layoutParams) {
            super(layoutParams);
            this.f1286b = new Rect();
            this.f1287c = true;
            this.f1288d = false;
        }

        public p(ViewGroup.MarginLayoutParams marginLayoutParams) {
            super(marginLayoutParams);
            this.f1286b = new Rect();
            this.f1287c = true;
            this.f1288d = false;
        }

        public p(p pVar) {
            super((ViewGroup.LayoutParams) pVar);
            this.f1286b = new Rect();
            this.f1287c = true;
            this.f1288d = false;
        }

        public int a() {
            return this.f1285a.f();
        }

        public boolean b() {
            return this.f1285a.p();
        }

        public boolean c() {
            return this.f1285a.m();
        }
    }

    public interface q {
        void a(View view);

        void b(View view);
    }

    public static abstract class r {
    }

    public interface s {
        boolean a(RecyclerView recyclerView, MotionEvent motionEvent);

        void b(RecyclerView recyclerView, MotionEvent motionEvent);

        void c(boolean z);
    }

    public static abstract class t {
        public void a(RecyclerView recyclerView, int i) {
        }

        public void b(RecyclerView recyclerView, int i, int i2) {
        }
    }

    public static class u {

        /* renamed from: a, reason: collision with root package name */
        SparseArray<a> f1289a = new SparseArray<>();

        /* renamed from: b, reason: collision with root package name */
        private int f1290b = 0;

        static class a {

            /* renamed from: a, reason: collision with root package name */
            final ArrayList<D> f1291a = new ArrayList<>();

            /* renamed from: b, reason: collision with root package name */
            int f1292b = 5;

            /* renamed from: c, reason: collision with root package name */
            long f1293c = 0;

            /* renamed from: d, reason: collision with root package name */
            long f1294d = 0;

            a() {
            }
        }

        private a e(int i) {
            a aVar = this.f1289a.get(i);
            if (aVar != null) {
                return aVar;
            }
            a aVar2 = new a();
            this.f1289a.put(i, aVar2);
            return aVar2;
        }

        void a() {
            this.f1290b++;
        }

        void b() {
            this.f1290b--;
        }

        void c(int i, long j) {
            a e = e(i);
            e.f1294d = h(e.f1294d, j);
        }

        void d(int i, long j) {
            a e = e(i);
            e.f1293c = h(e.f1293c, j);
        }

        void f(g gVar, g gVar2, boolean z) {
            if (gVar != null) {
                this.f1290b--;
            }
            if (!z && this.f1290b == 0) {
                for (int i = 0; i < this.f1289a.size(); i++) {
                    this.f1289a.valueAt(i).f1291a.clear();
                }
            }
            if (gVar2 != null) {
                this.f1290b++;
            }
        }

        public void g(D d2) {
            int i = d2.f;
            ArrayList<D> arrayList = e(i).f1291a;
            if (this.f1289a.get(i).f1292b <= arrayList.size()) {
                return;
            }
            d2.t();
            arrayList.add(d2);
        }

        long h(long j, long j2) {
            if (j == 0) {
                return j2;
            }
            return (j2 / 4) + ((j / 4) * 3);
        }

        boolean i(int i, long j, long j2) {
            long j3 = e(i).f1294d;
            return j3 == 0 || j + j3 < j2;
        }

        boolean j(int i, long j, long j2) {
            long j3 = e(i).f1293c;
            return j3 == 0 || j + j3 < j2;
        }
    }

    public final class v {

        /* renamed from: a, reason: collision with root package name */
        final ArrayList<D> f1295a;

        /* renamed from: b, reason: collision with root package name */
        ArrayList<D> f1296b;

        /* renamed from: c, reason: collision with root package name */
        final ArrayList<D> f1297c;

        /* renamed from: d, reason: collision with root package name */
        private final List<D> f1298d;
        private int e;
        int f;
        u g;

        public v() {
            ArrayList<D> arrayList = new ArrayList<>();
            this.f1295a = arrayList;
            this.f1296b = null;
            this.f1297c = new ArrayList<>();
            this.f1298d = Collections.unmodifiableList(arrayList);
            this.e = 2;
            this.f = 2;
        }

        private void f(ViewGroup viewGroup, boolean z) {
            for (int childCount = viewGroup.getChildCount() - 1; childCount >= 0; childCount--) {
                View childAt = viewGroup.getChildAt(childCount);
                if (childAt instanceof ViewGroup) {
                    f((ViewGroup) childAt, true);
                }
            }
            if (z) {
                if (viewGroup.getVisibility() == 4) {
                    viewGroup.setVisibility(0);
                    viewGroup.setVisibility(4);
                } else {
                    int visibility = viewGroup.getVisibility();
                    viewGroup.setVisibility(4);
                    viewGroup.setVisibility(visibility);
                }
            }
        }

        void a(D d2, boolean z) {
            RecyclerView.clearNestedRecyclerViewIfNotNested(d2);
            View view = d2.f1257a;
            androidx.recyclerview.widget.q qVar = RecyclerView.this.mAccessibilityDelegate;
            if (qVar != null) {
                a.h.h.a k = qVar.k();
                a.h.h.q.n(view, k instanceof q.a ? ((q.a) k).k(view) : null);
            }
            if (z) {
                w wVar = RecyclerView.this.mRecyclerListener;
                if (wVar != null) {
                    wVar.a(d2);
                }
                g gVar = RecyclerView.this.mAdapter;
                if (gVar != null) {
                    gVar.i(d2);
                }
                RecyclerView recyclerView = RecyclerView.this;
                if (recyclerView.mState != null) {
                    recyclerView.mViewInfoStore.i(d2);
                }
            }
            d2.r = null;
            d().g(d2);
        }

        public void b() {
            this.f1295a.clear();
            g();
        }

        public int c(int i) {
            if (i >= 0 && i < RecyclerView.this.mState.b()) {
                RecyclerView recyclerView = RecyclerView.this;
                return !recyclerView.mState.g ? i : recyclerView.mAdapterHelper.f(i, 0);
            }
            throw new IndexOutOfBoundsException("invalid position " + i + ". State item count is " + RecyclerView.this.mState.b() + RecyclerView.this.exceptionLabel());
        }

        u d() {
            if (this.g == null) {
                this.g = new u();
            }
            return this.g;
        }

        public List<D> e() {
            return this.f1298d;
        }

        void g() {
            for (int size = this.f1297c.size() - 1; size >= 0; size--) {
                h(size);
            }
            this.f1297c.clear();
            if (RecyclerView.ALLOW_THREAD_GAP_WORK) {
                j.b bVar = RecyclerView.this.mPrefetchRegistry;
                int[] iArr = bVar.f1390c;
                if (iArr != null) {
                    Arrays.fill(iArr, -1);
                }
                bVar.f1391d = 0;
            }
        }

        void h(int i) {
            a(this.f1297c.get(i), true);
            this.f1297c.remove(i);
        }

        public void i(View view) {
            D childViewHolderInt = RecyclerView.getChildViewHolderInt(view);
            if (childViewHolderInt.o()) {
                RecyclerView.this.removeDetachedView(view, false);
            }
            if (childViewHolderInt.n()) {
                childViewHolderInt.n.n(childViewHolderInt);
            } else if (childViewHolderInt.x()) {
                childViewHolderInt.d();
            }
            j(childViewHolderInt);
            if (RecyclerView.this.mItemAnimator == null || childViewHolderInt.l()) {
                return;
            }
            RecyclerView.this.mItemAnimator.e(childViewHolderInt);
        }

        /* JADX WARN: Removed duplicated region for block: B:20:0x0042  */
        /* JADX WARN: Removed duplicated region for block: B:56:0x009b  */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        void j(androidx.recyclerview.widget.RecyclerView.D r7) {
            /*
                Method dump skipped, instructions count: 279
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.v.j(androidx.recyclerview.widget.RecyclerView$D):void");
        }

        void k(View view) {
            ArrayList<D> arrayList;
            D childViewHolderInt = RecyclerView.getChildViewHolderInt(view);
            if (!childViewHolderInt.h(12) && childViewHolderInt.p() && !RecyclerView.this.canReuseUpdatedViewHolder(childViewHolderInt)) {
                if (this.f1296b == null) {
                    this.f1296b = new ArrayList<>();
                }
                childViewHolderInt.n = this;
                childViewHolderInt.o = true;
                arrayList = this.f1296b;
            } else {
                if (childViewHolderInt.k() && !childViewHolderInt.m() && !RecyclerView.this.mAdapter.e()) {
                    StringBuilder j = b.b.a.a.a.j("Called scrap view with an invalid view. Invalid views cannot be reused from scrap, they should rebound from recycler pool.");
                    j.append(RecyclerView.this.exceptionLabel());
                    throw new IllegalArgumentException(j.toString());
                }
                childViewHolderInt.n = this;
                childViewHolderInt.o = false;
                arrayList = this.f1295a;
            }
            arrayList.add(childViewHolderInt);
        }

        public void l(int i) {
            this.e = i;
            o();
        }

        /* JADX WARN: Code restructure failed: missing block: B:225:0x0422, code lost:
        
            if (r8.k() == false) goto L232;
         */
        /* JADX WARN: Removed duplicated region for block: B:118:0x021c  */
        /* JADX WARN: Removed duplicated region for block: B:147:0x02e0  */
        /* JADX WARN: Removed duplicated region for block: B:168:0x032d  */
        /* JADX WARN: Removed duplicated region for block: B:197:0x03ea  */
        /* JADX WARN: Removed duplicated region for block: B:206:0x04a1  */
        /* JADX WARN: Removed duplicated region for block: B:20:0x008e  */
        /* JADX WARN: Removed duplicated region for block: B:210:0x04c5 A[ADDED_TO_REGION] */
        /* JADX WARN: Removed duplicated region for block: B:214:0x04a8  */
        /* JADX WARN: Removed duplicated region for block: B:220:0x0414  */
        /* JADX WARN: Removed duplicated region for block: B:233:0x0469  */
        /* JADX WARN: Removed duplicated region for block: B:23:0x0095  */
        /* JADX WARN: Removed duplicated region for block: B:244:0x0496  */
        /* JADX WARN: Removed duplicated region for block: B:272:0x03cb  */
        /* JADX WARN: Removed duplicated region for block: B:39:0x0180  */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        androidx.recyclerview.widget.RecyclerView.D m(int r18, boolean r19, long r20) {
            /*
                Method dump skipped, instructions count: 1283
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.v.m(int, boolean, long):androidx.recyclerview.widget.RecyclerView$D");
        }

        void n(D d2) {
            (d2.o ? this.f1296b : this.f1295a).remove(d2);
            d2.n = null;
            d2.o = false;
            d2.d();
        }

        void o() {
            o oVar = RecyclerView.this.mLayout;
            this.f = this.e + (oVar != null ? oVar.l : 0);
            for (int size = this.f1297c.size() - 1; size >= 0 && this.f1297c.size() > this.f; size--) {
                h(size);
            }
        }
    }

    public interface w {
        void a(D d2);
    }

    private class x extends i {
        x() {
        }

        @Override // androidx.recyclerview.widget.RecyclerView.i
        public void a() {
            RecyclerView.this.assertNotInLayoutOrScroll(null);
            RecyclerView recyclerView = RecyclerView.this;
            recyclerView.mState.f = true;
            recyclerView.processDataSetCompletelyChanged(true);
            if (RecyclerView.this.mAdapterHelper.h()) {
                return;
            }
            RecyclerView.this.requestLayout();
        }
    }

    public static class y extends a.j.a.a {

        /* renamed from: c, reason: collision with root package name */
        Parcelable f1300c;

        y(Parcelable parcelable) {
            super(parcelable);
        }

        @Override // a.j.a.a, android.os.Parcelable
        public void writeToParcel(Parcel parcel, int i) {
            super.writeToParcel(parcel, i);
            parcel.writeParcelable(this.f1300c, 0);
        }
    }

    public static abstract class z {

        /* renamed from: b, reason: collision with root package name */
        private RecyclerView f1302b;

        /* renamed from: c, reason: collision with root package name */
        private o f1303c;

        /* renamed from: d, reason: collision with root package name */
        private boolean f1304d;
        private boolean e;
        private View f;
        private boolean h;

        /* renamed from: a, reason: collision with root package name */
        private int f1301a = -1;
        private final a g = new a(0, 0);

        public static class a {

            /* renamed from: a, reason: collision with root package name */
            private int f1305a;

            /* renamed from: b, reason: collision with root package name */
            private int f1306b;

            /* renamed from: d, reason: collision with root package name */
            private int f1308d = -1;
            private boolean f = false;
            private int g = 0;

            /* renamed from: c, reason: collision with root package name */
            private int f1307c = RecyclerView.UNDEFINED_DURATION;
            private Interpolator e = null;

            public a(int i, int i2) {
                this.f1305a = i;
                this.f1306b = i2;
            }

            boolean a() {
                return this.f1308d >= 0;
            }

            public void b(int i) {
                this.f1308d = i;
            }

            void c(RecyclerView recyclerView) {
                int i = this.f1308d;
                if (i >= 0) {
                    this.f1308d = -1;
                    recyclerView.jumpToPositionForSmoothScroller(i);
                    this.f = false;
                    return;
                }
                if (!this.f) {
                    this.g = 0;
                    return;
                }
                Interpolator interpolator = this.e;
                if (interpolator != null && this.f1307c < 1) {
                    throw new IllegalStateException("If you provide an interpolator, you must set a positive duration");
                }
                int i2 = this.f1307c;
                if (i2 < 1) {
                    throw new IllegalStateException("Scroll duration must be a positive number");
                }
                recyclerView.mViewFlinger.c(this.f1305a, this.f1306b, i2, interpolator);
                int i3 = this.g + 1;
                this.g = i3;
                if (i3 > 10) {
                    Log.e(RecyclerView.TAG, "Smooth Scroll action is being updated too frequently. Make sure you are not changing it unless necessary");
                }
                this.f = false;
            }

            public void d(int i, int i2, int i3, Interpolator interpolator) {
                this.f1305a = i;
                this.f1306b = i2;
                this.f1307c = i3;
                this.e = interpolator;
                this.f = true;
            }
        }

        public interface b {
            PointF a(int i);
        }

        public PointF a(int i) {
            Object obj = this.f1303c;
            if (obj instanceof b) {
                return ((b) obj).a(i);
            }
            StringBuilder j = b.b.a.a.a.j("You should override computeScrollVectorForPosition when the LayoutManager does not implement ");
            j.append(b.class.getCanonicalName());
            Log.w(RecyclerView.TAG, j.toString());
            return null;
        }

        public int b() {
            return this.f1302b.mLayout.B();
        }

        public o c() {
            return this.f1303c;
        }

        public int d() {
            return this.f1301a;
        }

        public boolean e() {
            return this.f1304d;
        }

        public boolean f() {
            return this.e;
        }

        void g(int i, int i2) {
            PointF a2;
            RecyclerView recyclerView = this.f1302b;
            if (this.f1301a == -1 || recyclerView == null) {
                l();
            }
            if (this.f1304d && this.f == null && this.f1303c != null && (a2 = a(this.f1301a)) != null) {
                float f = a2.x;
                if (f != 0.0f || a2.y != 0.0f) {
                    recyclerView.scrollStep((int) Math.signum(f), (int) Math.signum(a2.y), null);
                }
            }
            this.f1304d = false;
            View view = this.f;
            if (view != null) {
                if (this.f1302b.getChildLayoutPosition(view) == this.f1301a) {
                    i(this.f, recyclerView.mState, this.g);
                    this.g.c(recyclerView);
                    l();
                } else {
                    Log.e(RecyclerView.TAG, "Passed over target position while smooth scrolling.");
                    this.f = null;
                }
            }
            if (this.e) {
                A a3 = recyclerView.mState;
                a aVar = this.g;
                androidx.recyclerview.widget.l lVar = (androidx.recyclerview.widget.l) this;
                if (lVar.b() == 0) {
                    lVar.l();
                } else {
                    int i3 = lVar.o;
                    int i4 = i3 - i;
                    if (i3 * i4 <= 0) {
                        i4 = 0;
                    }
                    lVar.o = i4;
                    int i5 = lVar.p;
                    int i6 = i5 - i2;
                    int i7 = i5 * i6 > 0 ? i6 : 0;
                    lVar.p = i7;
                    if (i4 == 0 && i7 == 0) {
                        PointF a4 = lVar.a(lVar.f1301a);
                        if (a4 != null) {
                            if (a4.x != 0.0f || a4.y != 0.0f) {
                                float f2 = a4.y;
                                float sqrt = (float) Math.sqrt((f2 * f2) + (r9 * r9));
                                float f3 = a4.x / sqrt;
                                a4.x = f3;
                                float f4 = a4.y / sqrt;
                                a4.y = f4;
                                lVar.k = a4;
                                lVar.o = (int) (f3 * 10000.0f);
                                lVar.p = (int) (f4 * 10000.0f);
                                aVar.d((int) (lVar.o * 1.2f), (int) (lVar.p * 1.2f), (int) (lVar.p(10000) * 1.2f), lVar.i);
                            }
                        }
                        aVar.b(lVar.f1301a);
                        lVar.l();
                    }
                }
                boolean a5 = this.g.a();
                this.g.c(recyclerView);
                if (a5 && this.e) {
                    this.f1304d = true;
                    recyclerView.mViewFlinger.b();
                }
            }
        }

        protected void h(View view) {
            if (this.f1302b.getChildLayoutPosition(view) == this.f1301a) {
                this.f = view;
            }
        }

        protected abstract void i(View view, A a2, a aVar);

        public void j(int i) {
            this.f1301a = i;
        }

        void k(RecyclerView recyclerView, o oVar) {
            recyclerView.mViewFlinger.d();
            if (this.h) {
                StringBuilder j = b.b.a.a.a.j("An instance of ");
                j.append(getClass().getSimpleName());
                j.append(" was started more than once. Each instance of");
                j.append(getClass().getSimpleName());
                j.append(" is intended to only be used once. You should create a new instance for each use.");
                Log.w(RecyclerView.TAG, j.toString());
            }
            this.f1302b = recyclerView;
            this.f1303c = oVar;
            int i = this.f1301a;
            if (i == -1) {
                throw new IllegalArgumentException("Invalid target position");
            }
            recyclerView.mState.f1249a = i;
            this.e = true;
            this.f1304d = true;
            this.f = recyclerView.mLayout.v(i);
            this.f1302b.mViewFlinger.b();
            this.h = true;
        }

        protected final void l() {
            if (this.e) {
                this.e = false;
                androidx.recyclerview.widget.l lVar = (androidx.recyclerview.widget.l) this;
                lVar.p = 0;
                lVar.o = 0;
                lVar.k = null;
                this.f1302b.mState.f1249a = -1;
                this.f = null;
                this.f1301a = -1;
                this.f1304d = false;
                o oVar = this.f1303c;
                if (oVar.g == this) {
                    oVar.g = null;
                }
                this.f1303c = null;
                this.f1302b = null;
            }
        }
    }

    static {
        Class<?> cls = Integer.TYPE;
        LAYOUT_MANAGER_CONSTRUCTOR_SIGNATURE = new Class[]{Context.class, AttributeSet.class, cls, cls};
        sQuinticInterpolator = new InterpolatorC0113c();
    }

    public RecyclerView(Context context) {
        this(context, null);
    }

    public RecyclerView(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, org.libpag.R.attr.recyclerViewStyle);
    }

    public RecyclerView(Context context, AttributeSet attributeSet, int i2) {
        super(context, attributeSet, i2);
        this.mObserver = new x();
        this.mRecycler = new v();
        this.mViewInfoStore = new androidx.recyclerview.widget.v();
        this.mUpdateChildViewsRunnable = new RunnableC0111a();
        this.mTempRect = new Rect();
        this.mTempRect2 = new Rect();
        this.mTempRectF = new RectF();
        this.mItemDecorations = new ArrayList<>();
        this.mOnItemTouchListeners = new ArrayList<>();
        this.mInterceptRequestLayoutDepth = 0;
        this.mDataSetHasChangedAfterLayout = false;
        this.mDispatchItemsChangedEvent = false;
        this.mLayoutOrScrollCounter = 0;
        this.mDispatchScrollCounter = 0;
        this.mEdgeEffectFactory = new k();
        this.mItemAnimator = new c();
        this.mScrollState = 0;
        this.mScrollPointerId = -1;
        this.mScaledHorizontalScrollFactor = Float.MIN_VALUE;
        this.mScaledVerticalScrollFactor = Float.MIN_VALUE;
        this.mPreserveFocusAfterLayout = true;
        this.mViewFlinger = new C();
        this.mPrefetchRegistry = ALLOW_THREAD_GAP_WORK ? new j.b() : null;
        this.mState = new A();
        this.mItemsAddedOrRemoved = false;
        this.mItemsChanged = false;
        this.mItemAnimatorListener = new m();
        this.mPostedAnimatorRunner = false;
        this.mMinMaxLayoutPositions = new int[2];
        this.mScrollOffset = new int[2];
        this.mNestedOffsets = new int[2];
        this.mReusableIntPair = new int[2];
        this.mPendingAccessibilityImportanceChange = new ArrayList();
        this.mItemAnimatorRunner = new RunnableC0112b();
        this.mViewInfoProcessCallback = new C0114d();
        setScrollContainer(true);
        setFocusableInTouchMode(true);
        ViewConfiguration viewConfiguration = ViewConfiguration.get(context);
        this.mTouchSlop = viewConfiguration.getScaledTouchSlop();
        this.mScaledHorizontalScrollFactor = viewConfiguration.getScaledHorizontalScrollFactor();
        this.mScaledVerticalScrollFactor = viewConfiguration.getScaledVerticalScrollFactor();
        this.mMinFlingVelocity = viewConfiguration.getScaledMinimumFlingVelocity();
        this.mMaxFlingVelocity = viewConfiguration.getScaledMaximumFlingVelocity();
        setWillNotDraw(getOverScrollMode() == 2);
        this.mItemAnimator.n(this.mItemAnimatorListener);
        initAdapterManager();
        initChildrenHelper();
        initAutofill();
        int i3 = a.h.h.q.e;
        if (getImportantForAccessibility() == 0) {
            setImportantForAccessibility(1);
        }
        this.mAccessibilityManager = (AccessibilityManager) getContext().getSystemService("accessibility");
        setAccessibilityDelegateCompat(new androidx.recyclerview.widget.q(this));
        int[] iArr = a.n.a.f465a;
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, iArr, i2, 0);
        saveAttributeDataForStyleable(context, iArr, attributeSet, obtainStyledAttributes, i2, 0);
        String string = obtainStyledAttributes.getString(8);
        if (obtainStyledAttributes.getInt(2, -1) == -1) {
            setDescendantFocusability(262144);
        }
        this.mClipToPadding = obtainStyledAttributes.getBoolean(1, true);
        boolean z2 = obtainStyledAttributes.getBoolean(3, false);
        this.mEnableFastScroller = z2;
        if (z2) {
            initFastScroller((StateListDrawable) obtainStyledAttributes.getDrawable(6), obtainStyledAttributes.getDrawable(7), (StateListDrawable) obtainStyledAttributes.getDrawable(4), obtainStyledAttributes.getDrawable(5));
        }
        obtainStyledAttributes.recycle();
        createLayoutManager(context, string, attributeSet, i2, 0);
        int[] iArr2 = NESTED_SCROLLING_ATTRS;
        TypedArray obtainStyledAttributes2 = context.obtainStyledAttributes(attributeSet, iArr2, i2, 0);
        saveAttributeDataForStyleable(context, iArr2, attributeSet, obtainStyledAttributes2, i2, 0);
        boolean z3 = obtainStyledAttributes2.getBoolean(0, true);
        obtainStyledAttributes2.recycle();
        setNestedScrollingEnabled(z3);
    }

    private void addAnimatingView(D d2) {
        View view = d2.f1257a;
        boolean z2 = view.getParent() == this;
        this.mRecycler.n(getChildViewHolder(view));
        boolean o2 = d2.o();
        b bVar = this.mChildHelper;
        if (o2) {
            bVar.b(view, -1, view.getLayoutParams(), true);
        } else if (z2) {
            bVar.i(view);
        } else {
            bVar.a(view, -1, true);
        }
    }

    private void animateChange(D d2, D d3, l.c cVar, l.c cVar2, boolean z2, boolean z3) {
        d2.v(false);
        if (z2) {
            addAnimatingView(d2);
        }
        if (d2 != d3) {
            if (z3) {
                addAnimatingView(d3);
            }
            d2.h = d3;
            addAnimatingView(d2);
            this.mRecycler.n(d2);
            d3.v(false);
            d3.i = d2;
        }
        if (this.mItemAnimator.a(d2, d3, cVar, cVar2)) {
            postAnimationRunner();
        }
    }

    private void cancelScroll() {
        resetScroll();
        setScrollState(0);
    }

    static void clearNestedRecyclerViewIfNotNested(D d2) {
        WeakReference<RecyclerView> weakReference = d2.f1258b;
        if (weakReference != null) {
            Object obj = weakReference.get();
            while (true) {
                for (View view = (View) obj; view != null; view = null) {
                    if (view == d2.f1257a) {
                        return;
                    }
                    obj = view.getParent();
                    if (obj instanceof View) {
                        break;
                    }
                }
                d2.f1258b = null;
                return;
            }
        }
    }

    private void createLayoutManager(Context context, String str, AttributeSet attributeSet, int i2, int i3) {
        Constructor constructor;
        if (str != null) {
            String trim = str.trim();
            if (trim.isEmpty()) {
                return;
            }
            String fullClassName = getFullClassName(context, trim);
            try {
                Class<? extends U> asSubclass = Class.forName(fullClassName, false, isInEditMode() ? getClass().getClassLoader() : context.getClassLoader()).asSubclass(o.class);
                Object[] objArr = null;
                try {
                    constructor = asSubclass.getConstructor(LAYOUT_MANAGER_CONSTRUCTOR_SIGNATURE);
                    objArr = new Object[]{context, attributeSet, Integer.valueOf(i2), Integer.valueOf(i3)};
                } catch (NoSuchMethodException e2) {
                    try {
                        constructor = asSubclass.getConstructor(new Class[0]);
                    } catch (NoSuchMethodException e3) {
                        e3.initCause(e2);
                        throw new IllegalStateException(attributeSet.getPositionDescription() + ": Error creating LayoutManager " + fullClassName, e3);
                    }
                }
                constructor.setAccessible(true);
                setLayoutManager((o) constructor.newInstance(objArr));
            } catch (ClassCastException e4) {
                throw new IllegalStateException(attributeSet.getPositionDescription() + ": Class is not a LayoutManager " + fullClassName, e4);
            } catch (ClassNotFoundException e5) {
                throw new IllegalStateException(attributeSet.getPositionDescription() + ": Unable to find LayoutManager " + fullClassName, e5);
            } catch (IllegalAccessException e6) {
                throw new IllegalStateException(attributeSet.getPositionDescription() + ": Cannot access non-public constructor " + fullClassName, e6);
            } catch (InstantiationException e7) {
                throw new IllegalStateException(attributeSet.getPositionDescription() + ": Could not instantiate the LayoutManager: " + fullClassName, e7);
            } catch (InvocationTargetException e8) {
                throw new IllegalStateException(attributeSet.getPositionDescription() + ": Could not instantiate the LayoutManager: " + fullClassName, e8);
            }
        }
    }

    private boolean didChildRangeChange(int i2, int i3) {
        findMinMaxChildLayoutPositions(this.mMinMaxLayoutPositions);
        int[] iArr = this.mMinMaxLayoutPositions;
        return (iArr[0] == i2 && iArr[1] == i3) ? false : true;
    }

    private void dispatchContentChangedIfNecessary() {
        int i2 = this.mEatenAccessibilityChangeFlags;
        this.mEatenAccessibilityChangeFlags = 0;
        if (i2 == 0 || !isAccessibilityEnabled()) {
            return;
        }
        AccessibilityEvent obtain = AccessibilityEvent.obtain();
        obtain.setEventType(2048);
        obtain.setContentChangeTypes(i2);
        sendAccessibilityEventUnchecked(obtain);
    }

    private void dispatchLayoutStep1() {
        this.mState.a(1);
        fillRemainingScrollValues(this.mState);
        this.mState.i = false;
        startInterceptRequestLayout();
        androidx.recyclerview.widget.v vVar = this.mViewInfoStore;
        vVar.f1419a.clear();
        vVar.f1420b.a();
        onEnterLayoutOrScroll();
        processAdapterUpdatesAndSetAnimationFlags();
        saveFocusInfo();
        A a2 = this.mState;
        a2.h = a2.j && this.mItemsChanged;
        this.mItemsChanged = false;
        this.mItemsAddedOrRemoved = false;
        a2.g = a2.k;
        a2.e = this.mAdapter.b();
        findMinMaxChildLayoutPositions(this.mMinMaxLayoutPositions);
        if (this.mState.j) {
            int e2 = this.mChildHelper.e();
            for (int i2 = 0; i2 < e2; i2++) {
                D childViewHolderInt = getChildViewHolderInt(this.mChildHelper.d(i2));
                if (!childViewHolderInt.w() && (!childViewHolderInt.k() || this.mAdapter.e())) {
                    l lVar = this.mItemAnimator;
                    l.b(childViewHolderInt);
                    childViewHolderInt.g();
                    this.mViewInfoStore.c(childViewHolderInt, lVar.l(childViewHolderInt));
                    if (this.mState.h && childViewHolderInt.p() && !childViewHolderInt.m() && !childViewHolderInt.w() && !childViewHolderInt.k()) {
                        this.mViewInfoStore.f1420b.h(getChangedHolderKey(childViewHolderInt), childViewHolderInt);
                    }
                }
            }
        }
        if (this.mState.k) {
            saveOldPositions();
            A a3 = this.mState;
            boolean z2 = a3.f;
            a3.f = false;
            this.mLayout.H0(this.mRecycler, a3);
            this.mState.f = z2;
            for (int i3 = 0; i3 < this.mChildHelper.e(); i3++) {
                D childViewHolderInt2 = getChildViewHolderInt(this.mChildHelper.d(i3));
                if (!childViewHolderInt2.w()) {
                    v.a orDefault = this.mViewInfoStore.f1419a.getOrDefault(childViewHolderInt2, null);
                    if (!((orDefault == null || (orDefault.f1422a & 4) == 0) ? false : true)) {
                        l.b(childViewHolderInt2);
                        boolean h2 = childViewHolderInt2.h(8192);
                        l lVar2 = this.mItemAnimator;
                        childViewHolderInt2.g();
                        l.c l2 = lVar2.l(childViewHolderInt2);
                        if (h2) {
                            recordAnimationInfoIfBouncedHiddenView(childViewHolderInt2, l2);
                        } else {
                            androidx.recyclerview.widget.v vVar2 = this.mViewInfoStore;
                            v.a orDefault2 = vVar2.f1419a.getOrDefault(childViewHolderInt2, null);
                            if (orDefault2 == null) {
                                orDefault2 = v.a.a();
                                vVar2.f1419a.put(childViewHolderInt2, orDefault2);
                            }
                            orDefault2.f1422a |= 2;
                            orDefault2.f1423b = l2;
                        }
                    }
                }
            }
        }
        clearOldPositions();
        onExitLayoutOrScroll();
        stopInterceptRequestLayout(false);
        this.mState.f1252d = 2;
    }

    private void dispatchLayoutStep2() {
        startInterceptRequestLayout();
        onEnterLayoutOrScroll();
        this.mState.a(6);
        this.mAdapterHelper.c();
        this.mState.e = this.mAdapter.b();
        A a2 = this.mState;
        a2.f1251c = 0;
        a2.g = false;
        this.mLayout.H0(this.mRecycler, a2);
        A a3 = this.mState;
        a3.f = false;
        this.mPendingSavedState = null;
        a3.j = a3.j && this.mItemAnimator != null;
        a3.f1252d = 4;
        onExitLayoutOrScroll();
        stopInterceptRequestLayout(false);
    }

    private void dispatchLayoutStep3() {
        l.c cVar;
        C0114d c0114d;
        boolean q2;
        l.c cVar2;
        this.mState.a(4);
        startInterceptRequestLayout();
        onEnterLayoutOrScroll();
        A a2 = this.mState;
        a2.f1252d = 1;
        if (a2.j) {
            for (int e2 = this.mChildHelper.e() - 1; e2 >= 0; e2--) {
                D childViewHolderInt = getChildViewHolderInt(this.mChildHelper.d(e2));
                if (!childViewHolderInt.w()) {
                    long changedHolderKey = getChangedHolderKey(childViewHolderInt);
                    Objects.requireNonNull(this.mItemAnimator);
                    l.c cVar3 = new l.c();
                    View view = childViewHolderInt.f1257a;
                    cVar3.f1272a = view.getLeft();
                    cVar3.f1273b = view.getTop();
                    view.getRight();
                    view.getBottom();
                    D e3 = this.mViewInfoStore.f1420b.e(changedHolderKey, null);
                    if (e3 != null && !e3.w()) {
                        boolean d2 = this.mViewInfoStore.d(e3);
                        boolean d3 = this.mViewInfoStore.d(childViewHolderInt);
                        if (!d2 || e3 != childViewHolderInt) {
                            l.c g2 = this.mViewInfoStore.g(e3);
                            this.mViewInfoStore.b(childViewHolderInt, cVar3);
                            l.c f2 = this.mViewInfoStore.f(childViewHolderInt);
                            if (g2 == null) {
                                handleMissingPreInfoForChangeError(changedHolderKey, childViewHolderInt, e3);
                            } else {
                                animateChange(e3, childViewHolderInt, g2, f2, d2, d3);
                            }
                        }
                    }
                    this.mViewInfoStore.b(childViewHolderInt, cVar3);
                }
            }
            androidx.recyclerview.widget.v vVar = this.mViewInfoStore;
            v.b bVar = this.mViewInfoProcessCallback;
            for (int size = vVar.f1419a.size() - 1; size >= 0; size--) {
                D h2 = vVar.f1419a.h(size);
                v.a i2 = vVar.f1419a.i(size);
                int i3 = i2.f1422a;
                if ((i3 & 3) != 3) {
                    if ((i3 & 1) != 0) {
                        cVar = i2.f1423b;
                        cVar2 = cVar != null ? i2.f1424c : null;
                    } else {
                        if ((i3 & 14) != 14) {
                            if ((i3 & 12) == 12) {
                                l.c cVar4 = i2.f1423b;
                                l.c cVar5 = i2.f1424c;
                                C0114d c0114d2 = (C0114d) bVar;
                                Objects.requireNonNull(c0114d2);
                                h2.v(false);
                                RecyclerView recyclerView = RecyclerView.this;
                                boolean z2 = recyclerView.mDataSetHasChangedAfterLayout;
                                l lVar = recyclerView.mItemAnimator;
                                if (!z2) {
                                    androidx.recyclerview.widget.s sVar = (androidx.recyclerview.widget.s) lVar;
                                    Objects.requireNonNull(sVar);
                                    int i4 = cVar4.f1272a;
                                    int i5 = cVar5.f1272a;
                                    if (i4 == i5 && cVar4.f1273b == cVar5.f1273b) {
                                        sVar.c(h2);
                                        q2 = false;
                                        c0114d = c0114d2;
                                    } else {
                                        c0114d = c0114d2;
                                        q2 = sVar.q(h2, i4, cVar4.f1273b, i5, cVar5.f1273b);
                                    }
                                    if (!q2) {
                                    }
                                    RecyclerView.this.postAnimationRunner();
                                } else if (lVar.a(h2, h2, cVar4, cVar5)) {
                                    c0114d = c0114d2;
                                    RecyclerView.this.postAnimationRunner();
                                }
                            } else if ((i3 & 4) != 0) {
                                cVar = i2.f1423b;
                            } else if ((i3 & 8) == 0) {
                            }
                            v.a.b(i2);
                        }
                        RecyclerView.this.animateAppearance(h2, i2.f1423b, i2.f1424c);
                        v.a.b(i2);
                    }
                    C0114d c0114d3 = (C0114d) bVar;
                    RecyclerView.this.mRecycler.n(h2);
                    RecyclerView.this.animateDisappearance(h2, cVar, cVar2);
                    v.a.b(i2);
                }
                RecyclerView recyclerView2 = RecyclerView.this;
                recyclerView2.mLayout.T0(h2.f1257a, recyclerView2.mRecycler);
                v.a.b(i2);
            }
        }
        this.mLayout.S0(this.mRecycler);
        A a3 = this.mState;
        a3.f1250b = a3.e;
        this.mDataSetHasChangedAfterLayout = false;
        this.mDispatchItemsChangedEvent = false;
        a3.j = false;
        a3.k = false;
        this.mLayout.h = false;
        ArrayList<D> arrayList = this.mRecycler.f1296b;
        if (arrayList != null) {
            arrayList.clear();
        }
        o oVar = this.mLayout;
        if (oVar.m) {
            oVar.l = 0;
            oVar.m = false;
            this.mRecycler.o();
        }
        this.mLayout.I0(this.mState);
        onExitLayoutOrScroll();
        stopInterceptRequestLayout(false);
        androidx.recyclerview.widget.v vVar2 = this.mViewInfoStore;
        vVar2.f1419a.clear();
        vVar2.f1420b.a();
        int[] iArr = this.mMinMaxLayoutPositions;
        if (didChildRangeChange(iArr[0], iArr[1])) {
            dispatchOnScrolled(0, 0);
        }
        recoverFocusFromState();
        resetFocusInfo();
    }

    private boolean dispatchToOnItemTouchListeners(MotionEvent motionEvent) {
        s sVar = this.mInterceptingOnItemTouchListener;
        if (sVar == null) {
            if (motionEvent.getAction() == 0) {
                return false;
            }
            return findInterceptingOnItemTouchListener(motionEvent);
        }
        sVar.b(this, motionEvent);
        int action = motionEvent.getAction();
        if (action == 3 || action == 1) {
            this.mInterceptingOnItemTouchListener = null;
        }
        return true;
    }

    private boolean findInterceptingOnItemTouchListener(MotionEvent motionEvent) {
        int action = motionEvent.getAction();
        int size = this.mOnItemTouchListeners.size();
        for (int i2 = 0; i2 < size; i2++) {
            s sVar = this.mOnItemTouchListeners.get(i2);
            if (sVar.a(this, motionEvent) && action != 3) {
                this.mInterceptingOnItemTouchListener = sVar;
                return true;
            }
        }
        return false;
    }

    private void findMinMaxChildLayoutPositions(int[] iArr) {
        int e2 = this.mChildHelper.e();
        if (e2 == 0) {
            iArr[0] = -1;
            iArr[1] = -1;
            return;
        }
        int i2 = Integer.MAX_VALUE;
        int i3 = UNDEFINED_DURATION;
        for (int i4 = 0; i4 < e2; i4++) {
            D childViewHolderInt = getChildViewHolderInt(this.mChildHelper.d(i4));
            if (!childViewHolderInt.w()) {
                int f2 = childViewHolderInt.f();
                if (f2 < i2) {
                    i2 = f2;
                }
                if (f2 > i3) {
                    i3 = f2;
                }
            }
        }
        iArr[0] = i2;
        iArr[1] = i3;
    }

    static RecyclerView findNestedRecyclerView(View view) {
        if (!(view instanceof ViewGroup)) {
            return null;
        }
        if (view instanceof RecyclerView) {
            return (RecyclerView) view;
        }
        ViewGroup viewGroup = (ViewGroup) view;
        int childCount = viewGroup.getChildCount();
        for (int i2 = 0; i2 < childCount; i2++) {
            RecyclerView findNestedRecyclerView = findNestedRecyclerView(viewGroup.getChildAt(i2));
            if (findNestedRecyclerView != null) {
                return findNestedRecyclerView;
            }
        }
        return null;
    }

    private View findNextViewToFocus() {
        D findViewHolderForAdapterPosition;
        A a2 = this.mState;
        int i2 = a2.l;
        if (i2 == -1) {
            i2 = 0;
        }
        int b2 = a2.b();
        for (int i3 = i2; i3 < b2; i3++) {
            D findViewHolderForAdapterPosition2 = findViewHolderForAdapterPosition(i3);
            if (findViewHolderForAdapterPosition2 == null) {
                break;
            }
            if (findViewHolderForAdapterPosition2.f1257a.hasFocusable()) {
                return findViewHolderForAdapterPosition2.f1257a;
            }
        }
        int min = Math.min(b2, i2);
        do {
            min--;
            if (min < 0 || (findViewHolderForAdapterPosition = findViewHolderForAdapterPosition(min)) == null) {
                return null;
            }
        } while (!findViewHolderForAdapterPosition.f1257a.hasFocusable());
        return findViewHolderForAdapterPosition.f1257a;
    }

    static D getChildViewHolderInt(View view) {
        if (view == null) {
            return null;
        }
        return ((p) view.getLayoutParams()).f1285a;
    }

    static void getDecoratedBoundsWithMarginsInt(View view, Rect rect) {
        p pVar = (p) view.getLayoutParams();
        Rect rect2 = pVar.f1286b;
        rect.set((view.getLeft() - rect2.left) - ((ViewGroup.MarginLayoutParams) pVar).leftMargin, (view.getTop() - rect2.top) - ((ViewGroup.MarginLayoutParams) pVar).topMargin, view.getRight() + rect2.right + ((ViewGroup.MarginLayoutParams) pVar).rightMargin, view.getBottom() + rect2.bottom + ((ViewGroup.MarginLayoutParams) pVar).bottomMargin);
    }

    private int getDeepestFocusedViewWithId(View view) {
        int id;
        loop0: while (true) {
            id = view.getId();
            while (!view.isFocused() && (view instanceof ViewGroup) && view.hasFocus()) {
                view = ((ViewGroup) view).getFocusedChild();
                if (view.getId() != -1) {
                    break;
                }
            }
        }
        return id;
    }

    private String getFullClassName(Context context, String str) {
        StringBuilder sb;
        if (str.charAt(0) == '.') {
            sb = new StringBuilder();
            sb.append(context.getPackageName());
        } else {
            if (str.contains(".")) {
                return str;
            }
            sb = new StringBuilder();
            sb.append(RecyclerView.class.getPackage().getName());
            sb.append('.');
        }
        sb.append(str);
        return sb.toString();
    }

    private a.h.h.f getScrollingChildHelper() {
        if (this.mScrollingChildHelper == null) {
            this.mScrollingChildHelper = new a.h.h.f(this);
        }
        return this.mScrollingChildHelper;
    }

    private void handleMissingPreInfoForChangeError(long j2, D d2, D d3) {
        int e2 = this.mChildHelper.e();
        for (int i2 = 0; i2 < e2; i2++) {
            D childViewHolderInt = getChildViewHolderInt(this.mChildHelper.d(i2));
            if (childViewHolderInt != d2 && getChangedHolderKey(childViewHolderInt) == j2) {
                g gVar = this.mAdapter;
                if (gVar == null || !gVar.e()) {
                    throw new IllegalStateException("Two different ViewHolders have the same change ID. This might happen due to inconsistent Adapter update events or if the LayoutManager lays out the same View multiple times.\n ViewHolder 1:" + childViewHolderInt + " \n View Holder 2:" + d2 + exceptionLabel());
                }
                throw new IllegalStateException("Two different ViewHolders have the same stable ID. Stable IDs in your adapter MUST BE unique and SHOULD NOT change.\n ViewHolder 1:" + childViewHolderInt + " \n View Holder 2:" + d2 + exceptionLabel());
            }
        }
        Log.e(TAG, "Problem while matching changed view holders with the newones. The pre-layout information for the change holder " + d3 + " cannot be found but it is necessary for " + d2 + exceptionLabel());
    }

    private boolean hasUpdatedView() {
        int e2 = this.mChildHelper.e();
        for (int i2 = 0; i2 < e2; i2++) {
            D childViewHolderInt = getChildViewHolderInt(this.mChildHelper.d(i2));
            if (childViewHolderInt != null && !childViewHolderInt.w() && childViewHolderInt.p()) {
                return true;
            }
        }
        return false;
    }

    @SuppressLint({"InlinedApi"})
    private void initAutofill() {
        int i2 = a.h.h.q.e;
        if (getImportantForAutofill() == 0) {
            setImportantForAutofill(8);
        }
    }

    private void initChildrenHelper() {
        this.mChildHelper = new b(new e());
    }

    private boolean isPreferredNextFocus(View view, View view2, int i2) {
        int i3;
        if (view2 == null || view2 == this || findContainingItemView(view2) == null) {
            return false;
        }
        if (view == null || findContainingItemView(view) == null) {
            return true;
        }
        this.mTempRect.set(0, 0, view.getWidth(), view.getHeight());
        this.mTempRect2.set(0, 0, view2.getWidth(), view2.getHeight());
        offsetDescendantRectToMyCoords(view, this.mTempRect);
        offsetDescendantRectToMyCoords(view2, this.mTempRect2);
        char c2 = 65535;
        int i4 = this.mLayout.P() == 1 ? -1 : 1;
        Rect rect = this.mTempRect;
        int i5 = rect.left;
        Rect rect2 = this.mTempRect2;
        int i6 = rect2.left;
        if ((i5 < i6 || rect.right <= i6) && rect.right < rect2.right) {
            i3 = 1;
        } else {
            int i7 = rect.right;
            int i8 = rect2.right;
            i3 = ((i7 > i8 || i5 >= i8) && i5 > i6) ? -1 : 0;
        }
        int i9 = rect.top;
        int i10 = rect2.top;
        if ((i9 < i10 || rect.bottom <= i10) && rect.bottom < rect2.bottom) {
            c2 = 1;
        } else {
            int i11 = rect.bottom;
            int i12 = rect2.bottom;
            if ((i11 <= i12 && i9 < i12) || i9 <= i10) {
                c2 = 0;
            }
        }
        if (i2 == 1) {
            return c2 < 0 || (c2 == 0 && i3 * i4 <= 0);
        }
        if (i2 == 2) {
            return c2 > 0 || (c2 == 0 && i3 * i4 >= 0);
        }
        if (i2 == 17) {
            return i3 < 0;
        }
        if (i2 == 33) {
            return c2 < 0;
        }
        if (i2 == 66) {
            return i3 > 0;
        }
        if (i2 == 130) {
            return c2 > 0;
        }
        throw new IllegalArgumentException("Invalid direction: " + i2 + exceptionLabel());
    }

    private void onPointerUp(MotionEvent motionEvent) {
        int actionIndex = motionEvent.getActionIndex();
        if (motionEvent.getPointerId(actionIndex) == this.mScrollPointerId) {
            int i2 = actionIndex == 0 ? 1 : 0;
            this.mScrollPointerId = motionEvent.getPointerId(i2);
            int x2 = (int) (motionEvent.getX(i2) + 0.5f);
            this.mLastTouchX = x2;
            this.mInitialTouchX = x2;
            int y2 = (int) (motionEvent.getY(i2) + 0.5f);
            this.mLastTouchY = y2;
            this.mInitialTouchY = y2;
        }
    }

    private boolean predictiveItemAnimationsEnabled() {
        return this.mItemAnimator != null && this.mLayout.k1();
    }

    private void processAdapterUpdatesAndSetAnimationFlags() {
        boolean z2;
        if (this.mDataSetHasChangedAfterLayout) {
            this.mAdapterHelper.n();
            if (this.mDispatchItemsChangedEvent) {
                this.mLayout.C0(this);
            }
        }
        if (predictiveItemAnimationsEnabled()) {
            this.mAdapterHelper.k();
        } else {
            this.mAdapterHelper.c();
        }
        boolean z3 = false;
        boolean z4 = this.mItemsAddedOrRemoved || this.mItemsChanged;
        this.mState.j = this.mFirstLayoutComplete && this.mItemAnimator != null && ((z2 = this.mDataSetHasChangedAfterLayout) || z4 || this.mLayout.h) && (!z2 || this.mAdapter.e());
        A a2 = this.mState;
        if (a2.j && z4 && !this.mDataSetHasChangedAfterLayout && predictiveItemAnimationsEnabled()) {
            z3 = true;
        }
        a2.k = z3;
    }

    /* JADX WARN: Removed duplicated region for block: B:17:0x0053  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x003d  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private void pullGlows(float r7, float r8, float r9, float r10) {
        /*
            r6 = this;
            r0 = 0
            int r1 = (r8 > r0 ? 1 : (r8 == r0 ? 0 : -1))
            r2 = 1065353216(0x3f800000, float:1.0)
            r3 = 1
            if (r1 >= 0) goto L21
            r6.ensureLeftGlow()
            android.widget.EdgeEffect r1 = r6.mLeftGlow
            float r4 = -r8
            int r5 = r6.getWidth()
            float r5 = (float) r5
            float r4 = r4 / r5
            int r5 = r6.getHeight()
            float r5 = (float) r5
            float r9 = r9 / r5
            float r9 = r2 - r9
        L1c:
            r1.onPull(r4, r9)
            r9 = r3
            goto L39
        L21:
            int r1 = (r8 > r0 ? 1 : (r8 == r0 ? 0 : -1))
            if (r1 <= 0) goto L38
            r6.ensureRightGlow()
            android.widget.EdgeEffect r1 = r6.mRightGlow
            int r4 = r6.getWidth()
            float r4 = (float) r4
            float r4 = r8 / r4
            int r5 = r6.getHeight()
            float r5 = (float) r5
            float r9 = r9 / r5
            goto L1c
        L38:
            r9 = 0
        L39:
            int r1 = (r10 > r0 ? 1 : (r10 == r0 ? 0 : -1))
            if (r1 >= 0) goto L53
            r6.ensureTopGlow()
            android.widget.EdgeEffect r9 = r6.mTopGlow
            float r1 = -r10
            int r2 = r6.getHeight()
            float r2 = (float) r2
            float r1 = r1 / r2
            int r2 = r6.getWidth()
            float r2 = (float) r2
            float r7 = r7 / r2
            r9.onPull(r1, r7)
            goto L6f
        L53:
            int r1 = (r10 > r0 ? 1 : (r10 == r0 ? 0 : -1))
            if (r1 <= 0) goto L6e
            r6.ensureBottomGlow()
            android.widget.EdgeEffect r9 = r6.mBottomGlow
            int r1 = r6.getHeight()
            float r1 = (float) r1
            float r1 = r10 / r1
            int r4 = r6.getWidth()
            float r4 = (float) r4
            float r7 = r7 / r4
            float r2 = r2 - r7
            r9.onPull(r1, r2)
            goto L6f
        L6e:
            r3 = r9
        L6f:
            if (r3 != 0) goto L79
            int r7 = (r8 > r0 ? 1 : (r8 == r0 ? 0 : -1))
            if (r7 != 0) goto L79
            int r7 = (r10 > r0 ? 1 : (r10 == r0 ? 0 : -1))
            if (r7 == 0) goto L7e
        L79:
            int r7 = a.h.h.q.e
            r6.postInvalidateOnAnimation()
        L7e:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.pullGlows(float, float, float, float):void");
    }

    private void recoverFocusFromState() {
        View findViewById;
        if (!this.mPreserveFocusAfterLayout || this.mAdapter == null || !hasFocus() || getDescendantFocusability() == 393216) {
            return;
        }
        if (getDescendantFocusability() == 131072 && isFocused()) {
            return;
        }
        if (!isFocused()) {
            View focusedChild = getFocusedChild();
            if (!IGNORE_DETACHED_FOCUSED_CHILD || (focusedChild.getParent() != null && focusedChild.hasFocus())) {
                if (!this.mChildHelper.l(focusedChild)) {
                    return;
                }
            } else if (this.mChildHelper.e() == 0) {
                requestFocus();
                return;
            }
        }
        View view = null;
        D findViewHolderForItemId = (this.mState.m == -1 || !this.mAdapter.e()) ? null : findViewHolderForItemId(this.mState.m);
        if (findViewHolderForItemId != null && !this.mChildHelper.l(findViewHolderForItemId.f1257a) && findViewHolderForItemId.f1257a.hasFocusable()) {
            view = findViewHolderForItemId.f1257a;
        } else if (this.mChildHelper.e() > 0) {
            view = findNextViewToFocus();
        }
        if (view != null) {
            int i2 = this.mState.n;
            if (i2 != -1 && (findViewById = view.findViewById(i2)) != null && findViewById.isFocusable()) {
                view = findViewById;
            }
            view.requestFocus();
        }
    }

    private void releaseGlows() {
        boolean z2;
        EdgeEffect edgeEffect = this.mLeftGlow;
        if (edgeEffect != null) {
            edgeEffect.onRelease();
            z2 = this.mLeftGlow.isFinished();
        } else {
            z2 = false;
        }
        EdgeEffect edgeEffect2 = this.mTopGlow;
        if (edgeEffect2 != null) {
            edgeEffect2.onRelease();
            z2 |= this.mTopGlow.isFinished();
        }
        EdgeEffect edgeEffect3 = this.mRightGlow;
        if (edgeEffect3 != null) {
            edgeEffect3.onRelease();
            z2 |= this.mRightGlow.isFinished();
        }
        EdgeEffect edgeEffect4 = this.mBottomGlow;
        if (edgeEffect4 != null) {
            edgeEffect4.onRelease();
            z2 |= this.mBottomGlow.isFinished();
        }
        if (z2) {
            int i2 = a.h.h.q.e;
            postInvalidateOnAnimation();
        }
    }

    private void requestChildOnScreen(View view, View view2) {
        View view3 = view2 != null ? view2 : view;
        this.mTempRect.set(0, 0, view3.getWidth(), view3.getHeight());
        ViewGroup.LayoutParams layoutParams = view3.getLayoutParams();
        if (layoutParams instanceof p) {
            p pVar = (p) layoutParams;
            if (!pVar.f1287c) {
                Rect rect = pVar.f1286b;
                Rect rect2 = this.mTempRect;
                rect2.left -= rect.left;
                rect2.right += rect.right;
                rect2.top -= rect.top;
                rect2.bottom += rect.bottom;
            }
        }
        if (view2 != null) {
            offsetDescendantRectToMyCoords(view2, this.mTempRect);
            offsetRectIntoDescendantCoords(view, this.mTempRect);
        }
        this.mLayout.V0(this, view, this.mTempRect, !this.mFirstLayoutComplete, view2 == null);
    }

    private void resetFocusInfo() {
        A a2 = this.mState;
        a2.m = -1L;
        a2.l = -1;
        a2.n = -1;
    }

    private void resetScroll() {
        VelocityTracker velocityTracker = this.mVelocityTracker;
        if (velocityTracker != null) {
            velocityTracker.clear();
        }
        stopNestedScroll(0);
        releaseGlows();
    }

    private void saveFocusInfo() {
        View focusedChild = (this.mPreserveFocusAfterLayout && hasFocus() && this.mAdapter != null) ? getFocusedChild() : null;
        D findContainingViewHolder = focusedChild != null ? findContainingViewHolder(focusedChild) : null;
        if (findContainingViewHolder == null) {
            resetFocusInfo();
            return;
        }
        this.mState.m = this.mAdapter.e() ? findContainingViewHolder.e : -1L;
        this.mState.l = this.mDataSetHasChangedAfterLayout ? -1 : findContainingViewHolder.m() ? findContainingViewHolder.f1260d : findContainingViewHolder.e();
        this.mState.n = getDeepestFocusedViewWithId(findContainingViewHolder.f1257a);
    }

    private void setAdapterInternal(g gVar, boolean z2, boolean z3) {
        g gVar2 = this.mAdapter;
        if (gVar2 != null) {
            gVar2.l(this.mObserver);
            Objects.requireNonNull(this.mAdapter);
        }
        if (!z2 || z3) {
            removeAndRecycleViews();
        }
        this.mAdapterHelper.n();
        g gVar3 = this.mAdapter;
        this.mAdapter = gVar;
        if (gVar != null) {
            gVar.j(this.mObserver);
        }
        o oVar = this.mLayout;
        if (oVar != null) {
            oVar.p0();
        }
        v vVar = this.mRecycler;
        g gVar4 = this.mAdapter;
        vVar.b();
        vVar.d().f(gVar3, gVar4, z2);
        this.mState.f = true;
    }

    private void stopScrollersInternal() {
        z zVar;
        this.mViewFlinger.d();
        o oVar = this.mLayout;
        if (oVar == null || (zVar = oVar.g) == null) {
            return;
        }
        zVar.l();
    }

    void absorbGlows(int i2, int i3) {
        if (i2 < 0) {
            ensureLeftGlow();
            if (this.mLeftGlow.isFinished()) {
                this.mLeftGlow.onAbsorb(-i2);
            }
        } else if (i2 > 0) {
            ensureRightGlow();
            if (this.mRightGlow.isFinished()) {
                this.mRightGlow.onAbsorb(i2);
            }
        }
        if (i3 < 0) {
            ensureTopGlow();
            if (this.mTopGlow.isFinished()) {
                this.mTopGlow.onAbsorb(-i3);
            }
        } else if (i3 > 0) {
            ensureBottomGlow();
            if (this.mBottomGlow.isFinished()) {
                this.mBottomGlow.onAbsorb(i3);
            }
        }
        if (i2 == 0 && i3 == 0) {
            return;
        }
        int i4 = a.h.h.q.e;
        postInvalidateOnAnimation();
    }

    @Override // android.view.ViewGroup, android.view.View
    public void addFocusables(ArrayList<View> arrayList, int i2, int i3) {
        o oVar = this.mLayout;
        if (oVar == null || !oVar.q0()) {
            super.addFocusables(arrayList, i2, i3);
        }
    }

    public void addItemDecoration(n nVar) {
        addItemDecoration(nVar, -1);
    }

    public void addItemDecoration(n nVar, int i2) {
        o oVar = this.mLayout;
        if (oVar != null) {
            oVar.g("Cannot add item decoration during a scroll  or layout");
        }
        if (this.mItemDecorations.isEmpty()) {
            setWillNotDraw(false);
        }
        if (i2 < 0) {
            this.mItemDecorations.add(nVar);
        } else {
            this.mItemDecorations.add(i2, nVar);
        }
        markItemDecorInsetsDirty();
        requestLayout();
    }

    public void addOnChildAttachStateChangeListener(q qVar) {
        if (this.mOnChildAttachStateListeners == null) {
            this.mOnChildAttachStateListeners = new ArrayList();
        }
        this.mOnChildAttachStateListeners.add(qVar);
    }

    public void addOnItemTouchListener(s sVar) {
        this.mOnItemTouchListeners.add(sVar);
    }

    public void addOnScrollListener(t tVar) {
        if (this.mScrollListeners == null) {
            this.mScrollListeners = new ArrayList();
        }
        this.mScrollListeners.add(tVar);
    }

    void animateAppearance(D d2, l.c cVar, l.c cVar2) {
        boolean z2;
        int i2;
        int i3;
        d2.v(false);
        androidx.recyclerview.widget.s sVar = (androidx.recyclerview.widget.s) this.mItemAnimator;
        Objects.requireNonNull(sVar);
        if (cVar == null || ((i2 = cVar.f1272a) == (i3 = cVar2.f1272a) && cVar.f1273b == cVar2.f1273b)) {
            sVar.o(d2);
            z2 = true;
        } else {
            z2 = sVar.q(d2, i2, cVar.f1273b, i3, cVar2.f1273b);
        }
        if (z2) {
            postAnimationRunner();
        }
    }

    void animateDisappearance(D d2, l.c cVar, l.c cVar2) {
        boolean z2;
        addAnimatingView(d2);
        d2.v(false);
        androidx.recyclerview.widget.s sVar = (androidx.recyclerview.widget.s) this.mItemAnimator;
        Objects.requireNonNull(sVar);
        int i2 = cVar.f1272a;
        int i3 = cVar.f1273b;
        View view = d2.f1257a;
        int left = cVar2 == null ? view.getLeft() : cVar2.f1272a;
        int top = cVar2 == null ? view.getTop() : cVar2.f1273b;
        if (d2.m() || (i2 == left && i3 == top)) {
            sVar.r(d2);
            z2 = true;
        } else {
            view.layout(left, top, view.getWidth() + left, view.getHeight() + top);
            z2 = sVar.q(d2, i2, i3, left, top);
        }
        if (z2) {
            postAnimationRunner();
        }
    }

    void assertInLayoutOrScroll(String str) {
        if (isComputingLayout()) {
            return;
        }
        if (str == null) {
            StringBuilder j2 = b.b.a.a.a.j("Cannot call this method unless RecyclerView is computing a layout or scrolling");
            j2.append(exceptionLabel());
            throw new IllegalStateException(j2.toString());
        }
        StringBuilder j3 = b.b.a.a.a.j(str);
        j3.append(exceptionLabel());
        throw new IllegalStateException(j3.toString());
    }

    void assertNotInLayoutOrScroll(String str) {
        if (isComputingLayout()) {
            if (str != null) {
                throw new IllegalStateException(str);
            }
            StringBuilder j2 = b.b.a.a.a.j("Cannot call this method while RecyclerView is computing a layout or scrolling");
            j2.append(exceptionLabel());
            throw new IllegalStateException(j2.toString());
        }
        if (this.mDispatchScrollCounter > 0) {
            StringBuilder j3 = b.b.a.a.a.j("");
            j3.append(exceptionLabel());
            Log.w(TAG, "Cannot call this method in a scroll callback. Scroll callbacks mightbe run during a measure & layout pass where you cannot change theRecyclerView data. Any method call that might change the structureof the RecyclerView or the adapter contents should be postponed tothe next frame.", new IllegalStateException(j3.toString()));
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:13:? A[RETURN, SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    boolean canReuseUpdatedViewHolder(androidx.recyclerview.widget.RecyclerView.D r4) {
        /*
            r3 = this;
            androidx.recyclerview.widget.RecyclerView$l r3 = r3.mItemAnimator
            r0 = 0
            r1 = 1
            if (r3 == 0) goto L2b
            java.util.List r2 = r4.g()
            androidx.recyclerview.widget.c r3 = (androidx.recyclerview.widget.c) r3
            java.util.Objects.requireNonNull(r3)
            boolean r2 = r2.isEmpty()
            if (r2 == 0) goto L28
            boolean r3 = r3.g
            if (r3 == 0) goto L22
            boolean r3 = r4.k()
            if (r3 == 0) goto L20
            goto L22
        L20:
            r3 = r0
            goto L23
        L22:
            r3 = r1
        L23:
            if (r3 == 0) goto L26
            goto L28
        L26:
            r3 = r0
            goto L29
        L28:
            r3 = r1
        L29:
            if (r3 == 0) goto L2c
        L2b:
            r0 = r1
        L2c:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.canReuseUpdatedViewHolder(androidx.recyclerview.widget.RecyclerView$D):boolean");
    }

    @Override // android.view.ViewGroup
    protected boolean checkLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return (layoutParams instanceof p) && this.mLayout.j((p) layoutParams);
    }

    void clearOldPositions() {
        int h2 = this.mChildHelper.h();
        for (int i2 = 0; i2 < h2; i2++) {
            D childViewHolderInt = getChildViewHolderInt(this.mChildHelper.g(i2));
            if (!childViewHolderInt.w()) {
                childViewHolderInt.c();
            }
        }
        v vVar = this.mRecycler;
        int size = vVar.f1297c.size();
        for (int i3 = 0; i3 < size; i3++) {
            vVar.f1297c.get(i3).c();
        }
        int size2 = vVar.f1295a.size();
        for (int i4 = 0; i4 < size2; i4++) {
            vVar.f1295a.get(i4).c();
        }
        ArrayList<D> arrayList = vVar.f1296b;
        if (arrayList != null) {
            int size3 = arrayList.size();
            for (int i5 = 0; i5 < size3; i5++) {
                vVar.f1296b.get(i5).c();
            }
        }
    }

    public void clearOnChildAttachStateChangeListeners() {
        List<q> list = this.mOnChildAttachStateListeners;
        if (list != null) {
            list.clear();
        }
    }

    public void clearOnScrollListeners() {
        List<t> list = this.mScrollListeners;
        if (list != null) {
            list.clear();
        }
    }

    @Override // android.view.View
    public int computeHorizontalScrollExtent() {
        o oVar = this.mLayout;
        if (oVar != null && oVar.h()) {
            return this.mLayout.n(this.mState);
        }
        return 0;
    }

    @Override // android.view.View
    public int computeHorizontalScrollOffset() {
        o oVar = this.mLayout;
        if (oVar != null && oVar.h()) {
            return this.mLayout.o(this.mState);
        }
        return 0;
    }

    @Override // android.view.View
    public int computeHorizontalScrollRange() {
        o oVar = this.mLayout;
        if (oVar != null && oVar.h()) {
            return this.mLayout.p(this.mState);
        }
        return 0;
    }

    @Override // android.view.View
    public int computeVerticalScrollExtent() {
        o oVar = this.mLayout;
        if (oVar != null && oVar.i()) {
            return this.mLayout.q(this.mState);
        }
        return 0;
    }

    @Override // android.view.View
    public int computeVerticalScrollOffset() {
        o oVar = this.mLayout;
        if (oVar != null && oVar.i()) {
            return this.mLayout.r(this.mState);
        }
        return 0;
    }

    @Override // android.view.View
    public int computeVerticalScrollRange() {
        o oVar = this.mLayout;
        if (oVar != null && oVar.i()) {
            return this.mLayout.s(this.mState);
        }
        return 0;
    }

    void considerReleasingGlowsOnScroll(int i2, int i3) {
        boolean z2;
        EdgeEffect edgeEffect = this.mLeftGlow;
        if (edgeEffect == null || edgeEffect.isFinished() || i2 <= 0) {
            z2 = false;
        } else {
            this.mLeftGlow.onRelease();
            z2 = this.mLeftGlow.isFinished();
        }
        EdgeEffect edgeEffect2 = this.mRightGlow;
        if (edgeEffect2 != null && !edgeEffect2.isFinished() && i2 < 0) {
            this.mRightGlow.onRelease();
            z2 |= this.mRightGlow.isFinished();
        }
        EdgeEffect edgeEffect3 = this.mTopGlow;
        if (edgeEffect3 != null && !edgeEffect3.isFinished() && i3 > 0) {
            this.mTopGlow.onRelease();
            z2 |= this.mTopGlow.isFinished();
        }
        EdgeEffect edgeEffect4 = this.mBottomGlow;
        if (edgeEffect4 != null && !edgeEffect4.isFinished() && i3 < 0) {
            this.mBottomGlow.onRelease();
            z2 |= this.mBottomGlow.isFinished();
        }
        if (z2) {
            int i4 = a.h.h.q.e;
            postInvalidateOnAnimation();
        }
    }

    void consumePendingUpdateOperations() {
        if (!this.mFirstLayoutComplete || this.mDataSetHasChangedAfterLayout) {
            Trace.beginSection(TRACE_ON_DATA_SET_CHANGE_LAYOUT_TAG);
            dispatchLayout();
            Trace.endSection();
            return;
        }
        if (this.mAdapterHelper.h()) {
            if (this.mAdapterHelper.g(4) && !this.mAdapterHelper.g(11)) {
                Trace.beginSection(TRACE_HANDLE_ADAPTER_UPDATES_TAG);
                startInterceptRequestLayout();
                onEnterLayoutOrScroll();
                this.mAdapterHelper.k();
                if (!this.mLayoutWasDefered) {
                    if (hasUpdatedView()) {
                        dispatchLayout();
                    } else {
                        this.mAdapterHelper.b();
                    }
                }
                stopInterceptRequestLayout(true);
                onExitLayoutOrScroll();
            } else {
                if (!this.mAdapterHelper.h()) {
                    return;
                }
                Trace.beginSection(TRACE_ON_DATA_SET_CHANGE_LAYOUT_TAG);
                dispatchLayout();
            }
            Trace.endSection();
        }
    }

    void defaultOnMeasure(int i2, int i3) {
        int paddingRight = getPaddingRight() + getPaddingLeft();
        int i4 = a.h.h.q.e;
        setMeasuredDimension(o.k(i2, paddingRight, getMinimumWidth()), o.k(i3, getPaddingBottom() + getPaddingTop(), getMinimumHeight()));
    }

    void dispatchChildAttached(View view) {
        D childViewHolderInt = getChildViewHolderInt(view);
        onChildAttachedToWindow(view);
        g gVar = this.mAdapter;
        if (gVar != null && childViewHolderInt != null) {
            Objects.requireNonNull(gVar);
        }
        List<q> list = this.mOnChildAttachStateListeners;
        if (list != null) {
            for (int size = list.size() - 1; size >= 0; size--) {
                this.mOnChildAttachStateListeners.get(size).a(view);
            }
        }
    }

    void dispatchChildDetached(View view) {
        D childViewHolderInt = getChildViewHolderInt(view);
        onChildDetachedFromWindow(view);
        g gVar = this.mAdapter;
        if (gVar != null && childViewHolderInt != null) {
            Objects.requireNonNull(gVar);
        }
        List<q> list = this.mOnChildAttachStateListeners;
        if (list != null) {
            for (int size = list.size() - 1; size >= 0; size--) {
                this.mOnChildAttachStateListeners.get(size).b(view);
            }
        }
    }

    void dispatchLayout() {
        String str;
        if (this.mAdapter == null) {
            str = "No adapter attached; skipping layout";
        } else {
            if (this.mLayout != null) {
                A a2 = this.mState;
                boolean z2 = false;
                a2.i = false;
                if (a2.f1252d == 1) {
                    dispatchLayoutStep1();
                } else {
                    a aVar = this.mAdapterHelper;
                    if (!aVar.f1330c.isEmpty() && !aVar.f1329b.isEmpty()) {
                        z2 = true;
                    }
                    if (!z2 && this.mLayout.e0() == getWidth() && this.mLayout.M() == getHeight()) {
                        this.mLayout.a1(this);
                        dispatchLayoutStep3();
                        return;
                    }
                }
                this.mLayout.a1(this);
                dispatchLayoutStep2();
                dispatchLayoutStep3();
                return;
            }
            str = "No layout manager attached; skipping layout";
        }
        Log.e(TAG, str);
    }

    @Override // android.view.View
    public boolean dispatchNestedFling(float f2, float f3, boolean z2) {
        return getScrollingChildHelper().a(f2, f3, z2);
    }

    @Override // android.view.View
    public boolean dispatchNestedPreFling(float f2, float f3) {
        return getScrollingChildHelper().b(f2, f3);
    }

    @Override // android.view.View
    public boolean dispatchNestedPreScroll(int i2, int i3, int[] iArr, int[] iArr2) {
        return getScrollingChildHelper().c(i2, i3, iArr, iArr2, 0);
    }

    public boolean dispatchNestedPreScroll(int i2, int i3, int[] iArr, int[] iArr2, int i4) {
        return getScrollingChildHelper().c(i2, i3, iArr, iArr2, i4);
    }

    public final void dispatchNestedScroll(int i2, int i3, int i4, int i5, int[] iArr, int i6, int[] iArr2) {
        getScrollingChildHelper().d(i2, i3, i4, i5, iArr, i6, iArr2);
    }

    @Override // android.view.View
    public boolean dispatchNestedScroll(int i2, int i3, int i4, int i5, int[] iArr) {
        return getScrollingChildHelper().e(i2, i3, i4, i5, iArr);
    }

    public boolean dispatchNestedScroll(int i2, int i3, int i4, int i5, int[] iArr, int i6) {
        return getScrollingChildHelper().f(i2, i3, i4, i5, iArr, i6);
    }

    void dispatchOnScrollStateChanged(int i2) {
        o oVar = this.mLayout;
        if (oVar != null) {
            oVar.O0(i2);
        }
        onScrollStateChanged(i2);
        t tVar = this.mScrollListener;
        if (tVar != null) {
            tVar.a(this, i2);
        }
        List<t> list = this.mScrollListeners;
        if (list != null) {
            for (int size = list.size() - 1; size >= 0; size--) {
                this.mScrollListeners.get(size).a(this, i2);
            }
        }
    }

    void dispatchOnScrolled(int i2, int i3) {
        this.mDispatchScrollCounter++;
        int scrollX = getScrollX();
        int scrollY = getScrollY();
        onScrollChanged(scrollX, scrollY, scrollX - i2, scrollY - i3);
        onScrolled(i2, i3);
        t tVar = this.mScrollListener;
        if (tVar != null) {
            tVar.b(this, i2, i3);
        }
        List<t> list = this.mScrollListeners;
        if (list != null) {
            for (int size = list.size() - 1; size >= 0; size--) {
                this.mScrollListeners.get(size).b(this, i2, i3);
            }
        }
        this.mDispatchScrollCounter--;
    }

    void dispatchPendingImportantForAccessibilityChanges() {
        int i2;
        for (int size = this.mPendingAccessibilityImportanceChange.size() - 1; size >= 0; size--) {
            D d2 = this.mPendingAccessibilityImportanceChange.get(size);
            if (d2.f1257a.getParent() == this && !d2.w() && (i2 = d2.q) != -1) {
                View view = d2.f1257a;
                int i3 = a.h.h.q.e;
                view.setImportantForAccessibility(i2);
                d2.q = -1;
            }
        }
        this.mPendingAccessibilityImportanceChange.clear();
    }

    @Override // android.view.View
    public boolean dispatchPopulateAccessibilityEvent(AccessibilityEvent accessibilityEvent) {
        onPopulateAccessibilityEvent(accessibilityEvent);
        return true;
    }

    @Override // android.view.ViewGroup, android.view.View
    protected void dispatchRestoreInstanceState(SparseArray<Parcelable> sparseArray) {
        dispatchThawSelfOnly(sparseArray);
    }

    @Override // android.view.ViewGroup, android.view.View
    protected void dispatchSaveInstanceState(SparseArray<Parcelable> sparseArray) {
        dispatchFreezeSelfOnly(sparseArray);
    }

    @Override // android.view.View
    public void draw(Canvas canvas) {
        boolean z2;
        float f2;
        float f3;
        super.draw(canvas);
        int size = this.mItemDecorations.size();
        boolean z3 = false;
        for (int i2 = 0; i2 < size; i2++) {
            this.mItemDecorations.get(i2).f(canvas, this, this.mState);
        }
        EdgeEffect edgeEffect = this.mLeftGlow;
        if (edgeEffect == null || edgeEffect.isFinished()) {
            z2 = false;
        } else {
            int save = canvas.save();
            int paddingBottom = this.mClipToPadding ? getPaddingBottom() : 0;
            canvas.rotate(270.0f);
            canvas.translate((-getHeight()) + paddingBottom, 0.0f);
            EdgeEffect edgeEffect2 = this.mLeftGlow;
            z2 = edgeEffect2 != null && edgeEffect2.draw(canvas);
            canvas.restoreToCount(save);
        }
        EdgeEffect edgeEffect3 = this.mTopGlow;
        if (edgeEffect3 != null && !edgeEffect3.isFinished()) {
            int save2 = canvas.save();
            if (this.mClipToPadding) {
                canvas.translate(getPaddingLeft(), getPaddingTop());
            }
            EdgeEffect edgeEffect4 = this.mTopGlow;
            z2 |= edgeEffect4 != null && edgeEffect4.draw(canvas);
            canvas.restoreToCount(save2);
        }
        EdgeEffect edgeEffect5 = this.mRightGlow;
        if (edgeEffect5 != null && !edgeEffect5.isFinished()) {
            int save3 = canvas.save();
            int width = getWidth();
            int paddingTop = this.mClipToPadding ? getPaddingTop() : 0;
            canvas.rotate(90.0f);
            canvas.translate(-paddingTop, -width);
            EdgeEffect edgeEffect6 = this.mRightGlow;
            z2 |= edgeEffect6 != null && edgeEffect6.draw(canvas);
            canvas.restoreToCount(save3);
        }
        EdgeEffect edgeEffect7 = this.mBottomGlow;
        if (edgeEffect7 != null && !edgeEffect7.isFinished()) {
            int save4 = canvas.save();
            canvas.rotate(180.0f);
            if (this.mClipToPadding) {
                f2 = getPaddingRight() + (-getWidth());
                f3 = getPaddingBottom() + (-getHeight());
            } else {
                f2 = -getWidth();
                f3 = -getHeight();
            }
            canvas.translate(f2, f3);
            EdgeEffect edgeEffect8 = this.mBottomGlow;
            if (edgeEffect8 != null && edgeEffect8.draw(canvas)) {
                z3 = true;
            }
            z2 |= z3;
            canvas.restoreToCount(save4);
        }
        if ((z2 || this.mItemAnimator == null || this.mItemDecorations.size() <= 0 || !this.mItemAnimator.k()) ? z2 : true) {
            int i3 = a.h.h.q.e;
            postInvalidateOnAnimation();
        }
    }

    @Override // android.view.ViewGroup
    public boolean drawChild(Canvas canvas, View view, long j2) {
        return super.drawChild(canvas, view, j2);
    }

    void ensureBottomGlow() {
        if (this.mBottomGlow != null) {
            return;
        }
        EdgeEffect a2 = this.mEdgeEffectFactory.a(this);
        this.mBottomGlow = a2;
        if (this.mClipToPadding) {
            a2.setSize((getMeasuredWidth() - getPaddingLeft()) - getPaddingRight(), (getMeasuredHeight() - getPaddingTop()) - getPaddingBottom());
        } else {
            a2.setSize(getMeasuredWidth(), getMeasuredHeight());
        }
    }

    void ensureLeftGlow() {
        if (this.mLeftGlow != null) {
            return;
        }
        EdgeEffect a2 = this.mEdgeEffectFactory.a(this);
        this.mLeftGlow = a2;
        if (this.mClipToPadding) {
            a2.setSize((getMeasuredHeight() - getPaddingTop()) - getPaddingBottom(), (getMeasuredWidth() - getPaddingLeft()) - getPaddingRight());
        } else {
            a2.setSize(getMeasuredHeight(), getMeasuredWidth());
        }
    }

    void ensureRightGlow() {
        if (this.mRightGlow != null) {
            return;
        }
        EdgeEffect a2 = this.mEdgeEffectFactory.a(this);
        this.mRightGlow = a2;
        if (this.mClipToPadding) {
            a2.setSize((getMeasuredHeight() - getPaddingTop()) - getPaddingBottom(), (getMeasuredWidth() - getPaddingLeft()) - getPaddingRight());
        } else {
            a2.setSize(getMeasuredHeight(), getMeasuredWidth());
        }
    }

    void ensureTopGlow() {
        if (this.mTopGlow != null) {
            return;
        }
        EdgeEffect a2 = this.mEdgeEffectFactory.a(this);
        this.mTopGlow = a2;
        if (this.mClipToPadding) {
            a2.setSize((getMeasuredWidth() - getPaddingLeft()) - getPaddingRight(), (getMeasuredHeight() - getPaddingTop()) - getPaddingBottom());
        } else {
            a2.setSize(getMeasuredWidth(), getMeasuredHeight());
        }
    }

    String exceptionLabel() {
        StringBuilder j2 = b.b.a.a.a.j(" ");
        j2.append(super.toString());
        j2.append(", adapter:");
        j2.append(this.mAdapter);
        j2.append(", layout:");
        j2.append(this.mLayout);
        j2.append(", context:");
        j2.append(getContext());
        return j2.toString();
    }

    final void fillRemainingScrollValues(A a2) {
        if (getScrollState() != 2) {
            Objects.requireNonNull(a2);
            return;
        }
        OverScroller overScroller = this.mViewFlinger.f1255c;
        overScroller.getFinalX();
        overScroller.getCurrX();
        Objects.requireNonNull(a2);
        overScroller.getFinalY();
        overScroller.getCurrY();
    }

    public View findChildViewUnder(float f2, float f3) {
        for (int e2 = this.mChildHelper.e() - 1; e2 >= 0; e2--) {
            View d2 = this.mChildHelper.d(e2);
            float translationX = d2.getTranslationX();
            float translationY = d2.getTranslationY();
            if (f2 >= d2.getLeft() + translationX && f2 <= d2.getRight() + translationX && f3 >= d2.getTop() + translationY && f3 <= d2.getBottom() + translationY) {
                return d2;
            }
        }
        return null;
    }

    /* JADX WARN: Code restructure failed: missing block: B:11:?, code lost:
    
        return r3;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public android.view.View findContainingItemView(android.view.View r3) {
        /*
            r2 = this;
        L0:
            android.view.ViewParent r0 = r3.getParent()
            if (r0 == 0) goto L10
            if (r0 == r2) goto L10
            boolean r1 = r0 instanceof android.view.View
            if (r1 == 0) goto L10
            r3 = r0
            android.view.View r3 = (android.view.View) r3
            goto L0
        L10:
            if (r0 != r2) goto L13
            goto L14
        L13:
            r3 = 0
        L14:
            return r3
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.findContainingItemView(android.view.View):android.view.View");
    }

    public D findContainingViewHolder(View view) {
        View findContainingItemView = findContainingItemView(view);
        if (findContainingItemView == null) {
            return null;
        }
        return getChildViewHolder(findContainingItemView);
    }

    public D findViewHolderForAdapterPosition(int i2) {
        D d2 = null;
        if (this.mDataSetHasChangedAfterLayout) {
            return null;
        }
        int h2 = this.mChildHelper.h();
        for (int i3 = 0; i3 < h2; i3++) {
            D childViewHolderInt = getChildViewHolderInt(this.mChildHelper.g(i3));
            if (childViewHolderInt != null && !childViewHolderInt.m() && getAdapterPositionFor(childViewHolderInt) == i2) {
                if (!this.mChildHelper.l(childViewHolderInt.f1257a)) {
                    return childViewHolderInt;
                }
                d2 = childViewHolderInt;
            }
        }
        return d2;
    }

    public D findViewHolderForItemId(long j2) {
        g gVar = this.mAdapter;
        D d2 = null;
        if (gVar != null && gVar.e()) {
            int h2 = this.mChildHelper.h();
            for (int i2 = 0; i2 < h2; i2++) {
                D childViewHolderInt = getChildViewHolderInt(this.mChildHelper.g(i2));
                if (childViewHolderInt != null && !childViewHolderInt.m() && childViewHolderInt.e == j2) {
                    if (!this.mChildHelper.l(childViewHolderInt.f1257a)) {
                        return childViewHolderInt;
                    }
                    d2 = childViewHolderInt;
                }
            }
        }
        return d2;
    }

    public D findViewHolderForLayoutPosition(int i2) {
        return findViewHolderForPosition(i2, false);
    }

    @Deprecated
    public D findViewHolderForPosition(int i2) {
        return findViewHolderForPosition(i2, false);
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0034  */
    /* JADX WARN: Removed duplicated region for block: B:15:0x0036 A[SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    androidx.recyclerview.widget.RecyclerView.D findViewHolderForPosition(int r6, boolean r7) {
        /*
            r5 = this;
            androidx.recyclerview.widget.b r0 = r5.mChildHelper
            int r0 = r0.h()
            r1 = 0
            r2 = 0
        L8:
            if (r2 >= r0) goto L3a
            androidx.recyclerview.widget.b r3 = r5.mChildHelper
            android.view.View r3 = r3.g(r2)
            androidx.recyclerview.widget.RecyclerView$D r3 = getChildViewHolderInt(r3)
            if (r3 == 0) goto L37
            boolean r4 = r3.m()
            if (r4 != 0) goto L37
            if (r7 == 0) goto L23
            int r4 = r3.f1259c
            if (r4 == r6) goto L2a
            goto L37
        L23:
            int r4 = r3.f()
            if (r4 == r6) goto L2a
            goto L37
        L2a:
            androidx.recyclerview.widget.b r1 = r5.mChildHelper
            android.view.View r4 = r3.f1257a
            boolean r1 = r1.l(r4)
            if (r1 == 0) goto L36
            r1 = r3
            goto L37
        L36:
            return r3
        L37:
            int r2 = r2 + 1
            goto L8
        L3a:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.findViewHolderForPosition(int, boolean):androidx.recyclerview.widget.RecyclerView$D");
    }

    /* JADX WARN: Removed duplicated region for block: B:45:0x0098 A[RETURN] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public boolean fling(int r10, int r11) {
        /*
            r9 = this;
            androidx.recyclerview.widget.RecyclerView$o r0 = r9.mLayout
            r1 = 0
            if (r0 != 0) goto Ld
            java.lang.String r9 = "RecyclerView"
            java.lang.String r10 = "Cannot fling without a LayoutManager set. Call setLayoutManager with a non-null argument."
            android.util.Log.e(r9, r10)
            return r1
        Ld:
            boolean r2 = r9.mLayoutSuppressed
            if (r2 == 0) goto L12
            return r1
        L12:
            boolean r0 = r0.h()
            androidx.recyclerview.widget.RecyclerView$o r2 = r9.mLayout
            boolean r2 = r2.i()
            if (r0 == 0) goto L26
            int r3 = java.lang.Math.abs(r10)
            int r4 = r9.mMinFlingVelocity
            if (r3 >= r4) goto L27
        L26:
            r10 = r1
        L27:
            if (r2 == 0) goto L31
            int r3 = java.lang.Math.abs(r11)
            int r4 = r9.mMinFlingVelocity
            if (r3 >= r4) goto L32
        L31:
            r11 = r1
        L32:
            if (r10 != 0) goto L37
            if (r11 != 0) goto L37
            return r1
        L37:
            float r3 = (float) r10
            float r4 = (float) r11
            boolean r5 = r9.dispatchNestedPreFling(r3, r4)
            if (r5 != 0) goto Lc1
            r5 = 1
            if (r0 != 0) goto L47
            if (r2 == 0) goto L45
            goto L47
        L45:
            r6 = r1
            goto L48
        L47:
            r6 = r5
        L48:
            r9.dispatchNestedFling(r3, r4, r6)
            androidx.recyclerview.widget.RecyclerView$r r3 = r9.mOnFlingListener
            if (r3 == 0) goto L99
            androidx.recyclerview.widget.t r3 = (androidx.recyclerview.widget.t) r3
            androidx.recyclerview.widget.RecyclerView r4 = r3.f1408a
            androidx.recyclerview.widget.RecyclerView$o r4 = r4.getLayoutManager()
            if (r4 != 0) goto L5a
            goto L95
        L5a:
            androidx.recyclerview.widget.RecyclerView r7 = r3.f1408a
            androidx.recyclerview.widget.RecyclerView$g r7 = r7.getAdapter()
            if (r7 != 0) goto L63
            goto L95
        L63:
            androidx.recyclerview.widget.RecyclerView r7 = r3.f1408a
            int r7 = r7.getMinFlingVelocity()
            int r8 = java.lang.Math.abs(r11)
            if (r8 > r7) goto L75
            int r8 = java.lang.Math.abs(r10)
            if (r8 <= r7) goto L95
        L75:
            boolean r7 = r4 instanceof androidx.recyclerview.widget.RecyclerView.z.b
            if (r7 != 0) goto L7a
            goto L88
        L7a:
            androidx.recyclerview.widget.l r7 = r3.d(r4)
            if (r7 != 0) goto L81
            goto L88
        L81:
            int r3 = r3.f(r4, r10, r11)
            r8 = -1
            if (r3 != r8) goto L8a
        L88:
            r3 = r1
            goto L91
        L8a:
            r7.j(r3)
            r4.j1(r7)
            r3 = r5
        L91:
            if (r3 == 0) goto L95
            r3 = r5
            goto L96
        L95:
            r3 = r1
        L96:
            if (r3 == 0) goto L99
            return r5
        L99:
            if (r6 == 0) goto Lc1
            if (r0 == 0) goto L9e
            r1 = r5
        L9e:
            if (r2 == 0) goto La2
            r1 = r1 | 2
        La2:
            r9.startNestedScroll(r1, r5)
            int r0 = r9.mMaxFlingVelocity
            int r1 = -r0
            int r10 = java.lang.Math.min(r10, r0)
            int r10 = java.lang.Math.max(r1, r10)
            int r0 = r9.mMaxFlingVelocity
            int r1 = -r0
            int r11 = java.lang.Math.min(r11, r0)
            int r11 = java.lang.Math.max(r1, r11)
            androidx.recyclerview.widget.RecyclerView$C r9 = r9.mViewFlinger
            r9.a(r10, r11)
            return r5
        Lc1:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.fling(int, int):boolean");
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public View focusSearch(View view, int i2) {
        View view2;
        boolean z2;
        View A0 = this.mLayout.A0();
        if (A0 != null) {
            return A0;
        }
        boolean z3 = (this.mAdapter == null || this.mLayout == null || isComputingLayout() || this.mLayoutSuppressed) ? false : true;
        FocusFinder focusFinder = FocusFinder.getInstance();
        if (z3 && (i2 == 2 || i2 == 1)) {
            if (this.mLayout.i()) {
                int i3 = i2 == 2 ? 130 : 33;
                z2 = focusFinder.findNextFocus(this, view, i3) == null;
                if (FORCE_ABS_FOCUS_SEARCH_DIRECTION) {
                    i2 = i3;
                }
            } else {
                z2 = false;
            }
            if (!z2 && this.mLayout.h()) {
                int i4 = (this.mLayout.P() == 1) ^ (i2 == 2) ? 66 : 17;
                boolean z4 = focusFinder.findNextFocus(this, view, i4) == null;
                if (FORCE_ABS_FOCUS_SEARCH_DIRECTION) {
                    i2 = i4;
                }
                z2 = z4;
            }
            if (z2) {
                consumePendingUpdateOperations();
                if (findContainingItemView(view) == null) {
                    return null;
                }
                startInterceptRequestLayout();
                this.mLayout.u0(view, i2, this.mRecycler, this.mState);
                stopInterceptRequestLayout(false);
            }
            view2 = focusFinder.findNextFocus(this, view, i2);
        } else {
            View findNextFocus = focusFinder.findNextFocus(this, view, i2);
            if (findNextFocus == null && z3) {
                consumePendingUpdateOperations();
                if (findContainingItemView(view) == null) {
                    return null;
                }
                startInterceptRequestLayout();
                view2 = this.mLayout.u0(view, i2, this.mRecycler, this.mState);
                stopInterceptRequestLayout(false);
            } else {
                view2 = findNextFocus;
            }
        }
        if (view2 == null || view2.hasFocusable()) {
            return isPreferredNextFocus(view, view2, i2) ? view2 : super.focusSearch(view, i2);
        }
        if (getFocusedChild() == null) {
            return super.focusSearch(view, i2);
        }
        requestChildOnScreen(view2, null);
        return view;
    }

    @Override // android.view.ViewGroup
    protected ViewGroup.LayoutParams generateDefaultLayoutParams() {
        o oVar = this.mLayout;
        if (oVar != null) {
            return oVar.w();
        }
        StringBuilder j2 = b.b.a.a.a.j("RecyclerView has no LayoutManager");
        j2.append(exceptionLabel());
        throw new IllegalStateException(j2.toString());
    }

    @Override // android.view.ViewGroup
    public ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet) {
        o oVar = this.mLayout;
        if (oVar != null) {
            return oVar.x(getContext(), attributeSet);
        }
        StringBuilder j2 = b.b.a.a.a.j("RecyclerView has no LayoutManager");
        j2.append(exceptionLabel());
        throw new IllegalStateException(j2.toString());
    }

    @Override // android.view.ViewGroup
    protected ViewGroup.LayoutParams generateLayoutParams(ViewGroup.LayoutParams layoutParams) {
        o oVar = this.mLayout;
        if (oVar != null) {
            return oVar.y(layoutParams);
        }
        StringBuilder j2 = b.b.a.a.a.j("RecyclerView has no LayoutManager");
        j2.append(exceptionLabel());
        throw new IllegalStateException(j2.toString());
    }

    @Override // android.view.ViewGroup, android.view.View
    public CharSequence getAccessibilityClassName() {
        return "androidx.recyclerview.widget.RecyclerView";
    }

    public g getAdapter() {
        return this.mAdapter;
    }

    int getAdapterPositionFor(D d2) {
        if (d2.h(524) || !d2.j()) {
            return -1;
        }
        a aVar = this.mAdapterHelper;
        int i2 = d2.f1259c;
        int size = aVar.f1329b.size();
        for (int i3 = 0; i3 < size; i3++) {
            a.b bVar = aVar.f1329b.get(i3);
            int i4 = bVar.f1332a;
            if (i4 != 1) {
                if (i4 == 2) {
                    int i5 = bVar.f1333b;
                    if (i5 <= i2) {
                        int i6 = bVar.f1335d;
                        if (i5 + i6 > i2) {
                            return -1;
                        }
                        i2 -= i6;
                    } else {
                        continue;
                    }
                } else if (i4 == 8) {
                    int i7 = bVar.f1333b;
                    if (i7 == i2) {
                        i2 = bVar.f1335d;
                    } else {
                        if (i7 < i2) {
                            i2--;
                        }
                        if (bVar.f1335d <= i2) {
                            i2++;
                        }
                    }
                }
            } else if (bVar.f1333b <= i2) {
                i2 += bVar.f1335d;
            }
        }
        return i2;
    }

    @Override // android.view.View
    public int getBaseline() {
        o oVar = this.mLayout;
        if (oVar == null) {
            return super.getBaseline();
        }
        Objects.requireNonNull(oVar);
        return -1;
    }

    long getChangedHolderKey(D d2) {
        return this.mAdapter.e() ? d2.e : d2.f1259c;
    }

    public int getChildAdapterPosition(View view) {
        D childViewHolderInt = getChildViewHolderInt(view);
        if (childViewHolderInt != null) {
            return childViewHolderInt.e();
        }
        return -1;
    }

    @Override // android.view.ViewGroup
    protected int getChildDrawingOrder(int i2, int i3) {
        j jVar = this.mChildDrawingOrderCallback;
        return jVar == null ? super.getChildDrawingOrder(i2, i3) : jVar.a(i2, i3);
    }

    public long getChildItemId(View view) {
        D childViewHolderInt;
        g gVar = this.mAdapter;
        if (gVar == null || !gVar.e() || (childViewHolderInt = getChildViewHolderInt(view)) == null) {
            return -1L;
        }
        return childViewHolderInt.e;
    }

    public int getChildLayoutPosition(View view) {
        D childViewHolderInt = getChildViewHolderInt(view);
        if (childViewHolderInt != null) {
            return childViewHolderInt.f();
        }
        return -1;
    }

    @Deprecated
    public int getChildPosition(View view) {
        return getChildAdapterPosition(view);
    }

    public D getChildViewHolder(View view) {
        ViewParent parent = view.getParent();
        if (parent == null || parent == this) {
            return getChildViewHolderInt(view);
        }
        throw new IllegalArgumentException("View " + view + " is not a direct child of " + this);
    }

    @Override // android.view.ViewGroup
    public boolean getClipToPadding() {
        return this.mClipToPadding;
    }

    public androidx.recyclerview.widget.q getCompatAccessibilityDelegate() {
        return this.mAccessibilityDelegate;
    }

    public void getDecoratedBoundsWithMargins(View view, Rect rect) {
        getDecoratedBoundsWithMarginsInt(view, rect);
    }

    public k getEdgeEffectFactory() {
        return this.mEdgeEffectFactory;
    }

    public l getItemAnimator() {
        return this.mItemAnimator;
    }

    Rect getItemDecorInsetsForChild(View view) {
        p pVar = (p) view.getLayoutParams();
        if (!pVar.f1287c) {
            return pVar.f1286b;
        }
        if (this.mState.g && (pVar.b() || pVar.f1285a.k())) {
            return pVar.f1286b;
        }
        Rect rect = pVar.f1286b;
        rect.set(0, 0, 0, 0);
        int size = this.mItemDecorations.size();
        for (int i2 = 0; i2 < size; i2++) {
            this.mTempRect.set(0, 0, 0, 0);
            this.mItemDecorations.get(i2).d(this.mTempRect, view, this, this.mState);
            int i3 = rect.left;
            Rect rect2 = this.mTempRect;
            rect.left = i3 + rect2.left;
            rect.top += rect2.top;
            rect.right += rect2.right;
            rect.bottom += rect2.bottom;
        }
        pVar.f1287c = false;
        return rect;
    }

    public n getItemDecorationAt(int i2) {
        int itemDecorationCount = getItemDecorationCount();
        if (i2 >= 0 && i2 < itemDecorationCount) {
            return this.mItemDecorations.get(i2);
        }
        throw new IndexOutOfBoundsException(i2 + " is an invalid index for size " + itemDecorationCount);
    }

    public int getItemDecorationCount() {
        return this.mItemDecorations.size();
    }

    public o getLayoutManager() {
        return this.mLayout;
    }

    public int getMaxFlingVelocity() {
        return this.mMaxFlingVelocity;
    }

    public int getMinFlingVelocity() {
        return this.mMinFlingVelocity;
    }

    long getNanoTime() {
        if (ALLOW_THREAD_GAP_WORK) {
            return System.nanoTime();
        }
        return 0L;
    }

    public r getOnFlingListener() {
        return this.mOnFlingListener;
    }

    public boolean getPreserveFocusAfterLayout() {
        return this.mPreserveFocusAfterLayout;
    }

    public u getRecycledViewPool() {
        return this.mRecycler.d();
    }

    public int getScrollState() {
        return this.mScrollState;
    }

    public boolean hasFixedSize() {
        return this.mHasFixedSize;
    }

    @Override // android.view.View
    public boolean hasNestedScrollingParent() {
        return getScrollingChildHelper().i(0);
    }

    public boolean hasNestedScrollingParent(int i2) {
        return getScrollingChildHelper().i(i2);
    }

    public boolean hasPendingAdapterUpdates() {
        return !this.mFirstLayoutComplete || this.mDataSetHasChangedAfterLayout || this.mAdapterHelper.h();
    }

    void initAdapterManager() {
        this.mAdapterHelper = new a(new f());
    }

    void initFastScroller(StateListDrawable stateListDrawable, Drawable drawable, StateListDrawable stateListDrawable2, Drawable drawable2) {
        if (stateListDrawable == null || drawable == null || stateListDrawable2 == null || drawable2 == null) {
            StringBuilder j2 = b.b.a.a.a.j("Trying to set fast scroller without both required drawables.");
            j2.append(exceptionLabel());
            throw new IllegalArgumentException(j2.toString());
        }
        Resources resources = getContext().getResources();
        new androidx.recyclerview.widget.i(this, stateListDrawable, drawable, stateListDrawable2, drawable2, resources.getDimensionPixelSize(org.libpag.R.dimen.fastscroll_default_thickness), resources.getDimensionPixelSize(org.libpag.R.dimen.fastscroll_minimum_range), resources.getDimensionPixelOffset(org.libpag.R.dimen.fastscroll_margin));
    }

    void invalidateGlows() {
        this.mBottomGlow = null;
        this.mTopGlow = null;
        this.mRightGlow = null;
        this.mLeftGlow = null;
    }

    public void invalidateItemDecorations() {
        if (this.mItemDecorations.size() == 0) {
            return;
        }
        o oVar = this.mLayout;
        if (oVar != null) {
            oVar.g("Cannot invalidate item decorations during a scroll or layout");
        }
        markItemDecorInsetsDirty();
        requestLayout();
    }

    boolean isAccessibilityEnabled() {
        AccessibilityManager accessibilityManager = this.mAccessibilityManager;
        return accessibilityManager != null && accessibilityManager.isEnabled();
    }

    public boolean isAnimating() {
        l lVar = this.mItemAnimator;
        return lVar != null && lVar.k();
    }

    @Override // android.view.View
    public boolean isAttachedToWindow() {
        return this.mIsAttached;
    }

    public boolean isComputingLayout() {
        return this.mLayoutOrScrollCounter > 0;
    }

    @Deprecated
    public boolean isLayoutFrozen() {
        return isLayoutSuppressed();
    }

    @Override // android.view.ViewGroup
    public final boolean isLayoutSuppressed() {
        return this.mLayoutSuppressed;
    }

    @Override // android.view.View
    public boolean isNestedScrollingEnabled() {
        return getScrollingChildHelper().j();
    }

    void jumpToPositionForSmoothScroller(int i2) {
        if (this.mLayout == null) {
            return;
        }
        setScrollState(2);
        this.mLayout.Y0(i2);
        awakenScrollBars();
    }

    void markItemDecorInsetsDirty() {
        int h2 = this.mChildHelper.h();
        for (int i2 = 0; i2 < h2; i2++) {
            ((p) this.mChildHelper.g(i2).getLayoutParams()).f1287c = true;
        }
        v vVar = this.mRecycler;
        int size = vVar.f1297c.size();
        for (int i3 = 0; i3 < size; i3++) {
            p pVar = (p) vVar.f1297c.get(i3).f1257a.getLayoutParams();
            if (pVar != null) {
                pVar.f1287c = true;
            }
        }
    }

    void markKnownViewsInvalid() {
        int h2 = this.mChildHelper.h();
        for (int i2 = 0; i2 < h2; i2++) {
            D childViewHolderInt = getChildViewHolderInt(this.mChildHelper.g(i2));
            if (childViewHolderInt != null && !childViewHolderInt.w()) {
                childViewHolderInt.b(6);
            }
        }
        markItemDecorInsetsDirty();
        v vVar = this.mRecycler;
        int size = vVar.f1297c.size();
        for (int i3 = 0; i3 < size; i3++) {
            D d2 = vVar.f1297c.get(i3);
            if (d2 != null) {
                d2.b(6);
                d2.a(null);
            }
        }
        g gVar = RecyclerView.this.mAdapter;
        if (gVar == null || !gVar.e()) {
            vVar.g();
        }
    }

    public void offsetChildrenHorizontal(int i2) {
        int e2 = this.mChildHelper.e();
        for (int i3 = 0; i3 < e2; i3++) {
            this.mChildHelper.d(i3).offsetLeftAndRight(i2);
        }
    }

    public void offsetChildrenVertical(int i2) {
        int e2 = this.mChildHelper.e();
        for (int i3 = 0; i3 < e2; i3++) {
            this.mChildHelper.d(i3).offsetTopAndBottom(i2);
        }
    }

    void offsetPositionRecordsForInsert(int i2, int i3) {
        int h2 = this.mChildHelper.h();
        for (int i4 = 0; i4 < h2; i4++) {
            D childViewHolderInt = getChildViewHolderInt(this.mChildHelper.g(i4));
            if (childViewHolderInt != null && !childViewHolderInt.w() && childViewHolderInt.f1259c >= i2) {
                childViewHolderInt.q(i3, false);
                this.mState.f = true;
            }
        }
        v vVar = this.mRecycler;
        int size = vVar.f1297c.size();
        for (int i5 = 0; i5 < size; i5++) {
            D d2 = vVar.f1297c.get(i5);
            if (d2 != null && d2.f1259c >= i2) {
                d2.q(i3, true);
            }
        }
        requestLayout();
    }

    void offsetPositionRecordsForMove(int i2, int i3) {
        int i4;
        int i5;
        int i6;
        int i7;
        int i8;
        int i9;
        int i10;
        int h2 = this.mChildHelper.h();
        int i11 = -1;
        if (i2 < i3) {
            i5 = i2;
            i4 = i3;
            i6 = -1;
        } else {
            i4 = i2;
            i5 = i3;
            i6 = 1;
        }
        for (int i12 = 0; i12 < h2; i12++) {
            D childViewHolderInt = getChildViewHolderInt(this.mChildHelper.g(i12));
            if (childViewHolderInt != null && (i10 = childViewHolderInt.f1259c) >= i5 && i10 <= i4) {
                if (i10 == i2) {
                    childViewHolderInt.q(i3 - i2, false);
                } else {
                    childViewHolderInt.q(i6, false);
                }
                this.mState.f = true;
            }
        }
        v vVar = this.mRecycler;
        if (i2 < i3) {
            i8 = i2;
            i7 = i3;
        } else {
            i7 = i2;
            i11 = 1;
            i8 = i3;
        }
        int size = vVar.f1297c.size();
        for (int i13 = 0; i13 < size; i13++) {
            D d2 = vVar.f1297c.get(i13);
            if (d2 != null && (i9 = d2.f1259c) >= i8 && i9 <= i7) {
                if (i9 == i2) {
                    d2.q(i3 - i2, false);
                } else {
                    d2.q(i11, false);
                }
            }
        }
        requestLayout();
    }

    void offsetPositionRecordsForRemove(int i2, int i3, boolean z2) {
        int i4 = i2 + i3;
        int h2 = this.mChildHelper.h();
        for (int i5 = 0; i5 < h2; i5++) {
            D childViewHolderInt = getChildViewHolderInt(this.mChildHelper.g(i5));
            if (childViewHolderInt != null && !childViewHolderInt.w()) {
                int i6 = childViewHolderInt.f1259c;
                if (i6 >= i4) {
                    childViewHolderInt.q(-i3, z2);
                } else if (i6 >= i2) {
                    childViewHolderInt.b(8);
                    childViewHolderInt.q(-i3, z2);
                    childViewHolderInt.f1259c = i2 - 1;
                }
                this.mState.f = true;
            }
        }
        v vVar = this.mRecycler;
        int size = vVar.f1297c.size();
        while (true) {
            size--;
            if (size < 0) {
                requestLayout();
                return;
            }
            D d2 = vVar.f1297c.get(size);
            if (d2 != null) {
                int i7 = d2.f1259c;
                if (i7 >= i4) {
                    d2.q(-i3, z2);
                } else if (i7 >= i2) {
                    d2.b(8);
                    vVar.h(size);
                }
            }
        }
    }

    @Override // android.view.ViewGroup, android.view.View
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        this.mLayoutOrScrollCounter = 0;
        this.mIsAttached = true;
        this.mFirstLayoutComplete = this.mFirstLayoutComplete && !isLayoutRequested();
        o oVar = this.mLayout;
        if (oVar != null) {
            oVar.i = true;
            oVar.r0();
        }
        this.mPostedAnimatorRunner = false;
        if (ALLOW_THREAD_GAP_WORK) {
            ThreadLocal<androidx.recyclerview.widget.j> threadLocal = androidx.recyclerview.widget.j.e;
            androidx.recyclerview.widget.j jVar = threadLocal.get();
            this.mGapWorker = jVar;
            if (jVar == null) {
                this.mGapWorker = new androidx.recyclerview.widget.j();
                int i2 = a.h.h.q.e;
                Display display = getDisplay();
                float f2 = 60.0f;
                if (!isInEditMode() && display != null) {
                    float refreshRate = display.getRefreshRate();
                    if (refreshRate >= 30.0f) {
                        f2 = refreshRate;
                    }
                }
                androidx.recyclerview.widget.j jVar2 = this.mGapWorker;
                jVar2.f1386c = (long) (1.0E9f / f2);
                threadLocal.set(jVar2);
            }
            this.mGapWorker.f1384a.add(this);
        }
    }

    public void onChildAttachedToWindow(View view) {
    }

    public void onChildDetachedFromWindow(View view) {
    }

    @Override // android.view.ViewGroup, android.view.View
    protected void onDetachedFromWindow() {
        androidx.recyclerview.widget.j jVar;
        super.onDetachedFromWindow();
        l lVar = this.mItemAnimator;
        if (lVar != null) {
            lVar.f();
        }
        stopScroll();
        this.mIsAttached = false;
        o oVar = this.mLayout;
        if (oVar != null) {
            v vVar = this.mRecycler;
            oVar.i = false;
            oVar.t0(this, vVar);
        }
        this.mPendingAccessibilityImportanceChange.clear();
        removeCallbacks(this.mItemAnimatorRunner);
        Objects.requireNonNull(this.mViewInfoStore);
        while (v.a.f1421d.b() != null) {
        }
        if (!ALLOW_THREAD_GAP_WORK || (jVar = this.mGapWorker) == null) {
            return;
        }
        jVar.f1384a.remove(this);
        this.mGapWorker = null;
    }

    @Override // android.view.View
    public void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        int size = this.mItemDecorations.size();
        for (int i2 = 0; i2 < size; i2++) {
            this.mItemDecorations.get(i2).e(canvas, this, this.mState);
        }
    }

    void onEnterLayoutOrScroll() {
        this.mLayoutOrScrollCounter++;
    }

    void onExitLayoutOrScroll() {
        onExitLayoutOrScroll(true);
    }

    void onExitLayoutOrScroll(boolean z2) {
        int i2 = this.mLayoutOrScrollCounter - 1;
        this.mLayoutOrScrollCounter = i2;
        if (i2 < 1) {
            this.mLayoutOrScrollCounter = 0;
            if (z2) {
                dispatchContentChangedIfNecessary();
                dispatchPendingImportantForAccessibilityChanges();
            }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:20:0x0068  */
    @Override // android.view.View
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public boolean onGenericMotionEvent(android.view.MotionEvent r6) {
        /*
            r5 = this;
            androidx.recyclerview.widget.RecyclerView$o r0 = r5.mLayout
            r1 = 0
            if (r0 != 0) goto L6
            return r1
        L6:
            boolean r0 = r5.mLayoutSuppressed
            if (r0 == 0) goto Lb
            return r1
        Lb:
            int r0 = r6.getAction()
            r2 = 8
            if (r0 != r2) goto L77
            int r0 = r6.getSource()
            r0 = r0 & 2
            r2 = 0
            if (r0 == 0) goto L3e
            androidx.recyclerview.widget.RecyclerView$o r0 = r5.mLayout
            boolean r0 = r0.i()
            if (r0 == 0) goto L2c
            r0 = 9
            float r0 = r6.getAxisValue(r0)
            float r0 = -r0
            goto L2d
        L2c:
            r0 = r2
        L2d:
            androidx.recyclerview.widget.RecyclerView$o r3 = r5.mLayout
            boolean r3 = r3.h()
            if (r3 == 0) goto L3c
            r3 = 10
            float r3 = r6.getAxisValue(r3)
            goto L64
        L3c:
            r3 = r2
            goto L64
        L3e:
            int r0 = r6.getSource()
            r3 = 4194304(0x400000, float:5.877472E-39)
            r0 = r0 & r3
            if (r0 == 0) goto L62
            r0 = 26
            float r0 = r6.getAxisValue(r0)
            androidx.recyclerview.widget.RecyclerView$o r3 = r5.mLayout
            boolean r3 = r3.i()
            if (r3 == 0) goto L57
            float r0 = -r0
            goto L3c
        L57:
            androidx.recyclerview.widget.RecyclerView$o r3 = r5.mLayout
            boolean r3 = r3.h()
            if (r3 == 0) goto L62
            r3 = r0
            r0 = r2
            goto L64
        L62:
            r0 = r2
            r3 = r0
        L64:
            int r4 = (r0 > r2 ? 1 : (r0 == r2 ? 0 : -1))
            if (r4 != 0) goto L6c
            int r2 = (r3 > r2 ? 1 : (r3 == r2 ? 0 : -1))
            if (r2 == 0) goto L77
        L6c:
            float r2 = r5.mScaledHorizontalScrollFactor
            float r3 = r3 * r2
            int r2 = (int) r3
            float r3 = r5.mScaledVerticalScrollFactor
            float r0 = r0 * r3
            int r0 = (int) r0
            r5.scrollByInternal(r2, r0, r6)
        L77:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.onGenericMotionEvent(android.view.MotionEvent):boolean");
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // android.view.ViewGroup
    public boolean onInterceptTouchEvent(MotionEvent motionEvent) {
        boolean z2;
        if (this.mLayoutSuppressed) {
            return false;
        }
        this.mInterceptingOnItemTouchListener = null;
        if (findInterceptingOnItemTouchListener(motionEvent)) {
            cancelScroll();
            return true;
        }
        o oVar = this.mLayout;
        if (oVar == null) {
            return false;
        }
        boolean h2 = oVar.h();
        boolean i2 = this.mLayout.i();
        if (this.mVelocityTracker == null) {
            this.mVelocityTracker = VelocityTracker.obtain();
        }
        this.mVelocityTracker.addMovement(motionEvent);
        int actionMasked = motionEvent.getActionMasked();
        int actionIndex = motionEvent.getActionIndex();
        if (actionMasked == 0) {
            if (this.mIgnoreMotionEventTillDown) {
                this.mIgnoreMotionEventTillDown = false;
            }
            this.mScrollPointerId = motionEvent.getPointerId(0);
            int x2 = (int) (motionEvent.getX() + 0.5f);
            this.mLastTouchX = x2;
            this.mInitialTouchX = x2;
            int y2 = (int) (motionEvent.getY() + 0.5f);
            this.mLastTouchY = y2;
            this.mInitialTouchY = y2;
            if (this.mScrollState == 2) {
                getParent().requestDisallowInterceptTouchEvent(true);
                setScrollState(1);
                stopNestedScroll(1);
            }
            int[] iArr = this.mNestedOffsets;
            iArr[1] = 0;
            iArr[0] = 0;
            int i3 = h2;
            if (i2) {
                i3 = (h2 ? 1 : 0) | 2;
            }
            startNestedScroll(i3, 0);
        } else if (actionMasked == 1) {
            this.mVelocityTracker.clear();
            stopNestedScroll(0);
        } else if (actionMasked == 2) {
            int findPointerIndex = motionEvent.findPointerIndex(this.mScrollPointerId);
            if (findPointerIndex < 0) {
                StringBuilder j2 = b.b.a.a.a.j("Error processing scroll; pointer index for id ");
                j2.append(this.mScrollPointerId);
                j2.append(" not found. Did any MotionEvents get skipped?");
                Log.e(TAG, j2.toString());
                return false;
            }
            int x3 = (int) (motionEvent.getX(findPointerIndex) + 0.5f);
            int y3 = (int) (motionEvent.getY(findPointerIndex) + 0.5f);
            if (this.mScrollState != 1) {
                int i4 = x3 - this.mInitialTouchX;
                int i5 = y3 - this.mInitialTouchY;
                if (h2 == 0 || Math.abs(i4) <= this.mTouchSlop) {
                    z2 = false;
                } else {
                    this.mLastTouchX = x3;
                    z2 = true;
                }
                if (i2 && Math.abs(i5) > this.mTouchSlop) {
                    this.mLastTouchY = y3;
                    z2 = true;
                }
                if (z2) {
                    setScrollState(1);
                }
            }
        } else if (actionMasked == 3) {
            cancelScroll();
        } else if (actionMasked == 5) {
            this.mScrollPointerId = motionEvent.getPointerId(actionIndex);
            int x4 = (int) (motionEvent.getX(actionIndex) + 0.5f);
            this.mLastTouchX = x4;
            this.mInitialTouchX = x4;
            int y4 = (int) (motionEvent.getY(actionIndex) + 0.5f);
            this.mLastTouchY = y4;
            this.mInitialTouchY = y4;
        } else if (actionMasked == 6) {
            onPointerUp(motionEvent);
        }
        return this.mScrollState == 1;
    }

    @Override // android.view.ViewGroup, android.view.View
    protected void onLayout(boolean z2, int i2, int i3, int i4, int i5) {
        Trace.beginSection(TRACE_ON_LAYOUT_TAG);
        dispatchLayout();
        Trace.endSection();
        this.mFirstLayoutComplete = true;
    }

    @Override // android.view.View
    protected void onMeasure(int i2, int i3) {
        o oVar = this.mLayout;
        if (oVar == null) {
            defaultOnMeasure(i2, i3);
            return;
        }
        boolean z2 = false;
        if (oVar.g0()) {
            int mode = View.MeasureSpec.getMode(i2);
            int mode2 = View.MeasureSpec.getMode(i3);
            this.mLayout.J0(i2, i3);
            if (mode == 1073741824 && mode2 == 1073741824) {
                z2 = true;
            }
            if (z2 || this.mAdapter == null) {
                return;
            }
            if (this.mState.f1252d == 1) {
                dispatchLayoutStep1();
            }
            this.mLayout.b1(i2, i3);
            this.mState.i = true;
            dispatchLayoutStep2();
            this.mLayout.d1(i2, i3);
            if (this.mLayout.g1()) {
                this.mLayout.b1(View.MeasureSpec.makeMeasureSpec(getMeasuredWidth(), 1073741824), View.MeasureSpec.makeMeasureSpec(getMeasuredHeight(), 1073741824));
                this.mState.i = true;
                dispatchLayoutStep2();
                this.mLayout.d1(i2, i3);
                return;
            }
            return;
        }
        if (this.mHasFixedSize) {
            this.mLayout.J0(i2, i3);
            return;
        }
        if (this.mAdapterUpdateDuringMeasure) {
            startInterceptRequestLayout();
            onEnterLayoutOrScroll();
            processAdapterUpdatesAndSetAnimationFlags();
            onExitLayoutOrScroll();
            A a2 = this.mState;
            if (a2.k) {
                a2.g = true;
            } else {
                this.mAdapterHelper.c();
                this.mState.g = false;
            }
            this.mAdapterUpdateDuringMeasure = false;
            stopInterceptRequestLayout(false);
        } else if (this.mState.k) {
            setMeasuredDimension(getMeasuredWidth(), getMeasuredHeight());
            return;
        }
        g gVar = this.mAdapter;
        if (gVar != null) {
            this.mState.e = gVar.b();
        } else {
            this.mState.e = 0;
        }
        startInterceptRequestLayout();
        this.mLayout.J0(i2, i3);
        stopInterceptRequestLayout(false);
        this.mState.g = false;
    }

    @Override // android.view.ViewGroup
    protected boolean onRequestFocusInDescendants(int i2, Rect rect) {
        if (isComputingLayout()) {
            return false;
        }
        return super.onRequestFocusInDescendants(i2, rect);
    }

    @Override // android.view.View
    protected void onRestoreInstanceState(Parcelable parcelable) {
        Parcelable parcelable2;
        if (!(parcelable instanceof y)) {
            super.onRestoreInstanceState(parcelable);
            return;
        }
        y yVar = (y) parcelable;
        this.mPendingSavedState = yVar;
        super.onRestoreInstanceState(yVar.f());
        o oVar = this.mLayout;
        if (oVar == null || (parcelable2 = this.mPendingSavedState.f1300c) == null) {
            return;
        }
        oVar.M0(parcelable2);
    }

    @Override // android.view.View
    protected Parcelable onSaveInstanceState() {
        y yVar = new y(super.onSaveInstanceState());
        y yVar2 = this.mPendingSavedState;
        if (yVar2 != null) {
            yVar.f1300c = yVar2.f1300c;
        } else {
            o oVar = this.mLayout;
            yVar.f1300c = oVar != null ? oVar.N0() : null;
        }
        return yVar;
    }

    public void onScrollStateChanged(int i2) {
    }

    public void onScrolled(int i2, int i3) {
    }

    @Override // android.view.View
    protected void onSizeChanged(int i2, int i3, int i4, int i5) {
        super.onSizeChanged(i2, i3, i4, i5);
        if (i2 == i4 && i3 == i5) {
            return;
        }
        invalidateGlows();
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:50:0x00dc  */
    /* JADX WARN: Removed duplicated region for block: B:57:0x00f0  */
    @Override // android.view.View
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public boolean onTouchEvent(android.view.MotionEvent r18) {
        /*
            Method dump skipped, instructions count: 472
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.onTouchEvent(android.view.MotionEvent):boolean");
    }

    void postAnimationRunner() {
        if (this.mPostedAnimatorRunner || !this.mIsAttached) {
            return;
        }
        Runnable runnable = this.mItemAnimatorRunner;
        int i2 = a.h.h.q.e;
        postOnAnimation(runnable);
        this.mPostedAnimatorRunner = true;
    }

    void processDataSetCompletelyChanged(boolean z2) {
        this.mDispatchItemsChangedEvent = z2 | this.mDispatchItemsChangedEvent;
        this.mDataSetHasChangedAfterLayout = true;
        markKnownViewsInvalid();
    }

    void recordAnimationInfoIfBouncedHiddenView(D d2, l.c cVar) {
        d2.u(0, 8192);
        if (this.mState.h && d2.p() && !d2.m() && !d2.w()) {
            this.mViewInfoStore.f1420b.h(getChangedHolderKey(d2), d2);
        }
        this.mViewInfoStore.c(d2, cVar);
    }

    void removeAndRecycleViews() {
        l lVar = this.mItemAnimator;
        if (lVar != null) {
            lVar.f();
        }
        o oVar = this.mLayout;
        if (oVar != null) {
            oVar.R0(this.mRecycler);
            this.mLayout.S0(this.mRecycler);
        }
        this.mRecycler.b();
    }

    boolean removeAnimatingView(View view) {
        startInterceptRequestLayout();
        boolean o2 = this.mChildHelper.o(view);
        if (o2) {
            D childViewHolderInt = getChildViewHolderInt(view);
            this.mRecycler.n(childViewHolderInt);
            this.mRecycler.j(childViewHolderInt);
        }
        stopInterceptRequestLayout(!o2);
        return o2;
    }

    @Override // android.view.ViewGroup
    protected void removeDetachedView(View view, boolean z2) {
        D childViewHolderInt = getChildViewHolderInt(view);
        if (childViewHolderInt != null) {
            if (childViewHolderInt.o()) {
                childViewHolderInt.j &= -257;
            } else if (!childViewHolderInt.w()) {
                throw new IllegalArgumentException("Called removeDetachedView with a view which is not flagged as tmp detached." + childViewHolderInt + exceptionLabel());
            }
        }
        view.clearAnimation();
        dispatchChildDetached(view);
        super.removeDetachedView(view, z2);
    }

    public void removeItemDecoration(n nVar) {
        o oVar = this.mLayout;
        if (oVar != null) {
            oVar.g("Cannot remove item decoration during a scroll  or layout");
        }
        this.mItemDecorations.remove(nVar);
        if (this.mItemDecorations.isEmpty()) {
            setWillNotDraw(getOverScrollMode() == 2);
        }
        markItemDecorInsetsDirty();
        requestLayout();
    }

    public void removeItemDecorationAt(int i2) {
        int itemDecorationCount = getItemDecorationCount();
        if (i2 >= 0 && i2 < itemDecorationCount) {
            removeItemDecoration(getItemDecorationAt(i2));
            return;
        }
        throw new IndexOutOfBoundsException(i2 + " is an invalid index for size " + itemDecorationCount);
    }

    public void removeOnChildAttachStateChangeListener(q qVar) {
        List<q> list = this.mOnChildAttachStateListeners;
        if (list == null) {
            return;
        }
        list.remove(qVar);
    }

    public void removeOnItemTouchListener(s sVar) {
        this.mOnItemTouchListeners.remove(sVar);
        if (this.mInterceptingOnItemTouchListener == sVar) {
            this.mInterceptingOnItemTouchListener = null;
        }
    }

    public void removeOnScrollListener(t tVar) {
        List<t> list = this.mScrollListeners;
        if (list != null) {
            list.remove(tVar);
        }
    }

    void repositionShadowingViews() {
        D d2;
        int e2 = this.mChildHelper.e();
        for (int i2 = 0; i2 < e2; i2++) {
            View d3 = this.mChildHelper.d(i2);
            D childViewHolder = getChildViewHolder(d3);
            if (childViewHolder != null && (d2 = childViewHolder.i) != null) {
                View view = d2.f1257a;
                int left = d3.getLeft();
                int top = d3.getTop();
                if (left != view.getLeft() || top != view.getTop()) {
                    view.layout(left, top, view.getWidth() + left, view.getHeight() + top);
                }
            }
        }
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public void requestChildFocus(View view, View view2) {
        if (!this.mLayout.L0(this, view, view2) && view2 != null) {
            requestChildOnScreen(view, view2);
        }
        super.requestChildFocus(view, view2);
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public boolean requestChildRectangleOnScreen(View view, Rect rect, boolean z2) {
        return this.mLayout.V0(this, view, rect, z2, false);
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public void requestDisallowInterceptTouchEvent(boolean z2) {
        int size = this.mOnItemTouchListeners.size();
        for (int i2 = 0; i2 < size; i2++) {
            this.mOnItemTouchListeners.get(i2).c(z2);
        }
        super.requestDisallowInterceptTouchEvent(z2);
    }

    @Override // android.view.View, android.view.ViewParent
    public void requestLayout() {
        if (this.mInterceptRequestLayoutDepth != 0 || this.mLayoutSuppressed) {
            this.mLayoutWasDefered = true;
        } else {
            super.requestLayout();
        }
    }

    void saveOldPositions() {
        int h2 = this.mChildHelper.h();
        for (int i2 = 0; i2 < h2; i2++) {
            D childViewHolderInt = getChildViewHolderInt(this.mChildHelper.g(i2));
            if (!childViewHolderInt.w() && childViewHolderInt.f1260d == -1) {
                childViewHolderInt.f1260d = childViewHolderInt.f1259c;
            }
        }
    }

    @Override // android.view.View
    public void scrollBy(int i2, int i3) {
        o oVar = this.mLayout;
        if (oVar == null) {
            Log.e(TAG, "Cannot scroll without a LayoutManager set. Call setLayoutManager with a non-null argument.");
            return;
        }
        if (this.mLayoutSuppressed) {
            return;
        }
        boolean h2 = oVar.h();
        boolean i4 = this.mLayout.i();
        if (h2 || i4) {
            if (!h2) {
                i2 = 0;
            }
            if (!i4) {
                i3 = 0;
            }
            scrollByInternal(i2, i3, null);
        }
    }

    boolean scrollByInternal(int i2, int i3, MotionEvent motionEvent) {
        int i4;
        int i5;
        int i6;
        int i7;
        consumePendingUpdateOperations();
        if (this.mAdapter != null) {
            int[] iArr = this.mReusableIntPair;
            iArr[0] = 0;
            iArr[1] = 0;
            scrollStep(i2, i3, iArr);
            int[] iArr2 = this.mReusableIntPair;
            int i8 = iArr2[0];
            int i9 = iArr2[1];
            i4 = i9;
            i5 = i8;
            i6 = i2 - i8;
            i7 = i3 - i9;
        } else {
            i4 = 0;
            i5 = 0;
            i6 = 0;
            i7 = 0;
        }
        if (!this.mItemDecorations.isEmpty()) {
            invalidate();
        }
        int[] iArr3 = this.mReusableIntPair;
        iArr3[0] = 0;
        iArr3[1] = 0;
        dispatchNestedScroll(i5, i4, i6, i7, this.mScrollOffset, 0, iArr3);
        int[] iArr4 = this.mReusableIntPair;
        int i10 = i6 - iArr4[0];
        int i11 = i7 - iArr4[1];
        boolean z2 = (iArr4[0] == 0 && iArr4[1] == 0) ? false : true;
        int i12 = this.mLastTouchX;
        int[] iArr5 = this.mScrollOffset;
        this.mLastTouchX = i12 - iArr5[0];
        this.mLastTouchY -= iArr5[1];
        int[] iArr6 = this.mNestedOffsets;
        iArr6[0] = iArr6[0] + iArr5[0];
        iArr6[1] = iArr6[1] + iArr5[1];
        if (getOverScrollMode() != 2) {
            if (motionEvent != null) {
                if (!((motionEvent.getSource() & 8194) == 8194)) {
                    pullGlows(motionEvent.getX(), i10, motionEvent.getY(), i11);
                }
            }
            considerReleasingGlowsOnScroll(i2, i3);
        }
        if (i5 != 0 || i4 != 0) {
            dispatchOnScrolled(i5, i4);
        }
        if (!awakenScrollBars()) {
            invalidate();
        }
        return (!z2 && i5 == 0 && i4 == 0) ? false : true;
    }

    void scrollStep(int i2, int i3, int[] iArr) {
        startInterceptRequestLayout();
        onEnterLayoutOrScroll();
        Trace.beginSection(TRACE_SCROLL_TAG);
        fillRemainingScrollValues(this.mState);
        int X0 = i2 != 0 ? this.mLayout.X0(i2, this.mRecycler, this.mState) : 0;
        int Z0 = i3 != 0 ? this.mLayout.Z0(i3, this.mRecycler, this.mState) : 0;
        Trace.endSection();
        repositionShadowingViews();
        onExitLayoutOrScroll();
        stopInterceptRequestLayout(false);
        if (iArr != null) {
            iArr[0] = X0;
            iArr[1] = Z0;
        }
    }

    @Override // android.view.View
    public void scrollTo(int i2, int i3) {
        Log.w(TAG, "RecyclerView does not support scrolling to an absolute position. Use scrollToPosition instead");
    }

    public void scrollToPosition(int i2) {
        if (this.mLayoutSuppressed) {
            return;
        }
        stopScroll();
        o oVar = this.mLayout;
        if (oVar == null) {
            Log.e(TAG, "Cannot scroll to position a LayoutManager set. Call setLayoutManager with a non-null argument.");
        } else {
            oVar.Y0(i2);
            awakenScrollBars();
        }
    }

    @Override // android.view.View, android.view.accessibility.AccessibilityEventSource
    public void sendAccessibilityEventUnchecked(AccessibilityEvent accessibilityEvent) {
        if (shouldDeferAccessibilityEvent(accessibilityEvent)) {
            return;
        }
        super.sendAccessibilityEventUnchecked(accessibilityEvent);
    }

    public void setAccessibilityDelegateCompat(androidx.recyclerview.widget.q qVar) {
        this.mAccessibilityDelegate = qVar;
        a.h.h.q.n(this, qVar);
    }

    public void setAdapter(g gVar) {
        setLayoutFrozen(false);
        setAdapterInternal(gVar, false, true);
        processDataSetCompletelyChanged(false);
        requestLayout();
    }

    public void setChildDrawingOrderCallback(j jVar) {
        if (jVar == this.mChildDrawingOrderCallback) {
            return;
        }
        this.mChildDrawingOrderCallback = jVar;
        setChildrenDrawingOrderEnabled(jVar != null);
    }

    boolean setChildImportantForAccessibilityInternal(D d2, int i2) {
        if (isComputingLayout()) {
            d2.q = i2;
            this.mPendingAccessibilityImportanceChange.add(d2);
            return false;
        }
        View view = d2.f1257a;
        int i3 = a.h.h.q.e;
        view.setImportantForAccessibility(i2);
        return true;
    }

    @Override // android.view.ViewGroup
    public void setClipToPadding(boolean z2) {
        if (z2 != this.mClipToPadding) {
            invalidateGlows();
        }
        this.mClipToPadding = z2;
        super.setClipToPadding(z2);
        if (this.mFirstLayoutComplete) {
            requestLayout();
        }
    }

    public void setEdgeEffectFactory(k kVar) {
        Objects.requireNonNull(kVar);
        this.mEdgeEffectFactory = kVar;
        invalidateGlows();
    }

    public void setHasFixedSize(boolean z2) {
        this.mHasFixedSize = z2;
    }

    public void setItemAnimator(l lVar) {
        l lVar2 = this.mItemAnimator;
        if (lVar2 != null) {
            lVar2.f();
            this.mItemAnimator.n(null);
        }
        this.mItemAnimator = lVar;
        if (lVar != null) {
            lVar.n(this.mItemAnimatorListener);
        }
    }

    public void setItemViewCacheSize(int i2) {
        this.mRecycler.l(i2);
    }

    @Deprecated
    public void setLayoutFrozen(boolean z2) {
        suppressLayout(z2);
    }

    public void setLayoutManager(o oVar) {
        if (oVar == this.mLayout) {
            return;
        }
        stopScroll();
        if (this.mLayout != null) {
            l lVar = this.mItemAnimator;
            if (lVar != null) {
                lVar.f();
            }
            this.mLayout.R0(this.mRecycler);
            this.mLayout.S0(this.mRecycler);
            this.mRecycler.b();
            if (this.mIsAttached) {
                o oVar2 = this.mLayout;
                v vVar = this.mRecycler;
                oVar2.i = false;
                oVar2.t0(this, vVar);
            }
            this.mLayout.e1(null);
            this.mLayout = null;
        } else {
            this.mRecycler.b();
        }
        b bVar = this.mChildHelper;
        b.a aVar = bVar.f1337b;
        aVar.f1339a = 0L;
        b.a aVar2 = aVar.f1340b;
        if (aVar2 != null) {
            aVar2.g();
        }
        int size = bVar.f1338c.size();
        while (true) {
            size--;
            if (size < 0) {
                break;
            }
            b.InterfaceC0038b interfaceC0038b = bVar.f1336a;
            View view = bVar.f1338c.get(size);
            e eVar = (e) interfaceC0038b;
            Objects.requireNonNull(eVar);
            D childViewHolderInt = getChildViewHolderInt(view);
            if (childViewHolderInt != null) {
                childViewHolderInt.s(RecyclerView.this);
            }
            bVar.f1338c.remove(size);
        }
        e eVar2 = (e) bVar.f1336a;
        int b2 = eVar2.b();
        for (int i2 = 0; i2 < b2; i2++) {
            View a2 = eVar2.a(i2);
            RecyclerView.this.dispatchChildDetached(a2);
            a2.clearAnimation();
        }
        RecyclerView.this.removeAllViews();
        this.mLayout = oVar;
        if (oVar != null) {
            if (oVar.f1276b != null) {
                throw new IllegalArgumentException("LayoutManager " + oVar + " is already attached to a RecyclerView:" + oVar.f1276b.exceptionLabel());
            }
            oVar.e1(this);
            if (this.mIsAttached) {
                o oVar3 = this.mLayout;
                oVar3.i = true;
                oVar3.r0();
            }
        }
        this.mRecycler.o();
        requestLayout();
    }

    @Override // android.view.ViewGroup
    @Deprecated
    public void setLayoutTransition(LayoutTransition layoutTransition) {
        if (layoutTransition != null) {
            throw new IllegalArgumentException("Providing a LayoutTransition into RecyclerView is not supported. Please use setItemAnimator() instead for animating changes to the items in this RecyclerView");
        }
        super.setLayoutTransition(null);
    }

    @Override // android.view.View
    public void setNestedScrollingEnabled(boolean z2) {
        getScrollingChildHelper().k(z2);
    }

    public void setOnFlingListener(r rVar) {
        this.mOnFlingListener = rVar;
    }

    @Deprecated
    public void setOnScrollListener(t tVar) {
        this.mScrollListener = tVar;
    }

    public void setPreserveFocusAfterLayout(boolean z2) {
        this.mPreserveFocusAfterLayout = z2;
    }

    public void setRecycledViewPool(u uVar) {
        v vVar = this.mRecycler;
        u uVar2 = vVar.g;
        if (uVar2 != null) {
            uVar2.b();
        }
        vVar.g = uVar;
        if (uVar == null || RecyclerView.this.getAdapter() == null) {
            return;
        }
        vVar.g.a();
    }

    public void setRecyclerListener(w wVar) {
        this.mRecyclerListener = wVar;
    }

    void setScrollState(int i2) {
        if (i2 == this.mScrollState) {
            return;
        }
        this.mScrollState = i2;
        if (i2 != 2) {
            stopScrollersInternal();
        }
        dispatchOnScrollStateChanged(i2);
    }

    public void setScrollingTouchSlop(int i2) {
        int scaledTouchSlop;
        ViewConfiguration viewConfiguration = ViewConfiguration.get(getContext());
        if (i2 != 0) {
            if (i2 == 1) {
                scaledTouchSlop = viewConfiguration.getScaledPagingTouchSlop();
                this.mTouchSlop = scaledTouchSlop;
            } else {
                Log.w(TAG, "setScrollingTouchSlop(): bad argument constant " + i2 + "; using default value");
            }
        }
        scaledTouchSlop = viewConfiguration.getScaledTouchSlop();
        this.mTouchSlop = scaledTouchSlop;
    }

    public void setViewCacheExtension(B b2) {
        Objects.requireNonNull(this.mRecycler);
    }

    boolean shouldDeferAccessibilityEvent(AccessibilityEvent accessibilityEvent) {
        if (!isComputingLayout()) {
            return false;
        }
        int contentChangeTypes = accessibilityEvent != null ? accessibilityEvent.getContentChangeTypes() : 0;
        this.mEatenAccessibilityChangeFlags |= contentChangeTypes != 0 ? contentChangeTypes : 0;
        return true;
    }

    public void smoothScrollBy(int i2, int i3) {
        smoothScrollBy(i2, i3, null);
    }

    public void smoothScrollBy(int i2, int i3, Interpolator interpolator) {
        smoothScrollBy(i2, i3, interpolator, UNDEFINED_DURATION);
    }

    public void smoothScrollBy(int i2, int i3, Interpolator interpolator, int i4) {
        smoothScrollBy(i2, i3, interpolator, i4, false);
    }

    void smoothScrollBy(int i2, int i3, Interpolator interpolator, int i4, boolean z2) {
        o oVar = this.mLayout;
        if (oVar == null) {
            Log.e(TAG, "Cannot smooth scroll without a LayoutManager set. Call setLayoutManager with a non-null argument.");
            return;
        }
        if (this.mLayoutSuppressed) {
            return;
        }
        if (!oVar.h()) {
            i2 = 0;
        }
        if (!this.mLayout.i()) {
            i3 = 0;
        }
        if (i2 == 0 && i3 == 0) {
            return;
        }
        if (!(i4 == Integer.MIN_VALUE || i4 > 0)) {
            scrollBy(i2, i3);
            return;
        }
        if (z2) {
            int i5 = i2 != 0 ? 1 : 0;
            if (i3 != 0) {
                i5 |= 2;
            }
            startNestedScroll(i5, 1);
        }
        this.mViewFlinger.c(i2, i3, i4, interpolator);
    }

    public void smoothScrollToPosition(int i2) {
        if (this.mLayoutSuppressed) {
            return;
        }
        o oVar = this.mLayout;
        if (oVar == null) {
            Log.e(TAG, "Cannot smooth scroll without a LayoutManager set. Call setLayoutManager with a non-null argument.");
        } else {
            oVar.i1(this, this.mState, i2);
        }
    }

    void startInterceptRequestLayout() {
        int i2 = this.mInterceptRequestLayoutDepth + 1;
        this.mInterceptRequestLayoutDepth = i2;
        if (i2 != 1 || this.mLayoutSuppressed) {
            return;
        }
        this.mLayoutWasDefered = false;
    }

    @Override // android.view.View
    public boolean startNestedScroll(int i2) {
        return getScrollingChildHelper().l(i2, 0);
    }

    public boolean startNestedScroll(int i2, int i3) {
        return getScrollingChildHelper().l(i2, i3);
    }

    void stopInterceptRequestLayout(boolean z2) {
        if (this.mInterceptRequestLayoutDepth < 1) {
            this.mInterceptRequestLayoutDepth = 1;
        }
        if (!z2 && !this.mLayoutSuppressed) {
            this.mLayoutWasDefered = false;
        }
        if (this.mInterceptRequestLayoutDepth == 1) {
            if (z2 && this.mLayoutWasDefered && !this.mLayoutSuppressed && this.mLayout != null && this.mAdapter != null) {
                dispatchLayout();
            }
            if (!this.mLayoutSuppressed) {
                this.mLayoutWasDefered = false;
            }
        }
        this.mInterceptRequestLayoutDepth--;
    }

    @Override // android.view.View
    public void stopNestedScroll() {
        getScrollingChildHelper().m(0);
    }

    public void stopNestedScroll(int i2) {
        getScrollingChildHelper().m(i2);
    }

    public void stopScroll() {
        setScrollState(0);
        stopScrollersInternal();
    }

    @Override // android.view.ViewGroup
    public final void suppressLayout(boolean z2) {
        if (z2 != this.mLayoutSuppressed) {
            assertNotInLayoutOrScroll("Do not suppressLayout in layout or scroll");
            if (z2) {
                long uptimeMillis = SystemClock.uptimeMillis();
                onTouchEvent(MotionEvent.obtain(uptimeMillis, uptimeMillis, 3, 0.0f, 0.0f, 0));
                this.mLayoutSuppressed = true;
                this.mIgnoreMotionEventTillDown = true;
                stopScroll();
                return;
            }
            this.mLayoutSuppressed = false;
            if (this.mLayoutWasDefered && this.mLayout != null && this.mAdapter != null) {
                requestLayout();
            }
            this.mLayoutWasDefered = false;
        }
    }

    public void swapAdapter(g gVar, boolean z2) {
        setLayoutFrozen(false);
        setAdapterInternal(gVar, true, z2);
        processDataSetCompletelyChanged(true);
        requestLayout();
    }

    void viewRangeUpdate(int i2, int i3, Object obj) {
        int i4;
        int i5;
        int h2 = this.mChildHelper.h();
        int i6 = i3 + i2;
        for (int i7 = 0; i7 < h2; i7++) {
            View g2 = this.mChildHelper.g(i7);
            D childViewHolderInt = getChildViewHolderInt(g2);
            if (childViewHolderInt != null && !childViewHolderInt.w() && (i5 = childViewHolderInt.f1259c) >= i2 && i5 < i6) {
                childViewHolderInt.b(2);
                childViewHolderInt.a(obj);
                ((p) g2.getLayoutParams()).f1287c = true;
            }
        }
        v vVar = this.mRecycler;
        int size = vVar.f1297c.size();
        while (true) {
            size--;
            if (size < 0) {
                return;
            }
            D d2 = vVar.f1297c.get(size);
            if (d2 != null && (i4 = d2.f1259c) >= i2 && i4 < i6) {
                d2.b(2);
                vVar.h(size);
            }
        }
    }
}
