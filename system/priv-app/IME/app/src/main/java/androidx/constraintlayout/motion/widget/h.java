package androidx.constraintlayout.motion.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseIntArray;
import java.util.HashMap;

/* loaded from: classes.dex */
public class h extends i {
    String g = null;
    int h = -1;
    int i = 0;
    float j = Float.NaN;
    float k = Float.NaN;
    float l = Float.NaN;
    float m = Float.NaN;
    float n = Float.NaN;
    float o = Float.NaN;
    int p = 0;
    private float q = Float.NaN;
    private float r = Float.NaN;

    private static class a {

        /* renamed from: a, reason: collision with root package name */
        private static SparseIntArray f905a;

        static {
            SparseIntArray sparseIntArray = new SparseIntArray();
            f905a = sparseIntArray;
            sparseIntArray.append(4, 1);
            f905a.append(2, 2);
            f905a.append(11, 3);
            f905a.append(0, 4);
            f905a.append(1, 5);
            f905a.append(8, 6);
            f905a.append(9, 7);
            f905a.append(3, 9);
            f905a.append(10, 8);
            f905a.append(7, 11);
            f905a.append(6, 12);
            f905a.append(5, 10);
        }

        static void a(h hVar, TypedArray typedArray) {
            float f;
            int indexCount = typedArray.getIndexCount();
            for (int i = 0; i < indexCount; i++) {
                int index = typedArray.getIndex(i);
                switch (f905a.get(index)) {
                    case 1:
                        if (MotionLayout.IS_IN_EDIT_MODE) {
                            int resourceId = typedArray.getResourceId(index, hVar.f898b);
                            hVar.f898b = resourceId;
                            if (resourceId != -1) {
                                break;
                            }
                            hVar.f899c = typedArray.getString(index);
                            break;
                        } else {
                            if (typedArray.peekValue(index).type != 3) {
                                hVar.f898b = typedArray.getResourceId(index, hVar.f898b);
                                continue;
                            }
                            hVar.f899c = typedArray.getString(index);
                        }
                    case 2:
                        hVar.f897a = typedArray.getInt(index, hVar.f897a);
                        continue;
                    case 3:
                        hVar.g = typedArray.peekValue(index).type == 3 ? typedArray.getString(index) : a.f.a.i.a.c.f140c[typedArray.getInteger(index, 0)];
                        continue;
                    case 4:
                        hVar.f = typedArray.getInteger(index, hVar.f);
                        continue;
                    case 5:
                        hVar.i = typedArray.getInt(index, hVar.i);
                        continue;
                    case 6:
                        hVar.l = typedArray.getFloat(index, hVar.l);
                        continue;
                    case MotionLayout.TOUCH_UP_NEVER_TO_END /* 7 */:
                        hVar.m = typedArray.getFloat(index, hVar.m);
                        continue;
                    case 8:
                        f = typedArray.getFloat(index, hVar.k);
                        hVar.j = f;
                        break;
                    case 9:
                        hVar.p = typedArray.getInt(index, hVar.p);
                        continue;
                    case 10:
                        hVar.h = typedArray.getInt(index, hVar.h);
                        continue;
                    case 11:
                        hVar.j = typedArray.getFloat(index, hVar.j);
                        continue;
                    case 12:
                        f = typedArray.getFloat(index, hVar.k);
                        break;
                    default:
                        StringBuilder j = b.b.a.a.a.j("unused attribute 0x");
                        j.append(Integer.toHexString(index));
                        j.append("   ");
                        j.append(f905a.get(index));
                        Log.e("KeyPosition", j.toString());
                        continue;
                }
                hVar.k = f;
            }
            if (hVar.f897a == -1) {
                Log.e("KeyPosition", "no frame position");
            }
        }
    }

    public h() {
        this.f900d = 2;
    }

    @Override // androidx.constraintlayout.motion.widget.d
    public void a(HashMap<String, a.f.b.a.d> hashMap) {
    }

    @Override // androidx.constraintlayout.motion.widget.d
    /* renamed from: b */
    public d clone() {
        h hVar = new h();
        super.c(this);
        hVar.g = this.g;
        hVar.h = this.h;
        hVar.i = this.i;
        hVar.j = this.j;
        hVar.k = Float.NaN;
        hVar.l = this.l;
        hVar.m = this.m;
        hVar.n = this.n;
        hVar.o = this.o;
        hVar.q = this.q;
        hVar.r = this.r;
        return hVar;
    }

    @Override // androidx.constraintlayout.motion.widget.d
    public void e(Context context, AttributeSet attributeSet) {
        a.a(this, context.obtainStyledAttributes(attributeSet, androidx.constraintlayout.widget.f.l));
    }

    public void j(int i) {
        this.p = i;
    }

    public void k(String str, Object obj) {
        float h;
        switch (str) {
            case "transitionEasing":
                this.g = obj.toString();
                return;
            case "percentWidth":
                this.j = h(obj);
                return;
            case "percentHeight":
                h = h(obj);
                break;
            case "drawPath":
                this.i = i(obj);
                return;
            case "sizePercent":
                h = h(obj);
                this.j = h;
                break;
            case "percentX":
                this.l = h(obj);
                return;
            case "percentY":
                this.m = h(obj);
                return;
            default:
                return;
        }
        this.k = h;
    }
}
