package androidx.appcompat.app;

import android.R;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.util.TypedValue;
import android.view.ContextThemeWrapper;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ListAdapter;
import android.widget.ListView;
import androidx.appcompat.app.AlertController;
import androidx.core.widget.NestedScrollView;
import java.util.Objects;

/* loaded from: classes.dex */
public class c extends l implements DialogInterface {

    /* renamed from: c, reason: collision with root package name */
    final AlertController f561c;

    public static class a {

        /* renamed from: a, reason: collision with root package name */
        private final AlertController.b f562a;

        /* renamed from: b, reason: collision with root package name */
        private final int f563b;

        public a(Context context) {
            int d2 = c.d(context, 0);
            this.f562a = new AlertController.b(new ContextThemeWrapper(context, c.d(context, d2)));
            this.f563b = d2;
        }

        public c a() {
            c cVar = new c(this.f562a.f553a, this.f563b);
            AlertController.b bVar = this.f562a;
            AlertController alertController = cVar.f561c;
            View view = bVar.e;
            if (view != null) {
                alertController.e(view);
            } else {
                CharSequence charSequence = bVar.f556d;
                if (charSequence != null) {
                    alertController.g(charSequence);
                }
                Drawable drawable = bVar.f555c;
                if (drawable != null) {
                    alertController.f(drawable);
                }
            }
            if (bVar.g != null) {
                AlertController.RecycleListView recycleListView = (AlertController.RecycleListView) bVar.f554b.inflate(alertController.L, (ViewGroup) null);
                int i = bVar.i ? alertController.N : alertController.O;
                ListAdapter listAdapter = bVar.g;
                if (listAdapter == null) {
                    listAdapter = new AlertController.d(bVar.f553a, i, R.id.text1, null);
                }
                alertController.H = listAdapter;
                alertController.I = bVar.j;
                if (bVar.h != null) {
                    recycleListView.setOnItemClickListener(new b(bVar, alertController));
                }
                if (bVar.i) {
                    recycleListView.setChoiceMode(1);
                }
                alertController.g = recycleListView;
            }
            Objects.requireNonNull(this.f562a);
            cVar.setCancelable(true);
            Objects.requireNonNull(this.f562a);
            cVar.setCanceledOnTouchOutside(true);
            Objects.requireNonNull(this.f562a);
            cVar.setOnCancelListener(null);
            Objects.requireNonNull(this.f562a);
            cVar.setOnDismissListener(null);
            DialogInterface.OnKeyListener onKeyListener = this.f562a.f;
            if (onKeyListener != null) {
                cVar.setOnKeyListener(onKeyListener);
            }
            return cVar;
        }

        public Context b() {
            return this.f562a.f553a;
        }

        public a c(ListAdapter listAdapter, DialogInterface.OnClickListener onClickListener) {
            AlertController.b bVar = this.f562a;
            bVar.g = listAdapter;
            bVar.h = onClickListener;
            return this;
        }

        public a d(View view) {
            this.f562a.e = view;
            return this;
        }

        public a e(Drawable drawable) {
            this.f562a.f555c = drawable;
            return this;
        }

        public a f(DialogInterface.OnKeyListener onKeyListener) {
            this.f562a.f = onKeyListener;
            return this;
        }

        public a g(ListAdapter listAdapter, int i, DialogInterface.OnClickListener onClickListener) {
            AlertController.b bVar = this.f562a;
            bVar.g = listAdapter;
            bVar.h = onClickListener;
            bVar.j = i;
            bVar.i = true;
            return this;
        }

        public a h(CharSequence charSequence) {
            this.f562a.f556d = charSequence;
            return this;
        }
    }

    protected c(Context context, int i) {
        super(context, d(context, i));
        this.f561c = new AlertController(getContext(), this, getWindow());
    }

    static int d(Context context, int i) {
        if (((i >>> 24) & 255) >= 1) {
            return i;
        }
        TypedValue typedValue = new TypedValue();
        context.getTheme().resolveAttribute(org.libpag.R.attr.alertDialogTheme, typedValue, true);
        return typedValue.resourceId;
    }

    public ListView c() {
        return this.f561c.g;
    }

    @Override // androidx.appcompat.app.l, android.app.Dialog
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        this.f561c.c();
    }

    @Override // android.app.Dialog, android.view.KeyEvent.Callback
    public boolean onKeyDown(int i, KeyEvent keyEvent) {
        NestedScrollView nestedScrollView = this.f561c.A;
        if (nestedScrollView != null && nestedScrollView.executeKeyEvent(keyEvent)) {
            return true;
        }
        return super.onKeyDown(i, keyEvent);
    }

    @Override // android.app.Dialog, android.view.KeyEvent.Callback
    public boolean onKeyUp(int i, KeyEvent keyEvent) {
        NestedScrollView nestedScrollView = this.f561c.A;
        if (nestedScrollView != null && nestedScrollView.executeKeyEvent(keyEvent)) {
            return true;
        }
        return super.onKeyUp(i, keyEvent);
    }

    @Override // androidx.appcompat.app.l, android.app.Dialog
    public void setTitle(CharSequence charSequence) {
        super.setTitle(charSequence);
        this.f561c.g(charSequence);
    }
}
