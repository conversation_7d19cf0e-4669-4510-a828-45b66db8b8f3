package androidx.constraintlayout.helper.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import androidx.constraintlayout.motion.widget.MotionHelper;
import androidx.constraintlayout.motion.widget.MotionLayout;
import androidx.constraintlayout.motion.widget.q;
import androidx.constraintlayout.widget.c;
import androidx.constraintlayout.widget.f;
import java.util.ArrayList;
import java.util.Iterator;

/* loaded from: classes.dex */
public class Carousel extends MotionHelper {
    private static final boolean DEBUG = false;
    private static final String TAG = "Carousel";
    public static final int TOUCH_UP_CARRY_ON = 2;
    public static final int TOUCH_UP_IMMEDIATE_STOP = 1;
    private int backwardTransition;
    private float dampening;
    private int emptyViewBehavior;
    private int firstViewReference;
    private int forwardTransition;
    private boolean infiniteCarousel;
    private b mAdapter;
    private int mAnimateTargetDelay;
    private int mIndex;
    int mLastStartId;
    private final ArrayList<View> mList;
    private MotionLayout mMotionLayout;
    private int mPreviousIndex;
    private int mTargetIndex;
    Runnable mUpdateRunnable;
    private int nextState;
    private int previousState;
    private int startIndex;
    private int touchUpMode;
    private float velocityThreshold;

    class a implements Runnable {

        /* renamed from: androidx.constraintlayout.helper.widget.Carousel$a$a, reason: collision with other inner class name */
        class RunnableC0029a implements Runnable {

            /* renamed from: a, reason: collision with root package name */
            final /* synthetic */ float f864a;

            RunnableC0029a(float f) {
                this.f864a = f;
            }

            @Override // java.lang.Runnable
            public void run() {
                Carousel.this.mMotionLayout.touchAnimateTo(5, 1.0f, this.f864a);
            }
        }

        a() {
        }

        @Override // java.lang.Runnable
        public void run() {
            Carousel.this.mMotionLayout.setProgress(0.0f);
            Carousel.this.updateItems();
            Carousel.this.mAdapter.c(Carousel.this.mIndex);
            float velocity = Carousel.this.mMotionLayout.getVelocity();
            if (Carousel.this.touchUpMode != 2 || velocity <= Carousel.this.velocityThreshold || Carousel.this.mIndex >= Carousel.this.mAdapter.a() - 1) {
                return;
            }
            float f = velocity * Carousel.this.dampening;
            if (Carousel.this.mIndex != 0 || Carousel.this.mPreviousIndex <= Carousel.this.mIndex) {
                if (Carousel.this.mIndex != Carousel.this.mAdapter.a() - 1 || Carousel.this.mPreviousIndex >= Carousel.this.mIndex) {
                    Carousel.this.mMotionLayout.post(new RunnableC0029a(f));
                }
            }
        }
    }

    public interface b {
        int a();

        void b(View view, int i);

        void c(int i);
    }

    public Carousel(Context context) {
        super(context);
        this.mAdapter = null;
        this.mList = new ArrayList<>();
        this.mPreviousIndex = 0;
        this.mIndex = 0;
        this.firstViewReference = -1;
        this.infiniteCarousel = false;
        this.backwardTransition = -1;
        this.forwardTransition = -1;
        this.previousState = -1;
        this.nextState = -1;
        this.dampening = 0.9f;
        this.startIndex = 0;
        this.emptyViewBehavior = 4;
        this.touchUpMode = 1;
        this.velocityThreshold = 2.0f;
        this.mTargetIndex = -1;
        this.mAnimateTargetDelay = 200;
        this.mLastStartId = -1;
        this.mUpdateRunnable = new a();
    }

    public Carousel(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        this.mAdapter = null;
        this.mList = new ArrayList<>();
        this.mPreviousIndex = 0;
        this.mIndex = 0;
        this.firstViewReference = -1;
        this.infiniteCarousel = false;
        this.backwardTransition = -1;
        this.forwardTransition = -1;
        this.previousState = -1;
        this.nextState = -1;
        this.dampening = 0.9f;
        this.startIndex = 0;
        this.emptyViewBehavior = 4;
        this.touchUpMode = 1;
        this.velocityThreshold = 2.0f;
        this.mTargetIndex = -1;
        this.mAnimateTargetDelay = 200;
        this.mLastStartId = -1;
        this.mUpdateRunnable = new a();
        init(context, attributeSet);
    }

    public Carousel(Context context, AttributeSet attributeSet, int i) {
        super(context, attributeSet, i);
        this.mAdapter = null;
        this.mList = new ArrayList<>();
        this.mPreviousIndex = 0;
        this.mIndex = 0;
        this.firstViewReference = -1;
        this.infiniteCarousel = false;
        this.backwardTransition = -1;
        this.forwardTransition = -1;
        this.previousState = -1;
        this.nextState = -1;
        this.dampening = 0.9f;
        this.startIndex = 0;
        this.emptyViewBehavior = 4;
        this.touchUpMode = 1;
        this.velocityThreshold = 2.0f;
        this.mTargetIndex = -1;
        this.mAnimateTargetDelay = 200;
        this.mLastStartId = -1;
        this.mUpdateRunnable = new a();
        init(context, attributeSet);
    }

    private void enableAllTransitions(boolean z) {
        Iterator<q.b> it = this.mMotionLayout.getDefinedTransitions().iterator();
        while (it.hasNext()) {
            it.next().D(z);
        }
    }

    private boolean enableTransition(int i, boolean z) {
        MotionLayout motionLayout;
        q.b transition;
        if (i == -1 || (motionLayout = this.mMotionLayout) == null || (transition = motionLayout.getTransition(i)) == null || z == transition.A()) {
            return false;
        }
        transition.D(z);
        return true;
    }

    private void init(Context context, AttributeSet attributeSet) {
        if (attributeSet != null) {
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, f.f1019a);
            int indexCount = obtainStyledAttributes.getIndexCount();
            for (int i = 0; i < indexCount; i++) {
                int index = obtainStyledAttributes.getIndex(i);
                if (index == 2) {
                    this.firstViewReference = obtainStyledAttributes.getResourceId(index, this.firstViewReference);
                } else if (index == 0) {
                    this.backwardTransition = obtainStyledAttributes.getResourceId(index, this.backwardTransition);
                } else if (index == 3) {
                    this.forwardTransition = obtainStyledAttributes.getResourceId(index, this.forwardTransition);
                } else if (index == 1) {
                    this.emptyViewBehavior = obtainStyledAttributes.getInt(index, this.emptyViewBehavior);
                } else if (index == 6) {
                    this.previousState = obtainStyledAttributes.getResourceId(index, this.previousState);
                } else if (index == 5) {
                    this.nextState = obtainStyledAttributes.getResourceId(index, this.nextState);
                } else if (index == 8) {
                    this.dampening = obtainStyledAttributes.getFloat(index, this.dampening);
                } else if (index == 7) {
                    this.touchUpMode = obtainStyledAttributes.getInt(index, this.touchUpMode);
                } else if (index == 9) {
                    this.velocityThreshold = obtainStyledAttributes.getFloat(index, this.velocityThreshold);
                } else if (index == 4) {
                    this.infiniteCarousel = obtainStyledAttributes.getBoolean(index, this.infiniteCarousel);
                }
            }
            obtainStyledAttributes.recycle();
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void updateItems() {
        b bVar;
        b bVar2 = this.mAdapter;
        if (bVar2 == null || this.mMotionLayout == null || bVar2.a() == 0) {
            return;
        }
        int size = this.mList.size();
        for (int i = 0; i < size; i++) {
            View view = this.mList.get(i);
            int i2 = (this.mIndex + i) - this.startIndex;
            if (!this.infiniteCarousel) {
                if (i2 < 0 || i2 >= this.mAdapter.a()) {
                    updateViewVisibility(view, this.emptyViewBehavior);
                }
                updateViewVisibility(view, 0);
            } else if (i2 < 0) {
                int i3 = this.emptyViewBehavior;
                if (i3 != 4) {
                    updateViewVisibility(view, i3);
                } else {
                    updateViewVisibility(view, 0);
                }
                if (i2 % this.mAdapter.a() == 0) {
                    this.mAdapter.b(view, 0);
                } else {
                    bVar = this.mAdapter;
                    i2 = (i2 % this.mAdapter.a()) + bVar.a();
                    bVar.b(view, i2);
                }
            } else {
                if (i2 >= this.mAdapter.a()) {
                    if (i2 == this.mAdapter.a()) {
                        i2 = 0;
                    } else if (i2 > this.mAdapter.a()) {
                        i2 %= this.mAdapter.a();
                    }
                    int i4 = this.emptyViewBehavior;
                    if (i4 != 4) {
                        updateViewVisibility(view, i4);
                    }
                }
                updateViewVisibility(view, 0);
            }
            bVar = this.mAdapter;
            bVar.b(view, i2);
        }
        int i5 = this.mTargetIndex;
        if (i5 != -1 && i5 != this.mIndex) {
            this.mMotionLayout.post(new Runnable() { // from class: androidx.constraintlayout.helper.widget.a
                @Override // java.lang.Runnable
                public final void run() {
                    Carousel.this.a();
                }
            });
        } else if (i5 == this.mIndex) {
            this.mTargetIndex = -1;
        }
        if (this.backwardTransition == -1 || this.forwardTransition == -1) {
            Log.w(TAG, "No backward or forward transitions defined for Carousel!");
            return;
        }
        if (this.infiniteCarousel) {
            return;
        }
        int a2 = this.mAdapter.a();
        if (this.mIndex == 0) {
            enableTransition(this.backwardTransition, false);
        } else {
            enableTransition(this.backwardTransition, true);
            this.mMotionLayout.setTransition(this.backwardTransition);
        }
        if (this.mIndex == a2 - 1) {
            enableTransition(this.forwardTransition, false);
        } else {
            enableTransition(this.forwardTransition, true);
            this.mMotionLayout.setTransition(this.forwardTransition);
        }
    }

    private boolean updateViewVisibility(int i, View view, int i2) {
        c.a q;
        c constraintSet = this.mMotionLayout.getConstraintSet(i);
        if (constraintSet == null || (q = constraintSet.q(view.getId())) == null) {
            return false;
        }
        q.f997c.f1013c = 1;
        view.setVisibility(i2);
        return true;
    }

    private boolean updateViewVisibility(View view, int i) {
        MotionLayout motionLayout = this.mMotionLayout;
        if (motionLayout == null) {
            return false;
        }
        boolean z = false;
        for (int i2 : motionLayout.getConstraintSetIds()) {
            z |= updateViewVisibility(i2, view, i);
        }
        return z;
    }

    public /* synthetic */ void a() {
        MotionLayout motionLayout;
        int i;
        this.mMotionLayout.setTransitionDuration(this.mAnimateTargetDelay);
        if (this.mTargetIndex < this.mIndex) {
            motionLayout = this.mMotionLayout;
            i = this.previousState;
        } else {
            motionLayout = this.mMotionLayout;
            i = this.nextState;
        }
        motionLayout.transitionToState(i, this.mAnimateTargetDelay);
    }

    public int getCount() {
        b bVar = this.mAdapter;
        if (bVar != null) {
            return bVar.a();
        }
        return 0;
    }

    public int getCurrentIndex() {
        return this.mIndex;
    }

    public void jumpToIndex(int i) {
        this.mIndex = Math.max(0, Math.min(getCount() - 1, i));
        refresh();
    }

    @Override // androidx.constraintlayout.widget.ConstraintHelper, android.view.View
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        if (getParent() instanceof MotionLayout) {
            MotionLayout motionLayout = (MotionLayout) getParent();
            for (int i = 0; i < this.mCount; i++) {
                int i2 = this.mIds[i];
                View viewById = motionLayout.getViewById(i2);
                if (this.firstViewReference == i2) {
                    this.startIndex = i;
                }
                this.mList.add(viewById);
            }
            this.mMotionLayout = motionLayout;
            if (this.touchUpMode == 2) {
                q.b transition = motionLayout.getTransition(this.forwardTransition);
                if (transition != null) {
                    transition.F(5);
                }
                q.b transition2 = this.mMotionLayout.getTransition(this.backwardTransition);
                if (transition2 != null) {
                    transition2.F(5);
                }
            }
            updateItems();
        }
    }

    @Override // androidx.constraintlayout.motion.widget.MotionHelper, androidx.constraintlayout.motion.widget.MotionLayout.k
    public void onTransitionChange(MotionLayout motionLayout, int i, int i2, float f) {
        this.mLastStartId = i;
    }

    /* JADX WARN: Removed duplicated region for block: B:15:0x0054  */
    /* JADX WARN: Removed duplicated region for block: B:18:? A[RETURN, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:19:0x0034  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0019  */
    @Override // androidx.constraintlayout.motion.widget.MotionHelper, androidx.constraintlayout.motion.widget.MotionLayout.k
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void onTransitionCompleted(androidx.constraintlayout.motion.widget.MotionLayout r2, int r3) {
        /*
            r1 = this;
            int r2 = r1.mIndex
            r1.mPreviousIndex = r2
            int r0 = r1.nextState
            if (r3 != r0) goto Ld
            int r2 = r2 + 1
        La:
            r1.mIndex = r2
            goto L14
        Ld:
            int r0 = r1.previousState
            if (r3 != r0) goto L14
            int r2 = r2 + (-1)
            goto La
        L14:
            boolean r2 = r1.infiniteCarousel
            r3 = 0
            if (r2 == 0) goto L34
            int r2 = r1.mIndex
            androidx.constraintlayout.helper.widget.Carousel$b r0 = r1.mAdapter
            int r0 = r0.a()
            if (r2 < r0) goto L25
            r1.mIndex = r3
        L25:
            int r2 = r1.mIndex
            if (r2 >= 0) goto L4e
            androidx.constraintlayout.helper.widget.Carousel$b r2 = r1.mAdapter
            int r2 = r2.a()
            int r2 = r2 + (-1)
            r1.mIndex = r2
            goto L4e
        L34:
            int r2 = r1.mIndex
            androidx.constraintlayout.helper.widget.Carousel$b r0 = r1.mAdapter
            int r0 = r0.a()
            if (r2 < r0) goto L48
            androidx.constraintlayout.helper.widget.Carousel$b r2 = r1.mAdapter
            int r2 = r2.a()
            int r2 = r2 + (-1)
            r1.mIndex = r2
        L48:
            int r2 = r1.mIndex
            if (r2 >= 0) goto L4e
            r1.mIndex = r3
        L4e:
            int r2 = r1.mPreviousIndex
            int r3 = r1.mIndex
            if (r2 == r3) goto L5b
            androidx.constraintlayout.motion.widget.MotionLayout r2 = r1.mMotionLayout
            java.lang.Runnable r1 = r1.mUpdateRunnable
            r2.post(r1)
        L5b:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.helper.widget.Carousel.onTransitionCompleted(androidx.constraintlayout.motion.widget.MotionLayout, int):void");
    }

    public void refresh() {
        int size = this.mList.size();
        for (int i = 0; i < size; i++) {
            View view = this.mList.get(i);
            if (this.mAdapter.a() == 0) {
                updateViewVisibility(view, this.emptyViewBehavior);
            } else {
                updateViewVisibility(view, 0);
            }
        }
        this.mMotionLayout.rebuildScene();
        updateItems();
    }

    public void setAdapter(b bVar) {
        this.mAdapter = bVar;
    }

    public void transitionToIndex(int i, int i2) {
        MotionLayout motionLayout;
        int i3;
        this.mTargetIndex = Math.max(0, Math.min(getCount() - 1, i));
        int max = Math.max(0, i2);
        this.mAnimateTargetDelay = max;
        this.mMotionLayout.setTransitionDuration(max);
        if (i < this.mIndex) {
            motionLayout = this.mMotionLayout;
            i3 = this.previousState;
        } else {
            motionLayout = this.mMotionLayout;
            i3 = this.nextState;
        }
        motionLayout.transitionToState(i3, this.mAnimateTargetDelay);
    }
}
