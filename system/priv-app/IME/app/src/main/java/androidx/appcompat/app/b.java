package androidx.appcompat.app;

import android.view.View;
import android.widget.AdapterView;
import androidx.appcompat.app.AlertController;

/* loaded from: classes.dex */
class b implements AdapterView.OnItemClickListener {

    /* renamed from: a, reason: collision with root package name */
    final /* synthetic */ AlertController f559a;

    /* renamed from: b, reason: collision with root package name */
    final /* synthetic */ AlertController.b f560b;

    b(AlertController.b bVar, AlertController alertController) {
        this.f560b = bVar;
        this.f559a = alertController;
    }

    @Override // android.widget.AdapterView.OnItemClickListener
    public void onItemClick(AdapterView<?> adapterView, View view, int i, long j) {
        this.f560b.h.onClick(this.f559a.f549b, i);
        if (this.f560b.i) {
            return;
        }
        this.f559a.f549b.dismiss();
    }
}
