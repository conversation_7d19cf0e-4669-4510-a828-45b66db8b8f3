package androidx.constraintlayout.motion.widget;

import a.f.a.j.e;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.DashPathEffect;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.RectF;
import android.os.Bundle;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseArray;
import android.util.SparseBooleanArray;
import android.util.SparseIntArray;
import android.view.Display;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Interpolator;
import androidx.constraintlayout.motion.widget.q;
import androidx.constraintlayout.widget.Barrier;
import androidx.constraintlayout.widget.ConstraintHelper;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.Constraints;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;
import org.libpag.R;

/* loaded from: classes.dex */
public class MotionLayout extends ConstraintLayout implements a.h.h.h {
    private static final boolean DEBUG = false;
    public static final int DEBUG_SHOW_NONE = 0;
    public static final int DEBUG_SHOW_PATH = 2;
    public static final int DEBUG_SHOW_PROGRESS = 1;
    private static final float EPSILON = 1.0E-5f;
    public static boolean IS_IN_EDIT_MODE = false;
    static final int MAX_KEY_FRAMES = 50;
    static final String TAG = "MotionLayout";
    public static final int TOUCH_UP_COMPLETE = 0;
    public static final int TOUCH_UP_COMPLETE_TO_END = 2;
    public static final int TOUCH_UP_COMPLETE_TO_START = 1;
    public static final int TOUCH_UP_DECELERATE = 4;
    public static final int TOUCH_UP_DECELERATE_AND_COMPLETE = 5;
    public static final int TOUCH_UP_NEVER_TO_END = 7;
    public static final int TOUCH_UP_NEVER_TO_START = 6;
    public static final int TOUCH_UP_STOP = 3;
    public static final int VELOCITY_LAYOUT = 1;
    public static final int VELOCITY_POST_LAYOUT = 0;
    public static final int VELOCITY_STATIC_LAYOUT = 3;
    public static final int VELOCITY_STATIC_POST_LAYOUT = 2;
    boolean firstDown;
    private float lastPos;
    private float lastY;
    private long mAnimationStartTime;
    private int mBeginState;
    private RectF mBoundsCheck;
    int mCurrentState;
    int mDebugPath;
    private e mDecelerateLogic;
    private ArrayList<MotionHelper> mDecoratorsHelpers;
    private boolean mDelayedApply;
    private androidx.constraintlayout.motion.widget.b mDesignTool;
    f mDevModeDraw;
    private int mEndState;
    int mEndWrapHeight;
    int mEndWrapWidth;
    HashMap<View, n> mFrameArrayList;
    private int mFrames;
    int mHeightMeasureMode;
    private boolean mInLayout;
    private boolean mInRotation;
    boolean mInTransition;
    boolean mIndirectTransition;
    private boolean mInteractionEnabled;
    Interpolator mInterpolator;
    private Matrix mInverseMatrix;
    boolean mIsAnimating;
    private boolean mKeepAnimating;
    private a.f.a.i.a.d mKeyCache;
    private long mLastDrawTime;
    private float mLastFps;
    private int mLastHeightMeasureSpec;
    int mLastLayoutHeight;
    int mLastLayoutWidth;
    float mLastVelocity;
    private int mLastWidthMeasureSpec;
    private float mListenerPosition;
    private int mListenerState;
    protected boolean mMeasureDuringTransition;
    g mModel;
    private boolean mNeedsFireTransitionCompleted;
    int mOldHeight;
    int mOldWidth;
    private Runnable mOnComplete;
    private ArrayList<MotionHelper> mOnHideHelpers;
    private ArrayList<MotionHelper> mOnShowHelpers;
    float mPostInterpolationPosition;
    HashMap<View, a.f.b.a.e> mPreRotate;
    private int mPreRotateHeight;
    private int mPreRotateWidth;
    private int mPreviouseRotation;
    Interpolator mProgressInterpolator;
    private View mRegionView;
    int mRotatMode;
    q mScene;
    private int[] mScheduledTransitionTo;
    int mScheduledTransitions;
    float mScrollTargetDT;
    float mScrollTargetDX;
    float mScrollTargetDY;
    long mScrollTargetTime;
    int mStartWrapHeight;
    int mStartWrapWidth;
    private j mStateCache;
    private a.f.b.a.b mStopLogic;
    Rect mTempRect;
    private boolean mTemporalInterpolator;
    ArrayList<Integer> mTransitionCompleted;
    private float mTransitionDuration;
    float mTransitionGoalPosition;
    private boolean mTransitionInstantly;
    float mTransitionLastPosition;
    private long mTransitionLastTime;
    private k mTransitionListener;
    private CopyOnWriteArrayList<k> mTransitionListeners;
    float mTransitionPosition;
    l mTransitionState;
    boolean mUndergoingMotion;
    int mWidthMeasureMode;

    class a implements Runnable {
        a() {
        }

        @Override // java.lang.Runnable
        public void run() {
            MotionLayout.this.mStateCache.a();
        }
    }

    class b implements Runnable {
        b() {
        }

        @Override // java.lang.Runnable
        public void run() {
            MotionLayout.this.mInRotation = false;
        }
    }

    class c implements Runnable {

        /* renamed from: a, reason: collision with root package name */
        final /* synthetic */ View f869a;

        c(MotionLayout motionLayout, View view) {
            this.f869a = view;
        }

        @Override // java.lang.Runnable
        public void run() {
            this.f869a.setNestedScrollingEnabled(true);
        }
    }

    class d implements Runnable {
        d() {
        }

        @Override // java.lang.Runnable
        public void run() {
            MotionLayout.this.mStateCache.a();
        }
    }

    class e extends o {

        /* renamed from: a, reason: collision with root package name */
        float f871a = 0.0f;

        /* renamed from: b, reason: collision with root package name */
        float f872b = 0.0f;

        /* renamed from: c, reason: collision with root package name */
        float f873c;

        e() {
        }

        @Override // androidx.constraintlayout.motion.widget.o
        public float a() {
            return MotionLayout.this.mLastVelocity;
        }

        @Override // android.animation.TimeInterpolator
        public float getInterpolation(float f) {
            float f2 = this.f871a;
            if (f2 > 0.0f) {
                float f3 = this.f873c;
                if (f2 / f3 < f) {
                    f = f2 / f3;
                }
                MotionLayout.this.mLastVelocity = f2 - (f3 * f);
                return ((f2 * f) - (((f3 * f) * f) / 2.0f)) + this.f872b;
            }
            float f4 = this.f873c;
            if ((-f2) / f4 < f) {
                f = (-f2) / f4;
            }
            MotionLayout.this.mLastVelocity = (f4 * f) + f2;
            return (((f4 * f) * f) / 2.0f) + (f2 * f) + this.f872b;
        }
    }

    private class f {

        /* renamed from: a, reason: collision with root package name */
        float[] f875a;

        /* renamed from: b, reason: collision with root package name */
        int[] f876b;

        /* renamed from: c, reason: collision with root package name */
        float[] f877c;

        /* renamed from: d, reason: collision with root package name */
        Path f878d;
        Paint e;
        Paint f;
        Paint g;
        Paint h;
        Paint i;
        private float[] j;
        DashPathEffect k;
        int l;
        Rect m = new Rect();
        int n = 1;

        public f() {
            Paint paint = new Paint();
            this.e = paint;
            paint.setAntiAlias(true);
            this.e.setColor(-21965);
            this.e.setStrokeWidth(2.0f);
            this.e.setStyle(Paint.Style.STROKE);
            Paint paint2 = new Paint();
            this.f = paint2;
            paint2.setAntiAlias(true);
            this.f.setColor(-2067046);
            this.f.setStrokeWidth(2.0f);
            this.f.setStyle(Paint.Style.STROKE);
            Paint paint3 = new Paint();
            this.g = paint3;
            paint3.setAntiAlias(true);
            this.g.setColor(-13391360);
            this.g.setStrokeWidth(2.0f);
            this.g.setStyle(Paint.Style.STROKE);
            Paint paint4 = new Paint();
            this.h = paint4;
            paint4.setAntiAlias(true);
            this.h.setColor(-13391360);
            this.h.setTextSize(MotionLayout.this.getContext().getResources().getDisplayMetrics().density * 12.0f);
            this.j = new float[8];
            Paint paint5 = new Paint();
            this.i = paint5;
            paint5.setAntiAlias(true);
            DashPathEffect dashPathEffect = new DashPathEffect(new float[]{4.0f, 8.0f}, 0.0f);
            this.k = dashPathEffect;
            this.g.setPathEffect(dashPathEffect);
            this.f877c = new float[100];
            this.f876b = new int[MotionLayout.MAX_KEY_FRAMES];
        }

        private void c(Canvas canvas) {
            float[] fArr = this.f875a;
            float f = fArr[0];
            float f2 = fArr[1];
            float f3 = fArr[fArr.length - 2];
            float f4 = fArr[fArr.length - 1];
            canvas.drawLine(Math.min(f, f3), Math.max(f2, f4), Math.max(f, f3), Math.max(f2, f4), this.g);
            canvas.drawLine(Math.min(f, f3), Math.min(f2, f4), Math.min(f, f3), Math.max(f2, f4), this.g);
        }

        private void d(Canvas canvas, float f, float f2) {
            float[] fArr = this.f875a;
            float f3 = fArr[0];
            float f4 = fArr[1];
            float f5 = fArr[fArr.length - 2];
            float f6 = fArr[fArr.length - 1];
            float min = Math.min(f3, f5);
            float max = Math.max(f4, f6);
            float min2 = f - Math.min(f3, f5);
            float max2 = Math.max(f4, f6) - f2;
            StringBuilder j = b.b.a.a.a.j("");
            j.append(((int) (((min2 * 100.0f) / Math.abs(f5 - f3)) + 0.5d)) / 100.0f);
            String sb = j.toString();
            h(sb, this.h);
            canvas.drawText(sb, ((min2 / 2.0f) - (this.m.width() / 2)) + min, f2 - 20.0f, this.h);
            canvas.drawLine(f, f2, Math.min(f3, f5), f2, this.g);
            StringBuilder j2 = b.b.a.a.a.j("");
            j2.append(((int) (((max2 * 100.0f) / Math.abs(f6 - f4)) + 0.5d)) / 100.0f);
            String sb2 = j2.toString();
            h(sb2, this.h);
            canvas.drawText(sb2, f + 5.0f, max - ((max2 / 2.0f) - (this.m.height() / 2)), this.h);
            canvas.drawLine(f, f2, f, Math.max(f4, f6), this.g);
        }

        private void e(Canvas canvas) {
            float[] fArr = this.f875a;
            canvas.drawLine(fArr[0], fArr[1], fArr[fArr.length - 2], fArr[fArr.length - 1], this.g);
        }

        private void f(Canvas canvas, float f, float f2) {
            float[] fArr = this.f875a;
            float f3 = fArr[0];
            float f4 = fArr[1];
            float f5 = fArr[fArr.length - 2];
            float f6 = fArr[fArr.length - 1];
            float hypot = (float) Math.hypot(f3 - f5, f4 - f6);
            float f7 = f5 - f3;
            float f8 = f6 - f4;
            float f9 = (((f2 - f4) * f8) + ((f - f3) * f7)) / (hypot * hypot);
            float f10 = f3 + (f7 * f9);
            float f11 = f4 + (f9 * f8);
            Path path = new Path();
            path.moveTo(f, f2);
            path.lineTo(f10, f11);
            float hypot2 = (float) Math.hypot(f10 - f, f11 - f2);
            StringBuilder j = b.b.a.a.a.j("");
            j.append(((int) ((hypot2 * 100.0f) / hypot)) / 100.0f);
            String sb = j.toString();
            h(sb, this.h);
            canvas.drawTextOnPath(sb, path, (hypot2 / 2.0f) - (this.m.width() / 2), -20.0f, this.h);
            canvas.drawLine(f, f2, f10, f11, this.g);
        }

        private void g(Canvas canvas, float f, float f2, int i, int i2) {
            StringBuilder j = b.b.a.a.a.j("");
            j.append(((int) ((((f - (i / 2)) * 100.0f) / (MotionLayout.this.getWidth() - i)) + 0.5d)) / 100.0f);
            String sb = j.toString();
            h(sb, this.h);
            canvas.drawText(sb, ((f / 2.0f) - (this.m.width() / 2)) + 0.0f, f2 - 20.0f, this.h);
            canvas.drawLine(f, f2, Math.min(0.0f, 1.0f), f2, this.g);
            StringBuilder j2 = b.b.a.a.a.j("");
            j2.append(((int) ((((f2 - (i2 / 2)) * 100.0f) / (MotionLayout.this.getHeight() - i2)) + 0.5d)) / 100.0f);
            String sb2 = j2.toString();
            h(sb2, this.h);
            canvas.drawText(sb2, f + 5.0f, 0.0f - ((f2 / 2.0f) - (this.m.height() / 2)), this.h);
            canvas.drawLine(f, f2, f, Math.max(0.0f, 1.0f), this.g);
        }

        public void a(Canvas canvas, HashMap<View, n> hashMap, int i, int i2) {
            if (hashMap == null || hashMap.size() == 0) {
                return;
            }
            canvas.save();
            if (!MotionLayout.this.isInEditMode() && (i2 & 1) == 2) {
                String str = MotionLayout.this.getContext().getResources().getResourceName(MotionLayout.this.mEndState) + ":" + MotionLayout.this.getProgress();
                canvas.drawText(str, 10.0f, MotionLayout.this.getHeight() - 30, this.h);
                canvas.drawText(str, 11.0f, MotionLayout.this.getHeight() - 29, this.e);
            }
            for (n nVar : hashMap.values()) {
                int k = nVar.k();
                if (i2 > 0 && k == 0) {
                    k = 1;
                }
                if (k != 0) {
                    this.l = nVar.c(this.f877c, this.f876b);
                    if (k >= 1) {
                        int i3 = i / 16;
                        float[] fArr = this.f875a;
                        if (fArr == null || fArr.length != i3 * 2) {
                            this.f875a = new float[i3 * 2];
                            this.f878d = new Path();
                        }
                        int i4 = this.n;
                        canvas.translate(i4, i4);
                        this.e.setColor(1996488704);
                        this.i.setColor(1996488704);
                        this.f.setColor(1996488704);
                        this.g.setColor(1996488704);
                        nVar.d(this.f875a, i3);
                        b(canvas, k, this.l, nVar);
                        this.e.setColor(-21965);
                        this.f.setColor(-2067046);
                        this.i.setColor(-2067046);
                        this.g.setColor(-13391360);
                        int i5 = this.n;
                        canvas.translate(-i5, -i5);
                        b(canvas, k, this.l, nVar);
                        if (k == 5) {
                            this.f878d.reset();
                            for (int i6 = 0; i6 <= MotionLayout.MAX_KEY_FRAMES; i6++) {
                                nVar.e(i6 / MotionLayout.MAX_KEY_FRAMES, this.j, 0);
                                Path path = this.f878d;
                                float[] fArr2 = this.j;
                                path.moveTo(fArr2[0], fArr2[1]);
                                Path path2 = this.f878d;
                                float[] fArr3 = this.j;
                                path2.lineTo(fArr3[2], fArr3[3]);
                                Path path3 = this.f878d;
                                float[] fArr4 = this.j;
                                path3.lineTo(fArr4[4], fArr4[5]);
                                Path path4 = this.f878d;
                                float[] fArr5 = this.j;
                                path4.lineTo(fArr5[6], fArr5[7]);
                                this.f878d.close();
                            }
                            this.e.setColor(1140850688);
                            canvas.translate(2.0f, 2.0f);
                            canvas.drawPath(this.f878d, this.e);
                            canvas.translate(-2.0f, -2.0f);
                            this.e.setColor(-65536);
                            canvas.drawPath(this.f878d, this.e);
                        }
                    }
                }
            }
            canvas.restore();
        }

        public void b(Canvas canvas, int i, int i2, n nVar) {
            int i3;
            int i4;
            float f;
            float f2;
            int i5;
            if (i == 4) {
                boolean z = false;
                boolean z2 = false;
                for (int i6 = 0; i6 < this.l; i6++) {
                    int[] iArr = this.f876b;
                    if (iArr[i6] == 1) {
                        z = true;
                    }
                    if (iArr[i6] == 0) {
                        z2 = true;
                    }
                }
                if (z) {
                    e(canvas);
                }
                if (z2) {
                    c(canvas);
                }
            }
            if (i == 2) {
                e(canvas);
            }
            if (i == 3) {
                c(canvas);
            }
            canvas.drawLines(this.f875a, this.e);
            View view = nVar.f914b;
            if (view != null) {
                i3 = view.getWidth();
                i4 = nVar.f914b.getHeight();
            } else {
                i3 = 0;
                i4 = 0;
            }
            int i7 = 1;
            while (i7 < i2 - 1) {
                if (i == 4 && this.f876b[i7 - 1] == 0) {
                    i5 = i7;
                } else {
                    float[] fArr = this.f877c;
                    int i8 = i7 * 2;
                    float f3 = fArr[i8];
                    float f4 = fArr[i8 + 1];
                    this.f878d.reset();
                    this.f878d.moveTo(f3, f4 + 10.0f);
                    this.f878d.lineTo(f3 + 10.0f, f4);
                    this.f878d.lineTo(f3, f4 - 10.0f);
                    this.f878d.lineTo(f3 - 10.0f, f4);
                    this.f878d.close();
                    int i9 = i7 - 1;
                    nVar.n(i9);
                    if (i == 4) {
                        int[] iArr2 = this.f876b;
                        if (iArr2[i9] == 1) {
                            f(canvas, f3 - 0.0f, f4 - 0.0f);
                        } else if (iArr2[i9] == 0) {
                            d(canvas, f3 - 0.0f, f4 - 0.0f);
                        } else if (iArr2[i9] == 2) {
                            f = f4;
                            f2 = f3;
                            i5 = i7;
                            g(canvas, f3 - 0.0f, f4 - 0.0f, i3, i4);
                            canvas.drawPath(this.f878d, this.i);
                        }
                        f = f4;
                        f2 = f3;
                        i5 = i7;
                        canvas.drawPath(this.f878d, this.i);
                    } else {
                        f = f4;
                        f2 = f3;
                        i5 = i7;
                    }
                    if (i == 2) {
                        f(canvas, f2 - 0.0f, f - 0.0f);
                    }
                    if (i == 3) {
                        d(canvas, f2 - 0.0f, f - 0.0f);
                    }
                    if (i == 6) {
                        g(canvas, f2 - 0.0f, f - 0.0f, i3, i4);
                    }
                    canvas.drawPath(this.f878d, this.i);
                }
                i7 = i5 + 1;
            }
            float[] fArr2 = this.f875a;
            if (fArr2.length > 1) {
                canvas.drawCircle(fArr2[0], fArr2[1], 8.0f, this.f);
                float[] fArr3 = this.f875a;
                canvas.drawCircle(fArr3[fArr3.length - 2], fArr3[fArr3.length - 1], 8.0f, this.f);
            }
        }

        void h(String str, Paint paint) {
            paint.getTextBounds(str, 0, str.length(), this.m);
        }
    }

    class g {

        /* renamed from: a, reason: collision with root package name */
        a.f.a.j.f f879a = new a.f.a.j.f();

        /* renamed from: b, reason: collision with root package name */
        a.f.a.j.f f880b = new a.f.a.j.f();

        /* renamed from: c, reason: collision with root package name */
        androidx.constraintlayout.widget.c f881c = null;

        /* renamed from: d, reason: collision with root package name */
        androidx.constraintlayout.widget.c f882d = null;
        int e;
        int f;

        g() {
        }

        private void b(int i, int i2) {
            int optimizationLevel = MotionLayout.this.getOptimizationLevel();
            MotionLayout motionLayout = MotionLayout.this;
            if (motionLayout.mCurrentState == motionLayout.getStartState()) {
                MotionLayout motionLayout2 = MotionLayout.this;
                a.f.a.j.f fVar = this.f880b;
                androidx.constraintlayout.widget.c cVar = this.f882d;
                motionLayout2.resolveSystem(fVar, optimizationLevel, (cVar == null || cVar.f993c == 0) ? i : i2, (cVar == null || cVar.f993c == 0) ? i2 : i);
                androidx.constraintlayout.widget.c cVar2 = this.f881c;
                if (cVar2 != null) {
                    MotionLayout motionLayout3 = MotionLayout.this;
                    a.f.a.j.f fVar2 = this.f879a;
                    int i3 = cVar2.f993c;
                    int i4 = i3 == 0 ? i : i2;
                    if (i3 == 0) {
                        i = i2;
                    }
                    motionLayout3.resolveSystem(fVar2, optimizationLevel, i4, i);
                    return;
                }
                return;
            }
            androidx.constraintlayout.widget.c cVar3 = this.f881c;
            if (cVar3 != null) {
                MotionLayout motionLayout4 = MotionLayout.this;
                a.f.a.j.f fVar3 = this.f879a;
                int i5 = cVar3.f993c;
                motionLayout4.resolveSystem(fVar3, optimizationLevel, i5 == 0 ? i : i2, i5 == 0 ? i2 : i);
            }
            MotionLayout motionLayout5 = MotionLayout.this;
            a.f.a.j.f fVar4 = this.f880b;
            androidx.constraintlayout.widget.c cVar4 = this.f882d;
            int i6 = (cVar4 == null || cVar4.f993c == 0) ? i : i2;
            if (cVar4 == null || cVar4.f993c == 0) {
                i = i2;
            }
            motionLayout5.resolveSystem(fVar4, optimizationLevel, i6, i);
        }

        /* JADX WARN: Multi-variable type inference failed */
        private void g(a.f.a.j.f fVar, androidx.constraintlayout.widget.c cVar) {
            SparseArray<a.f.a.j.e> sparseArray = new SparseArray<>();
            Constraints.a aVar = new Constraints.a(-2, -2);
            sparseArray.clear();
            sparseArray.put(0, fVar);
            sparseArray.put(MotionLayout.this.getId(), fVar);
            if (cVar != null && cVar.f993c != 0) {
                MotionLayout motionLayout = MotionLayout.this;
                motionLayout.resolveSystem(this.f880b, motionLayout.getOptimizationLevel(), View.MeasureSpec.makeMeasureSpec(MotionLayout.this.getHeight(), 1073741824), View.MeasureSpec.makeMeasureSpec(MotionLayout.this.getWidth(), 1073741824));
            }
            Iterator<a.f.a.j.e> it = fVar.N0.iterator();
            while (it.hasNext()) {
                a.f.a.j.e next = it.next();
                sparseArray.put(((View) next.s()).getId(), next);
            }
            Iterator<a.f.a.j.e> it2 = fVar.N0.iterator();
            while (it2.hasNext()) {
                a.f.a.j.e next2 = it2.next();
                View view = (View) next2.s();
                cVar.g(view.getId(), aVar);
                next2.S0(cVar.w(view.getId()));
                next2.A0(cVar.r(view.getId()));
                if (view instanceof ConstraintHelper) {
                    cVar.e((ConstraintHelper) view, next2, aVar, sparseArray);
                    if (view instanceof Barrier) {
                        ((Barrier) view).validateParams();
                    }
                }
                aVar.resolveLayoutDirection(MotionLayout.this.getLayoutDirection());
                MotionLayout.this.applyConstraintsFromLayoutParams(false, view, next2, aVar, sparseArray);
                next2.R0(cVar.v(view.getId()) == 1 ? view.getVisibility() : cVar.u(view.getId()));
            }
            Iterator<a.f.a.j.e> it3 = fVar.N0.iterator();
            while (it3.hasNext()) {
                a.f.a.j.e next3 = it3.next();
                if (next3 instanceof a.f.a.j.m) {
                    ConstraintHelper constraintHelper = (ConstraintHelper) next3.s();
                    a.f.a.j.i iVar = (a.f.a.j.i) next3;
                    constraintHelper.updatePreLayout(fVar, iVar, sparseArray);
                    ((a.f.a.j.m) iVar).a1();
                }
            }
        }

        /* JADX WARN: Removed duplicated region for block: B:20:0x00e9  */
        /* JADX WARN: Removed duplicated region for block: B:29:0x013d A[SYNTHETIC] */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public void a() {
            /*
                Method dump skipped, instructions count: 360
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.motion.widget.MotionLayout.g.a():void");
        }

        void c(a.f.a.j.f fVar, a.f.a.j.f fVar2) {
            ArrayList<a.f.a.j.e> arrayList = fVar.N0;
            HashMap<a.f.a.j.e, a.f.a.j.e> hashMap = new HashMap<>();
            hashMap.put(fVar, fVar2);
            fVar2.N0.clear();
            fVar2.l(fVar, hashMap);
            Iterator<a.f.a.j.e> it = arrayList.iterator();
            while (it.hasNext()) {
                a.f.a.j.e next = it.next();
                a.f.a.j.e aVar = next instanceof a.f.a.j.a ? new a.f.a.j.a() : next instanceof a.f.a.j.h ? new a.f.a.j.h() : next instanceof a.f.a.j.g ? new a.f.a.j.g() : next instanceof a.f.a.j.l ? new a.f.a.j.l() : next instanceof a.f.a.j.i ? new a.f.a.j.j() : new a.f.a.j.e();
                fVar2.N0.add(aVar);
                a.f.a.j.e eVar = aVar.X;
                if (eVar != null) {
                    ((a.f.a.j.n) eVar).N0.remove(aVar);
                    aVar.k0();
                }
                aVar.X = fVar2;
                hashMap.put(next, aVar);
            }
            Iterator<a.f.a.j.e> it2 = arrayList.iterator();
            while (it2.hasNext()) {
                a.f.a.j.e next2 = it2.next();
                hashMap.get(next2).l(next2, hashMap);
            }
        }

        a.f.a.j.e d(a.f.a.j.f fVar, View view) {
            if (fVar.s() == view) {
                return fVar;
            }
            ArrayList<a.f.a.j.e> arrayList = fVar.N0;
            int size = arrayList.size();
            for (int i = 0; i < size; i++) {
                a.f.a.j.e eVar = arrayList.get(i);
                if (eVar.s() == view) {
                    return eVar;
                }
            }
            return null;
        }

        void e(androidx.constraintlayout.widget.c cVar, androidx.constraintlayout.widget.c cVar2) {
            e.a aVar = e.a.WRAP_CONTENT;
            this.f881c = cVar;
            this.f882d = cVar2;
            this.f879a = new a.f.a.j.f();
            this.f880b = new a.f.a.j.f();
            this.f879a.p1(((ConstraintLayout) MotionLayout.this).mLayoutWidget.g1());
            this.f880b.p1(((ConstraintLayout) MotionLayout.this).mLayoutWidget.g1());
            this.f879a.N0.clear();
            this.f880b.N0.clear();
            c(((ConstraintLayout) MotionLayout.this).mLayoutWidget, this.f879a);
            c(((ConstraintLayout) MotionLayout.this).mLayoutWidget, this.f880b);
            if (MotionLayout.this.mTransitionLastPosition > 0.5d) {
                if (cVar != null) {
                    g(this.f879a, cVar);
                }
                g(this.f880b, cVar2);
            } else {
                g(this.f880b, cVar2);
                if (cVar != null) {
                    g(this.f879a, cVar);
                }
            }
            this.f879a.s1(MotionLayout.this.isRtl());
            this.f879a.t1();
            this.f880b.s1(MotionLayout.this.isRtl());
            this.f880b.t1();
            ViewGroup.LayoutParams layoutParams = MotionLayout.this.getLayoutParams();
            if (layoutParams != null) {
                if (layoutParams.width == -2) {
                    this.f879a.W[0] = aVar;
                    this.f880b.W[0] = aVar;
                }
                if (layoutParams.height == -2) {
                    this.f879a.W[1] = aVar;
                    this.f880b.W[1] = aVar;
                }
            }
        }

        public void f() {
            int i = MotionLayout.this.mLastWidthMeasureSpec;
            int i2 = MotionLayout.this.mLastHeightMeasureSpec;
            int mode = View.MeasureSpec.getMode(i);
            int mode2 = View.MeasureSpec.getMode(i2);
            MotionLayout motionLayout = MotionLayout.this;
            motionLayout.mWidthMeasureMode = mode;
            motionLayout.mHeightMeasureMode = mode2;
            motionLayout.getOptimizationLevel();
            b(i, i2);
            if (((MotionLayout.this.getParent() instanceof MotionLayout) && mode == 1073741824 && mode2 == 1073741824) ? false : true) {
                b(i, i2);
                MotionLayout.this.mStartWrapWidth = this.f879a.Q();
                MotionLayout.this.mStartWrapHeight = this.f879a.w();
                MotionLayout.this.mEndWrapWidth = this.f880b.Q();
                MotionLayout.this.mEndWrapHeight = this.f880b.w();
                MotionLayout motionLayout2 = MotionLayout.this;
                motionLayout2.mMeasureDuringTransition = (motionLayout2.mStartWrapWidth == motionLayout2.mEndWrapWidth && motionLayout2.mStartWrapHeight == motionLayout2.mEndWrapHeight) ? false : true;
            }
            MotionLayout motionLayout3 = MotionLayout.this;
            int i3 = motionLayout3.mStartWrapWidth;
            int i4 = motionLayout3.mStartWrapHeight;
            int i5 = motionLayout3.mWidthMeasureMode;
            if (i5 == Integer.MIN_VALUE || i5 == 0) {
                i3 = (int) ((motionLayout3.mPostInterpolationPosition * (motionLayout3.mEndWrapWidth - i3)) + i3);
            }
            int i6 = i3;
            int i7 = motionLayout3.mHeightMeasureMode;
            MotionLayout.this.resolveMeasuredDimension(i, i2, i6, (i7 == Integer.MIN_VALUE || i7 == 0) ? (int) ((motionLayout3.mPostInterpolationPosition * (motionLayout3.mEndWrapHeight - i4)) + i4) : i4, this.f879a.l1() || this.f880b.l1(), this.f879a.j1() || this.f880b.j1());
            MotionLayout.this.setupMotionViews();
        }
    }

    protected interface h {
    }

    /* JADX INFO: Access modifiers changed from: private */
    static class i implements h {

        /* renamed from: b, reason: collision with root package name */
        private static i f883b = new i();

        /* renamed from: a, reason: collision with root package name */
        VelocityTracker f884a;

        private i() {
        }

        public static i d() {
            f883b.f884a = VelocityTracker.obtain();
            return f883b;
        }

        public void a(int i) {
            VelocityTracker velocityTracker = this.f884a;
            if (velocityTracker != null) {
                velocityTracker.computeCurrentVelocity(i);
            }
        }

        public float b() {
            VelocityTracker velocityTracker = this.f884a;
            if (velocityTracker != null) {
                return velocityTracker.getXVelocity();
            }
            return 0.0f;
        }

        public float c() {
            VelocityTracker velocityTracker = this.f884a;
            if (velocityTracker != null) {
                return velocityTracker.getYVelocity();
            }
            return 0.0f;
        }
    }

    class j {

        /* renamed from: a, reason: collision with root package name */
        float f885a = Float.NaN;

        /* renamed from: b, reason: collision with root package name */
        float f886b = Float.NaN;

        /* renamed from: c, reason: collision with root package name */
        int f887c = -1;

        /* renamed from: d, reason: collision with root package name */
        int f888d = -1;

        j() {
        }

        void a() {
            int i = this.f887c;
            if (i != -1 || this.f888d != -1) {
                if (i == -1) {
                    MotionLayout.this.transitionToState(this.f888d);
                } else {
                    int i2 = this.f888d;
                    if (i2 == -1) {
                        MotionLayout.this.setState(i, -1, -1);
                    } else {
                        MotionLayout.this.setTransition(i, i2);
                    }
                }
                MotionLayout.this.setState(l.SETUP);
            }
            if (Float.isNaN(this.f886b)) {
                if (Float.isNaN(this.f885a)) {
                    return;
                }
                MotionLayout.this.setProgress(this.f885a);
            } else {
                MotionLayout.this.setProgress(this.f885a, this.f886b);
                this.f885a = Float.NaN;
                this.f886b = Float.NaN;
                this.f887c = -1;
                this.f888d = -1;
            }
        }
    }

    public interface k {
        void onTransitionChange(MotionLayout motionLayout, int i, int i2, float f);

        void onTransitionCompleted(MotionLayout motionLayout, int i);

        void onTransitionStarted(MotionLayout motionLayout, int i, int i2);

        void onTransitionTrigger(MotionLayout motionLayout, int i, boolean z, float f);
    }

    enum l {
        UNDEFINED,
        SETUP,
        MOVING,
        FINISHED
    }

    public MotionLayout(Context context) {
        super(context);
        this.mProgressInterpolator = null;
        this.mLastVelocity = 0.0f;
        this.mBeginState = -1;
        this.mCurrentState = -1;
        this.mEndState = -1;
        this.mLastWidthMeasureSpec = 0;
        this.mLastHeightMeasureSpec = 0;
        this.mInteractionEnabled = true;
        this.mFrameArrayList = new HashMap<>();
        this.mAnimationStartTime = 0L;
        this.mTransitionDuration = 1.0f;
        this.mTransitionPosition = 0.0f;
        this.mTransitionLastPosition = 0.0f;
        this.mTransitionGoalPosition = 0.0f;
        this.mInTransition = false;
        this.mIndirectTransition = false;
        this.mDebugPath = 0;
        this.mTemporalInterpolator = false;
        this.mStopLogic = new a.f.b.a.b();
        this.mDecelerateLogic = new e();
        this.firstDown = true;
        this.mUndergoingMotion = false;
        this.mKeepAnimating = false;
        this.mOnShowHelpers = null;
        this.mOnHideHelpers = null;
        this.mDecoratorsHelpers = null;
        this.mTransitionListeners = null;
        this.mFrames = 0;
        this.mLastDrawTime = -1L;
        this.mLastFps = 0.0f;
        this.mListenerState = 0;
        this.mListenerPosition = 0.0f;
        this.mIsAnimating = false;
        this.mMeasureDuringTransition = false;
        this.mKeyCache = new a.f.a.i.a.d();
        this.mInLayout = false;
        this.mOnComplete = null;
        this.mScheduledTransitionTo = null;
        this.mScheduledTransitions = 0;
        this.mInRotation = false;
        this.mRotatMode = 0;
        this.mPreRotate = new HashMap<>();
        this.mTempRect = new Rect();
        this.mDelayedApply = false;
        this.mTransitionState = l.UNDEFINED;
        this.mModel = new g();
        this.mNeedsFireTransitionCompleted = false;
        this.mBoundsCheck = new RectF();
        this.mRegionView = null;
        this.mInverseMatrix = null;
        this.mTransitionCompleted = new ArrayList<>();
        init(null);
    }

    public MotionLayout(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        this.mProgressInterpolator = null;
        this.mLastVelocity = 0.0f;
        this.mBeginState = -1;
        this.mCurrentState = -1;
        this.mEndState = -1;
        this.mLastWidthMeasureSpec = 0;
        this.mLastHeightMeasureSpec = 0;
        this.mInteractionEnabled = true;
        this.mFrameArrayList = new HashMap<>();
        this.mAnimationStartTime = 0L;
        this.mTransitionDuration = 1.0f;
        this.mTransitionPosition = 0.0f;
        this.mTransitionLastPosition = 0.0f;
        this.mTransitionGoalPosition = 0.0f;
        this.mInTransition = false;
        this.mIndirectTransition = false;
        this.mDebugPath = 0;
        this.mTemporalInterpolator = false;
        this.mStopLogic = new a.f.b.a.b();
        this.mDecelerateLogic = new e();
        this.firstDown = true;
        this.mUndergoingMotion = false;
        this.mKeepAnimating = false;
        this.mOnShowHelpers = null;
        this.mOnHideHelpers = null;
        this.mDecoratorsHelpers = null;
        this.mTransitionListeners = null;
        this.mFrames = 0;
        this.mLastDrawTime = -1L;
        this.mLastFps = 0.0f;
        this.mListenerState = 0;
        this.mListenerPosition = 0.0f;
        this.mIsAnimating = false;
        this.mMeasureDuringTransition = false;
        this.mKeyCache = new a.f.a.i.a.d();
        this.mInLayout = false;
        this.mOnComplete = null;
        this.mScheduledTransitionTo = null;
        this.mScheduledTransitions = 0;
        this.mInRotation = false;
        this.mRotatMode = 0;
        this.mPreRotate = new HashMap<>();
        this.mTempRect = new Rect();
        this.mDelayedApply = false;
        this.mTransitionState = l.UNDEFINED;
        this.mModel = new g();
        this.mNeedsFireTransitionCompleted = false;
        this.mBoundsCheck = new RectF();
        this.mRegionView = null;
        this.mInverseMatrix = null;
        this.mTransitionCompleted = new ArrayList<>();
        init(attributeSet);
    }

    public MotionLayout(Context context, AttributeSet attributeSet, int i2) {
        super(context, attributeSet, i2);
        this.mProgressInterpolator = null;
        this.mLastVelocity = 0.0f;
        this.mBeginState = -1;
        this.mCurrentState = -1;
        this.mEndState = -1;
        this.mLastWidthMeasureSpec = 0;
        this.mLastHeightMeasureSpec = 0;
        this.mInteractionEnabled = true;
        this.mFrameArrayList = new HashMap<>();
        this.mAnimationStartTime = 0L;
        this.mTransitionDuration = 1.0f;
        this.mTransitionPosition = 0.0f;
        this.mTransitionLastPosition = 0.0f;
        this.mTransitionGoalPosition = 0.0f;
        this.mInTransition = false;
        this.mIndirectTransition = false;
        this.mDebugPath = 0;
        this.mTemporalInterpolator = false;
        this.mStopLogic = new a.f.b.a.b();
        this.mDecelerateLogic = new e();
        this.firstDown = true;
        this.mUndergoingMotion = false;
        this.mKeepAnimating = false;
        this.mOnShowHelpers = null;
        this.mOnHideHelpers = null;
        this.mDecoratorsHelpers = null;
        this.mTransitionListeners = null;
        this.mFrames = 0;
        this.mLastDrawTime = -1L;
        this.mLastFps = 0.0f;
        this.mListenerState = 0;
        this.mListenerPosition = 0.0f;
        this.mIsAnimating = false;
        this.mMeasureDuringTransition = false;
        this.mKeyCache = new a.f.a.i.a.d();
        this.mInLayout = false;
        this.mOnComplete = null;
        this.mScheduledTransitionTo = null;
        this.mScheduledTransitions = 0;
        this.mInRotation = false;
        this.mRotatMode = 0;
        this.mPreRotate = new HashMap<>();
        this.mTempRect = new Rect();
        this.mDelayedApply = false;
        this.mTransitionState = l.UNDEFINED;
        this.mModel = new g();
        this.mNeedsFireTransitionCompleted = false;
        this.mBoundsCheck = new RectF();
        this.mRegionView = null;
        this.mInverseMatrix = null;
        this.mTransitionCompleted = new ArrayList<>();
        init(attributeSet);
    }

    private boolean callTransformedTouchEvent(View view, MotionEvent motionEvent, float f2, float f3) {
        Matrix matrix = view.getMatrix();
        if (matrix.isIdentity()) {
            motionEvent.offsetLocation(f2, f3);
            boolean onTouchEvent = view.onTouchEvent(motionEvent);
            motionEvent.offsetLocation(-f2, -f3);
            return onTouchEvent;
        }
        MotionEvent obtain = MotionEvent.obtain(motionEvent);
        obtain.offsetLocation(f2, f3);
        if (this.mInverseMatrix == null) {
            this.mInverseMatrix = new Matrix();
        }
        matrix.invert(this.mInverseMatrix);
        obtain.transform(this.mInverseMatrix);
        boolean onTouchEvent2 = view.onTouchEvent(obtain);
        obtain.recycle();
        return onTouchEvent2;
    }

    private void checkStructure() {
        q qVar = this.mScene;
        if (qVar == null) {
            Log.e(TAG, "CHECK: motion scene not set! set \"app:layoutDescription=\"@xml/file\"");
            return;
        }
        int x = qVar.x();
        q qVar2 = this.mScene;
        checkStructure(x, qVar2.i(qVar2.x()));
        SparseIntArray sparseIntArray = new SparseIntArray();
        SparseIntArray sparseIntArray2 = new SparseIntArray();
        Iterator<q.b> it = this.mScene.k().iterator();
        while (it.hasNext()) {
            q.b next = it.next();
            if (next == this.mScene.f923c) {
                Log.v(TAG, "CHECK: CURRENT");
            }
            checkStructure(next);
            int y = next.y();
            int w = next.w();
            String c2 = a.b.a.c(getContext(), y);
            String c3 = a.b.a.c(getContext(), w);
            if (sparseIntArray.get(y) == w) {
                Log.e(TAG, "CHECK: two transitions with the same start and end " + c2 + "->" + c3);
            }
            if (sparseIntArray2.get(w) == y) {
                Log.e(TAG, "CHECK: you can't have reverse transitions" + c2 + "->" + c3);
            }
            sparseIntArray.put(y, w);
            sparseIntArray2.put(w, y);
            if (this.mScene.i(y) == null) {
                Log.e(TAG, " no such constraintSetStart " + c2);
            }
            if (this.mScene.i(w) == null) {
                Log.e(TAG, " no such constraintSetEnd " + c2);
            }
        }
    }

    private void checkStructure(int i2, androidx.constraintlayout.widget.c cVar) {
        String c2 = a.b.a.c(getContext(), i2);
        int childCount = getChildCount();
        for (int i3 = 0; i3 < childCount; i3++) {
            View childAt = getChildAt(i3);
            int id = childAt.getId();
            if (id == -1) {
                StringBuilder k2 = b.b.a.a.a.k("CHECK: ", c2, " ALL VIEWS SHOULD HAVE ID's ");
                k2.append(childAt.getClass().getName());
                k2.append(" does not!");
                Log.w(TAG, k2.toString());
            }
            if (cVar.q(id) == null) {
                StringBuilder k3 = b.b.a.a.a.k("CHECK: ", c2, " NO CONSTRAINTS for ");
                k3.append(a.b.a.d(childAt));
                Log.w(TAG, k3.toString());
            }
        }
        int[] s = cVar.s();
        for (int i4 = 0; i4 < s.length; i4++) {
            int i5 = s[i4];
            String c3 = a.b.a.c(getContext(), i5);
            if (findViewById(s[i4]) == null) {
                Log.w(TAG, "CHECK: " + c2 + " NO View matches id " + c3);
            }
            if (cVar.r(i5) == -1) {
                Log.w(TAG, b.b.a.a.a.i("CHECK: ", c2, "(", c3, ") no LAYOUT_HEIGHT"));
            }
            if (cVar.w(i5) == -1) {
                Log.w(TAG, b.b.a.a.a.i("CHECK: ", c2, "(", c3, ") no LAYOUT_HEIGHT"));
            }
        }
    }

    private void checkStructure(q.b bVar) {
        if (bVar.y() == bVar.w()) {
            Log.e(TAG, "CHECK: start and end constraint set should not be the same!");
        }
    }

    private void computeCurrentPositions() {
        int childCount = getChildCount();
        for (int i2 = 0; i2 < childCount; i2++) {
            View childAt = getChildAt(i2);
            n nVar = this.mFrameArrayList.get(childAt);
            if (nVar != null) {
                nVar.x(childAt);
            }
        }
    }

    @SuppressLint({"LogConditional"})
    private void debugPos() {
        for (int i2 = 0; i2 < getChildCount(); i2++) {
            View childAt = getChildAt(i2);
            StringBuilder j2 = b.b.a.a.a.j(" ");
            j2.append(a.b.a.b());
            j2.append(" ");
            j2.append(a.b.a.d(this));
            j2.append(" ");
            j2.append(a.b.a.c(getContext(), this.mCurrentState));
            j2.append(" ");
            j2.append(a.b.a.d(childAt));
            j2.append(childAt.getLeft());
            j2.append(" ");
            j2.append(childAt.getTop());
            Log.v(TAG, j2.toString());
        }
    }

    private void evaluateLayout() {
        boolean z;
        float signum = Math.signum(this.mTransitionGoalPosition - this.mTransitionLastPosition);
        long nanoTime = getNanoTime();
        Interpolator interpolator = this.mInterpolator;
        float f2 = this.mTransitionLastPosition + (!(interpolator instanceof a.f.b.a.b) ? (((nanoTime - this.mTransitionLastTime) * signum) * 1.0E-9f) / this.mTransitionDuration : 0.0f);
        if (this.mTransitionInstantly) {
            f2 = this.mTransitionGoalPosition;
        }
        if ((signum <= 0.0f || f2 < this.mTransitionGoalPosition) && (signum > 0.0f || f2 > this.mTransitionGoalPosition)) {
            z = false;
        } else {
            f2 = this.mTransitionGoalPosition;
            z = true;
        }
        if (interpolator != null && !z) {
            f2 = this.mTemporalInterpolator ? interpolator.getInterpolation((nanoTime - this.mAnimationStartTime) * 1.0E-9f) : interpolator.getInterpolation(f2);
        }
        if ((signum > 0.0f && f2 >= this.mTransitionGoalPosition) || (signum <= 0.0f && f2 <= this.mTransitionGoalPosition)) {
            f2 = this.mTransitionGoalPosition;
        }
        this.mPostInterpolationPosition = f2;
        int childCount = getChildCount();
        long nanoTime2 = getNanoTime();
        Interpolator interpolator2 = this.mProgressInterpolator;
        if (interpolator2 != null) {
            f2 = interpolator2.getInterpolation(f2);
        }
        for (int i2 = 0; i2 < childCount; i2++) {
            View childAt = getChildAt(i2);
            n nVar = this.mFrameArrayList.get(childAt);
            if (nVar != null) {
                nVar.r(childAt, f2, nanoTime2, this.mKeyCache);
            }
        }
        if (this.mMeasureDuringTransition) {
            requestLayout();
        }
    }

    private void fireTransitionChange() {
        CopyOnWriteArrayList<k> copyOnWriteArrayList;
        if ((this.mTransitionListener == null && ((copyOnWriteArrayList = this.mTransitionListeners) == null || copyOnWriteArrayList.isEmpty())) || this.mListenerPosition == this.mTransitionPosition) {
            return;
        }
        if (this.mListenerState != -1) {
            k kVar = this.mTransitionListener;
            if (kVar != null) {
                kVar.onTransitionStarted(this, this.mBeginState, this.mEndState);
            }
            CopyOnWriteArrayList<k> copyOnWriteArrayList2 = this.mTransitionListeners;
            if (copyOnWriteArrayList2 != null) {
                Iterator<k> it = copyOnWriteArrayList2.iterator();
                while (it.hasNext()) {
                    it.next().onTransitionStarted(this, this.mBeginState, this.mEndState);
                }
            }
            this.mIsAnimating = true;
        }
        this.mListenerState = -1;
        float f2 = this.mTransitionPosition;
        this.mListenerPosition = f2;
        k kVar2 = this.mTransitionListener;
        if (kVar2 != null) {
            kVar2.onTransitionChange(this, this.mBeginState, this.mEndState, f2);
        }
        CopyOnWriteArrayList<k> copyOnWriteArrayList3 = this.mTransitionListeners;
        if (copyOnWriteArrayList3 != null) {
            Iterator<k> it2 = copyOnWriteArrayList3.iterator();
            while (it2.hasNext()) {
                it2.next().onTransitionChange(this, this.mBeginState, this.mEndState, this.mTransitionPosition);
            }
        }
        this.mIsAnimating = true;
    }

    private void fireTransitionStarted(MotionLayout motionLayout, int i2, int i3) {
        k kVar = this.mTransitionListener;
        if (kVar != null) {
            kVar.onTransitionStarted(this, i2, i3);
        }
        CopyOnWriteArrayList<k> copyOnWriteArrayList = this.mTransitionListeners;
        if (copyOnWriteArrayList != null) {
            Iterator<k> it = copyOnWriteArrayList.iterator();
            while (it.hasNext()) {
                it.next().onTransitionStarted(motionLayout, i2, i3);
            }
        }
    }

    private boolean handlesTouchEvent(float f2, float f3, View view, MotionEvent motionEvent) {
        boolean z;
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            for (int childCount = viewGroup.getChildCount() - 1; childCount >= 0; childCount--) {
                if (handlesTouchEvent((r3.getLeft() + f2) - view.getScrollX(), (r3.getTop() + f3) - view.getScrollY(), viewGroup.getChildAt(childCount), motionEvent)) {
                    z = true;
                    break;
                }
            }
        }
        z = false;
        if (!z) {
            this.mBoundsCheck.set(f2, f3, (view.getRight() + f2) - view.getLeft(), (view.getBottom() + f3) - view.getTop());
            if ((motionEvent.getAction() != 0 || this.mBoundsCheck.contains(motionEvent.getX(), motionEvent.getY())) && callTransformedTouchEvent(view, motionEvent, -f2, -f3)) {
                return true;
            }
        }
        return z;
    }

    private void init(AttributeSet attributeSet) {
        q qVar;
        IS_IN_EDIT_MODE = isInEditMode();
        if (attributeSet != null) {
            TypedArray obtainStyledAttributes = getContext().obtainStyledAttributes(attributeSet, androidx.constraintlayout.widget.f.u);
            int indexCount = obtainStyledAttributes.getIndexCount();
            boolean z = true;
            for (int i2 = 0; i2 < indexCount; i2++) {
                int index = obtainStyledAttributes.getIndex(i2);
                if (index == 2) {
                    this.mScene = new q(getContext(), this, obtainStyledAttributes.getResourceId(index, -1));
                } else if (index == 1) {
                    this.mCurrentState = obtainStyledAttributes.getResourceId(index, -1);
                } else if (index == 4) {
                    this.mTransitionGoalPosition = obtainStyledAttributes.getFloat(index, 0.0f);
                    this.mInTransition = true;
                } else if (index == 0) {
                    z = obtainStyledAttributes.getBoolean(index, z);
                } else if (index == 5) {
                    if (this.mDebugPath == 0) {
                        this.mDebugPath = obtainStyledAttributes.getBoolean(index, false) ? 2 : 0;
                    }
                } else if (index == 3) {
                    this.mDebugPath = obtainStyledAttributes.getInt(index, 0);
                }
            }
            obtainStyledAttributes.recycle();
            if (this.mScene == null) {
                Log.e(TAG, "WARNING NO app:layoutDescription tag");
            }
            if (!z) {
                this.mScene = null;
            }
        }
        if (this.mDebugPath != 0) {
            checkStructure();
        }
        if (this.mCurrentState != -1 || (qVar = this.mScene) == null) {
            return;
        }
        this.mCurrentState = qVar.x();
        this.mBeginState = this.mScene.x();
        this.mEndState = this.mScene.m();
    }

    private void processTransitionCompleted() {
        CopyOnWriteArrayList<k> copyOnWriteArrayList;
        if (this.mTransitionListener == null && ((copyOnWriteArrayList = this.mTransitionListeners) == null || copyOnWriteArrayList.isEmpty())) {
            return;
        }
        this.mIsAnimating = false;
        Iterator<Integer> it = this.mTransitionCompleted.iterator();
        while (it.hasNext()) {
            Integer next = it.next();
            k kVar = this.mTransitionListener;
            if (kVar != null) {
                kVar.onTransitionCompleted(this, next.intValue());
            }
            CopyOnWriteArrayList<k> copyOnWriteArrayList2 = this.mTransitionListeners;
            if (copyOnWriteArrayList2 != null) {
                Iterator<k> it2 = copyOnWriteArrayList2.iterator();
                while (it2.hasNext()) {
                    it2.next().onTransitionCompleted(this, next.intValue());
                }
            }
        }
        this.mTransitionCompleted.clear();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void setupMotionViews() {
        int childCount = getChildCount();
        this.mModel.a();
        boolean z = true;
        this.mInTransition = true;
        SparseArray sparseArray = new SparseArray();
        int i2 = 0;
        for (int i3 = 0; i3 < childCount; i3++) {
            View childAt = getChildAt(i3);
            sparseArray.put(childAt.getId(), this.mFrameArrayList.get(childAt));
        }
        int width = getWidth();
        int height = getHeight();
        q.b bVar = this.mScene.f923c;
        int i4 = bVar != null ? bVar.p : -1;
        if (i4 != -1) {
            for (int i5 = 0; i5 < childCount; i5++) {
                n nVar = this.mFrameArrayList.get(getChildAt(i5));
                if (nVar != null) {
                    nVar.w(i4);
                }
            }
        }
        SparseBooleanArray sparseBooleanArray = new SparseBooleanArray();
        int[] iArr = new int[this.mFrameArrayList.size()];
        int i6 = 0;
        for (int i7 = 0; i7 < childCount; i7++) {
            n nVar2 = this.mFrameArrayList.get(getChildAt(i7));
            if (nVar2.h() != -1) {
                sparseBooleanArray.put(nVar2.h(), true);
                iArr[i6] = nVar2.h();
                i6++;
            }
        }
        if (this.mDecoratorsHelpers != null) {
            for (int i8 = 0; i8 < i6; i8++) {
                n nVar3 = this.mFrameArrayList.get(findViewById(iArr[i8]));
                if (nVar3 != null) {
                    this.mScene.p(nVar3);
                }
            }
            Iterator<MotionHelper> it = this.mDecoratorsHelpers.iterator();
            while (it.hasNext()) {
                it.next().onPreSetup(this, this.mFrameArrayList);
            }
            for (int i9 = 0; i9 < i6; i9++) {
                n nVar4 = this.mFrameArrayList.get(findViewById(iArr[i9]));
                if (nVar4 != null) {
                    nVar4.A(width, height, getNanoTime());
                }
            }
        } else {
            for (int i10 = 0; i10 < i6; i10++) {
                n nVar5 = this.mFrameArrayList.get(findViewById(iArr[i10]));
                if (nVar5 != null) {
                    this.mScene.p(nVar5);
                    nVar5.A(width, height, getNanoTime());
                }
            }
        }
        for (int i11 = 0; i11 < childCount; i11++) {
            View childAt2 = getChildAt(i11);
            n nVar6 = this.mFrameArrayList.get(childAt2);
            if (!sparseBooleanArray.get(childAt2.getId()) && nVar6 != null) {
                this.mScene.p(nVar6);
                nVar6.A(width, height, getNanoTime());
            }
        }
        q.b bVar2 = this.mScene.f923c;
        float f2 = bVar2 != null ? bVar2.i : 0.0f;
        if (f2 != 0.0f) {
            boolean z2 = ((double) f2) < 0.0d;
            float abs = Math.abs(f2);
            float f3 = -3.4028235E38f;
            float f4 = Float.MAX_VALUE;
            int i12 = 0;
            float f5 = -3.4028235E38f;
            float f6 = Float.MAX_VALUE;
            while (true) {
                if (i12 >= childCount) {
                    z = false;
                    break;
                }
                n nVar7 = this.mFrameArrayList.get(getChildAt(i12));
                if (!Float.isNaN(nVar7.l)) {
                    break;
                }
                float l2 = nVar7.l();
                float m = nVar7.m();
                float f7 = z2 ? m - l2 : m + l2;
                f6 = Math.min(f6, f7);
                f5 = Math.max(f5, f7);
                i12++;
            }
            if (!z) {
                while (i2 < childCount) {
                    n nVar8 = this.mFrameArrayList.get(getChildAt(i2));
                    float l3 = nVar8.l();
                    float m2 = nVar8.m();
                    float f8 = z2 ? m2 - l3 : m2 + l3;
                    nVar8.n = 1.0f / (1.0f - abs);
                    nVar8.m = abs - (((f8 - f6) * abs) / (f5 - f6));
                    i2++;
                }
                return;
            }
            for (int i13 = 0; i13 < childCount; i13++) {
                n nVar9 = this.mFrameArrayList.get(getChildAt(i13));
                if (!Float.isNaN(nVar9.l)) {
                    f4 = Math.min(f4, nVar9.l);
                    f3 = Math.max(f3, nVar9.l);
                }
            }
            while (i2 < childCount) {
                n nVar10 = this.mFrameArrayList.get(getChildAt(i2));
                if (!Float.isNaN(nVar10.l)) {
                    nVar10.n = 1.0f / (1.0f - abs);
                    float f9 = nVar10.l;
                    nVar10.m = abs - (z2 ? ((f3 - f9) / (f3 - f4)) * abs : ((f9 - f4) * abs) / (f3 - f4));
                }
                i2++;
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public Rect toRect(a.f.a.j.e eVar) {
        this.mTempRect.top = eVar.S();
        this.mTempRect.left = eVar.R();
        Rect rect = this.mTempRect;
        int Q = eVar.Q();
        Rect rect2 = this.mTempRect;
        rect.right = Q + rect2.left;
        int w = eVar.w();
        Rect rect3 = this.mTempRect;
        rect2.bottom = w + rect3.top;
        return rect3;
    }

    private static boolean willJump(float f2, float f3, float f4) {
        if (f2 > 0.0f) {
            float f5 = f2 / f4;
            return ((f2 * f5) - (((f4 * f5) * f5) / 2.0f)) + f3 > 1.0f;
        }
        float f6 = (-f2) / f4;
        return ((((f4 * f6) * f6) / 2.0f) + (f2 * f6)) + f3 < 0.0f;
    }

    public void addTransitionListener(k kVar) {
        if (this.mTransitionListeners == null) {
            this.mTransitionListeners = new CopyOnWriteArrayList<>();
        }
        this.mTransitionListeners.add(kVar);
    }

    void animateTo(float f2) {
        if (this.mScene == null) {
            return;
        }
        float f3 = this.mTransitionLastPosition;
        float f4 = this.mTransitionPosition;
        if (f3 != f4 && this.mTransitionInstantly) {
            this.mTransitionLastPosition = f4;
        }
        float f5 = this.mTransitionLastPosition;
        if (f5 == f2) {
            return;
        }
        this.mTemporalInterpolator = false;
        this.mTransitionGoalPosition = f2;
        this.mTransitionDuration = r0.l() / 1000.0f;
        setProgress(this.mTransitionGoalPosition);
        this.mInterpolator = null;
        this.mProgressInterpolator = this.mScene.o();
        this.mTransitionInstantly = false;
        this.mAnimationStartTime = getNanoTime();
        this.mInTransition = true;
        this.mTransitionPosition = f5;
        this.mTransitionLastPosition = f5;
        invalidate();
    }

    public boolean applyViewTransition(int i2, n nVar) {
        q qVar = this.mScene;
        if (qVar != null) {
            return qVar.r.b(i2, nVar);
        }
        return false;
    }

    public androidx.constraintlayout.widget.c cloneConstraintSet(int i2) {
        q qVar = this.mScene;
        if (qVar == null) {
            return null;
        }
        androidx.constraintlayout.widget.c i3 = qVar.i(i2);
        androidx.constraintlayout.widget.c cVar = new androidx.constraintlayout.widget.c();
        cVar.k(i3);
        return cVar;
    }

    void disableAutoTransition(boolean z) {
        q qVar = this.mScene;
        if (qVar == null) {
            return;
        }
        qVar.h(z);
    }

    /* JADX WARN: Removed duplicated region for block: B:37:0x00ed  */
    /* JADX WARN: Removed duplicated region for block: B:39:0x00f0  */
    @Override // androidx.constraintlayout.widget.ConstraintLayout, android.view.ViewGroup, android.view.View
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    protected void dispatchDraw(android.graphics.Canvas r10) {
        /*
            Method dump skipped, instructions count: 339
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.motion.widget.MotionLayout.dispatchDraw(android.graphics.Canvas):void");
    }

    public void enableTransition(int i2, boolean z) {
        boolean z2;
        q.b transition = getTransition(i2);
        if (z) {
            z2 = true;
        } else {
            q qVar = this.mScene;
            if (transition == qVar.f923c) {
                Iterator it = ((ArrayList) qVar.z(this.mCurrentState)).iterator();
                while (true) {
                    if (!it.hasNext()) {
                        break;
                    }
                    q.b bVar = (q.b) it.next();
                    if (bVar.A()) {
                        this.mScene.f923c = bVar;
                        break;
                    }
                }
            }
            z2 = false;
        }
        transition.D(z2);
    }

    public void enableViewTransition(int i2, boolean z) {
        q qVar = this.mScene;
        if (qVar != null) {
            qVar.r.c(i2, z);
        }
    }

    void endTrigger(boolean z) {
        int childCount = getChildCount();
        for (int i2 = 0; i2 < childCount; i2++) {
            n nVar = this.mFrameArrayList.get(getChildAt(i2));
            if (nVar != null) {
                nVar.f(z);
            }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:20:0x0246, code lost:
    
        if (r1 != r2) goto L163;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x0248, code lost:
    
        r7 = r6;
     */
    /* JADX WARN: Code restructure failed: missing block: B:31:0x024a, code lost:
    
        r7 = r8;
     */
    /* JADX WARN: Code restructure failed: missing block: B:35:0x0257, code lost:
    
        if (r1 != r2) goto L163;
     */
    /* JADX WARN: Removed duplicated region for block: B:107:0x0199  */
    /* JADX WARN: Removed duplicated region for block: B:112:0x01b0  */
    /* JADX WARN: Removed duplicated region for block: B:118:0x01bd  */
    /* JADX WARN: Removed duplicated region for block: B:121:0x01cb  */
    /* JADX WARN: Removed duplicated region for block: B:128:0x01ea  */
    /* JADX WARN: Removed duplicated region for block: B:133:0x0203  */
    /* JADX WARN: Removed duplicated region for block: B:141:0x0223  */
    /* JADX WARN: Removed duplicated region for block: B:161:0x0152  */
    /* JADX WARN: Removed duplicated region for block: B:75:0x0113  */
    /* JADX WARN: Removed duplicated region for block: B:78:0x011b  */
    /* JADX WARN: Removed duplicated region for block: B:93:0x0150  */
    /* JADX WARN: Removed duplicated region for block: B:96:0x015a  */
    /* JADX WARN: Removed duplicated region for block: B:99:0x0171  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    void evaluate(boolean r24) {
        /*
            Method dump skipped, instructions count: 621
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.motion.widget.MotionLayout.evaluate(boolean):void");
    }

    protected void fireTransitionCompleted() {
        int i2;
        CopyOnWriteArrayList<k> copyOnWriteArrayList;
        if ((this.mTransitionListener != null || ((copyOnWriteArrayList = this.mTransitionListeners) != null && !copyOnWriteArrayList.isEmpty())) && this.mListenerState == -1) {
            this.mListenerState = this.mCurrentState;
            if (this.mTransitionCompleted.isEmpty()) {
                i2 = -1;
            } else {
                ArrayList<Integer> arrayList = this.mTransitionCompleted;
                i2 = arrayList.get(arrayList.size() - 1).intValue();
            }
            int i3 = this.mCurrentState;
            if (i2 != i3 && i3 != -1) {
                this.mTransitionCompleted.add(Integer.valueOf(i3));
            }
        }
        processTransitionCompleted();
        Runnable runnable = this.mOnComplete;
        if (runnable != null) {
            runnable.run();
        }
        int[] iArr = this.mScheduledTransitionTo;
        if (iArr == null || this.mScheduledTransitions <= 0) {
            return;
        }
        transitionToState(iArr[0]);
        int[] iArr2 = this.mScheduledTransitionTo;
        System.arraycopy(iArr2, 1, iArr2, 0, iArr2.length - 1);
        this.mScheduledTransitions--;
    }

    public void fireTrigger(int i2, boolean z, float f2) {
        k kVar = this.mTransitionListener;
        if (kVar != null) {
            kVar.onTransitionTrigger(this, i2, z, f2);
        }
        CopyOnWriteArrayList<k> copyOnWriteArrayList = this.mTransitionListeners;
        if (copyOnWriteArrayList != null) {
            Iterator<k> it = copyOnWriteArrayList.iterator();
            while (it.hasNext()) {
                it.next().onTransitionTrigger(this, i2, z, f2);
            }
        }
    }

    void getAnchorDpDt(int i2, float f2, float f3, float f4, float[] fArr) {
        HashMap<View, n> hashMap = this.mFrameArrayList;
        View viewById = getViewById(i2);
        n nVar = hashMap.get(viewById);
        if (nVar != null) {
            nVar.j(f2, f3, f4, fArr);
            float y = viewById.getY();
            this.lastPos = f2;
            this.lastY = y;
            return;
        }
        Log.w(TAG, "WARNING could not find view id " + (viewById == null ? b.b.a.a.a.e("", i2) : viewById.getContext().getResources().getResourceName(i2)));
    }

    public androidx.constraintlayout.widget.c getConstraintSet(int i2) {
        q qVar = this.mScene;
        if (qVar == null) {
            return null;
        }
        return qVar.i(i2);
    }

    public int[] getConstraintSetIds() {
        q qVar = this.mScene;
        if (qVar == null) {
            return null;
        }
        return qVar.j();
    }

    String getConstraintSetNames(int i2) {
        q qVar = this.mScene;
        if (qVar == null) {
            return null;
        }
        return qVar.B(i2);
    }

    public int getCurrentState() {
        return this.mCurrentState;
    }

    public void getDebugMode(boolean z) {
        this.mDebugPath = z ? 2 : 1;
        invalidate();
    }

    public ArrayList<q.b> getDefinedTransitions() {
        q qVar = this.mScene;
        if (qVar == null) {
            return null;
        }
        return qVar.k();
    }

    public androidx.constraintlayout.motion.widget.b getDesignTool() {
        if (this.mDesignTool == null) {
            this.mDesignTool = new androidx.constraintlayout.motion.widget.b(this);
        }
        return this.mDesignTool;
    }

    public int getEndState() {
        return this.mEndState;
    }

    n getMotionController(int i2) {
        return this.mFrameArrayList.get(findViewById(i2));
    }

    protected long getNanoTime() {
        return System.nanoTime();
    }

    public float getProgress() {
        return this.mTransitionLastPosition;
    }

    public q getScene() {
        return this.mScene;
    }

    public int getStartState() {
        return this.mBeginState;
    }

    public float getTargetPosition() {
        return this.mTransitionGoalPosition;
    }

    public q.b getTransition(int i2) {
        return this.mScene.y(i2);
    }

    public Bundle getTransitionState() {
        if (this.mStateCache == null) {
            this.mStateCache = new j();
        }
        j jVar = this.mStateCache;
        jVar.f888d = MotionLayout.this.mEndState;
        jVar.f887c = MotionLayout.this.mBeginState;
        jVar.f886b = MotionLayout.this.getVelocity();
        jVar.f885a = MotionLayout.this.getProgress();
        j jVar2 = this.mStateCache;
        Objects.requireNonNull(jVar2);
        Bundle bundle = new Bundle();
        bundle.putFloat("motion.progress", jVar2.f885a);
        bundle.putFloat("motion.velocity", jVar2.f886b);
        bundle.putInt("motion.StartState", jVar2.f887c);
        bundle.putInt("motion.EndState", jVar2.f888d);
        return bundle;
    }

    public long getTransitionTimeMs() {
        if (this.mScene != null) {
            this.mTransitionDuration = r0.l() / 1000.0f;
        }
        return (long) (this.mTransitionDuration * 1000.0f);
    }

    public float getVelocity() {
        return this.mLastVelocity;
    }

    public void getViewVelocity(View view, float f2, float f3, float[] fArr, int i2) {
        float f4;
        float f5 = this.mLastVelocity;
        float f6 = this.mTransitionLastPosition;
        if (this.mInterpolator != null) {
            float signum = Math.signum(this.mTransitionGoalPosition - f6);
            float interpolation = this.mInterpolator.getInterpolation(this.mTransitionLastPosition + EPSILON);
            float interpolation2 = this.mInterpolator.getInterpolation(this.mTransitionLastPosition);
            f5 = (((interpolation - interpolation2) / EPSILON) * signum) / this.mTransitionDuration;
            f4 = interpolation2;
        } else {
            f4 = f6;
        }
        Interpolator interpolator = this.mInterpolator;
        if (interpolator instanceof o) {
            f5 = ((o) interpolator).a();
        }
        n nVar = this.mFrameArrayList.get(view);
        if ((i2 & 1) == 0) {
            nVar.o(f4, view.getWidth(), view.getHeight(), f2, f3, fArr);
        } else {
            nVar.j(f4, f2, f3, fArr);
        }
        if (i2 < 2) {
            fArr[0] = fArr[0] * f5;
            fArr[1] = fArr[1] * f5;
        }
    }

    @Override // android.view.View
    public boolean isAttachedToWindow() {
        return super.isAttachedToWindow();
    }

    public boolean isDelayedApplicationOfInitialState() {
        return this.mDelayedApply;
    }

    public boolean isInRotation() {
        return this.mInRotation;
    }

    public boolean isInteractionEnabled() {
        return this.mInteractionEnabled;
    }

    public boolean isViewTransitionEnabled(int i2) {
        q qVar = this.mScene;
        if (qVar != null) {
            return qVar.r.e(i2);
        }
        return false;
    }

    public void jumpToState(int i2) {
        float f2;
        if (!isAttachedToWindow()) {
            this.mCurrentState = i2;
        }
        if (this.mBeginState == i2) {
            f2 = 0.0f;
        } else {
            if (this.mEndState != i2) {
                setTransition(i2, i2);
                return;
            }
            f2 = 1.0f;
        }
        setProgress(f2);
    }

    @Override // androidx.constraintlayout.widget.ConstraintLayout
    public void loadLayoutDescription(int i2) {
        q.b bVar;
        if (i2 == 0) {
            this.mScene = null;
            return;
        }
        try {
            q qVar = new q(getContext(), this, i2);
            this.mScene = qVar;
            if (this.mCurrentState == -1 && qVar != null) {
                this.mCurrentState = qVar.x();
                this.mBeginState = this.mScene.x();
                this.mEndState = this.mScene.m();
            }
            if (!isAttachedToWindow()) {
                this.mScene = null;
                return;
            }
            try {
                Display display = getDisplay();
                this.mPreviouseRotation = display == null ? 0 : display.getRotation();
                q qVar2 = this.mScene;
                if (qVar2 != null) {
                    androidx.constraintlayout.widget.c i3 = qVar2.i(this.mCurrentState);
                    this.mScene.I(this);
                    ArrayList<MotionHelper> arrayList = this.mDecoratorsHelpers;
                    if (arrayList != null) {
                        Iterator<MotionHelper> it = arrayList.iterator();
                        while (it.hasNext()) {
                            it.next().onFinishedMotionScene(this);
                        }
                    }
                    if (i3 != null) {
                        i3.d(this);
                    }
                    this.mBeginState = this.mCurrentState;
                }
                onNewStateAttachHandlers();
                j jVar = this.mStateCache;
                if (jVar != null) {
                    if (this.mDelayedApply) {
                        post(new a());
                        return;
                    } else {
                        jVar.a();
                        return;
                    }
                }
                q qVar3 = this.mScene;
                if (qVar3 == null || (bVar = qVar3.f923c) == null || bVar.v() != 4) {
                    return;
                }
                transitionToEnd();
                setState(l.SETUP);
                setState(l.MOVING);
            } catch (Exception e2) {
                throw new IllegalArgumentException("unable to parse MotionScene file", e2);
            }
        } catch (Exception e3) {
            throw new IllegalArgumentException("unable to parse MotionScene file", e3);
        }
    }

    int lookUpConstraintId(String str) {
        q qVar = this.mScene;
        if (qVar == null) {
            return 0;
        }
        return qVar.A(str);
    }

    protected h obtainVelocityTracker() {
        return i.d();
    }

    @Override // android.view.ViewGroup, android.view.View
    protected void onAttachedToWindow() {
        q.b bVar;
        int i2;
        super.onAttachedToWindow();
        Display display = getDisplay();
        if (display != null) {
            this.mPreviouseRotation = display.getRotation();
        }
        q qVar = this.mScene;
        if (qVar != null && (i2 = this.mCurrentState) != -1) {
            androidx.constraintlayout.widget.c i3 = qVar.i(i2);
            this.mScene.I(this);
            ArrayList<MotionHelper> arrayList = this.mDecoratorsHelpers;
            if (arrayList != null) {
                Iterator<MotionHelper> it = arrayList.iterator();
                while (it.hasNext()) {
                    it.next().onFinishedMotionScene(this);
                }
            }
            if (i3 != null) {
                i3.d(this);
            }
            this.mBeginState = this.mCurrentState;
        }
        onNewStateAttachHandlers();
        j jVar = this.mStateCache;
        if (jVar != null) {
            if (this.mDelayedApply) {
                post(new d());
                return;
            } else {
                jVar.a();
                return;
            }
        }
        q qVar2 = this.mScene;
        if (qVar2 == null || (bVar = qVar2.f923c) == null || bVar.v() != 4) {
            return;
        }
        transitionToEnd();
        setState(l.SETUP);
        setState(l.MOVING);
    }

    @Override // android.view.ViewGroup
    public boolean onInterceptTouchEvent(MotionEvent motionEvent) {
        r z;
        int o;
        RectF n;
        q qVar = this.mScene;
        if (qVar != null && this.mInteractionEnabled) {
            u uVar = qVar.r;
            if (uVar != null) {
                uVar.g(motionEvent);
            }
            q.b bVar = this.mScene.f923c;
            if (bVar != null && bVar.A() && (z = bVar.z()) != null && ((motionEvent.getAction() != 0 || (n = z.n(this, new RectF())) == null || n.contains(motionEvent.getX(), motionEvent.getY())) && (o = z.o()) != -1)) {
                View view = this.mRegionView;
                if (view == null || view.getId() != o) {
                    this.mRegionView = findViewById(o);
                }
                if (this.mRegionView != null) {
                    this.mBoundsCheck.set(r0.getLeft(), this.mRegionView.getTop(), this.mRegionView.getRight(), this.mRegionView.getBottom());
                    if (this.mBoundsCheck.contains(motionEvent.getX(), motionEvent.getY()) && !handlesTouchEvent(this.mRegionView.getLeft(), this.mRegionView.getTop(), this.mRegionView, motionEvent)) {
                        return onTouchEvent(motionEvent);
                    }
                }
            }
        }
        return false;
    }

    @Override // androidx.constraintlayout.widget.ConstraintLayout, android.view.ViewGroup, android.view.View
    protected void onLayout(boolean z, int i2, int i3, int i4, int i5) {
        this.mInLayout = true;
        try {
            if (this.mScene == null) {
                super.onLayout(z, i2, i3, i4, i5);
                return;
            }
            int i6 = i4 - i2;
            int i7 = i5 - i3;
            if (this.mLastLayoutWidth != i6 || this.mLastLayoutHeight != i7) {
                rebuildScene();
                evaluate(true);
            }
            this.mLastLayoutWidth = i6;
            this.mLastLayoutHeight = i7;
            this.mOldWidth = i6;
            this.mOldHeight = i7;
        } finally {
            this.mInLayout = false;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:24:0x0048, code lost:
    
        if (((r3 == r5.e && r4 == r5.f) ? false : true) != false) goto L29;
     */
    @Override // androidx.constraintlayout.widget.ConstraintLayout, android.view.View
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    protected void onMeasure(int r8, int r9) {
        /*
            Method dump skipped, instructions count: 206
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.motion.widget.MotionLayout.onMeasure(int, int):void");
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public boolean onNestedFling(View view, float f2, float f3, boolean z) {
        return false;
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public boolean onNestedPreFling(View view, float f2, float f3) {
        return false;
    }

    @Override // a.h.h.g
    public void onNestedPreScroll(View view, int i2, int i3, int[] iArr, int i4) {
        q.b bVar;
        r z;
        int o;
        q qVar = this.mScene;
        if (qVar == null || (bVar = qVar.f923c) == null || !bVar.A()) {
            return;
        }
        int i5 = -1;
        if (!bVar.A() || (z = bVar.z()) == null || (o = z.o()) == -1 || view.getId() == o) {
            q.b bVar2 = qVar.f923c;
            if ((bVar2 == null || bVar2.l == null) ? false : qVar.f923c.l.g()) {
                r z2 = bVar.z();
                if (z2 != null && (z2.c() & 4) != 0) {
                    i5 = i3;
                }
                float f2 = this.mTransitionPosition;
                if ((f2 == 1.0f || f2 == 0.0f) && view.canScrollVertically(i5)) {
                    return;
                }
            }
            if (bVar.z() != null && (bVar.z().c() & 1) != 0) {
                float f3 = i2;
                float f4 = i3;
                q.b bVar3 = qVar.f923c;
                float h2 = (bVar3 == null || bVar3.l == null) ? 0.0f : qVar.f923c.l.h(f3, f4);
                float f5 = this.mTransitionLastPosition;
                if ((f5 <= 0.0f && h2 < 0.0f) || (f5 >= 1.0f && h2 > 0.0f)) {
                    view.setNestedScrollingEnabled(false);
                    view.post(new c(this, view));
                    return;
                }
            }
            float f6 = this.mTransitionPosition;
            long nanoTime = getNanoTime();
            float f7 = i2;
            this.mScrollTargetDX = f7;
            float f8 = i3;
            this.mScrollTargetDY = f8;
            this.mScrollTargetDT = (float) ((nanoTime - this.mScrollTargetTime) * 1.0E-9d);
            this.mScrollTargetTime = nanoTime;
            q.b bVar4 = qVar.f923c;
            if (bVar4 != null && bVar4.l != null) {
                qVar.f923c.l.r(f7, f8);
            }
            if (f6 != this.mTransitionPosition) {
                iArr[0] = i2;
                iArr[1] = i3;
            }
            evaluate(false);
            if (iArr[0] == 0 && iArr[1] == 0) {
                return;
            }
            this.mUndergoingMotion = true;
        }
    }

    @Override // a.h.h.g
    public void onNestedScroll(View view, int i2, int i3, int i4, int i5, int i6) {
    }

    @Override // a.h.h.h
    public void onNestedScroll(View view, int i2, int i3, int i4, int i5, int i6, int[] iArr) {
        if (this.mUndergoingMotion || i2 != 0 || i3 != 0) {
            iArr[0] = iArr[0] + i4;
            iArr[1] = iArr[1] + i5;
        }
        this.mUndergoingMotion = false;
    }

    @Override // a.h.h.g
    public void onNestedScrollAccepted(View view, View view2, int i2, int i3) {
        this.mScrollTargetTime = getNanoTime();
        this.mScrollTargetDT = 0.0f;
        this.mScrollTargetDX = 0.0f;
        this.mScrollTargetDY = 0.0f;
    }

    void onNewStateAttachHandlers() {
        q qVar;
        q.b bVar;
        q qVar2 = this.mScene;
        if (qVar2 == null) {
            return;
        }
        if (qVar2.g(this, this.mCurrentState)) {
            requestLayout();
            return;
        }
        int i2 = this.mCurrentState;
        if (i2 != -1) {
            this.mScene.f(this, i2);
        }
        if (!this.mScene.O() || (bVar = (qVar = this.mScene).f923c) == null || bVar.l == null) {
            return;
        }
        qVar.f923c.l.x();
    }

    @Override // android.view.View
    public void onRtlPropertiesChanged(int i2) {
        q qVar = this.mScene;
        if (qVar != null) {
            qVar.L(isRtl());
        }
    }

    @Override // a.h.h.g
    public boolean onStartNestedScroll(View view, View view2, int i2, int i3) {
        q.b bVar;
        q qVar = this.mScene;
        return (qVar == null || (bVar = qVar.f923c) == null || bVar.z() == null || (this.mScene.f923c.z().c() & 2) != 0) ? false : true;
    }

    @Override // a.h.h.g
    public void onStopNestedScroll(View view, int i2) {
        q qVar = this.mScene;
        if (qVar != null) {
            float f2 = this.mScrollTargetDT;
            if (f2 == 0.0f) {
                return;
            }
            float f3 = this.mScrollTargetDX / f2;
            float f4 = this.mScrollTargetDY / f2;
            q.b bVar = qVar.f923c;
            if (bVar == null || bVar.l == null) {
                return;
            }
            qVar.f923c.l.s(f3, f4);
        }
    }

    @Override // android.view.View
    public boolean onTouchEvent(MotionEvent motionEvent) {
        q qVar = this.mScene;
        if (qVar == null || !this.mInteractionEnabled || !qVar.O()) {
            return super.onTouchEvent(motionEvent);
        }
        q.b bVar = this.mScene.f923c;
        if (bVar != null && !bVar.A()) {
            return super.onTouchEvent(motionEvent);
        }
        this.mScene.G(motionEvent, getCurrentState(), this);
        if (this.mScene.f923c.B(4)) {
            return this.mScene.f923c.z().p();
        }
        return true;
    }

    @Override // androidx.constraintlayout.widget.ConstraintLayout, android.view.ViewGroup
    public void onViewAdded(View view) {
        super.onViewAdded(view);
        if (view instanceof MotionHelper) {
            MotionHelper motionHelper = (MotionHelper) view;
            if (this.mTransitionListeners == null) {
                this.mTransitionListeners = new CopyOnWriteArrayList<>();
            }
            this.mTransitionListeners.add(motionHelper);
            if (motionHelper.isUsedOnShow()) {
                if (this.mOnShowHelpers == null) {
                    this.mOnShowHelpers = new ArrayList<>();
                }
                this.mOnShowHelpers.add(motionHelper);
            }
            if (motionHelper.isUseOnHide()) {
                if (this.mOnHideHelpers == null) {
                    this.mOnHideHelpers = new ArrayList<>();
                }
                this.mOnHideHelpers.add(motionHelper);
            }
            if (motionHelper.isDecorator()) {
                if (this.mDecoratorsHelpers == null) {
                    this.mDecoratorsHelpers = new ArrayList<>();
                }
                this.mDecoratorsHelpers.add(motionHelper);
            }
        }
    }

    @Override // androidx.constraintlayout.widget.ConstraintLayout, android.view.ViewGroup
    public void onViewRemoved(View view) {
        super.onViewRemoved(view);
        ArrayList<MotionHelper> arrayList = this.mOnShowHelpers;
        if (arrayList != null) {
            arrayList.remove(view);
        }
        ArrayList<MotionHelper> arrayList2 = this.mOnHideHelpers;
        if (arrayList2 != null) {
            arrayList2.remove(view);
        }
    }

    @Override // androidx.constraintlayout.widget.ConstraintLayout
    protected void parseLayoutDescription(int i2) {
        this.mConstraintLayoutSpec = null;
    }

    @Deprecated
    public void rebuildMotion() {
        Log.e(TAG, "This method is deprecated. Please call rebuildScene() instead.");
        rebuildScene();
    }

    public void rebuildScene() {
        this.mModel.f();
        invalidate();
    }

    public boolean removeTransitionListener(k kVar) {
        CopyOnWriteArrayList<k> copyOnWriteArrayList = this.mTransitionListeners;
        if (copyOnWriteArrayList == null) {
            return false;
        }
        return copyOnWriteArrayList.remove(kVar);
    }

    @Override // androidx.constraintlayout.widget.ConstraintLayout, android.view.View, android.view.ViewParent
    public void requestLayout() {
        q qVar;
        q.b bVar;
        if (!this.mMeasureDuringTransition && this.mCurrentState == -1 && (qVar = this.mScene) != null && (bVar = qVar.f923c) != null) {
            int x = bVar.x();
            if (x == 0) {
                return;
            }
            if (x == 2) {
                int childCount = getChildCount();
                for (int i2 = 0; i2 < childCount; i2++) {
                    this.mFrameArrayList.get(getChildAt(i2)).f916d = true;
                }
                return;
            }
        }
        super.requestLayout();
    }

    public void rotateTo(int i2, int i3) {
        this.mInRotation = true;
        this.mPreRotateWidth = getWidth();
        this.mPreRotateHeight = getHeight();
        int rotation = getDisplay().getRotation();
        this.mRotatMode = (rotation + 1) % 4 <= (this.mPreviouseRotation + 1) % 4 ? 2 : 1;
        this.mPreviouseRotation = rotation;
        int childCount = getChildCount();
        for (int i4 = 0; i4 < childCount; i4++) {
            View childAt = getChildAt(i4);
            a.f.b.a.e eVar = this.mPreRotate.get(childAt);
            if (eVar == null) {
                eVar = new a.f.b.a.e();
                this.mPreRotate.put(childAt, eVar);
            }
            eVar.f255b = childAt.getLeft();
            eVar.f256c = childAt.getTop();
            eVar.f257d = childAt.getRight();
            eVar.e = childAt.getBottom();
            eVar.f254a = childAt.getRotation();
        }
        this.mBeginState = -1;
        this.mEndState = i2;
        this.mScene.M(-1, i2);
        this.mModel.e(null, this.mScene.i(this.mEndState));
        this.mTransitionPosition = 0.0f;
        this.mTransitionLastPosition = 0.0f;
        invalidate();
        transitionToEnd(new b());
        if (i3 > 0) {
            this.mTransitionDuration = i3 / 1000.0f;
        }
    }

    public void scheduleTransitionTo(int i2) {
        if (getCurrentState() == -1) {
            transitionToState(i2);
            return;
        }
        int[] iArr = this.mScheduledTransitionTo;
        if (iArr == null) {
            this.mScheduledTransitionTo = new int[4];
        } else if (iArr.length <= this.mScheduledTransitions) {
            this.mScheduledTransitionTo = Arrays.copyOf(iArr, iArr.length * 2);
        }
        int[] iArr2 = this.mScheduledTransitionTo;
        int i3 = this.mScheduledTransitions;
        this.mScheduledTransitions = i3 + 1;
        iArr2[i3] = i2;
    }

    public void setDebugMode(int i2) {
        this.mDebugPath = i2;
        invalidate();
    }

    public void setDelayedApplicationOfInitialState(boolean z) {
        this.mDelayedApply = z;
    }

    public void setInteractionEnabled(boolean z) {
        this.mInteractionEnabled = z;
    }

    public void setInterpolatedProgress(float f2) {
        if (this.mScene != null) {
            setState(l.MOVING);
            Interpolator o = this.mScene.o();
            if (o != null) {
                setProgress(o.getInterpolation(f2));
                return;
            }
        }
        setProgress(f2);
    }

    public void setOnHide(float f2) {
        ArrayList<MotionHelper> arrayList = this.mOnHideHelpers;
        if (arrayList != null) {
            int size = arrayList.size();
            for (int i2 = 0; i2 < size; i2++) {
                this.mOnHideHelpers.get(i2).setProgress(f2);
            }
        }
    }

    public void setOnShow(float f2) {
        ArrayList<MotionHelper> arrayList = this.mOnShowHelpers;
        if (arrayList != null) {
            int size = arrayList.size();
            for (int i2 = 0; i2 < size; i2++) {
                this.mOnShowHelpers.get(i2).setProgress(f2);
            }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:20:0x0045, code lost:
    
        if (r7.mTransitionLastPosition == 0.0f) goto L32;
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x0065, code lost:
    
        setState(r0);
     */
    /* JADX WARN: Code restructure failed: missing block: B:35:0x0063, code lost:
    
        if (r7.mTransitionLastPosition == 1.0f) goto L32;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void setProgress(float r8) {
        /*
            r7 = this;
            androidx.constraintlayout.motion.widget.MotionLayout$l r0 = androidx.constraintlayout.motion.widget.MotionLayout.l.FINISHED
            androidx.constraintlayout.motion.widget.MotionLayout$l r1 = androidx.constraintlayout.motion.widget.MotionLayout.l.MOVING
            r2 = 0
            int r3 = (r8 > r2 ? 1 : (r8 == r2 ? 0 : -1))
            r4 = 1065353216(0x3f800000, float:1.0)
            if (r3 < 0) goto Lf
            int r5 = (r8 > r4 ? 1 : (r8 == r4 ? 0 : -1))
            if (r5 <= 0) goto L16
        Lf:
            java.lang.String r5 = "MotionLayout"
            java.lang.String r6 = "Warning! Progress is defined for values between 0.0 and 1.0 inclusive"
            android.util.Log.w(r5, r6)
        L16:
            boolean r5 = r7.isAttachedToWindow()
            if (r5 != 0) goto L2c
            androidx.constraintlayout.motion.widget.MotionLayout$j r0 = r7.mStateCache
            if (r0 != 0) goto L27
            androidx.constraintlayout.motion.widget.MotionLayout$j r0 = new androidx.constraintlayout.motion.widget.MotionLayout$j
            r0.<init>()
            r7.mStateCache = r0
        L27:
            androidx.constraintlayout.motion.widget.MotionLayout$j r7 = r7.mStateCache
            r7.f885a = r8
            return
        L2c:
            if (r3 > 0) goto L48
            float r3 = r7.mTransitionLastPosition
            int r3 = (r3 > r4 ? 1 : (r3 == r4 ? 0 : -1))
            if (r3 != 0) goto L3d
            int r3 = r7.mCurrentState
            int r4 = r7.mEndState
            if (r3 != r4) goto L3d
            r7.setState(r1)
        L3d:
            int r1 = r7.mBeginState
            r7.mCurrentState = r1
            float r1 = r7.mTransitionLastPosition
            int r1 = (r1 > r2 ? 1 : (r1 == r2 ? 0 : -1))
            if (r1 != 0) goto L6f
            goto L65
        L48:
            int r3 = (r8 > r4 ? 1 : (r8 == r4 ? 0 : -1))
            if (r3 < 0) goto L69
            float r3 = r7.mTransitionLastPosition
            int r2 = (r3 > r2 ? 1 : (r3 == r2 ? 0 : -1))
            if (r2 != 0) goto L5b
            int r2 = r7.mCurrentState
            int r3 = r7.mBeginState
            if (r2 != r3) goto L5b
            r7.setState(r1)
        L5b:
            int r1 = r7.mEndState
            r7.mCurrentState = r1
            float r1 = r7.mTransitionLastPosition
            int r1 = (r1 > r4 ? 1 : (r1 == r4 ? 0 : -1))
            if (r1 != 0) goto L6f
        L65:
            r7.setState(r0)
            goto L6f
        L69:
            r0 = -1
            r7.mCurrentState = r0
            r7.setState(r1)
        L6f:
            androidx.constraintlayout.motion.widget.q r0 = r7.mScene
            if (r0 != 0) goto L74
            return
        L74:
            r0 = 1
            r7.mTransitionInstantly = r0
            r7.mTransitionGoalPosition = r8
            r7.mTransitionPosition = r8
            r1 = -1
            r7.mTransitionLastTime = r1
            r7.mAnimationStartTime = r1
            r8 = 0
            r7.mInterpolator = r8
            r7.mInTransition = r0
            r7.invalidate()
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.motion.widget.MotionLayout.setProgress(float):void");
    }

    public void setScene(q qVar) {
        this.mScene = qVar;
        qVar.L(isRtl());
        rebuildScene();
    }

    void setStartState(int i2) {
        if (isAttachedToWindow()) {
            this.mCurrentState = i2;
            return;
        }
        if (this.mStateCache == null) {
            this.mStateCache = new j();
        }
        j jVar = this.mStateCache;
        jVar.f887c = i2;
        jVar.f888d = i2;
    }

    @Override // androidx.constraintlayout.widget.ConstraintLayout
    public void setState(int i2, int i3, int i4) {
        setState(l.SETUP);
        this.mCurrentState = i2;
        this.mBeginState = -1;
        this.mEndState = -1;
        androidx.constraintlayout.widget.b bVar = this.mConstraintLayoutSpec;
        if (bVar != null) {
            bVar.b(i2, i3, i4);
            return;
        }
        q qVar = this.mScene;
        if (qVar != null) {
            qVar.i(i2).d(this);
        }
    }

    void setState(l lVar) {
        l lVar2 = l.FINISHED;
        if (lVar == lVar2 && this.mCurrentState == -1) {
            return;
        }
        l lVar3 = this.mTransitionState;
        this.mTransitionState = lVar;
        l lVar4 = l.MOVING;
        if (lVar3 == lVar4 && lVar == lVar4) {
            fireTransitionChange();
        }
        int ordinal = lVar3.ordinal();
        if (ordinal == 0 || ordinal == 1) {
            if (lVar == lVar4) {
                fireTransitionChange();
            }
            if (lVar != lVar2) {
                return;
            }
        } else if (ordinal != 2 || lVar != lVar2) {
            return;
        }
        fireTransitionCompleted();
    }

    public void setTransition(int i2) {
        q qVar;
        int i3;
        if (this.mScene != null) {
            q.b transition = getTransition(i2);
            this.mBeginState = transition.y();
            this.mEndState = transition.w();
            if (!isAttachedToWindow()) {
                if (this.mStateCache == null) {
                    this.mStateCache = new j();
                }
                j jVar = this.mStateCache;
                jVar.f887c = this.mBeginState;
                jVar.f888d = this.mEndState;
                return;
            }
            float f2 = Float.NaN;
            int i4 = this.mCurrentState;
            if (i4 == this.mBeginState) {
                f2 = 0.0f;
            } else if (i4 == this.mEndState) {
                f2 = 1.0f;
            }
            this.mScene.N(transition);
            this.mModel.e(this.mScene.i(this.mBeginState), this.mScene.i(this.mEndState));
            rebuildScene();
            if (this.mTransitionLastPosition != f2) {
                if (f2 == 0.0f) {
                    endTrigger(true);
                    qVar = this.mScene;
                    i3 = this.mBeginState;
                } else if (f2 == 1.0f) {
                    endTrigger(false);
                    qVar = this.mScene;
                    i3 = this.mEndState;
                }
                qVar.i(i3).d(this);
            }
            this.mTransitionLastPosition = Float.isNaN(f2) ? 0.0f : f2;
            if (!Float.isNaN(f2)) {
                setProgress(f2);
                return;
            }
            Log.v(TAG, a.b.a.b() + " transitionToStart ");
            transitionToStart();
        }
    }

    public void setTransitionDuration(int i2) {
        q qVar = this.mScene;
        if (qVar == null) {
            Log.e(TAG, "MotionScene not defined");
        } else {
            qVar.K(i2);
        }
    }

    public void setTransitionListener(k kVar) {
        this.mTransitionListener = kVar;
    }

    public void setTransitionState(Bundle bundle) {
        if (this.mStateCache == null) {
            this.mStateCache = new j();
        }
        j jVar = this.mStateCache;
        Objects.requireNonNull(jVar);
        jVar.f885a = bundle.getFloat("motion.progress");
        jVar.f886b = bundle.getFloat("motion.velocity");
        jVar.f887c = bundle.getInt("motion.StartState");
        jVar.f888d = bundle.getInt("motion.EndState");
        if (isAttachedToWindow()) {
            this.mStateCache.a();
        }
    }

    @Override // android.view.View
    public String toString() {
        Context context = getContext();
        return a.b.a.c(context, this.mBeginState) + "->" + a.b.a.c(context, this.mEndState) + " (pos:" + this.mTransitionLastPosition + " Dpos/Dt:" + this.mLastVelocity;
    }

    /* JADX WARN: Code restructure failed: missing block: B:17:0x0038, code lost:
    
        if (r13 != 7) goto L43;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void touchAnimateTo(int r13, float r14, float r15) {
        /*
            Method dump skipped, instructions count: 243
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.motion.widget.MotionLayout.touchAnimateTo(int, float, float):void");
    }

    public void touchSpringTo(float f2, float f3) {
        if (this.mScene == null || this.mTransitionLastPosition == f2) {
            return;
        }
        this.mTemporalInterpolator = true;
        this.mAnimationStartTime = getNanoTime();
        this.mTransitionDuration = this.mScene.l() / 1000.0f;
        this.mTransitionGoalPosition = f2;
        this.mInTransition = true;
        this.mStopLogic.d(this.mTransitionLastPosition, f2, f3, this.mScene.u(), this.mScene.v(), this.mScene.t(), this.mScene.w(), this.mScene.s());
        int i2 = this.mCurrentState;
        this.mTransitionGoalPosition = f2;
        this.mCurrentState = i2;
        this.mInterpolator = this.mStopLogic;
        this.mTransitionInstantly = false;
        this.mAnimationStartTime = getNanoTime();
        invalidate();
    }

    public void transitionToEnd() {
        animateTo(1.0f);
        this.mOnComplete = null;
    }

    public void transitionToEnd(Runnable runnable) {
        animateTo(1.0f);
        this.mOnComplete = runnable;
    }

    public void transitionToStart() {
        animateTo(0.0f);
    }

    public void transitionToState(int i2) {
        if (isAttachedToWindow()) {
            transitionToState(i2, -1, -1);
            return;
        }
        if (this.mStateCache == null) {
            this.mStateCache = new j();
        }
        this.mStateCache.f888d = i2;
    }

    public void transitionToState(int i2, int i3, int i4) {
        transitionToState(i2, i3, i4, -1);
    }

    public void updateState() {
        this.mModel.e(this.mScene.i(this.mBeginState), this.mScene.i(this.mEndState));
        rebuildScene();
    }

    public void updateState(int i2, androidx.constraintlayout.widget.c cVar) {
        q qVar = this.mScene;
        if (qVar != null) {
            qVar.J(i2, cVar);
        }
        updateState();
        if (this.mCurrentState == i2) {
            cVar.d(this);
        }
    }

    public void updateStateAnimate(int i2, androidx.constraintlayout.widget.c cVar, int i3) {
        if (this.mScene != null && this.mCurrentState == i2) {
            updateState(R.id.view_transition, getConstraintSet(i2));
            setState(R.id.view_transition, -1, -1);
            updateState(i2, cVar);
            q.b bVar = new q.b(-1, this.mScene, R.id.view_transition, i2);
            bVar.C(i3);
            setTransition(bVar);
            transitionToEnd();
        }
    }

    public void viewTransition(int i2, View... viewArr) {
        q qVar = this.mScene;
        if (qVar != null) {
            qVar.r.h(i2, viewArr);
        } else {
            Log.e(TAG, " no motionScene");
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:11:0x0029, code lost:
    
        if (r4 > 0.0f) goto L19;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x003a, code lost:
    
        r0 = 1.0f;
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x0038, code lost:
    
        if (r3 > 0.5f) goto L19;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void setProgress(float r3, float r4) {
        /*
            r2 = this;
            boolean r0 = r2.isAttachedToWindow()
            if (r0 != 0) goto L18
            androidx.constraintlayout.motion.widget.MotionLayout$j r0 = r2.mStateCache
            if (r0 != 0) goto L11
            androidx.constraintlayout.motion.widget.MotionLayout$j r0 = new androidx.constraintlayout.motion.widget.MotionLayout$j
            r0.<init>()
            r2.mStateCache = r0
        L11:
            androidx.constraintlayout.motion.widget.MotionLayout$j r2 = r2.mStateCache
            r2.f885a = r3
            r2.f886b = r4
            return
        L18:
            r2.setProgress(r3)
            androidx.constraintlayout.motion.widget.MotionLayout$l r0 = androidx.constraintlayout.motion.widget.MotionLayout.l.MOVING
            r2.setState(r0)
            r2.mLastVelocity = r4
            r0 = 0
            int r4 = (r4 > r0 ? 1 : (r4 == r0 ? 0 : -1))
            r1 = 1065353216(0x3f800000, float:1.0)
            if (r4 == 0) goto L2c
            if (r4 <= 0) goto L3b
            goto L3a
        L2c:
            int r4 = (r3 > r0 ? 1 : (r3 == r0 ? 0 : -1))
            if (r4 == 0) goto L3e
            int r4 = (r3 > r1 ? 1 : (r3 == r1 ? 0 : -1))
            if (r4 == 0) goto L3e
            r4 = 1056964608(0x3f000000, float:0.5)
            int r3 = (r3 > r4 ? 1 : (r3 == r4 ? 0 : -1))
            if (r3 <= 0) goto L3b
        L3a:
            r0 = r1
        L3b:
            r2.animateTo(r0)
        L3e:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.motion.widget.MotionLayout.setProgress(float, float):void");
    }

    public void transitionToState(int i2, int i3) {
        if (!isAttachedToWindow()) {
            if (this.mStateCache == null) {
                this.mStateCache = new j();
            }
            this.mStateCache.f888d = i2;
            return;
        }
        transitionToState(i2, -1, -1, i3);
    }

    public void setTransition(int i2, int i3) {
        if (!isAttachedToWindow()) {
            if (this.mStateCache == null) {
                this.mStateCache = new j();
            }
            j jVar = this.mStateCache;
            jVar.f887c = i2;
            jVar.f888d = i3;
            return;
        }
        q qVar = this.mScene;
        if (qVar != null) {
            this.mBeginState = i2;
            this.mEndState = i3;
            qVar.M(i2, i3);
            this.mModel.e(this.mScene.i(i2), this.mScene.i(i3));
            rebuildScene();
            this.mTransitionLastPosition = 0.0f;
            transitionToStart();
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:93:0x008d, code lost:
    
        if (r12 > 0) goto L37;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void transitionToState(int r9, int r10, int r11, int r12) {
        /*
            Method dump skipped, instructions count: 447
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.motion.widget.MotionLayout.transitionToState(int, int, int, int):void");
    }

    protected void setTransition(q.b bVar) {
        this.mScene.N(bVar);
        setState(l.SETUP);
        float f2 = this.mCurrentState == this.mScene.m() ? 1.0f : 0.0f;
        this.mTransitionLastPosition = f2;
        this.mTransitionPosition = f2;
        this.mTransitionGoalPosition = f2;
        this.mTransitionLastTime = bVar.B(1) ? -1L : getNanoTime();
        int x = this.mScene.x();
        int m = this.mScene.m();
        if (x == this.mBeginState && m == this.mEndState) {
            return;
        }
        this.mBeginState = x;
        this.mEndState = m;
        this.mScene.M(x, m);
        this.mModel.e(this.mScene.i(this.mBeginState), this.mScene.i(this.mEndState));
        g gVar = this.mModel;
        int i2 = this.mBeginState;
        int i3 = this.mEndState;
        gVar.e = i2;
        gVar.f = i3;
        gVar.f();
        rebuildScene();
    }
}
