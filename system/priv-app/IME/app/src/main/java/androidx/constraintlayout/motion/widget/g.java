package androidx.constraintlayout.motion.widget;

import android.content.Context;
import android.util.Log;
import android.util.Xml;
import androidx.constraintlayout.widget.ConstraintLayout;
import java.io.IOException;
import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

/* loaded from: classes.dex */
public class g {

    /* renamed from: b, reason: collision with root package name */
    static HashMap<String, Constructor<? extends d>> f903b;

    /* renamed from: a, reason: collision with root package name */
    private HashMap<Integer, ArrayList<d>> f904a = new HashMap<>();

    static {
        HashMap<String, Constructor<? extends d>> hashMap = new HashMap<>();
        f903b = hashMap;
        try {
            hashMap.put("KeyAttribute", e.class.getConstructor(new Class[0]));
            f903b.put("KeyPosition", h.class.getConstructor(new Class[0]));
            f903b.put("KeyCycle", f.class.getConstructor(new Class[0]));
            f903b.put("KeyTimeCycle", j.class.getConstructor(new Class[0]));
            f903b.put("KeyTrigger", k.class.getConstructor(new Class[0]));
        } catch (NoSuchMethodException e) {
            Log.e("KeyFrames", "unable to load", e);
        }
    }

    public g() {
    }

    public g(Context context, XmlPullParser xmlPullParser) {
        Exception e;
        d dVar;
        Constructor<? extends d> constructor;
        try {
            int eventType = xmlPullParser.getEventType();
            d dVar2 = null;
            while (eventType != 1) {
                if (eventType == 2) {
                    String name = xmlPullParser.getName();
                    if (f903b.containsKey(name)) {
                        try {
                            constructor = f903b.get(name);
                        } catch (Exception e2) {
                            d dVar3 = dVar2;
                            e = e2;
                            dVar = dVar3;
                        }
                        if (constructor == null) {
                            throw new NullPointerException("Keymaker for " + name + " not found");
                        }
                        dVar = constructor.newInstance(new Object[0]);
                        try {
                            dVar.e(context, Xml.asAttributeSet(xmlPullParser));
                            c(dVar);
                        } catch (Exception e3) {
                            e = e3;
                            Log.e("KeyFrames", "unable to create ", e);
                            dVar2 = dVar;
                            eventType = xmlPullParser.next();
                        }
                        dVar2 = dVar;
                    } else if (name.equalsIgnoreCase("CustomAttribute")) {
                        if (dVar2 != null && (r0 = dVar2.e) != null) {
                            androidx.constraintlayout.widget.a.h(context, xmlPullParser, r0);
                        }
                    } else if (name.equalsIgnoreCase("CustomMethod")) {
                        if (dVar2 != null) {
                            HashMap<String, androidx.constraintlayout.widget.a> hashMap = dVar2.e;
                            if (hashMap == null) {
                            }
                            androidx.constraintlayout.widget.a.h(context, xmlPullParser, hashMap);
                        }
                    }
                } else if (eventType == 3 && "KeyFrameSet".equals(xmlPullParser.getName())) {
                    return;
                }
                eventType = xmlPullParser.next();
            }
        } catch (IOException e4) {
            e4.printStackTrace();
        } catch (XmlPullParserException e5) {
            e5.printStackTrace();
        }
    }

    public void a(n nVar) {
        ArrayList<d> arrayList = this.f904a.get(-1);
        if (arrayList != null) {
            nVar.b(arrayList);
        }
    }

    public void b(n nVar) {
        ArrayList<d> arrayList = this.f904a.get(Integer.valueOf(nVar.f915c));
        if (arrayList != null) {
            nVar.b(arrayList);
        }
        ArrayList<d> arrayList2 = this.f904a.get(-1);
        if (arrayList2 != null) {
            Iterator<d> it = arrayList2.iterator();
            while (it.hasNext()) {
                d next = it.next();
                String str = ((ConstraintLayout.a) nVar.f914b.getLayoutParams()).Y;
                String str2 = next.f899c;
                if ((str2 == null || str == null) ? false : str.matches(str2)) {
                    nVar.a(next);
                }
            }
        }
    }

    public void c(d dVar) {
        if (!this.f904a.containsKey(Integer.valueOf(dVar.f898b))) {
            this.f904a.put(Integer.valueOf(dVar.f898b), new ArrayList<>());
        }
        ArrayList<d> arrayList = this.f904a.get(Integer.valueOf(dVar.f898b));
        if (arrayList != null) {
            arrayList.add(dVar);
        }
    }

    public ArrayList<d> d(int i) {
        return this.f904a.get(Integer.valueOf(i));
    }
}
