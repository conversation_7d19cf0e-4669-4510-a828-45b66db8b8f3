package androidx.constraintlayout.motion.widget;

import java.util.HashSet;

/* loaded from: classes.dex */
abstract class i extends d {
    int f = -1;

    i() {
    }

    @Override // androidx.constraintlayout.motion.widget.d
    public /* bridge */ /* synthetic */ Object clone() {
        return clone();
    }

    @Override // androidx.constraintlayout.motion.widget.d
    void d(HashSet<String> hashSet) {
    }
}
