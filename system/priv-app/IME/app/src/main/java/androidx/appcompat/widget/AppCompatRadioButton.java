package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.widget.RadioButton;
import org.libpag.R;

/* loaded from: classes.dex */
public class AppCompatRadioButton extends RadioButton {
    private final C0100e mBackgroundTintHelper;
    private final C0101f mCompoundButtonHelper;
    private final m mTextHelper;

    public AppCompatRadioButton(Context context) {
        this(context, null);
    }

    public AppCompatRadioButton(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, R.attr.radioButtonStyle);
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public AppCompatRadioButton(Context context, AttributeSet attributeSet, int i) {
        super(context, attributeSet, i);
        D.a(context);
        B.a(this, getContext());
        C0101f c0101f = new C0101f(this);
        this.mCompoundButtonHelper = c0101f;
        c0101f.d(attributeSet, i);
        C0100e c0100e = new C0100e(this);
        this.mBackgroundTintHelper = c0100e;
        c0100e.d(attributeSet, i);
        m mVar = new m(this);
        this.mTextHelper = mVar;
        mVar.f(attributeSet, i);
    }

    @Override // android.widget.CompoundButton, android.widget.TextView, android.view.View
    protected void drawableStateChanged() {
        super.drawableStateChanged();
        C0100e c0100e = this.mBackgroundTintHelper;
        if (c0100e != null) {
            c0100e.a();
        }
        m mVar = this.mTextHelper;
        if (mVar != null) {
            mVar.b();
        }
    }

    @Override // android.widget.CompoundButton, android.widget.TextView
    public int getCompoundPaddingLeft() {
        int compoundPaddingLeft = super.getCompoundPaddingLeft();
        C0101f c0101f = this.mCompoundButtonHelper;
        return compoundPaddingLeft;
    }

    public ColorStateList getSupportBackgroundTintList() {
        C0100e c0100e = this.mBackgroundTintHelper;
        if (c0100e != null) {
            return c0100e.b();
        }
        return null;
    }

    public PorterDuff.Mode getSupportBackgroundTintMode() {
        C0100e c0100e = this.mBackgroundTintHelper;
        if (c0100e != null) {
            return c0100e.c();
        }
        return null;
    }

    public ColorStateList getSupportButtonTintList() {
        C0101f c0101f = this.mCompoundButtonHelper;
        if (c0101f != null) {
            return c0101f.b();
        }
        return null;
    }

    public PorterDuff.Mode getSupportButtonTintMode() {
        C0101f c0101f = this.mCompoundButtonHelper;
        if (c0101f != null) {
            return c0101f.c();
        }
        return null;
    }

    @Override // android.view.View
    public void setBackgroundDrawable(Drawable drawable) {
        super.setBackgroundDrawable(drawable);
        C0100e c0100e = this.mBackgroundTintHelper;
        if (c0100e != null) {
            c0100e.e();
        }
    }

    @Override // android.view.View
    public void setBackgroundResource(int i) {
        super.setBackgroundResource(i);
        C0100e c0100e = this.mBackgroundTintHelper;
        if (c0100e != null) {
            c0100e.f(i);
        }
    }

    @Override // android.widget.CompoundButton
    public void setButtonDrawable(int i) {
        setButtonDrawable(a.b.c.a.a.a(getContext(), i));
    }

    @Override // android.widget.CompoundButton
    public void setButtonDrawable(Drawable drawable) {
        super.setButtonDrawable(drawable);
        C0101f c0101f = this.mCompoundButtonHelper;
        if (c0101f != null) {
            c0101f.e();
        }
    }

    public void setSupportBackgroundTintList(ColorStateList colorStateList) {
        C0100e c0100e = this.mBackgroundTintHelper;
        if (c0100e != null) {
            c0100e.h(colorStateList);
        }
    }

    public void setSupportBackgroundTintMode(PorterDuff.Mode mode) {
        C0100e c0100e = this.mBackgroundTintHelper;
        if (c0100e != null) {
            c0100e.i(mode);
        }
    }

    public void setSupportButtonTintList(ColorStateList colorStateList) {
        C0101f c0101f = this.mCompoundButtonHelper;
        if (c0101f != null) {
            c0101f.f(colorStateList);
        }
    }

    public void setSupportButtonTintMode(PorterDuff.Mode mode) {
        C0101f c0101f = this.mCompoundButtonHelper;
        if (c0101f != null) {
            c0101f.g(mode);
        }
    }
}
