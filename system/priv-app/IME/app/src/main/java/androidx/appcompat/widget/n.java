package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.widget.TextView;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.concurrent.ConcurrentHashMap;

/* loaded from: classes.dex */
class n {
    private static final RectF k = new RectF();
    private static ConcurrentHashMap<String, Method> l = new ConcurrentHashMap<>();
    private static ConcurrentHashMap<String, Field> m = new ConcurrentHashMap<>();
    private final TextView h;
    private final Context i;

    /* renamed from: a, reason: collision with root package name */
    private int f822a = 0;

    /* renamed from: b, reason: collision with root package name */
    private boolean f823b = false;

    /* renamed from: c, reason: collision with root package name */
    private float f824c = -1.0f;

    /* renamed from: d, reason: collision with root package name */
    private float f825d = -1.0f;
    private float e = -1.0f;
    private int[] f = new int[0];
    private boolean g = false;
    private final c j = new b();

    private static class a extends c {
        a() {
        }
    }

    private static class b extends a {
        b() {
        }
    }

    private static class c {
        c() {
        }
    }

    n(TextView textView) {
        this.h = textView;
        this.i = textView.getContext();
    }

    private int[] a(int[] iArr) {
        int length = iArr.length;
        if (length == 0) {
            return iArr;
        }
        Arrays.sort(iArr);
        ArrayList arrayList = new ArrayList();
        for (int i : iArr) {
            if (i > 0 && Collections.binarySearch(arrayList, Integer.valueOf(i)) < 0) {
                arrayList.add(Integer.valueOf(i));
            }
        }
        if (length == arrayList.size()) {
            return iArr;
        }
        int size = arrayList.size();
        int[] iArr2 = new int[size];
        for (int i2 = 0; i2 < size; i2++) {
            iArr2[i2] = ((Integer) arrayList.get(i2)).intValue();
        }
        return iArr2;
    }

    private boolean h() {
        if (j() && this.f822a == 1) {
            if (!this.g || this.f.length == 0) {
                int floor = ((int) Math.floor((this.e - this.f825d) / this.f824c)) + 1;
                int[] iArr = new int[floor];
                for (int i = 0; i < floor; i++) {
                    iArr[i] = Math.round((i * this.f824c) + this.f825d);
                }
                this.f = a(iArr);
            }
            this.f823b = true;
        } else {
            this.f823b = false;
        }
        return this.f823b;
    }

    private boolean i() {
        boolean z = this.f.length > 0;
        this.g = z;
        if (z) {
            this.f822a = 1;
            this.f825d = r0[0];
            this.e = r0[r1 - 1];
            this.f824c = -1.0f;
        }
        return z;
    }

    private boolean j() {
        return !(this.h instanceof AppCompatEditText);
    }

    private void k(float f, float f2, float f3) {
        if (f <= 0.0f) {
            throw new IllegalArgumentException("Minimum auto-size text size (" + f + "px) is less or equal to (0px)");
        }
        if (f2 <= f) {
            throw new IllegalArgumentException("Maximum auto-size text size (" + f2 + "px) is less or equal to minimum auto-size text size (" + f + "px)");
        }
        if (f3 <= 0.0f) {
            throw new IllegalArgumentException("The auto-size step granularity (" + f3 + "px) is less or equal to (0px)");
        }
        this.f822a = 1;
        this.f825d = f;
        this.e = f2;
        this.f824c = f3;
        this.g = false;
    }

    int b() {
        return Math.round(this.e);
    }

    int c() {
        return Math.round(this.f825d);
    }

    int d() {
        return Math.round(this.f824c);
    }

    int[] e() {
        return this.f;
    }

    int f() {
        return this.f822a;
    }

    void g(AttributeSet attributeSet, int i) {
        int resourceId;
        Context context = this.i;
        int[] iArr = a.b.b.j;
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, iArr, i, 0);
        TextView textView = this.h;
        Context context2 = textView.getContext();
        int i2 = a.h.h.q.e;
        textView.saveAttributeDataForStyleable(context2, iArr, attributeSet, obtainStyledAttributes, i, 0);
        if (obtainStyledAttributes.hasValue(5)) {
            this.f822a = obtainStyledAttributes.getInt(5, 0);
        }
        float dimension = obtainStyledAttributes.hasValue(4) ? obtainStyledAttributes.getDimension(4, -1.0f) : -1.0f;
        float dimension2 = obtainStyledAttributes.hasValue(2) ? obtainStyledAttributes.getDimension(2, -1.0f) : -1.0f;
        float dimension3 = obtainStyledAttributes.hasValue(1) ? obtainStyledAttributes.getDimension(1, -1.0f) : -1.0f;
        if (obtainStyledAttributes.hasValue(3) && (resourceId = obtainStyledAttributes.getResourceId(3, 0)) > 0) {
            TypedArray obtainTypedArray = obtainStyledAttributes.getResources().obtainTypedArray(resourceId);
            int length = obtainTypedArray.length();
            int[] iArr2 = new int[length];
            if (length > 0) {
                for (int i3 = 0; i3 < length; i3++) {
                    iArr2[i3] = obtainTypedArray.getDimensionPixelSize(i3, -1);
                }
                this.f = a(iArr2);
                i();
            }
            obtainTypedArray.recycle();
        }
        obtainStyledAttributes.recycle();
        if (!j()) {
            this.f822a = 0;
            return;
        }
        if (this.f822a == 1) {
            if (!this.g) {
                DisplayMetrics displayMetrics = this.i.getResources().getDisplayMetrics();
                if (dimension2 == -1.0f) {
                    dimension2 = TypedValue.applyDimension(2, 12.0f, displayMetrics);
                }
                if (dimension3 == -1.0f) {
                    dimension3 = TypedValue.applyDimension(2, 112.0f, displayMetrics);
                }
                if (dimension == -1.0f) {
                    dimension = 1.0f;
                }
                k(dimension2, dimension3, dimension);
            }
            h();
        }
    }
}
