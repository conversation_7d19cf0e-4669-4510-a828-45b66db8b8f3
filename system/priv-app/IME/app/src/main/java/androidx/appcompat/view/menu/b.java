package androidx.appcompat.view.menu;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.appcompat.view.menu.m;
import androidx.appcompat.view.menu.n;
import java.util.ArrayList;

/* loaded from: classes.dex */
public abstract class b implements m {

    /* renamed from: a, reason: collision with root package name */
    protected Context f626a;

    /* renamed from: b, reason: collision with root package name */
    protected Context f627b;

    /* renamed from: c, reason: collision with root package name */
    protected g f628c;

    /* renamed from: d, reason: collision with root package name */
    protected LayoutInflater f629d;
    private m.a e;
    private int f;
    private int g;
    protected n h;
    private int i;

    public b(Context context, int i, int i2) {
        this.f626a = context;
        this.f629d = LayoutInflater.from(context);
        this.f = i;
        this.g = i2;
    }

    @Override // androidx.appcompat.view.menu.m
    public void a(g gVar, boolean z) {
        m.a aVar = this.e;
        if (aVar != null) {
            aVar.a(gVar, z);
        }
    }

    @Override // androidx.appcompat.view.menu.m
    public int c() {
        return this.i;
    }

    public abstract void f(i iVar, n.a aVar);

    @Override // androidx.appcompat.view.menu.m
    public void g(Context context, g gVar) {
        this.f627b = context;
        LayoutInflater.from(context);
        this.f628c = gVar;
    }

    @Override // androidx.appcompat.view.menu.m
    public boolean i(g gVar, i iVar) {
        return false;
    }

    @Override // androidx.appcompat.view.menu.m
    public boolean j(g gVar, i iVar) {
        return false;
    }

    protected abstract boolean k(ViewGroup viewGroup, int i);

    @Override // androidx.appcompat.view.menu.m
    public void l(m.a aVar) {
        this.e = aVar;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r2v2, types: [androidx.appcompat.view.menu.g] */
    @Override // androidx.appcompat.view.menu.m
    public boolean m(r rVar) {
        m.a aVar = this.e;
        r rVar2 = rVar;
        if (aVar == null) {
            return false;
        }
        if (rVar == null) {
            rVar2 = this.f628c;
        }
        return aVar.b(rVar2);
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // androidx.appcompat.view.menu.m
    public void n(boolean z) {
        ViewGroup viewGroup = (ViewGroup) this.h;
        if (viewGroup == null) {
            return;
        }
        g gVar = this.f628c;
        int i = 0;
        if (gVar != null) {
            gVar.k();
            ArrayList<i> r = this.f628c.r();
            int size = r.size();
            int i2 = 0;
            for (int i3 = 0; i3 < size; i3++) {
                i iVar = r.get(i3);
                if (s(i2, iVar)) {
                    View childAt = viewGroup.getChildAt(i2);
                    i itemData = childAt instanceof n.a ? ((n.a) childAt).getItemData() : null;
                    View p = p(iVar, childAt, viewGroup);
                    if (iVar != itemData) {
                        p.setPressed(false);
                        p.jumpDrawablesToCurrentState();
                    }
                    if (p != childAt) {
                        ViewGroup viewGroup2 = (ViewGroup) p.getParent();
                        if (viewGroup2 != null) {
                            viewGroup2.removeView(p);
                        }
                        ((ViewGroup) this.h).addView(p, i2);
                    }
                    i2++;
                }
            }
            i = i2;
        }
        while (i < viewGroup.getChildCount()) {
            if (!k(viewGroup, i)) {
                i++;
            }
        }
    }

    public m.a o() {
        return this.e;
    }

    /* JADX WARN: Multi-variable type inference failed */
    public View p(i iVar, View view, ViewGroup viewGroup) {
        n.a aVar = view instanceof n.a ? (n.a) view : (n.a) this.f629d.inflate(this.g, viewGroup, false);
        f(iVar, aVar);
        return (View) aVar;
    }

    public n q(ViewGroup viewGroup) {
        if (this.h == null) {
            n nVar = (n) this.f629d.inflate(this.f, viewGroup, false);
            this.h = nVar;
            nVar.initialize(this.f628c);
            n(true);
        }
        return this.h;
    }

    public void r(int i) {
        this.i = i;
    }

    public abstract boolean s(int i, i iVar);
}
