package androidx.appcompat.widget;

import android.graphics.Rect;
import android.util.Log;
import android.view.View;
import java.lang.reflect.Method;

/* loaded from: classes.dex */
public class K {

    /* renamed from: a, reason: collision with root package name */
    private static Method f740a;

    /* renamed from: b, reason: collision with root package name */
    public static final /* synthetic */ int f741b = 0;

    static {
        try {
            Method declaredMethod = View.class.getDeclaredMethod("computeFitSystemWindows", Rect.class, Rect.class);
            f740a = declaredMethod;
            if (declaredMethod.isAccessible()) {
                return;
            }
            f740a.setAccessible(true);
        } catch (NoSuchMethodException unused) {
            Log.d("ViewUtils", "Could not find method computeFitSystemWindows. Oh well.");
        }
    }

    public static void a(View view, Rect rect, Rect rect2) {
        Method method = f740a;
        if (method != null) {
            try {
                method.invoke(view, rect, rect2);
            } catch (Exception e) {
                Log.d("ViewUtils", "Could not invoke computeFitSystemWindows", e);
            }
        }
    }

    public static boolean b(View view) {
        int i = a.h.h.q.e;
        return view.getLayoutDirection() == 1;
    }
}
