package androidx.appcompat.view.menu;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Parcelable;
import android.util.SparseArray;
import android.view.KeyCharacterMap;
import android.view.KeyEvent;
import android.view.MenuItem;
import android.view.SubMenu;
import android.view.View;
import android.view.ViewConfiguration;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/* loaded from: classes.dex */
public class g implements a.h.d.a.a {
    private static final int[] y = {1, 4, 5, 3, 2, 0};

    /* renamed from: a, reason: collision with root package name */
    private final Context f656a;

    /* renamed from: b, reason: collision with root package name */
    private final Resources f657b;

    /* renamed from: c, reason: collision with root package name */
    private boolean f658c;

    /* renamed from: d, reason: collision with root package name */
    private boolean f659d;
    private a e;
    private ArrayList<i> f;
    private ArrayList<i> g;
    private boolean h;
    private ArrayList<i> i;
    private ArrayList<i> j;
    private boolean k;
    CharSequence m;
    Drawable n;
    View o;
    private i v;
    private boolean x;
    private int l = 0;
    private boolean p = false;
    private boolean q = false;
    private boolean r = false;
    private boolean s = false;
    private ArrayList<i> t = new ArrayList<>();
    private CopyOnWriteArrayList<WeakReference<m>> u = new CopyOnWriteArrayList<>();
    private boolean w = false;

    public interface a {
        boolean a(g gVar, MenuItem menuItem);

        void b(g gVar);
    }

    public interface b {
        boolean invokeItem(i iVar);
    }

    public g(Context context) {
        boolean z = false;
        this.f656a = context;
        Resources resources = context.getResources();
        this.f657b = resources;
        this.f = new ArrayList<>();
        this.g = new ArrayList<>();
        this.h = true;
        this.i = new ArrayList<>();
        this.j = new ArrayList<>();
        this.k = true;
        if (resources.getConfiguration().keyboard != 1 && ViewConfiguration.get(context).shouldShowMenuShortcutsWhenKeyboardPresent()) {
            z = true;
        }
        this.f659d = z;
    }

    private void A(int i, boolean z) {
        if (i < 0 || i >= this.f.size()) {
            return;
        }
        this.f.remove(i);
        if (z) {
            x(true);
        }
    }

    private void L(int i, CharSequence charSequence, int i2, Drawable drawable, View view) {
        Resources resources = this.f657b;
        if (view != null) {
            this.o = view;
            this.m = null;
            this.n = null;
        } else {
            if (i > 0) {
                this.m = resources.getText(i);
            } else if (charSequence != null) {
                this.m = charSequence;
            }
            if (i2 > 0) {
                Context context = this.f656a;
                int i3 = a.h.b.a.f265b;
                this.n = context.getDrawable(i2);
            } else if (drawable != null) {
                this.n = drawable;
            }
            this.o = null;
        }
        x(false);
    }

    public void B(m mVar) {
        Iterator<WeakReference<m>> it = this.u.iterator();
        while (it.hasNext()) {
            WeakReference<m> next = it.next();
            m mVar2 = next.get();
            if (mVar2 == null || mVar2 == mVar) {
                this.u.remove(next);
            }
        }
    }

    public void C(Bundle bundle) {
        MenuItem findItem;
        if (bundle == null) {
            return;
        }
        SparseArray<Parcelable> sparseParcelableArray = bundle.getSparseParcelableArray(m());
        int size = size();
        for (int i = 0; i < size; i++) {
            MenuItem item = getItem(i);
            View actionView = item.getActionView();
            if (actionView != null && actionView.getId() != -1) {
                actionView.restoreHierarchyState(sparseParcelableArray);
            }
            if (item.hasSubMenu()) {
                ((r) item.getSubMenu()).C(bundle);
            }
        }
        int i2 = bundle.getInt("android:menu:expandedactionview");
        if (i2 <= 0 || (findItem = findItem(i2)) == null) {
            return;
        }
        findItem.expandActionView();
    }

    public void D(Bundle bundle) {
        Parcelable parcelable;
        SparseArray sparseParcelableArray = bundle.getSparseParcelableArray("android:menu:presenters");
        if (sparseParcelableArray == null || this.u.isEmpty()) {
            return;
        }
        Iterator<WeakReference<m>> it = this.u.iterator();
        while (it.hasNext()) {
            WeakReference<m> next = it.next();
            m mVar = next.get();
            if (mVar == null) {
                this.u.remove(next);
            } else {
                int c2 = mVar.c();
                if (c2 > 0 && (parcelable = (Parcelable) sparseParcelableArray.get(c2)) != null) {
                    mVar.h(parcelable);
                }
            }
        }
    }

    public void E(Bundle bundle) {
        int size = size();
        SparseArray<? extends Parcelable> sparseArray = null;
        for (int i = 0; i < size; i++) {
            MenuItem item = getItem(i);
            View actionView = item.getActionView();
            if (actionView != null && actionView.getId() != -1) {
                if (sparseArray == null) {
                    sparseArray = new SparseArray<>();
                }
                actionView.saveHierarchyState(sparseArray);
                if (item.isActionViewExpanded()) {
                    bundle.putInt("android:menu:expandedactionview", item.getItemId());
                }
            }
            if (item.hasSubMenu()) {
                ((r) item.getSubMenu()).E(bundle);
            }
        }
        if (sparseArray != null) {
            bundle.putSparseParcelableArray(m(), sparseArray);
        }
    }

    public void F(Bundle bundle) {
        Parcelable e;
        if (this.u.isEmpty()) {
            return;
        }
        SparseArray<? extends Parcelable> sparseArray = new SparseArray<>();
        Iterator<WeakReference<m>> it = this.u.iterator();
        while (it.hasNext()) {
            WeakReference<m> next = it.next();
            m mVar = next.get();
            if (mVar == null) {
                this.u.remove(next);
            } else {
                int c2 = mVar.c();
                if (c2 > 0 && (e = mVar.e()) != null) {
                    sparseArray.put(c2, e);
                }
            }
        }
        bundle.putSparseParcelableArray("android:menu:presenters", sparseArray);
    }

    public void G(a aVar) {
        this.e = aVar;
    }

    public g H(int i) {
        this.l = i;
        return this;
    }

    void I(MenuItem menuItem) {
        int groupId = ((i) menuItem).getGroupId();
        int size = this.f.size();
        R();
        for (int i = 0; i < size; i++) {
            i iVar = this.f.get(i);
            if (iVar.getGroupId() == groupId && iVar.l() && iVar.isCheckable()) {
                iVar.p(iVar == menuItem);
            }
        }
        Q();
    }

    protected g J(int i) {
        L(0, null, i, null, null);
        return this;
    }

    protected g K(Drawable drawable) {
        L(0, null, 0, drawable, null);
        return this;
    }

    protected g M(int i) {
        L(i, null, 0, null, null);
        return this;
    }

    protected g N(CharSequence charSequence) {
        L(0, charSequence, 0, null, null);
        return this;
    }

    protected g O(View view) {
        L(0, null, 0, null, view);
        return this;
    }

    public void P(boolean z) {
        this.x = z;
    }

    public void Q() {
        this.p = false;
        if (this.q) {
            this.q = false;
            x(this.r);
        }
    }

    public void R() {
        if (this.p) {
            return;
        }
        this.p = true;
        this.q = false;
        this.r = false;
    }

    protected MenuItem a(int i, int i2, int i3, CharSequence charSequence) {
        int i4;
        int i5 = ((-65536) & i3) >> 16;
        if (i5 >= 0) {
            int[] iArr = y;
            if (i5 < iArr.length) {
                int i6 = (iArr[i5] << 16) | (65535 & i3);
                i iVar = new i(this, i, i2, i3, i6, charSequence, this.l);
                ArrayList<i> arrayList = this.f;
                int size = arrayList.size();
                while (true) {
                    size--;
                    if (size < 0) {
                        i4 = 0;
                        break;
                    }
                    if (arrayList.get(size).e() <= i6) {
                        i4 = size + 1;
                        break;
                    }
                }
                arrayList.add(i4, iVar);
                x(true);
                return iVar;
            }
        }
        throw new IllegalArgumentException("order does not contain a valid category.");
    }

    @Override // android.view.Menu
    public MenuItem add(int i) {
        return a(0, 0, 0, this.f657b.getString(i));
    }

    @Override // android.view.Menu
    public MenuItem add(int i, int i2, int i3, int i4) {
        return a(i, i2, i3, this.f657b.getString(i4));
    }

    @Override // android.view.Menu
    public MenuItem add(int i, int i2, int i3, CharSequence charSequence) {
        return a(i, i2, i3, charSequence);
    }

    @Override // android.view.Menu
    public MenuItem add(CharSequence charSequence) {
        return a(0, 0, 0, charSequence);
    }

    @Override // android.view.Menu
    public int addIntentOptions(int i, int i2, int i3, ComponentName componentName, Intent[] intentArr, Intent intent, int i4, MenuItem[] menuItemArr) {
        int i5;
        PackageManager packageManager = this.f656a.getPackageManager();
        List<ResolveInfo> queryIntentActivityOptions = packageManager.queryIntentActivityOptions(componentName, intentArr, intent, 0);
        int size = queryIntentActivityOptions != null ? queryIntentActivityOptions.size() : 0;
        if ((i4 & 1) == 0) {
            removeGroup(i);
        }
        for (int i6 = 0; i6 < size; i6++) {
            ResolveInfo resolveInfo = queryIntentActivityOptions.get(i6);
            int i7 = resolveInfo.specificIndex;
            Intent intent2 = new Intent(i7 < 0 ? intent : intentArr[i7]);
            ActivityInfo activityInfo = resolveInfo.activityInfo;
            intent2.setComponent(new ComponentName(activityInfo.applicationInfo.packageName, activityInfo.name));
            MenuItem intent3 = a(i, i2, i3, resolveInfo.loadLabel(packageManager)).setIcon(resolveInfo.loadIcon(packageManager)).setIntent(intent2);
            if (menuItemArr != null && (i5 = resolveInfo.specificIndex) >= 0) {
                menuItemArr[i5] = intent3;
            }
        }
        return size;
    }

    @Override // android.view.Menu
    public SubMenu addSubMenu(int i) {
        return addSubMenu(0, 0, 0, this.f657b.getString(i));
    }

    @Override // android.view.Menu
    public SubMenu addSubMenu(int i, int i2, int i3, int i4) {
        return addSubMenu(i, i2, i3, this.f657b.getString(i4));
    }

    @Override // android.view.Menu
    public SubMenu addSubMenu(int i, int i2, int i3, CharSequence charSequence) {
        i iVar = (i) a(i, i2, i3, charSequence);
        r rVar = new r(this.f656a, this, iVar);
        iVar.s(rVar);
        return rVar;
    }

    @Override // android.view.Menu
    public SubMenu addSubMenu(CharSequence charSequence) {
        return addSubMenu(0, 0, 0, charSequence);
    }

    public void b(m mVar) {
        c(mVar, this.f656a);
    }

    public void c(m mVar, Context context) {
        this.u.add(new WeakReference<>(mVar));
        mVar.g(context, this);
        this.k = true;
    }

    @Override // android.view.Menu
    public void clear() {
        i iVar = this.v;
        if (iVar != null) {
            f(iVar);
        }
        this.f.clear();
        x(true);
    }

    public void clearHeader() {
        this.n = null;
        this.m = null;
        this.o = null;
        x(false);
    }

    @Override // android.view.Menu
    public void close() {
        e(true);
    }

    public void d() {
        a aVar = this.e;
        if (aVar != null) {
            aVar.b(this);
        }
    }

    public final void e(boolean z) {
        if (this.s) {
            return;
        }
        this.s = true;
        Iterator<WeakReference<m>> it = this.u.iterator();
        while (it.hasNext()) {
            WeakReference<m> next = it.next();
            m mVar = next.get();
            if (mVar == null) {
                this.u.remove(next);
            } else {
                mVar.a(this, z);
            }
        }
        this.s = false;
    }

    public boolean f(i iVar) {
        boolean z = false;
        if (!this.u.isEmpty() && this.v == iVar) {
            R();
            Iterator<WeakReference<m>> it = this.u.iterator();
            while (it.hasNext()) {
                WeakReference<m> next = it.next();
                m mVar = next.get();
                if (mVar == null) {
                    this.u.remove(next);
                } else {
                    z = mVar.i(this, iVar);
                    if (z) {
                        break;
                    }
                }
            }
            Q();
            if (z) {
                this.v = null;
            }
        }
        return z;
    }

    @Override // android.view.Menu
    public MenuItem findItem(int i) {
        MenuItem findItem;
        int size = size();
        for (int i2 = 0; i2 < size; i2++) {
            i iVar = this.f.get(i2);
            if (iVar.getItemId() == i) {
                return iVar;
            }
            if (iVar.hasSubMenu() && (findItem = ((g) iVar.getSubMenu()).findItem(i)) != null) {
                return findItem;
            }
        }
        return null;
    }

    boolean g(g gVar, MenuItem menuItem) {
        a aVar = this.e;
        return aVar != null && aVar.a(gVar, menuItem);
    }

    @Override // android.view.Menu
    public MenuItem getItem(int i) {
        return this.f.get(i);
    }

    public boolean h(i iVar) {
        boolean z = false;
        if (this.u.isEmpty()) {
            return false;
        }
        R();
        Iterator<WeakReference<m>> it = this.u.iterator();
        while (it.hasNext()) {
            WeakReference<m> next = it.next();
            m mVar = next.get();
            if (mVar == null) {
                this.u.remove(next);
            } else {
                z = mVar.j(this, iVar);
                if (z) {
                    break;
                }
            }
        }
        Q();
        if (z) {
            this.v = iVar;
        }
        return z;
    }

    @Override // android.view.Menu
    public boolean hasVisibleItems() {
        if (this.x) {
            return true;
        }
        int size = size();
        for (int i = 0; i < size; i++) {
            if (this.f.get(i).isVisible()) {
                return true;
            }
        }
        return false;
    }

    i i(int i, KeyEvent keyEvent) {
        ArrayList<i> arrayList = this.t;
        arrayList.clear();
        j(arrayList, i, keyEvent);
        if (arrayList.isEmpty()) {
            return null;
        }
        int metaState = keyEvent.getMetaState();
        KeyCharacterMap.KeyData keyData = new KeyCharacterMap.KeyData();
        keyEvent.getKeyData(keyData);
        int size = arrayList.size();
        if (size == 1) {
            return arrayList.get(0);
        }
        boolean t = t();
        for (int i2 = 0; i2 < size; i2++) {
            i iVar = arrayList.get(i2);
            char alphabeticShortcut = t ? iVar.getAlphabeticShortcut() : iVar.getNumericShortcut();
            char[] cArr = keyData.meta;
            if ((alphabeticShortcut == cArr[0] && (metaState & 2) == 0) || ((alphabeticShortcut == cArr[2] && (metaState & 2) != 0) || (t && alphabeticShortcut == '\b' && i == 67))) {
                return iVar;
            }
        }
        return null;
    }

    @Override // android.view.Menu
    public boolean isShortcutKey(int i, KeyEvent keyEvent) {
        return i(i, keyEvent) != null;
    }

    void j(List<i> list, int i, KeyEvent keyEvent) {
        boolean t = t();
        int modifiers = keyEvent.getModifiers();
        KeyCharacterMap.KeyData keyData = new KeyCharacterMap.KeyData();
        if (keyEvent.getKeyData(keyData) || i == 67) {
            int size = this.f.size();
            for (int i2 = 0; i2 < size; i2++) {
                i iVar = this.f.get(i2);
                if (iVar.hasSubMenu()) {
                    ((g) iVar.getSubMenu()).j(list, i, keyEvent);
                }
                char alphabeticShortcut = t ? iVar.getAlphabeticShortcut() : iVar.getNumericShortcut();
                if (((modifiers & 69647) == ((t ? iVar.getAlphabeticModifiers() : iVar.getNumericModifiers()) & 69647)) && alphabeticShortcut != 0) {
                    char[] cArr = keyData.meta;
                    if ((alphabeticShortcut == cArr[0] || alphabeticShortcut == cArr[2] || (t && alphabeticShortcut == '\b' && i == 67)) && iVar.isEnabled()) {
                        list.add(iVar);
                    }
                }
            }
        }
    }

    public void k() {
        ArrayList<i> r = r();
        if (this.k) {
            Iterator<WeakReference<m>> it = this.u.iterator();
            boolean z = false;
            while (it.hasNext()) {
                WeakReference<m> next = it.next();
                m mVar = next.get();
                if (mVar == null) {
                    this.u.remove(next);
                } else {
                    z |= mVar.d();
                }
            }
            if (z) {
                this.i.clear();
                this.j.clear();
                int size = r.size();
                for (int i = 0; i < size; i++) {
                    i iVar = r.get(i);
                    (iVar.k() ? this.i : this.j).add(iVar);
                }
            } else {
                this.i.clear();
                this.j.clear();
                this.j.addAll(r());
            }
            this.k = false;
        }
    }

    public ArrayList<i> l() {
        k();
        return this.i;
    }

    protected String m() {
        return "android:menu:actionviewstates";
    }

    public Context n() {
        return this.f656a;
    }

    public i o() {
        return this.v;
    }

    public ArrayList<i> p() {
        k();
        return this.j;
    }

    @Override // android.view.Menu
    public boolean performIdentifierAction(int i, int i2) {
        return y(findItem(i), i2);
    }

    @Override // android.view.Menu
    public boolean performShortcut(int i, KeyEvent keyEvent, int i2) {
        i i3 = i(i, keyEvent);
        boolean z = i3 != null ? z(i3, null, i2) : false;
        if ((i2 & 2) != 0) {
            e(true);
        }
        return z;
    }

    public g q() {
        return this;
    }

    public ArrayList<i> r() {
        if (!this.h) {
            return this.g;
        }
        this.g.clear();
        int size = this.f.size();
        for (int i = 0; i < size; i++) {
            i iVar = this.f.get(i);
            if (iVar.isVisible()) {
                this.g.add(iVar);
            }
        }
        this.h = false;
        this.k = true;
        return this.g;
    }

    @Override // android.view.Menu
    public void removeGroup(int i) {
        int size = size();
        int i2 = 0;
        while (true) {
            if (i2 >= size) {
                i2 = -1;
                break;
            } else if (this.f.get(i2).getGroupId() == i) {
                break;
            } else {
                i2++;
            }
        }
        if (i2 >= 0) {
            int size2 = this.f.size() - i2;
            int i3 = 0;
            while (true) {
                int i4 = i3 + 1;
                if (i3 >= size2 || this.f.get(i2).getGroupId() != i) {
                    break;
                }
                A(i2, false);
                i3 = i4;
            }
            x(true);
        }
    }

    @Override // android.view.Menu
    public void removeItem(int i) {
        int size = size();
        int i2 = 0;
        while (true) {
            if (i2 >= size) {
                i2 = -1;
                break;
            } else if (this.f.get(i2).getItemId() == i) {
                break;
            } else {
                i2++;
            }
        }
        A(i2, true);
    }

    public boolean s() {
        return this.w;
    }

    @Override // android.view.Menu
    public void setGroupCheckable(int i, boolean z, boolean z2) {
        int size = this.f.size();
        for (int i2 = 0; i2 < size; i2++) {
            i iVar = this.f.get(i2);
            if (iVar.getGroupId() == i) {
                iVar.q(z2);
                iVar.setCheckable(z);
            }
        }
    }

    @Override // android.view.Menu
    public void setGroupDividerEnabled(boolean z) {
        this.w = z;
    }

    @Override // android.view.Menu
    public void setGroupEnabled(int i, boolean z) {
        int size = this.f.size();
        for (int i2 = 0; i2 < size; i2++) {
            i iVar = this.f.get(i2);
            if (iVar.getGroupId() == i) {
                iVar.setEnabled(z);
            }
        }
    }

    @Override // android.view.Menu
    public void setGroupVisible(int i, boolean z) {
        int size = this.f.size();
        boolean z2 = false;
        for (int i2 = 0; i2 < size; i2++) {
            i iVar = this.f.get(i2);
            if (iVar.getGroupId() == i && iVar.t(z)) {
                z2 = true;
            }
        }
        if (z2) {
            x(true);
        }
    }

    @Override // android.view.Menu
    public void setQwertyMode(boolean z) {
        this.f658c = z;
        x(false);
    }

    @Override // android.view.Menu
    public int size() {
        return this.f.size();
    }

    boolean t() {
        return this.f658c;
    }

    public boolean u() {
        return this.f659d;
    }

    void v() {
        this.k = true;
        x(true);
    }

    void w() {
        this.h = true;
        x(true);
    }

    public void x(boolean z) {
        if (this.p) {
            this.q = true;
            if (z) {
                this.r = true;
                return;
            }
            return;
        }
        if (z) {
            this.h = true;
            this.k = true;
        }
        if (this.u.isEmpty()) {
            return;
        }
        R();
        Iterator<WeakReference<m>> it = this.u.iterator();
        while (it.hasNext()) {
            WeakReference<m> next = it.next();
            m mVar = next.get();
            if (mVar == null) {
                this.u.remove(next);
            } else {
                mVar.n(z);
            }
        }
        Q();
    }

    public boolean y(MenuItem menuItem, int i) {
        return z(menuItem, null, i);
    }

    /* JADX WARN: Code restructure failed: missing block: B:13:0x002c, code lost:
    
        if (r1 != false) goto L17;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x002e, code lost:
    
        e(true);
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x009d, code lost:
    
        return r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x003e, code lost:
    
        if ((r9 & 1) == 0) goto L17;
     */
    /* JADX WARN: Code restructure failed: missing block: B:52:0x009a, code lost:
    
        if (r1 == false) goto L17;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public boolean z(android.view.MenuItem r7, androidx.appcompat.view.menu.m r8, int r9) {
        /*
            r6 = this;
            androidx.appcompat.view.menu.i r7 = (androidx.appcompat.view.menu.i) r7
            r0 = 0
            if (r7 == 0) goto L9e
            boolean r1 = r7.isEnabled()
            if (r1 != 0) goto Ld
            goto L9e
        Ld:
            boolean r1 = r7.j()
            a.h.h.b r2 = r7.b()
            r3 = 1
            if (r2 == 0) goto L20
            boolean r4 = r2.a()
            if (r4 == 0) goto L20
            r4 = r3
            goto L21
        L20:
            r4 = r0
        L21:
            boolean r5 = r7.i()
            if (r5 == 0) goto L33
            boolean r7 = r7.expandActionView()
            r1 = r1 | r7
            if (r1 == 0) goto L9d
        L2e:
            r6.e(r3)
            goto L9d
        L33:
            boolean r5 = r7.hasSubMenu()
            if (r5 != 0) goto L41
            if (r4 == 0) goto L3c
            goto L41
        L3c:
            r7 = r9 & 1
            if (r7 != 0) goto L9d
            goto L2e
        L41:
            r9 = r9 & 4
            if (r9 != 0) goto L48
            r6.e(r0)
        L48:
            boolean r9 = r7.hasSubMenu()
            if (r9 != 0) goto L58
            androidx.appcompat.view.menu.r r9 = new androidx.appcompat.view.menu.r
            android.content.Context r5 = r6.f656a
            r9.<init>(r5, r6, r7)
            r7.s(r9)
        L58:
            android.view.SubMenu r7 = r7.getSubMenu()
            androidx.appcompat.view.menu.r r7 = (androidx.appcompat.view.menu.r) r7
            if (r4 == 0) goto L63
            r2.f(r7)
        L63:
            java.util.concurrent.CopyOnWriteArrayList<java.lang.ref.WeakReference<androidx.appcompat.view.menu.m>> r9 = r6.u
            boolean r9 = r9.isEmpty()
            if (r9 == 0) goto L6c
            goto L99
        L6c:
            if (r8 == 0) goto L72
            boolean r0 = r8.m(r7)
        L72:
            java.util.concurrent.CopyOnWriteArrayList<java.lang.ref.WeakReference<androidx.appcompat.view.menu.m>> r8 = r6.u
            java.util.Iterator r8 = r8.iterator()
        L78:
            boolean r9 = r8.hasNext()
            if (r9 == 0) goto L99
            java.lang.Object r9 = r8.next()
            java.lang.ref.WeakReference r9 = (java.lang.ref.WeakReference) r9
            java.lang.Object r2 = r9.get()
            androidx.appcompat.view.menu.m r2 = (androidx.appcompat.view.menu.m) r2
            if (r2 != 0) goto L92
            java.util.concurrent.CopyOnWriteArrayList<java.lang.ref.WeakReference<androidx.appcompat.view.menu.m>> r2 = r6.u
            r2.remove(r9)
            goto L78
        L92:
            if (r0 != 0) goto L78
            boolean r0 = r2.m(r7)
            goto L78
        L99:
            r1 = r1 | r0
            if (r1 != 0) goto L9d
            goto L2e
        L9d:
            return r1
        L9e:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.view.menu.g.z(android.view.MenuItem, androidx.appcompat.view.menu.m, int):boolean");
    }
}
