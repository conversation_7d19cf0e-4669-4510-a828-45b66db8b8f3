package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.PorterDuff;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.RippleDrawable;
import android.util.AttributeSet;
import android.widget.ImageView;

/* renamed from: androidx.appcompat.widget.h, reason: case insensitive filesystem */
/* loaded from: classes.dex */
public class C0103h {

    /* renamed from: a, reason: collision with root package name */
    private final ImageView f807a;

    /* renamed from: b, reason: collision with root package name */
    private E f808b;

    public C0103h(ImageView imageView) {
        this.f807a = imageView;
    }

    void a() {
        E e;
        Drawable drawable = this.f807a.getDrawable();
        if (drawable != null) {
            Rect rect = q.f828c;
        }
        if (drawable == null || (e = this.f808b) == null) {
            return;
        }
        int[] drawableState = this.f807a.getDrawableState();
        int i = C0102g.f801d;
        x.m(drawable, e, drawableState);
    }

    ColorStateList b() {
        E e = this.f808b;
        if (e != null) {
            return e.f724a;
        }
        return null;
    }

    PorterDuff.Mode c() {
        E e = this.f808b;
        if (e != null) {
            return e.f725b;
        }
        return null;
    }

    boolean d() {
        return !(this.f807a.getBackground() instanceof RippleDrawable);
    }

    public void e(AttributeSet attributeSet, int i) {
        int n;
        Context context = this.f807a.getContext();
        int[] iArr = a.b.b.g;
        G v = G.v(context, attributeSet, iArr, i, 0);
        ImageView imageView = this.f807a;
        Context context2 = imageView.getContext();
        TypedArray r = v.r();
        int i2 = a.h.h.q.e;
        imageView.saveAttributeDataForStyleable(context2, iArr, attributeSet, r, i, 0);
        try {
            Drawable drawable = this.f807a.getDrawable();
            if (drawable == null && (n = v.n(1, -1)) != -1 && (drawable = a.b.c.a.a.a(this.f807a.getContext(), n)) != null) {
                this.f807a.setImageDrawable(drawable);
            }
            if (drawable != null) {
                Rect rect = q.f828c;
            }
            if (v.s(2)) {
                this.f807a.setImageTintList(v.c(2));
            }
            if (v.s(3)) {
                this.f807a.setImageTintMode(q.c(v.k(3, -1), null));
            }
        } finally {
            v.w();
        }
    }

    public void f(int i) {
        if (i != 0) {
            Drawable a2 = a.b.c.a.a.a(this.f807a.getContext(), i);
            if (a2 != null) {
                Rect rect = q.f828c;
            }
            this.f807a.setImageDrawable(a2);
        } else {
            this.f807a.setImageDrawable(null);
        }
        a();
    }

    void g(ColorStateList colorStateList) {
        if (this.f808b == null) {
            this.f808b = new E();
        }
        E e = this.f808b;
        e.f724a = colorStateList;
        e.f727d = true;
        a();
    }

    void h(PorterDuff.Mode mode) {
        if (this.f808b == null) {
            this.f808b = new E();
        }
        E e = this.f808b;
        e.f725b = mode;
        e.f726c = true;
        a();
    }
}
