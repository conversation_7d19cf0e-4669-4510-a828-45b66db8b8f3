package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.util.TypedValue;

/* loaded from: classes.dex */
public class G {

    /* renamed from: a, reason: collision with root package name */
    private final Context f728a;

    /* renamed from: b, reason: collision with root package name */
    private final TypedArray f729b;

    /* renamed from: c, reason: collision with root package name */
    private TypedValue f730c;

    private G(Context context, TypedArray typedArray) {
        this.f728a = context;
        this.f729b = typedArray;
    }

    public static G t(Context context, int i, int[] iArr) {
        return new G(context, context.obtainStyledAttributes(i, iArr));
    }

    public static G u(Context context, AttributeSet attributeSet, int[] iArr) {
        return new G(context, context.obtainStyledAttributes(attributeSet, iArr));
    }

    public static G v(Context context, AttributeSet attributeSet, int[] iArr, int i, int i2) {
        return new G(context, context.obtainStyledAttributes(attributeSet, iArr, i, i2));
    }

    public boolean a(int i, boolean z) {
        return this.f729b.getBoolean(i, z);
    }

    public int b(int i, int i2) {
        return this.f729b.getColor(i, i2);
    }

    public ColorStateList c(int i) {
        int resourceId;
        if (this.f729b.hasValue(i) && (resourceId = this.f729b.getResourceId(i, 0)) != 0) {
            Context context = this.f728a;
            int i2 = a.b.c.a.a.f7d;
            ColorStateList colorStateList = context.getColorStateList(resourceId);
            if (colorStateList != null) {
                return colorStateList;
            }
        }
        return this.f729b.getColorStateList(i);
    }

    public float d(int i, float f) {
        return this.f729b.getDimension(i, f);
    }

    public int e(int i, int i2) {
        return this.f729b.getDimensionPixelOffset(i, i2);
    }

    public int f(int i, int i2) {
        return this.f729b.getDimensionPixelSize(i, i2);
    }

    public Drawable g(int i) {
        int resourceId;
        return (!this.f729b.hasValue(i) || (resourceId = this.f729b.getResourceId(i, 0)) == 0) ? this.f729b.getDrawable(i) : a.b.c.a.a.a(this.f728a, resourceId);
    }

    public Drawable h(int i) {
        int resourceId;
        if (!this.f729b.hasValue(i) || (resourceId = this.f729b.getResourceId(i, 0)) == 0) {
            return null;
        }
        return C0102g.b().d(this.f728a, resourceId, true);
    }

    public float i(int i, float f) {
        return this.f729b.getFloat(i, f);
    }

    public Typeface j(int i, int i2, a.h.b.b.f fVar) {
        int resourceId = this.f729b.getResourceId(i, 0);
        if (resourceId == 0) {
            return null;
        }
        if (this.f730c == null) {
            this.f730c = new TypedValue();
        }
        return a.h.b.b.a.c(this.f728a, resourceId, this.f730c, i2, fVar);
    }

    public int k(int i, int i2) {
        return this.f729b.getInt(i, i2);
    }

    public int l(int i, int i2) {
        return this.f729b.getInteger(i, i2);
    }

    public int m(int i, int i2) {
        return this.f729b.getLayoutDimension(i, i2);
    }

    public int n(int i, int i2) {
        return this.f729b.getResourceId(i, i2);
    }

    public String o(int i) {
        return this.f729b.getString(i);
    }

    public CharSequence p(int i) {
        return this.f729b.getText(i);
    }

    public CharSequence[] q(int i) {
        return this.f729b.getTextArray(i);
    }

    public TypedArray r() {
        return this.f729b;
    }

    public boolean s(int i) {
        return this.f729b.hasValue(i);
    }

    public void w() {
        this.f729b.recycle();
    }
}
