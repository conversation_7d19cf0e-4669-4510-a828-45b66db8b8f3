package androidx.constraintlayout.motion.widget;

import android.view.animation.Interpolator;

/* loaded from: classes.dex */
class m implements Interpolator {

    /* renamed from: a, reason: collision with root package name */
    final /* synthetic */ a.f.a.i.a.c f912a;

    m(a.f.a.i.a.c cVar) {
        this.f912a = cVar;
    }

    @Override // android.animation.TimeInterpolator
    public float getInterpolation(float f) {
        return (float) this.f912a.a(f);
    }
}
