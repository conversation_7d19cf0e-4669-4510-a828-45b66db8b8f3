package androidx.appcompat.widget;

import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.widget.CompoundButton;

/* renamed from: androidx.appcompat.widget.f, reason: case insensitive filesystem */
/* loaded from: classes.dex */
class C0101f {

    /* renamed from: a, reason: collision with root package name */
    private final CompoundButton f795a;

    /* renamed from: b, reason: collision with root package name */
    private ColorStateList f796b = null;

    /* renamed from: c, reason: collision with root package name */
    private PorterDuff.Mode f797c = null;

    /* renamed from: d, reason: collision with root package name */
    private boolean f798d = false;
    private boolean e = false;
    private boolean f;

    C0101f(CompoundButton compoundButton) {
        this.f795a = compoundButton;
    }

    void a() {
        Drawable buttonDrawable = this.f795a.getButtonDrawable();
        if (buttonDrawable != null) {
            if (this.f798d || this.e) {
                Drawable mutate = buttonDrawable.mutate();
                if (this.f798d) {
                    mutate.setTintList(this.f796b);
                }
                if (this.e) {
                    mutate.setTintMode(this.f797c);
                }
                if (mutate.isStateful()) {
                    mutate.setState(this.f795a.getDrawableState());
                }
                this.f795a.setButtonDrawable(mutate);
            }
        }
    }

    ColorStateList b() {
        return this.f796b;
    }

    PorterDuff.Mode c() {
        return this.f797c;
    }

    /* JADX WARN: Removed duplicated region for block: B:17:0x005d A[Catch: all -> 0x0080, TryCatch #0 {all -> 0x0080, blocks: (B:3:0x0020, B:5:0x0026, B:8:0x002c, B:10:0x003d, B:12:0x0043, B:14:0x0049, B:15:0x0056, B:17:0x005d, B:18:0x0066, B:20:0x006d), top: B:2:0x0020 }] */
    /* JADX WARN: Removed duplicated region for block: B:20:0x006d A[Catch: all -> 0x0080, TRY_LEAVE, TryCatch #0 {all -> 0x0080, blocks: (B:3:0x0020, B:5:0x0026, B:8:0x002c, B:10:0x003d, B:12:0x0043, B:14:0x0049, B:15:0x0056, B:17:0x005d, B:18:0x0066, B:20:0x006d), top: B:2:0x0020 }] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    void d(android.util.AttributeSet r10, int r11) {
        /*
            r9 = this;
            android.widget.CompoundButton r0 = r9.f795a
            android.content.Context r0 = r0.getContext()
            int[] r3 = a.b.b.m
            r8 = 0
            androidx.appcompat.widget.G r0 = androidx.appcompat.widget.G.v(r0, r10, r3, r11, r8)
            android.widget.CompoundButton r1 = r9.f795a
            android.content.Context r2 = r1.getContext()
            android.content.res.TypedArray r5 = r0.r()
            int r4 = a.h.h.q.e
            r7 = 0
            r4 = r10
            r6 = r11
            r1.saveAttributeDataForStyleable(r2, r3, r4, r5, r6, r7)
            r10 = 1
            boolean r11 = r0.s(r10)     // Catch: java.lang.Throwable -> L80
            if (r11 == 0) goto L3a
            int r11 = r0.n(r10, r8)     // Catch: java.lang.Throwable -> L80
            if (r11 == 0) goto L3a
            android.widget.CompoundButton r1 = r9.f795a     // Catch: android.content.res.Resources.NotFoundException -> L3a java.lang.Throwable -> L80
            android.content.Context r2 = r1.getContext()     // Catch: android.content.res.Resources.NotFoundException -> L3a java.lang.Throwable -> L80
            android.graphics.drawable.Drawable r11 = a.b.c.a.a.a(r2, r11)     // Catch: android.content.res.Resources.NotFoundException -> L3a java.lang.Throwable -> L80
            r1.setButtonDrawable(r11)     // Catch: android.content.res.Resources.NotFoundException -> L3a java.lang.Throwable -> L80
            goto L3b
        L3a:
            r10 = r8
        L3b:
            if (r10 != 0) goto L56
            boolean r10 = r0.s(r8)     // Catch: java.lang.Throwable -> L80
            if (r10 == 0) goto L56
            int r10 = r0.n(r8, r8)     // Catch: java.lang.Throwable -> L80
            if (r10 == 0) goto L56
            android.widget.CompoundButton r11 = r9.f795a     // Catch: java.lang.Throwable -> L80
            android.content.Context r1 = r11.getContext()     // Catch: java.lang.Throwable -> L80
            android.graphics.drawable.Drawable r10 = a.b.c.a.a.a(r1, r10)     // Catch: java.lang.Throwable -> L80
            r11.setButtonDrawable(r10)     // Catch: java.lang.Throwable -> L80
        L56:
            r10 = 2
            boolean r11 = r0.s(r10)     // Catch: java.lang.Throwable -> L80
            if (r11 == 0) goto L66
            android.widget.CompoundButton r11 = r9.f795a     // Catch: java.lang.Throwable -> L80
            android.content.res.ColorStateList r10 = r0.c(r10)     // Catch: java.lang.Throwable -> L80
            r11.setButtonTintList(r10)     // Catch: java.lang.Throwable -> L80
        L66:
            r10 = 3
            boolean r11 = r0.s(r10)     // Catch: java.lang.Throwable -> L80
            if (r11 == 0) goto L7c
            android.widget.CompoundButton r9 = r9.f795a     // Catch: java.lang.Throwable -> L80
            r11 = -1
            int r10 = r0.k(r10, r11)     // Catch: java.lang.Throwable -> L80
            r11 = 0
            android.graphics.PorterDuff$Mode r10 = androidx.appcompat.widget.q.c(r10, r11)     // Catch: java.lang.Throwable -> L80
            r9.setButtonTintMode(r10)     // Catch: java.lang.Throwable -> L80
        L7c:
            r0.w()
            return
        L80:
            r9 = move-exception
            r0.w()
            throw r9
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.C0101f.d(android.util.AttributeSet, int):void");
    }

    void e() {
        if (this.f) {
            this.f = false;
        } else {
            this.f = true;
            a();
        }
    }

    void f(ColorStateList colorStateList) {
        this.f796b = colorStateList;
        this.f798d = true;
        a();
    }

    void g(PorterDuff.Mode mode) {
        this.f797c = mode;
        this.e = true;
        a();
    }
}
