package androidx.cardview.widget;

import android.content.Context;
import android.content.res.ColorStateList;

/* loaded from: classes.dex */
interface c {
    float a(b bVar);

    float b(b bVar);

    float c(b bVar);

    float d(b bVar);

    void e(b bVar);

    ColorStateList f(b bVar);

    void g(b bVar, float f);

    void h(b bVar, Context context, ColorStateList colorStateList, float f, float f2, float f3);

    void i(b bVar, float f);

    void j(b bVar);

    void k(b bVar, ColorStateList colorStateList);

    float l(b bVar);

    void m(b bVar, float f);

    void n(b bVar);
}
