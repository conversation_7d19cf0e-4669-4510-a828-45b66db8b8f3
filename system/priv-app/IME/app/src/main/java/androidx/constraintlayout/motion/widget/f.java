package androidx.constraintlayout.motion.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseIntArray;
import com.google.android.material.button.MaterialButton;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;

/* loaded from: classes.dex */
public class f extends d {
    private String f = null;
    private int g = 0;
    private int h = -1;
    private String i = null;
    private float j = Float.NaN;
    private float k = 0.0f;
    private float l = 0.0f;
    private float m = Float.NaN;
    private int n = -1;
    private float o = Float.NaN;
    private float p = Float.NaN;
    private float q = Float.NaN;
    private float r = Float.NaN;
    private float s = Float.NaN;
    private float t = Float.NaN;
    private float u = Float.NaN;
    private float v = Float.NaN;
    private float w = Float.NaN;
    private float x = Float.NaN;
    private float y = Float.NaN;

    private static class a {

        /* renamed from: a, reason: collision with root package name */
        private static SparseIntArray f902a;

        static {
            SparseIntArray sparseIntArray = new SparseIntArray();
            f902a = sparseIntArray;
            sparseIntArray.append(13, 1);
            f902a.append(11, 2);
            f902a.append(14, 3);
            f902a.append(10, 4);
            f902a.append(19, 5);
            f902a.append(17, 6);
            f902a.append(16, 7);
            f902a.append(20, 8);
            f902a.append(0, 9);
            f902a.append(9, 10);
            f902a.append(5, 11);
            f902a.append(6, 12);
            f902a.append(7, 13);
            f902a.append(15, 14);
            f902a.append(3, 15);
            f902a.append(4, 16);
            f902a.append(1, 17);
            f902a.append(2, 18);
            f902a.append(8, 19);
            f902a.append(12, 20);
            f902a.append(18, 21);
        }

        static void a(f fVar, TypedArray typedArray) {
            int i;
            int indexCount = typedArray.getIndexCount();
            for (int i2 = 0; i2 < indexCount; i2++) {
                int index = typedArray.getIndex(i2);
                switch (f902a.get(index)) {
                    case 1:
                        if (MotionLayout.IS_IN_EDIT_MODE) {
                            int resourceId = typedArray.getResourceId(index, fVar.f898b);
                            fVar.f898b = resourceId;
                            if (resourceId != -1) {
                                break;
                            }
                            fVar.f899c = typedArray.getString(index);
                            break;
                        } else {
                            if (typedArray.peekValue(index).type != 3) {
                                fVar.f898b = typedArray.getResourceId(index, fVar.f898b);
                                break;
                            }
                            fVar.f899c = typedArray.getString(index);
                        }
                    case 2:
                        fVar.f897a = typedArray.getInt(index, fVar.f897a);
                        break;
                    case 3:
                        fVar.f = typedArray.getString(index);
                        break;
                    case 4:
                        fVar.g = typedArray.getInteger(index, fVar.g);
                        break;
                    case 5:
                        if (typedArray.peekValue(index).type == 3) {
                            fVar.i = typedArray.getString(index);
                            i = 7;
                        } else {
                            i = typedArray.getInt(index, fVar.h);
                        }
                        fVar.h = i;
                        break;
                    case 6:
                        fVar.j = typedArray.getFloat(index, fVar.j);
                        break;
                    case MotionLayout.TOUCH_UP_NEVER_TO_END /* 7 */:
                        fVar.k = typedArray.peekValue(index).type == 5 ? typedArray.getDimension(index, fVar.k) : typedArray.getFloat(index, fVar.k);
                        break;
                    case 8:
                        fVar.n = typedArray.getInt(index, fVar.n);
                        break;
                    case 9:
                        fVar.o = typedArray.getFloat(index, fVar.o);
                        break;
                    case 10:
                        fVar.p = typedArray.getDimension(index, fVar.p);
                        break;
                    case 11:
                        fVar.q = typedArray.getFloat(index, fVar.q);
                        break;
                    case 12:
                        fVar.s = typedArray.getFloat(index, fVar.s);
                        break;
                    case 13:
                        fVar.t = typedArray.getFloat(index, fVar.t);
                        break;
                    case 14:
                        fVar.r = typedArray.getFloat(index, fVar.r);
                        break;
                    case 15:
                        fVar.u = typedArray.getFloat(index, fVar.u);
                        break;
                    case MaterialButton.ICON_GRAVITY_TOP /* 16 */:
                        fVar.v = typedArray.getFloat(index, fVar.v);
                        break;
                    case 17:
                        fVar.w = typedArray.getDimension(index, fVar.w);
                        break;
                    case 18:
                        fVar.x = typedArray.getDimension(index, fVar.x);
                        break;
                    case 19:
                        fVar.y = typedArray.getDimension(index, fVar.y);
                        break;
                    case 20:
                        fVar.m = typedArray.getFloat(index, fVar.m);
                        break;
                    case 21:
                        fVar.l = typedArray.getFloat(index, fVar.l) / 360.0f;
                        break;
                    default:
                        StringBuilder j = b.b.a.a.a.j("unused attribute 0x");
                        j.append(Integer.toHexString(index));
                        j.append("   ");
                        j.append(f902a.get(index));
                        Log.e("KeyCycle", j.toString());
                        break;
                }
            }
        }
    }

    public f() {
        this.f900d = 4;
        this.e = new HashMap<>();
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Code restructure failed: missing block: B:59:0x00b3, code lost:
    
        if (r1.equals("scaleY") == false) goto L18;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void V(java.util.HashMap<java.lang.String, a.f.b.a.c> r14) {
        /*
            Method dump skipped, instructions count: 468
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.motion.widget.f.V(java.util.HashMap):void");
    }

    @Override // androidx.constraintlayout.motion.widget.d
    public void a(HashMap<String, a.f.b.a.d> hashMap) {
        int i;
        float f;
        StringBuilder j = b.b.a.a.a.j("add ");
        j.append(hashMap.size());
        j.append(" values");
        String sb = j.toString();
        StackTraceElement[] stackTrace = new Throwable().getStackTrace();
        int min = Math.min(2, stackTrace.length - 1);
        String str = " ";
        for (int i2 = 1; i2 <= min; i2++) {
            StackTraceElement stackTraceElement = stackTrace[i2];
            StringBuilder j2 = b.b.a.a.a.j(".(");
            j2.append(stackTrace[i2].getFileName());
            j2.append(":");
            j2.append(stackTrace[i2].getLineNumber());
            j2.append(") ");
            j2.append(stackTrace[i2].getMethodName());
            String sb2 = j2.toString();
            str = b.b.a.a.a.g(str, " ");
            Log.v("KeyCycle", sb + str + sb2 + str);
        }
        for (String str2 : hashMap.keySet()) {
            a.f.b.a.d dVar = hashMap.get(str2);
            if (dVar != null) {
                str2.hashCode();
                str2.hashCode();
                switch (str2) {
                    case "rotationX":
                        i = this.f897a;
                        f = this.s;
                        break;
                    case "rotationY":
                        i = this.f897a;
                        f = this.t;
                        break;
                    case "translationX":
                        i = this.f897a;
                        f = this.w;
                        break;
                    case "translationY":
                        i = this.f897a;
                        f = this.x;
                        break;
                    case "translationZ":
                        i = this.f897a;
                        f = this.y;
                        break;
                    case "progress":
                        i = this.f897a;
                        f = this.m;
                        break;
                    case "scaleX":
                        i = this.f897a;
                        f = this.u;
                        break;
                    case "scaleY":
                        i = this.f897a;
                        f = this.v;
                        break;
                    case "rotation":
                        i = this.f897a;
                        f = this.q;
                        break;
                    case "elevation":
                        i = this.f897a;
                        f = this.p;
                        break;
                    case "transitionPathRotate":
                        i = this.f897a;
                        f = this.r;
                        break;
                    case "alpha":
                        i = this.f897a;
                        f = this.o;
                        break;
                    case "waveOffset":
                        i = this.f897a;
                        f = this.k;
                        break;
                    case "wavePhase":
                        i = this.f897a;
                        f = this.l;
                        break;
                    default:
                        if (!str2.startsWith("CUSTOM")) {
                            Log.v("WARNING KeyCycle", "  UNKNOWN  " + str2);
                            break;
                        } else {
                            continue;
                        }
                }
                dVar.b(i, f);
            }
        }
    }

    @Override // androidx.constraintlayout.motion.widget.d
    /* renamed from: b */
    public d clone() {
        f fVar = new f();
        super.c(this);
        fVar.f = this.f;
        fVar.g = this.g;
        fVar.h = this.h;
        fVar.i = this.i;
        fVar.j = this.j;
        fVar.k = this.k;
        fVar.l = this.l;
        fVar.m = this.m;
        fVar.n = this.n;
        fVar.o = this.o;
        fVar.p = this.p;
        fVar.q = this.q;
        fVar.r = this.r;
        fVar.s = this.s;
        fVar.t = this.t;
        fVar.u = this.u;
        fVar.v = this.v;
        fVar.w = this.w;
        fVar.x = this.x;
        fVar.y = this.y;
        return fVar;
    }

    @Override // androidx.constraintlayout.motion.widget.d
    public void d(HashSet<String> hashSet) {
        if (!Float.isNaN(this.o)) {
            hashSet.add("alpha");
        }
        if (!Float.isNaN(this.p)) {
            hashSet.add("elevation");
        }
        if (!Float.isNaN(this.q)) {
            hashSet.add("rotation");
        }
        if (!Float.isNaN(this.s)) {
            hashSet.add("rotationX");
        }
        if (!Float.isNaN(this.t)) {
            hashSet.add("rotationY");
        }
        if (!Float.isNaN(this.u)) {
            hashSet.add("scaleX");
        }
        if (!Float.isNaN(this.v)) {
            hashSet.add("scaleY");
        }
        if (!Float.isNaN(this.r)) {
            hashSet.add("transitionPathRotate");
        }
        if (!Float.isNaN(this.w)) {
            hashSet.add("translationX");
        }
        if (!Float.isNaN(this.x)) {
            hashSet.add("translationY");
        }
        if (!Float.isNaN(this.y)) {
            hashSet.add("translationZ");
        }
        if (this.e.size() > 0) {
            Iterator<String> it = this.e.keySet().iterator();
            while (it.hasNext()) {
                hashSet.add("CUSTOM," + it.next());
            }
        }
    }

    @Override // androidx.constraintlayout.motion.widget.d
    public void e(Context context, AttributeSet attributeSet) {
        a.a(this, context.obtainStyledAttributes(attributeSet, androidx.constraintlayout.widget.f.k));
    }
}
