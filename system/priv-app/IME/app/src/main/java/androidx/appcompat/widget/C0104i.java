package androidx.appcompat.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.PopupWindow;

/* renamed from: androidx.appcompat.widget.i, reason: case insensitive filesystem */
/* loaded from: classes.dex */
class C0104i extends PopupWindow {
    public C0104i(Context context, AttributeSet attributeSet, int i, int i2) {
        super(context, attributeSet, i, i2);
        G v = G.v(context, attributeSet, a.b.b.t, i, i2);
        if (v.s(2)) {
            setOverlapAnchor(v.a(2, false));
        }
        setBackgroundDrawable(v.g(0));
        v.w();
    }

    @Override // android.widget.PopupWindow
    public void showAsDropDown(View view, int i, int i2) {
        super.showAsDropDown(view, i, i2);
    }

    @Override // android.widget.PopupWindow
    public void showAsDropDown(View view, int i, int i2, int i3) {
        super.showAsDropDown(view, i, i2, i3);
    }

    @Override // android.widget.PopupWindow
    public void update(View view, int i, int i2, int i3, int i4) {
        super.update(view, i, i2, i3, i4);
    }
}
