package androidx.appcompat.app;

import a.b.f.b;
import a.b.f.f;
import a.h.h.q;
import a.h.h.s;
import a.h.h.u;
import a.h.h.w;
import android.R;
import android.app.Activity;
import android.app.Dialog;
import android.app.UiModeManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Bundle;
import android.os.LocaleList;
import android.os.PowerManager;
import android.text.TextUtils;
import android.util.AndroidRuntimeException;
import android.util.AttributeSet;
import android.util.Log;
import android.util.TypedValue;
import android.view.ActionMode;
import android.view.ContextThemeWrapper;
import android.view.KeyCharacterMap;
import android.view.KeyEvent;
import android.view.KeyboardShortcutGroup;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.PopupWindow;
import android.widget.TextView;
import androidx.appcompat.app.p;
import androidx.appcompat.view.menu.g;
import androidx.appcompat.view.menu.m;
import androidx.appcompat.widget.ActionBarContextView;
import androidx.appcompat.widget.C0102g;
import androidx.appcompat.widget.ContentFrameLayout;
import androidx.appcompat.widget.G;
import androidx.appcompat.widget.K;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;

/* loaded from: classes.dex */
class g extends androidx.appcompat.app.f implements g.a, LayoutInflater.Factory2 {
    private static final a.e.h<String, Integer> Z = new a.e.h<>();
    private static final int[] a0 = {R.attr.windowBackground};
    private static final boolean b0 = !"robolectric".equals(Build.FINGERPRINT);
    private static final boolean c0 = true;
    boolean A;
    boolean B;
    boolean C;
    boolean D;
    private boolean E;
    private i[] F;
    private i G;
    private boolean H;
    private boolean I;
    private boolean J;
    private boolean K;
    boolean L;
    private int M;
    private int N;
    private boolean O;
    private boolean P;
    private f Q;
    private f R;
    boolean S;
    int T;
    private final Runnable U;
    private boolean V;
    private Rect W;
    private Rect X;
    private m Y;

    /* renamed from: d, reason: collision with root package name */
    final Object f567d;
    final Context e;
    Window f;
    private d g;
    final androidx.appcompat.app.e h;
    androidx.appcompat.app.a i;
    MenuInflater j;
    private CharSequence k;
    private androidx.appcompat.widget.o l;
    private b m;
    private j n;
    a.b.f.b o;
    ActionBarContextView p;
    PopupWindow q;
    Runnable r;
    s s;
    private boolean t;
    ViewGroup u;
    private TextView v;
    private View w;
    private boolean x;
    private boolean y;
    boolean z;

    class a implements Runnable {
        a() {
        }

        @Override // java.lang.Runnable
        public void run() {
            g gVar = g.this;
            if ((gVar.T & 1) != 0) {
                gVar.K(0);
            }
            g gVar2 = g.this;
            if ((gVar2.T & 4096) != 0) {
                gVar2.K(108);
            }
            g gVar3 = g.this;
            gVar3.S = false;
            gVar3.T = 0;
        }
    }

    private final class b implements m.a {
        b() {
        }

        @Override // androidx.appcompat.view.menu.m.a
        public void a(androidx.appcompat.view.menu.g gVar, boolean z) {
            g.this.F(gVar);
        }

        @Override // androidx.appcompat.view.menu.m.a
        public boolean b(androidx.appcompat.view.menu.g gVar) {
            Window.Callback Q = g.this.Q();
            if (Q == null) {
                return true;
            }
            Q.onMenuOpened(108, gVar);
            return true;
        }
    }

    class c implements b.a {

        /* renamed from: a, reason: collision with root package name */
        private b.a f570a;

        class a extends u {
            a() {
            }

            @Override // a.h.h.t
            public void a(View view) {
                g.this.p.setVisibility(8);
                g gVar = g.this;
                PopupWindow popupWindow = gVar.q;
                if (popupWindow != null) {
                    popupWindow.dismiss();
                } else if (gVar.p.getParent() instanceof View) {
                    View view2 = (View) g.this.p.getParent();
                    int i = q.e;
                    view2.requestApplyInsets();
                }
                g.this.p.removeAllViews();
                g.this.s.f(null);
                g gVar2 = g.this;
                gVar2.s = null;
                ViewGroup viewGroup = gVar2.u;
                int i2 = q.e;
                viewGroup.requestApplyInsets();
            }
        }

        public c(b.a aVar) {
            this.f570a = aVar;
        }

        @Override // a.b.f.b.a
        public boolean a(a.b.f.b bVar, Menu menu) {
            ViewGroup viewGroup = g.this.u;
            int i = q.e;
            viewGroup.requestApplyInsets();
            return this.f570a.a(bVar, menu);
        }

        @Override // a.b.f.b.a
        public boolean b(a.b.f.b bVar, MenuItem menuItem) {
            return this.f570a.b(bVar, menuItem);
        }

        @Override // a.b.f.b.a
        public boolean c(a.b.f.b bVar, Menu menu) {
            return this.f570a.c(bVar, menu);
        }

        @Override // a.b.f.b.a
        public void d(a.b.f.b bVar) {
            this.f570a.d(bVar);
            g gVar = g.this;
            if (gVar.q != null) {
                gVar.f.getDecorView().removeCallbacks(g.this.r);
            }
            g gVar2 = g.this;
            if (gVar2.p != null) {
                gVar2.L();
                g gVar3 = g.this;
                s c2 = q.c(gVar3.p);
                c2.a(0.0f);
                gVar3.s = c2;
                g.this.s.f(new a());
            }
            g gVar4 = g.this;
            androidx.appcompat.app.e eVar = gVar4.h;
            if (eVar != null) {
                eVar.h(gVar4.o);
            }
            g gVar5 = g.this;
            gVar5.o = null;
            ViewGroup viewGroup = gVar5.u;
            int i = q.e;
            viewGroup.requestApplyInsets();
        }
    }

    class d extends a.b.f.i {
        d(Window.Callback callback) {
            super(callback);
        }

        final ActionMode b(ActionMode.Callback callback) {
            f.a aVar = new f.a(g.this.e, callback);
            a.b.f.b c0 = g.this.c0(aVar);
            if (c0 != null) {
                return aVar.e(c0);
            }
            return null;
        }

        @Override // a.b.f.i, android.view.Window.Callback
        public boolean dispatchKeyEvent(KeyEvent keyEvent) {
            return g.this.J(keyEvent) || super.dispatchKeyEvent(keyEvent);
        }

        @Override // a.b.f.i, android.view.Window.Callback
        public boolean dispatchKeyShortcutEvent(KeyEvent keyEvent) {
            return super.dispatchKeyShortcutEvent(keyEvent) || g.this.V(keyEvent.getKeyCode(), keyEvent);
        }

        @Override // android.view.Window.Callback
        public void onContentChanged() {
        }

        @Override // a.b.f.i, android.view.Window.Callback
        public boolean onCreatePanelMenu(int i, Menu menu) {
            if (i != 0 || (menu instanceof androidx.appcompat.view.menu.g)) {
                return super.onCreatePanelMenu(i, menu);
            }
            return false;
        }

        @Override // a.b.f.i, android.view.Window.Callback
        public boolean onMenuOpened(int i, Menu menu) {
            super.onMenuOpened(i, menu);
            g.this.W(i);
            return true;
        }

        @Override // a.b.f.i, android.view.Window.Callback
        public void onPanelClosed(int i, Menu menu) {
            super.onPanelClosed(i, menu);
            g.this.X(i);
        }

        @Override // a.b.f.i, android.view.Window.Callback
        public boolean onPreparePanel(int i, View view, Menu menu) {
            androidx.appcompat.view.menu.g gVar = menu instanceof androidx.appcompat.view.menu.g ? (androidx.appcompat.view.menu.g) menu : null;
            if (i == 0 && gVar == null) {
                return false;
            }
            if (gVar != null) {
                gVar.P(true);
            }
            boolean onPreparePanel = super.onPreparePanel(i, view, menu);
            if (gVar != null) {
                gVar.P(false);
            }
            return onPreparePanel;
        }

        @Override // a.b.f.i, android.view.Window.Callback
        public void onProvideKeyboardShortcuts(List<KeyboardShortcutGroup> list, Menu menu, int i) {
            androidx.appcompat.view.menu.g gVar = g.this.P(0).h;
            if (gVar != null) {
                super.onProvideKeyboardShortcuts(list, gVar, i);
            } else {
                super.onProvideKeyboardShortcuts(list, menu, i);
            }
        }

        @Override // android.view.Window.Callback
        public ActionMode onWindowStartingActionMode(ActionMode.Callback callback) {
            return null;
        }

        @Override // a.b.f.i, android.view.Window.Callback
        public ActionMode onWindowStartingActionMode(ActionMode.Callback callback, int i) {
            return (g.this.T() && i == 0) ? b(callback) : super.onWindowStartingActionMode(callback, i);
        }
    }

    private class e extends f {

        /* renamed from: c, reason: collision with root package name */
        private final PowerManager f574c;

        e(Context context) {
            super();
            this.f574c = (PowerManager) context.getApplicationContext().getSystemService("power");
        }

        @Override // androidx.appcompat.app.g.f
        IntentFilter b() {
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction("android.os.action.POWER_SAVE_MODE_CHANGED");
            return intentFilter;
        }

        @Override // androidx.appcompat.app.g.f
        public int c() {
            return this.f574c.isPowerSaveMode() ? 2 : 1;
        }

        @Override // androidx.appcompat.app.g.f
        public void d() {
            g.this.B();
        }
    }

    abstract class f {

        /* renamed from: a, reason: collision with root package name */
        private BroadcastReceiver f576a;

        class a extends BroadcastReceiver {
            a() {
            }

            @Override // android.content.BroadcastReceiver
            public void onReceive(Context context, Intent intent) {
                f.this.d();
            }
        }

        f() {
        }

        void a() {
            BroadcastReceiver broadcastReceiver = this.f576a;
            if (broadcastReceiver != null) {
                try {
                    g.this.e.unregisterReceiver(broadcastReceiver);
                } catch (IllegalArgumentException unused) {
                }
                this.f576a = null;
            }
        }

        abstract IntentFilter b();

        abstract int c();

        abstract void d();

        void e() {
            a();
            IntentFilter b2 = b();
            if (b2 == null || b2.countActions() == 0) {
                return;
            }
            if (this.f576a == null) {
                this.f576a = new a();
            }
            g.this.e.registerReceiver(this.f576a, b2);
        }
    }

    /* renamed from: androidx.appcompat.app.g$g, reason: collision with other inner class name */
    private class C0025g extends f {

        /* renamed from: c, reason: collision with root package name */
        private final o f579c;

        C0025g(o oVar) {
            super();
            this.f579c = oVar;
        }

        @Override // androidx.appcompat.app.g.f
        IntentFilter b() {
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction("android.intent.action.TIME_SET");
            intentFilter.addAction("android.intent.action.TIMEZONE_CHANGED");
            intentFilter.addAction("android.intent.action.TIME_TICK");
            return intentFilter;
        }

        @Override // androidx.appcompat.app.g.f
        public int c() {
            return this.f579c.c() ? 2 : 1;
        }

        @Override // androidx.appcompat.app.g.f
        public void d() {
            g.this.B();
        }
    }

    private class h extends ContentFrameLayout {
        public h(Context context) {
            super(context);
        }

        @Override // android.view.ViewGroup, android.view.View
        public boolean dispatchKeyEvent(KeyEvent keyEvent) {
            return g.this.J(keyEvent) || super.dispatchKeyEvent(keyEvent);
        }

        @Override // android.view.ViewGroup
        public boolean onInterceptTouchEvent(MotionEvent motionEvent) {
            if (motionEvent.getAction() == 0) {
                int x = (int) motionEvent.getX();
                int y = (int) motionEvent.getY();
                if (x < -5 || y < -5 || x > getWidth() + 5 || y > getHeight() + 5) {
                    g gVar = g.this;
                    gVar.G(gVar.P(0), true);
                    return true;
                }
            }
            return super.onInterceptTouchEvent(motionEvent);
        }

        @Override // android.view.View
        public void setBackgroundResource(int i) {
            setBackgroundDrawable(a.b.c.a.a.a(getContext(), i));
        }
    }

    protected static final class i {

        /* renamed from: a, reason: collision with root package name */
        int f582a;

        /* renamed from: b, reason: collision with root package name */
        int f583b;

        /* renamed from: c, reason: collision with root package name */
        int f584c;

        /* renamed from: d, reason: collision with root package name */
        int f585d;
        ViewGroup e;
        View f;
        View g;
        androidx.appcompat.view.menu.g h;
        androidx.appcompat.view.menu.e i;
        Context j;
        boolean k;
        boolean l;
        boolean m;
        public boolean n;
        boolean o = false;
        boolean p;
        Bundle q;

        i(int i) {
            this.f582a = i;
        }

        void a(androidx.appcompat.view.menu.g gVar) {
            androidx.appcompat.view.menu.e eVar;
            androidx.appcompat.view.menu.g gVar2 = this.h;
            if (gVar == gVar2) {
                return;
            }
            if (gVar2 != null) {
                gVar2.B(this.i);
            }
            this.h = gVar;
            if (gVar == null || (eVar = this.i) == null) {
                return;
            }
            gVar.b(eVar);
        }
    }

    private final class j implements m.a {
        j() {
        }

        @Override // androidx.appcompat.view.menu.m.a
        public void a(androidx.appcompat.view.menu.g gVar, boolean z) {
            androidx.appcompat.view.menu.g q = gVar.q();
            boolean z2 = q != gVar;
            g gVar2 = g.this;
            if (z2) {
                gVar = q;
            }
            i O = gVar2.O(gVar);
            if (O != null) {
                if (!z2) {
                    g.this.G(O, z);
                } else {
                    g.this.E(O.f582a, O, q);
                    g.this.G(O, true);
                }
            }
        }

        @Override // androidx.appcompat.view.menu.m.a
        public boolean b(androidx.appcompat.view.menu.g gVar) {
            Window.Callback Q;
            if (gVar != gVar.q()) {
                return true;
            }
            g gVar2 = g.this;
            if (!gVar2.z || (Q = gVar2.Q()) == null || g.this.L) {
                return true;
            }
            Q.onMenuOpened(108, gVar);
            return true;
        }
    }

    g(Activity activity, androidx.appcompat.app.e eVar) {
        this(activity, null, eVar, activity);
    }

    g(Dialog dialog, androidx.appcompat.app.e eVar) {
        this(dialog.getContext(), dialog.getWindow(), eVar, dialog);
    }

    private g(Context context, Window window, androidx.appcompat.app.e eVar, Object obj) {
        a.e.h<String, Integer> hVar;
        Integer orDefault;
        androidx.appcompat.app.d dVar;
        this.s = null;
        this.M = -100;
        this.U = new a();
        this.e = context;
        this.h = eVar;
        this.f567d = obj;
        if (obj instanceof Dialog) {
            while (context != null) {
                if (!(context instanceof androidx.appcompat.app.d)) {
                    if (!(context instanceof ContextWrapper)) {
                        break;
                    } else {
                        context = ((ContextWrapper) context).getBaseContext();
                    }
                } else {
                    dVar = (androidx.appcompat.app.d) context;
                    break;
                }
            }
            dVar = null;
            if (dVar != null) {
                this.M = dVar.n().g();
            }
        }
        if (this.M == -100 && (orDefault = (hVar = Z).getOrDefault(this.f567d.getClass().getName(), null)) != null) {
            this.M = orDefault.intValue();
            hVar.remove(this.f567d.getClass().getName());
        }
        if (window != null) {
            D(window);
        }
        C0102g.h();
    }

    /* JADX WARN: Code restructure failed: missing block: B:43:0x00f4, code lost:
    
        if ((((androidx.lifecycle.g) r11).a().b().compareTo(androidx.lifecycle.d.b.STARTED) >= 0) != false) goto L61;
     */
    /* JADX WARN: Code restructure failed: missing block: B:44:0x00fb, code lost:
    
        r11.onConfigurationChanged(r6);
     */
    /* JADX WARN: Code restructure failed: missing block: B:46:0x00f9, code lost:
    
        if (r10.K != false) goto L61;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:31:0x009f A[ADDED_TO_REGION] */
    /* JADX WARN: Removed duplicated region for block: B:48:0x0102  */
    /* JADX WARN: Removed duplicated region for block: B:52:0x010f  */
    /* JADX WARN: Removed duplicated region for block: B:58:0x0130  */
    /* JADX WARN: Removed duplicated region for block: B:63:0x0143  */
    /* JADX WARN: Removed duplicated region for block: B:66:0x0126  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private boolean C(boolean r11) {
        /*
            Method dump skipped, instructions count: 331
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.app.g.C(boolean):boolean");
    }

    private void D(Window window) {
        if (this.f != null) {
            throw new IllegalStateException("AppCompat has already installed itself into the Window");
        }
        Window.Callback callback = window.getCallback();
        if (callback instanceof d) {
            throw new IllegalStateException("AppCompat has already installed itself into the Window");
        }
        d dVar = new d(callback);
        this.g = dVar;
        window.setCallback(dVar);
        G u = G.u(this.e, null, a0);
        Drawable h2 = u.h(0);
        if (h2 != null) {
            window.setBackgroundDrawable(h2);
        }
        u.w();
        this.f = window;
    }

    private Configuration H(Context context, int i2, Configuration configuration) {
        int i3 = i2 != 1 ? i2 != 2 ? context.getApplicationContext().getResources().getConfiguration().uiMode & 48 : 32 : 16;
        Configuration configuration2 = new Configuration();
        configuration2.fontScale = 0.0f;
        if (configuration != null) {
            configuration2.setTo(configuration);
        }
        configuration2.uiMode = i3 | (configuration2.uiMode & (-49));
        return configuration2;
    }

    private void M() {
        ViewGroup viewGroup;
        if (this.t) {
            return;
        }
        TypedArray obtainStyledAttributes = this.e.obtainStyledAttributes(a.b.b.k);
        if (!obtainStyledAttributes.hasValue(115)) {
            obtainStyledAttributes.recycle();
            throw new IllegalStateException("You need to use a Theme.AppCompat theme (or descendant) with this activity.");
        }
        if (obtainStyledAttributes.getBoolean(124, false)) {
            v(1);
        } else if (obtainStyledAttributes.getBoolean(115, false)) {
            v(108);
        }
        if (obtainStyledAttributes.getBoolean(116, false)) {
            v(109);
        }
        if (obtainStyledAttributes.getBoolean(117, false)) {
            v(10);
        }
        this.C = obtainStyledAttributes.getBoolean(0, false);
        obtainStyledAttributes.recycle();
        N();
        this.f.getDecorView();
        LayoutInflater from = LayoutInflater.from(this.e);
        if (this.D) {
            viewGroup = (ViewGroup) from.inflate(this.B ? org.libpag.R.layout.abc_screen_simple_overlay_action_mode : org.libpag.R.layout.abc_screen_simple, (ViewGroup) null);
        } else if (this.C) {
            viewGroup = (ViewGroup) from.inflate(org.libpag.R.layout.abc_dialog_title_material, (ViewGroup) null);
            this.A = false;
            this.z = false;
        } else if (this.z) {
            TypedValue typedValue = new TypedValue();
            this.e.getTheme().resolveAttribute(org.libpag.R.attr.actionBarTheme, typedValue, true);
            int i2 = typedValue.resourceId;
            viewGroup = (ViewGroup) LayoutInflater.from(i2 != 0 ? new a.b.f.d(this.e, i2) : this.e).inflate(org.libpag.R.layout.abc_screen_toolbar, (ViewGroup) null);
            androidx.appcompat.widget.o oVar = (androidx.appcompat.widget.o) viewGroup.findViewById(org.libpag.R.id.decor_content_parent);
            this.l = oVar;
            oVar.setWindowCallback(Q());
            if (this.A) {
                this.l.initFeature(109);
            }
            if (this.x) {
                this.l.initFeature(2);
            }
            if (this.y) {
                this.l.initFeature(5);
            }
        } else {
            viewGroup = null;
        }
        if (viewGroup == null) {
            StringBuilder j2 = b.b.a.a.a.j("AppCompat does not support the current theme features: { windowActionBar: ");
            j2.append(this.z);
            j2.append(", windowActionBarOverlay: ");
            j2.append(this.A);
            j2.append(", android:windowIsFloating: ");
            j2.append(this.C);
            j2.append(", windowActionModeOverlay: ");
            j2.append(this.B);
            j2.append(", windowNoTitle: ");
            j2.append(this.D);
            j2.append(" }");
            throw new IllegalArgumentException(j2.toString());
        }
        q.p(viewGroup, new androidx.appcompat.app.h(this));
        if (this.l == null) {
            this.v = (TextView) viewGroup.findViewById(org.libpag.R.id.title);
        }
        int i3 = K.f741b;
        try {
            Method method = viewGroup.getClass().getMethod("makeOptionalFitsSystemWindows", new Class[0]);
            if (!method.isAccessible()) {
                method.setAccessible(true);
            }
            method.invoke(viewGroup, new Object[0]);
        } catch (IllegalAccessException | InvocationTargetException e2) {
            Log.d("ViewUtils", "Could not invoke makeOptionalFitsSystemWindows", e2);
        } catch (NoSuchMethodException unused) {
            Log.d("ViewUtils", "Could not find method makeOptionalFitsSystemWindows. Oh well...");
        }
        ContentFrameLayout contentFrameLayout = (ContentFrameLayout) viewGroup.findViewById(org.libpag.R.id.action_bar_activity_content);
        ViewGroup viewGroup2 = (ViewGroup) this.f.findViewById(R.id.content);
        if (viewGroup2 != null) {
            while (viewGroup2.getChildCount() > 0) {
                View childAt = viewGroup2.getChildAt(0);
                viewGroup2.removeViewAt(0);
                contentFrameLayout.addView(childAt);
            }
            viewGroup2.setId(-1);
            contentFrameLayout.setId(R.id.content);
            if (viewGroup2 instanceof FrameLayout) {
                ((FrameLayout) viewGroup2).setForeground(null);
            }
        }
        this.f.setContentView(viewGroup);
        contentFrameLayout.setAttachListener(new androidx.appcompat.app.i(this));
        this.u = viewGroup;
        Object obj = this.f567d;
        CharSequence title = obj instanceof Activity ? ((Activity) obj).getTitle() : this.k;
        if (!TextUtils.isEmpty(title)) {
            androidx.appcompat.widget.o oVar2 = this.l;
            if (oVar2 != null) {
                oVar2.setWindowTitle(title);
            } else {
                androidx.appcompat.app.a aVar = this.i;
                if (aVar != null) {
                    ((p) aVar).e.setWindowTitle(title);
                } else {
                    TextView textView = this.v;
                    if (textView != null) {
                        textView.setText(title);
                    }
                }
            }
        }
        ContentFrameLayout contentFrameLayout2 = (ContentFrameLayout) this.u.findViewById(R.id.content);
        View decorView = this.f.getDecorView();
        contentFrameLayout2.setDecorPadding(decorView.getPaddingLeft(), decorView.getPaddingTop(), decorView.getPaddingRight(), decorView.getPaddingBottom());
        TypedArray obtainStyledAttributes2 = this.e.obtainStyledAttributes(a.b.b.k);
        obtainStyledAttributes2.getValue(122, contentFrameLayout2.getMinWidthMajor());
        obtainStyledAttributes2.getValue(123, contentFrameLayout2.getMinWidthMinor());
        if (obtainStyledAttributes2.hasValue(120)) {
            obtainStyledAttributes2.getValue(120, contentFrameLayout2.getFixedWidthMajor());
        }
        if (obtainStyledAttributes2.hasValue(121)) {
            obtainStyledAttributes2.getValue(121, contentFrameLayout2.getFixedWidthMinor());
        }
        if (obtainStyledAttributes2.hasValue(118)) {
            obtainStyledAttributes2.getValue(118, contentFrameLayout2.getFixedHeightMajor());
        }
        if (obtainStyledAttributes2.hasValue(119)) {
            obtainStyledAttributes2.getValue(119, contentFrameLayout2.getFixedHeightMinor());
        }
        obtainStyledAttributes2.recycle();
        contentFrameLayout2.requestLayout();
        this.t = true;
        i P = P(0);
        if (this.L || P.h != null) {
            return;
        }
        S(108);
    }

    private void N() {
        if (this.f == null) {
            Object obj = this.f567d;
            if (obj instanceof Activity) {
                D(((Activity) obj).getWindow());
            }
        }
        if (this.f == null) {
            throw new IllegalStateException("We have not been given a Window");
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:11:0x0032  */
    /* JADX WARN: Removed duplicated region for block: B:14:? A[RETURN, SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private void R() {
        /*
            r3 = this;
            r3.M()
            boolean r0 = r3.z
            if (r0 == 0) goto L37
            androidx.appcompat.app.a r0 = r3.i
            if (r0 == 0) goto Lc
            goto L37
        Lc:
            java.lang.Object r0 = r3.f567d
            boolean r1 = r0 instanceof android.app.Activity
            if (r1 == 0) goto L20
            androidx.appcompat.app.p r0 = new androidx.appcompat.app.p
            java.lang.Object r1 = r3.f567d
            android.app.Activity r1 = (android.app.Activity) r1
            boolean r2 = r3.A
            r0.<init>(r1, r2)
        L1d:
            r3.i = r0
            goto L2e
        L20:
            boolean r0 = r0 instanceof android.app.Dialog
            if (r0 == 0) goto L2e
            androidx.appcompat.app.p r0 = new androidx.appcompat.app.p
            java.lang.Object r1 = r3.f567d
            android.app.Dialog r1 = (android.app.Dialog) r1
            r0.<init>(r1)
            goto L1d
        L2e:
            androidx.appcompat.app.a r0 = r3.i
            if (r0 == 0) goto L37
            boolean r3 = r3.V
            r0.d(r3)
        L37:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.app.g.R():void");
    }

    private void S(int i2) {
        this.T = (1 << i2) | this.T;
        if (this.S) {
            return;
        }
        View decorView = this.f.getDecorView();
        Runnable runnable = this.U;
        int i3 = q.e;
        decorView.postOnAnimation(runnable);
        this.S = true;
    }

    /* JADX WARN: Code restructure failed: missing block: B:87:0x0131, code lost:
    
        if (r13 != null) goto L73;
     */
    /* JADX WARN: Removed duplicated region for block: B:58:0x0138  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private void Y(androidx.appcompat.app.g.i r14, android.view.KeyEvent r15) {
        /*
            Method dump skipped, instructions count: 433
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.app.g.Y(androidx.appcompat.app.g$i, android.view.KeyEvent):void");
    }

    private boolean Z(i iVar, int i2, KeyEvent keyEvent, int i3) {
        androidx.appcompat.view.menu.g gVar;
        boolean z = false;
        if (keyEvent.isSystem()) {
            return false;
        }
        if ((iVar.k || a0(iVar, keyEvent)) && (gVar = iVar.h) != null) {
            z = gVar.performShortcut(i2, keyEvent, i3);
        }
        if (z && (i3 & 1) == 0 && this.l == null) {
            G(iVar, true);
        }
        return z;
    }

    private boolean a0(i iVar, KeyEvent keyEvent) {
        androidx.appcompat.widget.o oVar;
        androidx.appcompat.widget.o oVar2;
        Resources.Theme theme;
        androidx.appcompat.widget.o oVar3;
        androidx.appcompat.widget.o oVar4;
        if (this.L) {
            return false;
        }
        if (iVar.k) {
            return true;
        }
        i iVar2 = this.G;
        if (iVar2 != null && iVar2 != iVar) {
            G(iVar2, false);
        }
        Window.Callback Q = Q();
        if (Q != null) {
            iVar.g = Q.onCreatePanelView(iVar.f582a);
        }
        int i2 = iVar.f582a;
        boolean z = i2 == 0 || i2 == 108;
        if (z && (oVar4 = this.l) != null) {
            oVar4.setMenuPrepared();
        }
        if (iVar.g == null) {
            androidx.appcompat.view.menu.g gVar = iVar.h;
            if (gVar == null || iVar.p) {
                if (gVar == null) {
                    Context context = this.e;
                    int i3 = iVar.f582a;
                    if ((i3 == 0 || i3 == 108) && this.l != null) {
                        TypedValue typedValue = new TypedValue();
                        Resources.Theme theme2 = context.getTheme();
                        theme2.resolveAttribute(org.libpag.R.attr.actionBarTheme, typedValue, true);
                        if (typedValue.resourceId != 0) {
                            theme = context.getResources().newTheme();
                            theme.setTo(theme2);
                            theme.applyStyle(typedValue.resourceId, true);
                            theme.resolveAttribute(org.libpag.R.attr.actionBarWidgetTheme, typedValue, true);
                        } else {
                            theme2.resolveAttribute(org.libpag.R.attr.actionBarWidgetTheme, typedValue, true);
                            theme = null;
                        }
                        if (typedValue.resourceId != 0) {
                            if (theme == null) {
                                theme = context.getResources().newTheme();
                                theme.setTo(theme2);
                            }
                            theme.applyStyle(typedValue.resourceId, true);
                        }
                        if (theme != null) {
                            a.b.f.d dVar = new a.b.f.d(context, 0);
                            dVar.getTheme().setTo(theme);
                            context = dVar;
                        }
                    }
                    androidx.appcompat.view.menu.g gVar2 = new androidx.appcompat.view.menu.g(context);
                    gVar2.G(this);
                    iVar.a(gVar2);
                    if (iVar.h == null) {
                        return false;
                    }
                }
                if (z && (oVar2 = this.l) != null) {
                    if (this.m == null) {
                        this.m = new b();
                    }
                    oVar2.setMenu(iVar.h, this.m);
                }
                iVar.h.R();
                if (!Q.onCreatePanelMenu(iVar.f582a, iVar.h)) {
                    iVar.a(null);
                    if (z && (oVar = this.l) != null) {
                        oVar.setMenu(null, this.m);
                    }
                    return false;
                }
                iVar.p = false;
            }
            iVar.h.R();
            Bundle bundle = iVar.q;
            if (bundle != null) {
                iVar.h.C(bundle);
                iVar.q = null;
            }
            if (!Q.onPreparePanel(0, iVar.g, iVar.h)) {
                if (z && (oVar3 = this.l) != null) {
                    oVar3.setMenu(null, this.m);
                }
                iVar.h.Q();
                return false;
            }
            boolean z2 = KeyCharacterMap.load(keyEvent != null ? keyEvent.getDeviceId() : -1).getKeyboardType() != 1;
            iVar.n = z2;
            iVar.h.setQwertyMode(z2);
            iVar.h.Q();
        }
        iVar.k = true;
        iVar.l = false;
        this.G = iVar;
        return true;
    }

    private void d0() {
        if (this.t) {
            throw new AndroidRuntimeException("Window feature must be requested before adding content");
        }
    }

    @Override // androidx.appcompat.app.f
    public final void A(CharSequence charSequence) {
        this.k = charSequence;
        androidx.appcompat.widget.o oVar = this.l;
        if (oVar != null) {
            oVar.setWindowTitle(charSequence);
            return;
        }
        androidx.appcompat.app.a aVar = this.i;
        if (aVar != null) {
            ((p) aVar).e.setWindowTitle(charSequence);
            return;
        }
        TextView textView = this.v;
        if (textView != null) {
            textView.setText(charSequence);
        }
    }

    public boolean B() {
        return C(true);
    }

    void E(int i2, i iVar, Menu menu) {
        if (menu == null && iVar != null) {
            menu = iVar.h;
        }
        if ((iVar == null || iVar.m) && !this.L) {
            this.g.a().onPanelClosed(i2, menu);
        }
    }

    void F(androidx.appcompat.view.menu.g gVar) {
        if (this.E) {
            return;
        }
        this.E = true;
        this.l.dismissPopups();
        Window.Callback Q = Q();
        if (Q != null && !this.L) {
            Q.onPanelClosed(108, gVar);
        }
        this.E = false;
    }

    void G(i iVar, boolean z) {
        ViewGroup viewGroup;
        androidx.appcompat.widget.o oVar;
        if (z && iVar.f582a == 0 && (oVar = this.l) != null && oVar.isOverflowMenuShowing()) {
            F(iVar.h);
            return;
        }
        WindowManager windowManager = (WindowManager) this.e.getSystemService("window");
        if (windowManager != null && iVar.m && (viewGroup = iVar.e) != null) {
            windowManager.removeView(viewGroup);
            if (z) {
                E(iVar.f582a, iVar, null);
            }
        }
        iVar.k = false;
        iVar.l = false;
        iVar.m = false;
        iVar.f = null;
        iVar.o = true;
        if (this.G == iVar) {
            this.G = null;
        }
    }

    void I() {
        androidx.appcompat.widget.o oVar = this.l;
        if (oVar != null) {
            oVar.dismissPopups();
        }
        if (this.q != null) {
            this.f.getDecorView().removeCallbacks(this.r);
            if (this.q.isShowing()) {
                try {
                    this.q.dismiss();
                } catch (IllegalArgumentException unused) {
                }
            }
            this.q = null;
        }
        L();
        androidx.appcompat.view.menu.g gVar = P(0).h;
        if (gVar != null) {
            gVar.e(true);
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:92:0x0120, code lost:
    
        if (r6 != false) goto L90;
     */
    /* JADX WARN: Removed duplicated region for block: B:84:? A[RETURN, SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    boolean J(android.view.KeyEvent r7) {
        /*
            Method dump skipped, instructions count: 296
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.app.g.J(android.view.KeyEvent):boolean");
    }

    void K(int i2) {
        i P = P(i2);
        if (P.h != null) {
            Bundle bundle = new Bundle();
            P.h.E(bundle);
            if (bundle.size() > 0) {
                P.q = bundle;
            }
            P.h.R();
            P.h.clear();
        }
        P.p = true;
        P.o = true;
        if ((i2 == 108 || i2 == 0) && this.l != null) {
            i P2 = P(0);
            P2.k = false;
            a0(P2, null);
        }
    }

    void L() {
        s sVar = this.s;
        if (sVar != null) {
            sVar.b();
        }
    }

    i O(Menu menu) {
        i[] iVarArr = this.F;
        int length = iVarArr != null ? iVarArr.length : 0;
        for (int i2 = 0; i2 < length; i2++) {
            i iVar = iVarArr[i2];
            if (iVar != null && iVar.h == menu) {
                return iVar;
            }
        }
        return null;
    }

    protected i P(int i2) {
        i[] iVarArr = this.F;
        if (iVarArr == null || iVarArr.length <= i2) {
            i[] iVarArr2 = new i[i2 + 1];
            if (iVarArr != null) {
                System.arraycopy(iVarArr, 0, iVarArr2, 0, iVarArr.length);
            }
            this.F = iVarArr2;
            iVarArr = iVarArr2;
        }
        i iVar = iVarArr[i2];
        if (iVar != null) {
            return iVar;
        }
        i iVar2 = new i(i2);
        iVarArr[i2] = iVar2;
        return iVar2;
    }

    final Window.Callback Q() {
        return this.f.getCallback();
    }

    public boolean T() {
        return true;
    }

    int U(Context context, int i2) {
        f fVar;
        if (i2 == -100) {
            return -1;
        }
        if (i2 != -1) {
            if (i2 != 0) {
                if (i2 != 1 && i2 != 2) {
                    if (i2 != 3) {
                        throw new IllegalStateException("Unknown value set for night mode. Please use one of the MODE_NIGHT values from AppCompatDelegate.");
                    }
                    if (this.R == null) {
                        this.R = new e(context);
                    }
                    fVar = this.R;
                }
            } else {
                if (((UiModeManager) context.getApplicationContext().getSystemService(UiModeManager.class)).getNightMode() == 0) {
                    return -1;
                }
                if (this.Q == null) {
                    this.Q = new C0025g(o.a(context));
                }
                fVar = this.Q;
            }
            return fVar.c();
        }
        return i2;
    }

    boolean V(int i2, KeyEvent keyEvent) {
        boolean z;
        Menu e2;
        R();
        androidx.appcompat.app.a aVar = this.i;
        if (aVar != null) {
            p.d dVar = ((p) aVar).i;
            if (dVar == null || (e2 = dVar.e()) == null) {
                z = false;
            } else {
                e2.setQwertyMode(KeyCharacterMap.load(keyEvent.getDeviceId()).getKeyboardType() != 1);
                z = ((androidx.appcompat.view.menu.g) e2).performShortcut(i2, keyEvent, 0);
            }
            if (z) {
                return true;
            }
        }
        i iVar = this.G;
        if (iVar != null && Z(iVar, keyEvent.getKeyCode(), keyEvent, 1)) {
            i iVar2 = this.G;
            if (iVar2 != null) {
                iVar2.l = true;
            }
            return true;
        }
        if (this.G == null) {
            i P = P(0);
            a0(P, keyEvent);
            boolean Z2 = Z(P, keyEvent.getKeyCode(), keyEvent, 1);
            P.k = false;
            if (Z2) {
                return true;
            }
        }
        return false;
    }

    void W(int i2) {
        if (i2 == 108) {
            R();
            androidx.appcompat.app.a aVar = this.i;
            if (aVar != null) {
                aVar.a(true);
            }
        }
    }

    void X(int i2) {
        if (i2 == 108) {
            R();
            androidx.appcompat.app.a aVar = this.i;
            if (aVar != null) {
                aVar.a(false);
                return;
            }
            return;
        }
        if (i2 == 0) {
            i P = P(i2);
            if (P.m) {
                G(P, false);
            }
        }
    }

    @Override // androidx.appcompat.view.menu.g.a
    public boolean a(androidx.appcompat.view.menu.g gVar, MenuItem menuItem) {
        i O;
        Window.Callback Q = Q();
        if (Q == null || this.L || (O = O(gVar.q())) == null) {
            return false;
        }
        return Q.onMenuItemSelected(O.f582a, menuItem);
    }

    @Override // androidx.appcompat.view.menu.g.a
    public void b(androidx.appcompat.view.menu.g gVar) {
        androidx.appcompat.widget.o oVar = this.l;
        if (oVar == null || !oVar.canShowOverflowMenu() || (ViewConfiguration.get(this.e).hasPermanentMenuKey() && !this.l.isOverflowMenuShowPending())) {
            i P = P(0);
            P.o = true;
            G(P, false);
            Y(P, null);
            return;
        }
        Window.Callback Q = Q();
        if (this.l.isOverflowMenuShowing()) {
            this.l.hideOverflowMenu();
            if (this.L) {
                return;
            }
            Q.onPanelClosed(108, P(0).h);
            return;
        }
        if (Q == null || this.L) {
            return;
        }
        if (this.S && (1 & this.T) != 0) {
            this.f.getDecorView().removeCallbacks(this.U);
            this.U.run();
        }
        i P2 = P(0);
        androidx.appcompat.view.menu.g gVar2 = P2.h;
        if (gVar2 == null || P2.p || !Q.onPreparePanel(0, P2.g, gVar2)) {
            return;
        }
        Q.onMenuOpened(108, P2.h);
        this.l.showOverflowMenu();
    }

    final boolean b0() {
        ViewGroup viewGroup;
        if (this.t && (viewGroup = this.u) != null) {
            int i2 = q.e;
            if (viewGroup.isLaidOut()) {
                return true;
            }
        }
        return false;
    }

    /* JADX WARN: Removed duplicated region for block: B:30:0x0079  */
    /* JADX WARN: Removed duplicated region for block: B:37:0x007d  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public a.b.f.b c0(a.b.f.b.a r9) {
        /*
            Method dump skipped, instructions count: 470
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.app.g.c0(a.b.f.b$a):a.b.f.b");
    }

    @Override // androidx.appcompat.app.f
    public void d(View view, ViewGroup.LayoutParams layoutParams) {
        M();
        ((ViewGroup) this.u.findViewById(R.id.content)).addView(view, layoutParams);
        this.g.a().onContentChanged();
    }

    @Override // androidx.appcompat.app.f
    public Context e(Context context) {
        this.I = true;
        int i2 = this.M;
        if (i2 == -100) {
            i2 = -100;
        }
        int U = U(context, i2);
        Configuration configuration = null;
        if (c0 && (context instanceof ContextThemeWrapper)) {
            try {
                ((ContextThemeWrapper) context).applyOverrideConfiguration(H(context, U, null));
                return context;
            } catch (IllegalStateException unused) {
            }
        }
        if (context instanceof a.b.f.d) {
            try {
                ((a.b.f.d) context).a(H(context, U, null));
                return context;
            } catch (IllegalStateException unused2) {
            }
        }
        if (!b0) {
            return context;
        }
        try {
            Configuration configuration2 = context.getPackageManager().getResourcesForApplication(context.getApplicationInfo()).getConfiguration();
            Configuration configuration3 = context.getResources().getConfiguration();
            if (!configuration2.equals(configuration3)) {
                configuration = new Configuration();
                configuration.fontScale = 0.0f;
                if (configuration3 != null && configuration2.diff(configuration3) != 0) {
                    float f2 = configuration2.fontScale;
                    float f3 = configuration3.fontScale;
                    if (f2 != f3) {
                        configuration.fontScale = f3;
                    }
                    int i3 = configuration2.mcc;
                    int i4 = configuration3.mcc;
                    if (i3 != i4) {
                        configuration.mcc = i4;
                    }
                    int i5 = configuration2.mnc;
                    int i6 = configuration3.mnc;
                    if (i5 != i6) {
                        configuration.mnc = i6;
                    }
                    LocaleList locales = configuration2.getLocales();
                    LocaleList locales2 = configuration3.getLocales();
                    if (!locales.equals(locales2)) {
                        configuration.setLocales(locales2);
                        configuration.locale = configuration3.locale;
                    }
                    int i7 = configuration2.touchscreen;
                    int i8 = configuration3.touchscreen;
                    if (i7 != i8) {
                        configuration.touchscreen = i8;
                    }
                    int i9 = configuration2.keyboard;
                    int i10 = configuration3.keyboard;
                    if (i9 != i10) {
                        configuration.keyboard = i10;
                    }
                    int i11 = configuration2.keyboardHidden;
                    int i12 = configuration3.keyboardHidden;
                    if (i11 != i12) {
                        configuration.keyboardHidden = i12;
                    }
                    int i13 = configuration2.navigation;
                    int i14 = configuration3.navigation;
                    if (i13 != i14) {
                        configuration.navigation = i14;
                    }
                    int i15 = configuration2.navigationHidden;
                    int i16 = configuration3.navigationHidden;
                    if (i15 != i16) {
                        configuration.navigationHidden = i16;
                    }
                    int i17 = configuration2.orientation;
                    int i18 = configuration3.orientation;
                    if (i17 != i18) {
                        configuration.orientation = i18;
                    }
                    int i19 = configuration2.screenLayout & 15;
                    int i20 = configuration3.screenLayout & 15;
                    if (i19 != i20) {
                        configuration.screenLayout |= i20;
                    }
                    int i21 = configuration2.screenLayout & 192;
                    int i22 = configuration3.screenLayout & 192;
                    if (i21 != i22) {
                        configuration.screenLayout |= i22;
                    }
                    int i23 = configuration2.screenLayout & 48;
                    int i24 = configuration3.screenLayout & 48;
                    if (i23 != i24) {
                        configuration.screenLayout |= i24;
                    }
                    int i25 = configuration2.screenLayout & 768;
                    int i26 = configuration3.screenLayout & 768;
                    if (i25 != i26) {
                        configuration.screenLayout |= i26;
                    }
                    int i27 = configuration2.colorMode & 3;
                    int i28 = configuration3.colorMode & 3;
                    if (i27 != i28) {
                        configuration.colorMode |= i28;
                    }
                    int i29 = configuration2.colorMode & 12;
                    int i30 = configuration3.colorMode & 12;
                    if (i29 != i30) {
                        configuration.colorMode |= i30;
                    }
                    int i31 = configuration2.uiMode & 15;
                    int i32 = configuration3.uiMode & 15;
                    if (i31 != i32) {
                        configuration.uiMode |= i32;
                    }
                    int i33 = configuration2.uiMode & 48;
                    int i34 = configuration3.uiMode & 48;
                    if (i33 != i34) {
                        configuration.uiMode |= i34;
                    }
                    int i35 = configuration2.screenWidthDp;
                    int i36 = configuration3.screenWidthDp;
                    if (i35 != i36) {
                        configuration.screenWidthDp = i36;
                    }
                    int i37 = configuration2.screenHeightDp;
                    int i38 = configuration3.screenHeightDp;
                    if (i37 != i38) {
                        configuration.screenHeightDp = i38;
                    }
                    int i39 = configuration2.smallestScreenWidthDp;
                    int i40 = configuration3.smallestScreenWidthDp;
                    if (i39 != i40) {
                        configuration.smallestScreenWidthDp = i40;
                    }
                    int i41 = configuration2.densityDpi;
                    int i42 = configuration3.densityDpi;
                    if (i41 != i42) {
                        configuration.densityDpi = i42;
                    }
                }
            }
            Configuration H = H(context, U, configuration);
            a.b.f.d dVar = new a.b.f.d(context, org.libpag.R.style.Theme_AppCompat_Empty);
            dVar.a(H);
            boolean z = false;
            try {
                z = context.getTheme() != null;
            } catch (NullPointerException unused3) {
            }
            if (z) {
                dVar.getTheme().rebase();
            }
            return dVar;
        } catch (PackageManager.NameNotFoundException e2) {
            throw new RuntimeException("Application failed to obtain resources from itself", e2);
        }
    }

    final int e0(w wVar, Rect rect) {
        boolean z;
        boolean z2;
        int i2 = wVar.i();
        ActionBarContextView actionBarContextView = this.p;
        if (actionBarContextView == null || !(actionBarContextView.getLayoutParams() instanceof ViewGroup.MarginLayoutParams)) {
            z = false;
        } else {
            ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) this.p.getLayoutParams();
            if (this.p.isShown()) {
                if (this.W == null) {
                    this.W = new Rect();
                    this.X = new Rect();
                }
                Rect rect2 = this.W;
                Rect rect3 = this.X;
                rect2.set(wVar.g(), wVar.i(), wVar.h(), wVar.f());
                K.a(this.u, rect2, rect3);
                int i3 = rect2.top;
                int i4 = rect2.left;
                int i5 = rect2.right;
                w h2 = q.h(this.u);
                int g = h2 == null ? 0 : h2.g();
                int h3 = h2 == null ? 0 : h2.h();
                if (marginLayoutParams.topMargin == i3 && marginLayoutParams.leftMargin == i4 && marginLayoutParams.rightMargin == i5) {
                    z2 = false;
                } else {
                    marginLayoutParams.topMargin = i3;
                    marginLayoutParams.leftMargin = i4;
                    marginLayoutParams.rightMargin = i5;
                    z2 = true;
                }
                if (i3 <= 0 || this.w != null) {
                    View view = this.w;
                    if (view != null) {
                        ViewGroup.MarginLayoutParams marginLayoutParams2 = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
                        int i6 = marginLayoutParams2.height;
                        int i7 = marginLayoutParams.topMargin;
                        if (i6 != i7 || marginLayoutParams2.leftMargin != g || marginLayoutParams2.rightMargin != h3) {
                            marginLayoutParams2.height = i7;
                            marginLayoutParams2.leftMargin = g;
                            marginLayoutParams2.rightMargin = h3;
                            this.w.setLayoutParams(marginLayoutParams2);
                        }
                    }
                } else {
                    View view2 = new View(this.e);
                    this.w = view2;
                    view2.setVisibility(8);
                    FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(-1, marginLayoutParams.topMargin, 51);
                    layoutParams.leftMargin = g;
                    layoutParams.rightMargin = h3;
                    this.u.addView(this.w, -1, layoutParams);
                }
                View view3 = this.w;
                z = view3 != null;
                if (z && view3.getVisibility() != 0) {
                    View view4 = this.w;
                    int i8 = q.e;
                    r4 = (view4.getWindowSystemUiVisibility() & 8192) != 0;
                    Context context = this.e;
                    int i9 = r4 ? org.libpag.R.color.abc_decor_view_status_guard_light : org.libpag.R.color.abc_decor_view_status_guard;
                    int i10 = a.h.b.a.f265b;
                    view4.setBackgroundColor(context.getColor(i9));
                }
                if (!this.B && z) {
                    i2 = 0;
                }
                r4 = z2;
            } else if (marginLayoutParams.topMargin != 0) {
                marginLayoutParams.topMargin = 0;
                z = false;
            } else {
                r4 = false;
                z = false;
            }
            if (r4) {
                this.p.setLayoutParams(marginLayoutParams);
            }
        }
        View view5 = this.w;
        if (view5 != null) {
            view5.setVisibility(z ? 0 : 8);
        }
        return i2;
    }

    @Override // androidx.appcompat.app.f
    public <T extends View> T f(int i2) {
        M();
        return (T) this.f.findViewById(i2);
    }

    @Override // androidx.appcompat.app.f
    public int g() {
        return this.M;
    }

    @Override // androidx.appcompat.app.f
    public MenuInflater h() {
        if (this.j == null) {
            R();
            androidx.appcompat.app.a aVar = this.i;
            this.j = new a.b.f.g(aVar != null ? aVar.b() : this.e);
        }
        return this.j;
    }

    @Override // androidx.appcompat.app.f
    public androidx.appcompat.app.a i() {
        R();
        return this.i;
    }

    @Override // androidx.appcompat.app.f
    public void j() {
        LayoutInflater from = LayoutInflater.from(this.e);
        if (from.getFactory() == null) {
            from.setFactory2(this);
        } else {
            if (from.getFactory2() instanceof g) {
                return;
            }
            Log.i("AppCompatDelegate", "The Activity's LayoutInflater already has a Factory installed so we can not install AppCompat's");
        }
    }

    @Override // androidx.appcompat.app.f
    public void k() {
        R();
        androidx.appcompat.app.a aVar = this.i;
        S(0);
    }

    @Override // androidx.appcompat.app.f
    public void l(Configuration configuration) {
        if (this.z && this.t) {
            R();
            androidx.appcompat.app.a aVar = this.i;
            if (aVar != null) {
                aVar.c(configuration);
            }
        }
        C0102g.b().g(this.e);
        C(false);
    }

    @Override // androidx.appcompat.app.f
    public void m(Bundle bundle) {
        this.I = true;
        C(false);
        N();
        Object obj = this.f567d;
        if (obj instanceof Activity) {
            String str = null;
            try {
                Activity activity = (Activity) obj;
                try {
                    str = androidx.core.app.b.i(activity, activity.getComponentName());
                } catch (PackageManager.NameNotFoundException e2) {
                    throw new IllegalArgumentException(e2);
                }
            } catch (IllegalArgumentException unused) {
            }
            if (str != null) {
                androidx.appcompat.app.a aVar = this.i;
                if (aVar == null) {
                    this.V = true;
                } else {
                    aVar.d(true);
                }
            }
            androidx.appcompat.app.f.c(this);
        }
        this.J = true;
    }

    /* JADX WARN: Removed duplicated region for block: B:16:0x005b  */
    /* JADX WARN: Removed duplicated region for block: B:19:0x0062  */
    /* JADX WARN: Removed duplicated region for block: B:22:0x0069  */
    /* JADX WARN: Removed duplicated region for block: B:25:? A[RETURN, SYNTHETIC] */
    @Override // androidx.appcompat.app.f
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void n() {
        /*
            r3 = this;
            java.lang.Object r0 = r3.f567d
            boolean r0 = r0 instanceof android.app.Activity
            if (r0 == 0) goto L9
            androidx.appcompat.app.f.t(r3)
        L9:
            boolean r0 = r3.S
            if (r0 == 0) goto L18
            android.view.Window r0 = r3.f
            android.view.View r0 = r0.getDecorView()
            java.lang.Runnable r1 = r3.U
            r0.removeCallbacks(r1)
        L18:
            r0 = 0
            r3.K = r0
            r0 = 1
            r3.L = r0
            int r0 = r3.M
            r1 = -100
            if (r0 == r1) goto L48
            java.lang.Object r0 = r3.f567d
            boolean r1 = r0 instanceof android.app.Activity
            if (r1 == 0) goto L48
            android.app.Activity r0 = (android.app.Activity) r0
            boolean r0 = r0.isChangingConfigurations()
            if (r0 == 0) goto L48
            a.e.h<java.lang.String, java.lang.Integer> r0 = androidx.appcompat.app.g.Z
            java.lang.Object r1 = r3.f567d
            java.lang.Class r1 = r1.getClass()
            java.lang.String r1 = r1.getName()
            int r2 = r3.M
            java.lang.Integer r2 = java.lang.Integer.valueOf(r2)
            r0.put(r1, r2)
            goto L57
        L48:
            a.e.h<java.lang.String, java.lang.Integer> r0 = androidx.appcompat.app.g.Z
            java.lang.Object r1 = r3.f567d
            java.lang.Class r1 = r1.getClass()
            java.lang.String r1 = r1.getName()
            r0.remove(r1)
        L57:
            androidx.appcompat.app.a r0 = r3.i
            if (r0 == 0) goto L5e
            java.util.Objects.requireNonNull(r0)
        L5e:
            androidx.appcompat.app.g$f r0 = r3.Q
            if (r0 == 0) goto L65
            r0.a()
        L65:
            androidx.appcompat.app.g$f r3 = r3.R
            if (r3 == 0) goto L6c
            r3.a()
        L6c:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.app.g.n():void");
    }

    @Override // androidx.appcompat.app.f
    public void o(Bundle bundle) {
        M();
    }

    @Override // android.view.LayoutInflater.Factory2
    public final View onCreateView(View view, String str, Context context, AttributeSet attributeSet) {
        m mVar;
        if (this.Y == null) {
            String string = this.e.obtainStyledAttributes(a.b.b.k).getString(114);
            if (string == null) {
                mVar = new m();
            } else {
                try {
                    this.Y = (m) Class.forName(string).getDeclaredConstructor(new Class[0]).newInstance(new Object[0]);
                } catch (Throwable th) {
                    Log.i("AppCompatDelegate", "Failed to instantiate custom view inflater " + string + ". Falling back to default.", th);
                    mVar = new m();
                }
            }
            this.Y = mVar;
        }
        return this.Y.g(view, str, context, attributeSet, false, false, true, false);
    }

    @Override // android.view.LayoutInflater.Factory
    public View onCreateView(String str, Context context, AttributeSet attributeSet) {
        return onCreateView(null, str, context, attributeSet);
    }

    @Override // androidx.appcompat.app.f
    public void p() {
        R();
        androidx.appcompat.app.a aVar = this.i;
        if (aVar != null) {
            aVar.e(true);
        }
    }

    @Override // androidx.appcompat.app.f
    public void q(Bundle bundle) {
    }

    @Override // androidx.appcompat.app.f
    public void r() {
        this.K = true;
        B();
    }

    @Override // androidx.appcompat.app.f
    public void s() {
        this.K = false;
        R();
        androidx.appcompat.app.a aVar = this.i;
        if (aVar != null) {
            aVar.e(false);
        }
    }

    @Override // androidx.appcompat.app.f
    public boolean v(int i2) {
        if (i2 == 8) {
            Log.i("AppCompatDelegate", "You should now use the AppCompatDelegate.FEATURE_SUPPORT_ACTION_BAR id when requesting this feature.");
            i2 = 108;
        } else if (i2 == 9) {
            Log.i("AppCompatDelegate", "You should now use the AppCompatDelegate.FEATURE_SUPPORT_ACTION_BAR_OVERLAY id when requesting this feature.");
            i2 = 109;
        }
        if (this.D && i2 == 108) {
            return false;
        }
        if (this.z && i2 == 1) {
            this.z = false;
        }
        if (i2 == 1) {
            d0();
            this.D = true;
            return true;
        }
        if (i2 == 2) {
            d0();
            this.x = true;
            return true;
        }
        if (i2 == 5) {
            d0();
            this.y = true;
            return true;
        }
        if (i2 == 10) {
            d0();
            this.B = true;
            return true;
        }
        if (i2 == 108) {
            d0();
            this.z = true;
            return true;
        }
        if (i2 != 109) {
            return this.f.requestFeature(i2);
        }
        d0();
        this.A = true;
        return true;
    }

    @Override // androidx.appcompat.app.f
    public void w(int i2) {
        M();
        ViewGroup viewGroup = (ViewGroup) this.u.findViewById(R.id.content);
        viewGroup.removeAllViews();
        LayoutInflater.from(this.e).inflate(i2, viewGroup);
        this.g.a().onContentChanged();
    }

    @Override // androidx.appcompat.app.f
    public void x(View view) {
        M();
        ViewGroup viewGroup = (ViewGroup) this.u.findViewById(R.id.content);
        viewGroup.removeAllViews();
        viewGroup.addView(view);
        this.g.a().onContentChanged();
    }

    @Override // androidx.appcompat.app.f
    public void y(View view, ViewGroup.LayoutParams layoutParams) {
        M();
        ViewGroup viewGroup = (ViewGroup) this.u.findViewById(R.id.content);
        viewGroup.removeAllViews();
        viewGroup.addView(view, layoutParams);
        this.g.a().onContentChanged();
    }

    @Override // androidx.appcompat.app.f
    public void z(int i2) {
        this.N = i2;
    }
}
