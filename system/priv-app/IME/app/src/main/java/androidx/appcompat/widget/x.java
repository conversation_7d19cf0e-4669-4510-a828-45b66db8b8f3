package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.content.res.XmlResourceParser;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffColorFilter;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.LayerDrawable;
import android.util.AttributeSet;
import android.util.Log;
import android.util.TypedValue;
import android.util.Xml;
import androidx.appcompat.widget.C0102g;
import java.lang.ref.WeakReference;
import java.util.Objects;
import java.util.WeakHashMap;
import org.libpag.R;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

/* loaded from: classes.dex */
public final class x {
    private static x i;

    /* renamed from: a, reason: collision with root package name */
    private WeakHashMap<Context, a.e.i<ColorStateList>> f848a;

    /* renamed from: b, reason: collision with root package name */
    private a.e.h<String, b> f849b;

    /* renamed from: c, reason: collision with root package name */
    private a.e.i<String> f850c;

    /* renamed from: d, reason: collision with root package name */
    private final WeakHashMap<Context, a.e.e<WeakReference<Drawable.ConstantState>>> f851d = new WeakHashMap<>(0);
    private TypedValue e;
    private boolean f;
    private c g;
    private static final PorterDuff.Mode h = PorterDuff.Mode.SRC_IN;
    private static final a j = new a(6);

    private static class a extends a.e.f<Integer, PorterDuffColorFilter> {
        public a(int i) {
            super(i);
        }
    }

    private interface b {
        Drawable a(Context context, XmlPullParser xmlPullParser, AttributeSet attributeSet, Resources.Theme theme);
    }

    interface c {
    }

    private synchronized boolean a(Context context, long j2, Drawable drawable) {
        boolean z;
        Drawable.ConstantState constantState = drawable.getConstantState();
        if (constantState != null) {
            a.e.e<WeakReference<Drawable.ConstantState>> eVar = this.f851d.get(context);
            if (eVar == null) {
                eVar = new a.e.e<>();
                this.f851d.put(context, eVar);
            }
            eVar.h(j2, new WeakReference<>(constantState));
            z = true;
        } else {
            z = false;
        }
        return z;
    }

    private Drawable b(Context context, int i2) {
        if (this.e == null) {
            this.e = new TypedValue();
        }
        TypedValue typedValue = this.e;
        context.getResources().getValue(i2, typedValue, true);
        long j2 = (typedValue.assetCookie << 32) | typedValue.data;
        Drawable d2 = d(context, j2);
        if (d2 != null) {
            return d2;
        }
        c cVar = this.g;
        LayerDrawable layerDrawable = null;
        if (cVar != null) {
            if (i2 == R.drawable.abc_cab_background_top_material) {
                layerDrawable = new LayerDrawable(new Drawable[]{e(context, R.drawable.abc_cab_background_internal_bg), e(context, R.drawable.abc_cab_background_top_mtrl_alpha)});
            }
        }
        if (layerDrawable != null) {
            layerDrawable.setChangingConfigurations(typedValue.changingConfigurations);
            a(context, j2, layerDrawable);
        }
        return layerDrawable;
    }

    public static synchronized x c() {
        x xVar;
        synchronized (x.class) {
            if (i == null) {
                i = new x();
            }
            xVar = i;
        }
        return xVar;
    }

    private synchronized Drawable d(Context context, long j2) {
        a.e.e<WeakReference<Drawable.ConstantState>> eVar = this.f851d.get(context);
        if (eVar == null) {
            return null;
        }
        WeakReference<Drawable.ConstantState> e = eVar.e(j2, null);
        if (e != null) {
            Drawable.ConstantState constantState = e.get();
            if (constantState != null) {
                return constantState.newDrawable(context.getResources());
            }
            eVar.i(j2);
        }
        return null;
    }

    public static synchronized PorterDuffColorFilter g(int i2, PorterDuff.Mode mode) {
        PorterDuffColorFilter a2;
        synchronized (x.class) {
            a aVar = j;
            Objects.requireNonNull(aVar);
            int i3 = (i2 + 31) * 31;
            a2 = aVar.a(Integer.valueOf(mode.hashCode() + i3));
            if (a2 == null) {
                a2 = new PorterDuffColorFilter(i2, mode);
                Objects.requireNonNull(aVar);
                aVar.b(Integer.valueOf(mode.hashCode() + i3), a2);
            }
        }
        return a2;
    }

    private Drawable i(Context context, int i2) {
        int next;
        a.e.h<String, b> hVar = this.f849b;
        if (hVar == null || hVar.isEmpty()) {
            return null;
        }
        a.e.i<String> iVar = this.f850c;
        if (iVar != null) {
            String f = iVar.f(i2, null);
            if ("appcompat_skip_skip".equals(f) || (f != null && this.f849b.getOrDefault(f, null) == null)) {
                return null;
            }
        } else {
            this.f850c = new a.e.i<>(10);
        }
        if (this.e == null) {
            this.e = new TypedValue();
        }
        TypedValue typedValue = this.e;
        Resources resources = context.getResources();
        resources.getValue(i2, typedValue, true);
        long j2 = (typedValue.assetCookie << 32) | typedValue.data;
        Drawable d2 = d(context, j2);
        if (d2 != null) {
            return d2;
        }
        CharSequence charSequence = typedValue.string;
        if (charSequence != null && charSequence.toString().endsWith(".xml")) {
            try {
                XmlResourceParser xml = resources.getXml(i2);
                AttributeSet asAttributeSet = Xml.asAttributeSet(xml);
                do {
                    next = xml.next();
                    if (next == 2) {
                        break;
                    }
                } while (next != 1);
                if (next != 2) {
                    throw new XmlPullParserException("No start tag found");
                }
                String name = xml.getName();
                this.f850c.a(i2, name);
                b bVar = this.f849b.get(name);
                if (bVar != null) {
                    d2 = bVar.a(context, xml, asAttributeSet, context.getTheme());
                }
                if (d2 != null) {
                    d2.setChangingConfigurations(typedValue.changingConfigurations);
                    a(context, j2, d2);
                }
            } catch (Exception e) {
                Log.e("ResourceManagerInternal", "Exception while inflating drawable", e);
            }
        }
        if (d2 == null) {
            this.f850c.a(i2, "appcompat_skip_skip");
        }
        return d2;
    }

    private Drawable l(Context context, int i2, boolean z, Drawable drawable) {
        ColorStateList h2 = h(context, i2);
        PorterDuff.Mode mode = null;
        if (h2 == null) {
            c cVar = this.g;
            if (cVar != null && ((C0102g.a) cVar).e(context, i2, drawable)) {
                return drawable;
            }
            c cVar2 = this.g;
            if ((cVar2 != null && ((C0102g.a) cVar2).f(context, i2, drawable)) || !z) {
                return drawable;
            }
            return null;
        }
        if (q.a(drawable)) {
            drawable = drawable.mutate();
        }
        drawable.setTintList(h2);
        c cVar3 = this.g;
        if (cVar3 != null) {
            if (i2 == R.drawable.abc_switch_thumb_material) {
                mode = PorterDuff.Mode.MULTIPLY;
            }
        }
        if (mode == null) {
            return drawable;
        }
        drawable.setTintMode(mode);
        return drawable;
    }

    static void m(Drawable drawable, E e, int[] iArr) {
        if (q.a(drawable) && drawable.mutate() != drawable) {
            Log.d("ResourceManagerInternal", "Mutated drawable is not the same instance as the input.");
            return;
        }
        boolean z = e.f727d;
        if (!z && !e.f726c) {
            drawable.clearColorFilter();
            return;
        }
        PorterDuffColorFilter porterDuffColorFilter = null;
        ColorStateList colorStateList = z ? e.f724a : null;
        PorterDuff.Mode mode = e.f726c ? e.f725b : h;
        if (colorStateList != null && mode != null) {
            porterDuffColorFilter = g(colorStateList.getColorForState(iArr, 0), mode);
        }
        drawable.setColorFilter(porterDuffColorFilter);
    }

    public synchronized Drawable e(Context context, int i2) {
        return f(context, i2, false);
    }

    synchronized Drawable f(Context context, int i2, boolean z) {
        Drawable i3;
        if (!this.f) {
            boolean z2 = true;
            this.f = true;
            Drawable e = e(context, R.drawable.abc_vector_test);
            if (e != null) {
                if (!(e instanceof a.p.a.a.c) && !"android.graphics.drawable.VectorDrawable".equals(e.getClass().getName())) {
                    z2 = false;
                }
            }
            this.f = false;
            throw new IllegalStateException("This app has been built with an incorrect configuration. Please configure your build for VectorDrawableCompat.");
        }
        i3 = i(context, i2);
        if (i3 == null) {
            i3 = b(context, i2);
        }
        if (i3 == null) {
            int i4 = a.h.b.a.f265b;
            i3 = context.getDrawable(i2);
        }
        if (i3 != null) {
            i3 = l(context, i2, z, i3);
        }
        if (i3 != null) {
            Rect rect = q.f828c;
        }
        return i3;
    }

    synchronized ColorStateList h(Context context, int i2) {
        ColorStateList f;
        a.e.i<ColorStateList> iVar;
        WeakHashMap<Context, a.e.i<ColorStateList>> weakHashMap = this.f848a;
        ColorStateList colorStateList = null;
        f = (weakHashMap == null || (iVar = weakHashMap.get(context)) == null) ? null : iVar.f(i2, null);
        if (f == null) {
            c cVar = this.g;
            if (cVar != null) {
                colorStateList = ((C0102g.a) cVar).c(context, i2);
            }
            if (colorStateList != null) {
                if (this.f848a == null) {
                    this.f848a = new WeakHashMap<>();
                }
                a.e.i<ColorStateList> iVar2 = this.f848a.get(context);
                if (iVar2 == null) {
                    iVar2 = new a.e.i<>(10);
                    this.f848a.put(context, iVar2);
                }
                iVar2.a(i2, colorStateList);
            }
            f = colorStateList;
        }
        return f;
    }

    public synchronized void j(Context context) {
        a.e.e<WeakReference<Drawable.ConstantState>> eVar = this.f851d.get(context);
        if (eVar != null) {
            eVar.a();
        }
    }

    public synchronized void k(c cVar) {
        this.g = cVar;
    }
}
