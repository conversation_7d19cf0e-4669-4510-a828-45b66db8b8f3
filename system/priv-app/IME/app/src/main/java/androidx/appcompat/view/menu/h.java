package androidx.appcompat.view.menu;

import android.content.DialogInterface;
import android.os.IBinder;
import android.view.KeyEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import androidx.appcompat.app.c;
import androidx.appcompat.view.menu.e;
import androidx.appcompat.view.menu.m;
import org.libpag.R;

/* loaded from: classes.dex */
class h implements DialogInterface.OnKeyListener, DialogInterface.OnClickListener, DialogInterface.OnDismissListener, m.a {

    /* renamed from: a, reason: collision with root package name */
    private g f660a;

    /* renamed from: b, reason: collision with root package name */
    private androidx.appcompat.app.c f661b;

    /* renamed from: c, reason: collision with root package name */
    e f662c;

    public h(g gVar) {
        this.f660a = gVar;
    }

    @Override // androidx.appcompat.view.menu.m.a
    public void a(g gVar, boolean z) {
        androidx.appcompat.app.c cVar;
        if ((z || gVar == this.f660a) && (cVar = this.f661b) != null) {
            cVar.dismiss();
        }
    }

    @Override // androidx.appcompat.view.menu.m.a
    public boolean b(g gVar) {
        return false;
    }

    public void c(IBinder iBinder) {
        g gVar = this.f660a;
        c.a aVar = new c.a(gVar.n());
        e eVar = new e(aVar.b(), R.layout.abc_list_menu_item_layout);
        this.f662c = eVar;
        eVar.l(this);
        this.f660a.b(this.f662c);
        aVar.c(this.f662c.b(), this);
        View view = gVar.o;
        if (view != null) {
            aVar.d(view);
        } else {
            aVar.e(gVar.n);
            aVar.h(gVar.m);
        }
        aVar.f(this);
        androidx.appcompat.app.c a2 = aVar.a();
        this.f661b = a2;
        a2.setOnDismissListener(this);
        WindowManager.LayoutParams attributes = this.f661b.getWindow().getAttributes();
        attributes.type = 1003;
        attributes.flags |= 131072;
        this.f661b.show();
    }

    @Override // android.content.DialogInterface.OnClickListener
    public void onClick(DialogInterface dialogInterface, int i) {
        this.f660a.y(((e.a) this.f662c.b()).getItem(i), 0);
    }

    @Override // android.content.DialogInterface.OnDismissListener
    public void onDismiss(DialogInterface dialogInterface) {
        this.f662c.a(this.f660a, true);
    }

    @Override // android.content.DialogInterface.OnKeyListener
    public boolean onKey(DialogInterface dialogInterface, int i, KeyEvent keyEvent) {
        Window window;
        View decorView;
        KeyEvent.DispatcherState keyDispatcherState;
        View decorView2;
        KeyEvent.DispatcherState keyDispatcherState2;
        if (i == 82 || i == 4) {
            if (keyEvent.getAction() == 0 && keyEvent.getRepeatCount() == 0) {
                Window window2 = this.f661b.getWindow();
                if (window2 != null && (decorView2 = window2.getDecorView()) != null && (keyDispatcherState2 = decorView2.getKeyDispatcherState()) != null) {
                    keyDispatcherState2.startTracking(keyEvent, this);
                    return true;
                }
            } else if (keyEvent.getAction() == 1 && !keyEvent.isCanceled() && (window = this.f661b.getWindow()) != null && (decorView = window.getDecorView()) != null && (keyDispatcherState = decorView.getKeyDispatcherState()) != null && keyDispatcherState.isTracking(keyEvent)) {
                this.f660a.e(true);
                dialogInterface.dismiss();
                return true;
            }
        }
        return this.f660a.performShortcut(i, keyEvent, 0);
    }
}
