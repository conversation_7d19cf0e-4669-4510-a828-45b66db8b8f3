package androidx.appcompat.view.menu;

import a.h.h.b;
import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.util.Log;
import android.view.ActionProvider;
import android.view.ContextMenu;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.SubMenu;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewDebug;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.appcompat.view.menu.n;
import org.libpag.R;

/* loaded from: classes.dex */
public final class i implements a.h.d.a.b {
    private a.h.h.b A;
    private MenuItem.OnActionExpandListener B;
    private ContextMenu.ContextMenuInfo D;

    /* renamed from: a, reason: collision with root package name */
    private final int f663a;

    /* renamed from: b, reason: collision with root package name */
    private final int f664b;

    /* renamed from: c, reason: collision with root package name */
    private final int f665c;

    /* renamed from: d, reason: collision with root package name */
    private final int f666d;
    private CharSequence e;
    private CharSequence f;
    private Intent g;
    private char h;
    private char j;
    private Drawable l;
    g n;
    private r o;
    private MenuItem.OnMenuItemClickListener p;
    private CharSequence q;
    private CharSequence r;
    private int y;
    private View z;
    private int i = 4096;
    private int k = 4096;
    private int m = 0;
    private ColorStateList s = null;
    private PorterDuff.Mode t = null;
    private boolean u = false;
    private boolean v = false;
    private boolean w = false;
    private int x = 16;
    private boolean C = false;

    class a implements b.InterfaceC0010b {
        a() {
        }
    }

    i(g gVar, int i, int i2, int i3, int i4, CharSequence charSequence, int i5) {
        this.y = 0;
        this.n = gVar;
        this.f663a = i2;
        this.f664b = i;
        this.f665c = i3;
        this.f666d = i4;
        this.e = charSequence;
        this.y = i5;
    }

    private static void c(StringBuilder sb, int i, int i2, String str) {
        if ((i & i2) == i2) {
            sb.append(str);
        }
    }

    private Drawable d(Drawable drawable) {
        if (drawable != null && this.w && (this.u || this.v)) {
            drawable = drawable.mutate();
            if (this.u) {
                drawable.setTintList(this.s);
            }
            if (this.v) {
                drawable.setTintMode(this.t);
            }
            this.w = false;
        }
        return drawable;
    }

    @Override // a.h.d.a.b
    public a.h.d.a.b a(a.h.h.b bVar) {
        a.h.h.b bVar2 = this.A;
        if (bVar2 != null) {
            bVar2.h();
        }
        this.z = null;
        this.A = bVar;
        this.n.x(true);
        a.h.h.b bVar3 = this.A;
        if (bVar3 != null) {
            bVar3.j(new a());
        }
        return this;
    }

    @Override // a.h.d.a.b
    public a.h.h.b b() {
        return this.A;
    }

    @Override // a.h.d.a.b, android.view.MenuItem
    public boolean collapseActionView() {
        if ((this.y & 8) == 0) {
            return false;
        }
        if (this.z == null) {
            return true;
        }
        MenuItem.OnActionExpandListener onActionExpandListener = this.B;
        if (onActionExpandListener == null || onActionExpandListener.onMenuItemActionCollapse(this)) {
            return this.n.f(this);
        }
        return false;
    }

    public int e() {
        return this.f666d;
    }

    @Override // a.h.d.a.b, android.view.MenuItem
    public boolean expandActionView() {
        if (!i()) {
            return false;
        }
        MenuItem.OnActionExpandListener onActionExpandListener = this.B;
        if (onActionExpandListener == null || onActionExpandListener.onMenuItemActionExpand(this)) {
            return this.n.h(this);
        }
        return false;
    }

    char f() {
        return this.n.t() ? this.j : this.h;
    }

    String g() {
        int i;
        char f = f();
        if (f == 0) {
            return "";
        }
        Resources resources = this.n.n().getResources();
        StringBuilder sb = new StringBuilder();
        if (ViewConfiguration.get(this.n.n()).hasPermanentMenuKey()) {
            sb.append(resources.getString(R.string.abc_prepend_shortcut_label));
        }
        int i2 = this.n.t() ? this.k : this.i;
        c(sb, i2, 65536, resources.getString(R.string.abc_menu_meta_shortcut_label));
        c(sb, i2, 4096, resources.getString(R.string.abc_menu_ctrl_shortcut_label));
        c(sb, i2, 2, resources.getString(R.string.abc_menu_alt_shortcut_label));
        c(sb, i2, 1, resources.getString(R.string.abc_menu_shift_shortcut_label));
        c(sb, i2, 4, resources.getString(R.string.abc_menu_sym_shortcut_label));
        c(sb, i2, 8, resources.getString(R.string.abc_menu_function_shortcut_label));
        if (f == '\b') {
            i = R.string.abc_menu_delete_shortcut_label;
        } else if (f == '\n') {
            i = R.string.abc_menu_enter_shortcut_label;
        } else {
            if (f != ' ') {
                sb.append(f);
                return sb.toString();
            }
            i = R.string.abc_menu_space_shortcut_label;
        }
        sb.append(resources.getString(i));
        return sb.toString();
    }

    @Override // android.view.MenuItem
    public ActionProvider getActionProvider() {
        throw new UnsupportedOperationException("This is not supported, use MenuItemCompat.getActionProvider()");
    }

    @Override // a.h.d.a.b, android.view.MenuItem
    public View getActionView() {
        View view = this.z;
        if (view != null) {
            return view;
        }
        a.h.h.b bVar = this.A;
        if (bVar == null) {
            return null;
        }
        View d2 = bVar.d(this);
        this.z = d2;
        return d2;
    }

    @Override // a.h.d.a.b, android.view.MenuItem
    public int getAlphabeticModifiers() {
        return this.k;
    }

    @Override // android.view.MenuItem
    public char getAlphabeticShortcut() {
        return this.j;
    }

    @Override // a.h.d.a.b, android.view.MenuItem
    public CharSequence getContentDescription() {
        return this.q;
    }

    @Override // android.view.MenuItem
    public int getGroupId() {
        return this.f664b;
    }

    @Override // android.view.MenuItem
    public Drawable getIcon() {
        Drawable drawable = this.l;
        if (drawable != null) {
            return d(drawable);
        }
        if (this.m == 0) {
            return null;
        }
        Drawable a2 = a.b.c.a.a.a(this.n.n(), this.m);
        this.m = 0;
        this.l = a2;
        return d(a2);
    }

    @Override // a.h.d.a.b, android.view.MenuItem
    public ColorStateList getIconTintList() {
        return this.s;
    }

    @Override // a.h.d.a.b, android.view.MenuItem
    public PorterDuff.Mode getIconTintMode() {
        return this.t;
    }

    @Override // android.view.MenuItem
    public Intent getIntent() {
        return this.g;
    }

    @Override // android.view.MenuItem
    @ViewDebug.CapturedViewProperty
    public int getItemId() {
        return this.f663a;
    }

    @Override // android.view.MenuItem
    public ContextMenu.ContextMenuInfo getMenuInfo() {
        return this.D;
    }

    @Override // a.h.d.a.b, android.view.MenuItem
    public int getNumericModifiers() {
        return this.i;
    }

    @Override // android.view.MenuItem
    public char getNumericShortcut() {
        return this.h;
    }

    @Override // android.view.MenuItem
    public int getOrder() {
        return this.f665c;
    }

    @Override // android.view.MenuItem
    public SubMenu getSubMenu() {
        return this.o;
    }

    @Override // android.view.MenuItem
    @ViewDebug.CapturedViewProperty
    public CharSequence getTitle() {
        return this.e;
    }

    @Override // android.view.MenuItem
    public CharSequence getTitleCondensed() {
        CharSequence charSequence = this.f;
        return charSequence != null ? charSequence : this.e;
    }

    @Override // a.h.d.a.b, android.view.MenuItem
    public CharSequence getTooltipText() {
        return this.r;
    }

    CharSequence h(n.a aVar) {
        return aVar.prefersCondensedTitle() ? getTitleCondensed() : this.e;
    }

    @Override // android.view.MenuItem
    public boolean hasSubMenu() {
        return this.o != null;
    }

    public boolean i() {
        a.h.h.b bVar;
        if ((this.y & 8) == 0) {
            return false;
        }
        if (this.z == null && (bVar = this.A) != null) {
            this.z = bVar.d(this);
        }
        return this.z != null;
    }

    @Override // a.h.d.a.b, android.view.MenuItem
    public boolean isActionViewExpanded() {
        return this.C;
    }

    @Override // android.view.MenuItem
    public boolean isCheckable() {
        return (this.x & 1) == 1;
    }

    @Override // android.view.MenuItem
    public boolean isChecked() {
        return (this.x & 2) == 2;
    }

    @Override // android.view.MenuItem
    public boolean isEnabled() {
        return (this.x & 16) != 0;
    }

    @Override // android.view.MenuItem
    public boolean isVisible() {
        a.h.h.b bVar = this.A;
        return (bVar == null || !bVar.g()) ? (this.x & 8) == 0 : (this.x & 8) == 0 && this.A.b();
    }

    public boolean j() {
        MenuItem.OnMenuItemClickListener onMenuItemClickListener = this.p;
        if (onMenuItemClickListener != null && onMenuItemClickListener.onMenuItemClick(this)) {
            return true;
        }
        g gVar = this.n;
        if (gVar.g(gVar, this)) {
            return true;
        }
        if (this.g != null) {
            try {
                this.n.n().startActivity(this.g);
                return true;
            } catch (ActivityNotFoundException e) {
                Log.e("MenuItemImpl", "Can't find activity to handle intent; ignoring", e);
            }
        }
        a.h.h.b bVar = this.A;
        return bVar != null && bVar.e();
    }

    public boolean k() {
        return (this.x & 32) == 32;
    }

    public boolean l() {
        return (this.x & 4) != 0;
    }

    public boolean m() {
        return (this.y & 1) == 1;
    }

    public a.h.d.a.b n(View view) {
        int i;
        this.z = view;
        this.A = null;
        if (view != null && view.getId() == -1 && (i = this.f663a) > 0) {
            view.setId(i);
        }
        this.n.v();
        return this;
    }

    public void o(boolean z) {
        this.C = z;
        this.n.x(false);
    }

    void p(boolean z) {
        int i = this.x;
        int i2 = (z ? 2 : 0) | (i & (-3));
        this.x = i2;
        if (i != i2) {
            this.n.x(false);
        }
    }

    public void q(boolean z) {
        this.x = (z ? 4 : 0) | (this.x & (-5));
    }

    public void r(boolean z) {
        this.x = z ? this.x | 32 : this.x & (-33);
    }

    public boolean requiresActionButton() {
        return (this.y & 2) == 2;
    }

    public boolean requiresOverflow() {
        return (requiresActionButton() || m()) ? false : true;
    }

    public void s(r rVar) {
        this.o = rVar;
        rVar.N(this.e);
    }

    @Override // android.view.MenuItem
    public MenuItem setActionProvider(ActionProvider actionProvider) {
        throw new UnsupportedOperationException("This is not supported, use MenuItemCompat.setActionProvider()");
    }

    @Override // a.h.d.a.b, android.view.MenuItem
    public MenuItem setActionView(int i) {
        Context n = this.n.n();
        n(LayoutInflater.from(n).inflate(i, (ViewGroup) new LinearLayout(n), false));
        return this;
    }

    @Override // a.h.d.a.b, android.view.MenuItem
    public /* bridge */ /* synthetic */ MenuItem setActionView(View view) {
        n(view);
        return this;
    }

    @Override // android.view.MenuItem
    public MenuItem setAlphabeticShortcut(char c2) {
        if (this.j == c2) {
            return this;
        }
        this.j = Character.toLowerCase(c2);
        this.n.x(false);
        return this;
    }

    @Override // a.h.d.a.b, android.view.MenuItem
    public MenuItem setAlphabeticShortcut(char c2, int i) {
        if (this.j == c2 && this.k == i) {
            return this;
        }
        this.j = Character.toLowerCase(c2);
        this.k = KeyEvent.normalizeMetaState(i);
        this.n.x(false);
        return this;
    }

    @Override // android.view.MenuItem
    public MenuItem setCheckable(boolean z) {
        int i = this.x;
        int i2 = (z ? 1 : 0) | (i & (-2));
        this.x = i2;
        if (i != i2) {
            this.n.x(false);
        }
        return this;
    }

    @Override // android.view.MenuItem
    public MenuItem setChecked(boolean z) {
        if ((this.x & 4) != 0) {
            this.n.I(this);
        } else {
            p(z);
        }
        return this;
    }

    @Override // a.h.d.a.b, android.view.MenuItem
    public a.h.d.a.b setContentDescription(CharSequence charSequence) {
        this.q = charSequence;
        this.n.x(false);
        return this;
    }

    @Override // android.view.MenuItem
    public MenuItem setContentDescription(CharSequence charSequence) {
        this.q = charSequence;
        this.n.x(false);
        return this;
    }

    @Override // android.view.MenuItem
    public MenuItem setEnabled(boolean z) {
        this.x = z ? this.x | 16 : this.x & (-17);
        this.n.x(false);
        return this;
    }

    @Override // android.view.MenuItem
    public MenuItem setIcon(int i) {
        this.l = null;
        this.m = i;
        this.w = true;
        this.n.x(false);
        return this;
    }

    @Override // android.view.MenuItem
    public MenuItem setIcon(Drawable drawable) {
        this.m = 0;
        this.l = drawable;
        this.w = true;
        this.n.x(false);
        return this;
    }

    @Override // a.h.d.a.b, android.view.MenuItem
    public MenuItem setIconTintList(ColorStateList colorStateList) {
        this.s = colorStateList;
        this.u = true;
        this.w = true;
        this.n.x(false);
        return this;
    }

    @Override // a.h.d.a.b, android.view.MenuItem
    public MenuItem setIconTintMode(PorterDuff.Mode mode) {
        this.t = mode;
        this.v = true;
        this.w = true;
        this.n.x(false);
        return this;
    }

    @Override // android.view.MenuItem
    public MenuItem setIntent(Intent intent) {
        this.g = intent;
        return this;
    }

    @Override // android.view.MenuItem
    public MenuItem setNumericShortcut(char c2) {
        if (this.h == c2) {
            return this;
        }
        this.h = c2;
        this.n.x(false);
        return this;
    }

    @Override // a.h.d.a.b, android.view.MenuItem
    public MenuItem setNumericShortcut(char c2, int i) {
        if (this.h == c2 && this.i == i) {
            return this;
        }
        this.h = c2;
        this.i = KeyEvent.normalizeMetaState(i);
        this.n.x(false);
        return this;
    }

    @Override // android.view.MenuItem
    public MenuItem setOnActionExpandListener(MenuItem.OnActionExpandListener onActionExpandListener) {
        this.B = onActionExpandListener;
        return this;
    }

    @Override // android.view.MenuItem
    public MenuItem setOnMenuItemClickListener(MenuItem.OnMenuItemClickListener onMenuItemClickListener) {
        this.p = onMenuItemClickListener;
        return this;
    }

    @Override // android.view.MenuItem
    public MenuItem setShortcut(char c2, char c3) {
        this.h = c2;
        this.j = Character.toLowerCase(c3);
        this.n.x(false);
        return this;
    }

    @Override // a.h.d.a.b, android.view.MenuItem
    public MenuItem setShortcut(char c2, char c3, int i, int i2) {
        this.h = c2;
        this.i = KeyEvent.normalizeMetaState(i);
        this.j = Character.toLowerCase(c3);
        this.k = KeyEvent.normalizeMetaState(i2);
        this.n.x(false);
        return this;
    }

    @Override // a.h.d.a.b, android.view.MenuItem
    public void setShowAsAction(int i) {
        int i2 = i & 3;
        if (i2 != 0 && i2 != 1 && i2 != 2) {
            throw new IllegalArgumentException("SHOW_AS_ACTION_ALWAYS, SHOW_AS_ACTION_IF_ROOM, and SHOW_AS_ACTION_NEVER are mutually exclusive.");
        }
        this.y = i;
        this.n.v();
    }

    @Override // a.h.d.a.b, android.view.MenuItem
    public MenuItem setShowAsActionFlags(int i) {
        setShowAsAction(i);
        return this;
    }

    @Override // android.view.MenuItem
    public MenuItem setTitle(int i) {
        setTitle(this.n.n().getString(i));
        return this;
    }

    @Override // android.view.MenuItem
    public MenuItem setTitle(CharSequence charSequence) {
        this.e = charSequence;
        this.n.x(false);
        r rVar = this.o;
        if (rVar != null) {
            rVar.N(charSequence);
        }
        return this;
    }

    @Override // android.view.MenuItem
    public MenuItem setTitleCondensed(CharSequence charSequence) {
        this.f = charSequence;
        this.n.x(false);
        return this;
    }

    @Override // a.h.d.a.b, android.view.MenuItem
    public a.h.d.a.b setTooltipText(CharSequence charSequence) {
        this.r = charSequence;
        this.n.x(false);
        return this;
    }

    @Override // android.view.MenuItem
    public MenuItem setTooltipText(CharSequence charSequence) {
        this.r = charSequence;
        this.n.x(false);
        return this;
    }

    @Override // android.view.MenuItem
    public MenuItem setVisible(boolean z) {
        if (t(z)) {
            this.n.w();
        }
        return this;
    }

    boolean t(boolean z) {
        int i = this.x;
        int i2 = (z ? 0 : 8) | (i & (-9));
        this.x = i2;
        return i != i2;
    }

    public String toString() {
        CharSequence charSequence = this.e;
        if (charSequence != null) {
            return charSequence.toString();
        }
        return null;
    }

    boolean u() {
        return this.n.u() && f() != 0;
    }

    public boolean v() {
        return (this.y & 4) == 4;
    }
}
