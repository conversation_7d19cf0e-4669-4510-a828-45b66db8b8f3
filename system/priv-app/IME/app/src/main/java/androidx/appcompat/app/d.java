package androidx.appcompat.app;

import a.b.f.b;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import androidx.core.app.d;

/* loaded from: classes.dex */
public class d extends androidx.fragment.app.d implements e, d.a {
    private f m;

    @Override // android.app.Activity
    public void addContentView(View view, ViewGroup.LayoutParams layoutParams) {
        n().d(view, layoutParams);
    }

    @Override // android.app.Activity, android.view.ContextThemeWrapper, android.content.ContextWrapper
    protected void attachBaseContext(Context context) {
        super.attachBaseContext(n().e(context));
    }

    @Override // android.app.Activity
    public void closeOptionsMenu() {
        o();
        if (getWindow().hasFeature(0)) {
            super.closeOptionsMenu();
        }
    }

    @Override // androidx.core.app.c, android.app.Activity, android.view.Window.Callback
    public boolean dispatchKeyEvent(KeyEvent keyEvent) {
        keyEvent.getKeyCode();
        o();
        return super.dispatchKeyEvent(keyEvent);
    }

    @Override // androidx.core.app.d.a
    public Intent e() {
        return androidx.core.app.b.g(this);
    }

    @Override // androidx.appcompat.app.e
    public void f(a.b.f.b bVar) {
    }

    @Override // android.app.Activity
    public <T extends View> T findViewById(int i) {
        return (T) n().f(i);
    }

    @Override // android.app.Activity
    public MenuInflater getMenuInflater() {
        return n().h();
    }

    @Override // android.view.ContextThemeWrapper, android.content.ContextWrapper, android.content.Context
    public Resources getResources() {
        return super.getResources();
    }

    @Override // androidx.appcompat.app.e
    public void h(a.b.f.b bVar) {
    }

    @Override // androidx.appcompat.app.e
    public a.b.f.b i(b.a aVar) {
        return null;
    }

    @Override // android.app.Activity
    public void invalidateOptionsMenu() {
        n().k();
    }

    @Override // androidx.fragment.app.d
    public void m() {
        n().k();
    }

    public f n() {
        if (this.m == null) {
            int i = f.f566c;
            this.m = new g(this, this);
        }
        return this.m;
    }

    public a o() {
        return n().i();
    }

    @Override // androidx.fragment.app.d, android.app.Activity, android.content.ComponentCallbacks
    public void onConfigurationChanged(Configuration configuration) {
        super.onConfigurationChanged(configuration);
        n().l(configuration);
    }

    @Override // android.app.Activity, android.view.Window.Callback
    public void onContentChanged() {
    }

    @Override // androidx.fragment.app.d, androidx.activity.ComponentActivity, androidx.core.app.c, android.app.Activity
    protected void onCreate(Bundle bundle) {
        f n = n();
        n.j();
        n.m(bundle);
        super.onCreate(bundle);
    }

    @Override // androidx.fragment.app.d, android.app.Activity
    protected void onDestroy() {
        super.onDestroy();
        n().n();
    }

    @Override // android.app.Activity, android.view.KeyEvent.Callback
    public boolean onKeyDown(int i, KeyEvent keyEvent) {
        return super.onKeyDown(i, keyEvent);
    }

    @Override // androidx.fragment.app.d, android.app.Activity, android.view.Window.Callback
    public final boolean onMenuItemSelected(int i, MenuItem menuItem) {
        Intent g;
        if (super.onMenuItemSelected(i, menuItem)) {
            return true;
        }
        a o = o();
        if (menuItem.getItemId() == 16908332 && o != null && (((p) o).e.b() & 4) != 0 && (g = androidx.core.app.b.g(this)) != null) {
            if (!shouldUpRecreateTask(g)) {
                navigateUpTo(g);
                return true;
            }
            androidx.core.app.d b2 = androidx.core.app.d.b(this);
            b2.a(this);
            b2.c();
            try {
                int i2 = androidx.core.app.a.f1051c;
                finishAffinity();
                return true;
            } catch (IllegalStateException unused) {
                finish();
                return true;
            }
        }
        return false;
    }

    @Override // android.app.Activity, android.view.Window.Callback
    public boolean onMenuOpened(int i, Menu menu) {
        return super.onMenuOpened(i, menu);
    }

    @Override // androidx.fragment.app.d, android.app.Activity, android.view.Window.Callback
    public void onPanelClosed(int i, Menu menu) {
        super.onPanelClosed(i, menu);
    }

    @Override // android.app.Activity
    protected void onPostCreate(Bundle bundle) {
        super.onPostCreate(bundle);
        n().o(bundle);
    }

    @Override // androidx.fragment.app.d, android.app.Activity
    protected void onPostResume() {
        super.onPostResume();
        n().p();
    }

    @Override // androidx.fragment.app.d, androidx.activity.ComponentActivity, androidx.core.app.c, android.app.Activity
    protected void onSaveInstanceState(Bundle bundle) {
        super.onSaveInstanceState(bundle);
        n().q(bundle);
    }

    @Override // androidx.fragment.app.d, android.app.Activity
    protected void onStart() {
        super.onStart();
        n().r();
    }

    @Override // androidx.fragment.app.d, android.app.Activity
    protected void onStop() {
        super.onStop();
        n().s();
    }

    @Override // android.app.Activity
    protected void onTitleChanged(CharSequence charSequence, int i) {
        super.onTitleChanged(charSequence, i);
        n().A(charSequence);
    }

    @Override // android.app.Activity
    public void openOptionsMenu() {
        o();
        if (getWindow().hasFeature(0)) {
            super.openOptionsMenu();
        }
    }

    @Override // android.app.Activity
    public void setContentView(int i) {
        n().w(i);
    }

    @Override // android.app.Activity
    public void setContentView(View view) {
        n().x(view);
    }

    @Override // android.app.Activity
    public void setContentView(View view, ViewGroup.LayoutParams layoutParams) {
        n().y(view, layoutParams);
    }

    @Override // android.app.Activity, android.view.ContextThemeWrapper, android.content.ContextWrapper, android.content.Context
    public void setTheme(int i) {
        super.setTheme(i);
        n().z(i);
    }
}
