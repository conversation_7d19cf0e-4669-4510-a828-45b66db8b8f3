package androidx.appcompat.app;

import a.h.h.q;
import a.h.h.w;
import android.view.View;

/* loaded from: classes.dex */
class h implements a.h.h.j {

    /* renamed from: a, reason: collision with root package name */
    final /* synthetic */ g f587a;

    h(g gVar) {
        this.f587a = gVar;
    }

    @Override // a.h.h.j
    public w a(View view, w wVar) {
        int i = wVar.i();
        int e0 = this.f587a.e0(wVar, null);
        if (i != e0) {
            int g = wVar.g();
            int h = wVar.h();
            int f = wVar.f();
            w.a aVar = new w.a(wVar);
            aVar.c(a.h.c.b.a(g, e0, h, f));
            wVar = aVar.a();
        }
        return q.j(view, wVar);
    }
}
