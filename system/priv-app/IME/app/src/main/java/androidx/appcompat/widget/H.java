package androidx.appcompat.widget;

import android.R;
import android.view.View;
import android.view.Window;

/* loaded from: classes.dex */
class H implements View.OnClickListener {

    /* renamed from: a, reason: collision with root package name */
    final androidx.appcompat.view.menu.a f731a;

    /* renamed from: b, reason: collision with root package name */
    final /* synthetic */ I f732b;

    H(I i) {
        this.f732b = i;
        this.f731a = new androidx.appcompat.view.menu.a(i.f733a.getContext(), 0, R.id.home, 0, i.i);
    }

    @Override // android.view.View.OnClickListener
    public void onClick(View view) {
        I i = this.f732b;
        Window.Callback callback = i.l;
        if (callback == null || !i.m) {
            return;
        }
        callback.onMenuItemSelected(0, this.f731a);
    }
}
