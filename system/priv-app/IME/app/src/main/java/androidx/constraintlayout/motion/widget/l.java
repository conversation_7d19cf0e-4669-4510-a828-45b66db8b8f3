package androidx.constraintlayout.motion.widget;

import a.f.b.a.d;
import android.graphics.Rect;
import android.util.Log;
import android.view.View;
import androidx.constraintlayout.widget.c;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.Objects;

/* loaded from: classes.dex */
class l implements Comparable<l> {

    /* renamed from: c, reason: collision with root package name */
    int f910c;

    /* renamed from: a, reason: collision with root package name */
    private float f908a = 1.0f;

    /* renamed from: b, reason: collision with root package name */
    int f909b = 0;

    /* renamed from: d, reason: collision with root package name */
    private float f911d = 0.0f;
    private float e = 0.0f;
    private float f = 0.0f;
    public float g = 0.0f;
    private float h = 1.0f;
    private float i = 1.0f;
    private float j = Float.NaN;
    private float k = Float.NaN;
    private float l = 0.0f;
    private float m = 0.0f;
    private float n = 0.0f;
    private float o = Float.NaN;
    private float p = Float.NaN;
    LinkedHashMap<String, androidx.constraintlayout.widget.a> q = new LinkedHashMap<>();

    private boolean c(float f, float f2) {
        return (Float.isNaN(f) || Float.isNaN(f2)) ? Float.isNaN(f) != Float.isNaN(f2) : Math.abs(f - f2) > 1.0E-6f;
    }

    /* JADX WARN: Failed to restore switch over string. Please report as a decompilation issue */
    public void a(HashMap<String, a.f.b.a.d> hashMap, int i) {
        String str;
        for (String str2 : hashMap.keySet()) {
            a.f.b.a.d dVar = hashMap.get(str2);
            str2.hashCode();
            char c2 = 65535;
            switch (str2.hashCode()) {
                case -1249320806:
                    if (str2.equals("rotationX")) {
                        c2 = 0;
                        break;
                    }
                    break;
                case -1249320805:
                    if (str2.equals("rotationY")) {
                        c2 = 1;
                        break;
                    }
                    break;
                case -1225497657:
                    if (str2.equals("translationX")) {
                        c2 = 2;
                        break;
                    }
                    break;
                case -1225497656:
                    if (str2.equals("translationY")) {
                        c2 = 3;
                        break;
                    }
                    break;
                case -1225497655:
                    if (str2.equals("translationZ")) {
                        c2 = 4;
                        break;
                    }
                    break;
                case -1001078227:
                    if (str2.equals("progress")) {
                        c2 = 5;
                        break;
                    }
                    break;
                case -908189618:
                    if (str2.equals("scaleX")) {
                        c2 = 6;
                        break;
                    }
                    break;
                case -908189617:
                    if (str2.equals("scaleY")) {
                        c2 = 7;
                        break;
                    }
                    break;
                case -760884510:
                    if (str2.equals("transformPivotX")) {
                        c2 = '\b';
                        break;
                    }
                    break;
                case -760884509:
                    if (str2.equals("transformPivotY")) {
                        c2 = '\t';
                        break;
                    }
                    break;
                case -40300674:
                    if (str2.equals("rotation")) {
                        c2 = '\n';
                        break;
                    }
                    break;
                case -4379043:
                    if (str2.equals("elevation")) {
                        c2 = 11;
                        break;
                    }
                    break;
                case 37232917:
                    if (str2.equals("transitionPathRotate")) {
                        c2 = '\f';
                        break;
                    }
                    break;
                case 92909918:
                    if (str2.equals("alpha")) {
                        c2 = '\r';
                        break;
                    }
                    break;
            }
            float f = 1.0f;
            float f2 = 0.0f;
            switch (c2) {
                case 0:
                    if (!Float.isNaN(this.f)) {
                        f2 = this.f;
                    }
                    dVar.b(i, f2);
                    break;
                case 1:
                    if (!Float.isNaN(this.g)) {
                        f2 = this.g;
                    }
                    dVar.b(i, f2);
                    break;
                case 2:
                    if (!Float.isNaN(this.l)) {
                        f2 = this.l;
                    }
                    dVar.b(i, f2);
                    break;
                case 3:
                    if (!Float.isNaN(this.m)) {
                        f2 = this.m;
                    }
                    dVar.b(i, f2);
                    break;
                case 4:
                    if (!Float.isNaN(this.n)) {
                        f2 = this.n;
                    }
                    dVar.b(i, f2);
                    break;
                case 5:
                    if (!Float.isNaN(this.p)) {
                        f2 = this.p;
                    }
                    dVar.b(i, f2);
                    break;
                case 6:
                    if (!Float.isNaN(this.h)) {
                        f = this.h;
                    }
                    dVar.b(i, f);
                    break;
                case MotionLayout.TOUCH_UP_NEVER_TO_END /* 7 */:
                    if (!Float.isNaN(this.i)) {
                        f = this.i;
                    }
                    dVar.b(i, f);
                    break;
                case '\b':
                    if (!Float.isNaN(this.j)) {
                        f2 = this.j;
                    }
                    dVar.b(i, f2);
                    break;
                case '\t':
                    if (!Float.isNaN(this.k)) {
                        f2 = this.k;
                    }
                    dVar.b(i, f2);
                    break;
                case '\n':
                    if (!Float.isNaN(this.e)) {
                        f2 = this.e;
                    }
                    dVar.b(i, f2);
                    break;
                case 11:
                    if (!Float.isNaN(this.f911d)) {
                        f2 = this.f911d;
                    }
                    dVar.b(i, f2);
                    break;
                case '\f':
                    if (!Float.isNaN(this.o)) {
                        f2 = this.o;
                    }
                    dVar.b(i, f2);
                    break;
                case '\r':
                    if (!Float.isNaN(this.f908a)) {
                        f = this.f908a;
                    }
                    dVar.b(i, f);
                    break;
                default:
                    if (str2.startsWith("CUSTOM")) {
                        String str3 = str2.split(",")[1];
                        if (!this.q.containsKey(str3)) {
                            break;
                        } else {
                            androidx.constraintlayout.widget.a aVar = this.q.get(str3);
                            if (dVar instanceof d.b) {
                                ((d.b) dVar).g(i, aVar);
                                break;
                            } else {
                                str = str2 + " ViewSpline not a CustomSet frame = " + i + ", value" + aVar.d() + dVar;
                            }
                        }
                    } else {
                        str = "UNKNOWN spline " + str2;
                    }
                    Log.e("MotionPaths", str);
                    break;
            }
        }
    }

    public void b(View view) {
        this.f910c = view.getVisibility();
        this.f908a = view.getVisibility() != 0 ? 0.0f : view.getAlpha();
        this.f911d = view.getElevation();
        this.e = view.getRotation();
        this.f = view.getRotationX();
        this.g = view.getRotationY();
        this.h = view.getScaleX();
        this.i = view.getScaleY();
        this.j = view.getPivotX();
        this.k = view.getPivotY();
        this.l = view.getTranslationX();
        this.m = view.getTranslationY();
        this.n = view.getTranslationZ();
    }

    @Override // java.lang.Comparable
    public int compareTo(l lVar) {
        Objects.requireNonNull(lVar);
        return Float.compare(0.0f, 0.0f);
    }

    void d(l lVar, HashSet<String> hashSet) {
        if (c(this.f908a, lVar.f908a)) {
            hashSet.add("alpha");
        }
        if (c(this.f911d, lVar.f911d)) {
            hashSet.add("elevation");
        }
        int i = this.f910c;
        int i2 = lVar.f910c;
        if (i != i2 && this.f909b == 0 && (i == 0 || i2 == 0)) {
            hashSet.add("alpha");
        }
        if (c(this.e, lVar.e)) {
            hashSet.add("rotation");
        }
        if (!Float.isNaN(this.o) || !Float.isNaN(lVar.o)) {
            hashSet.add("transitionPathRotate");
        }
        if (!Float.isNaN(this.p) || !Float.isNaN(lVar.p)) {
            hashSet.add("progress");
        }
        if (c(this.f, lVar.f)) {
            hashSet.add("rotationX");
        }
        if (c(this.g, lVar.g)) {
            hashSet.add("rotationY");
        }
        if (c(this.j, lVar.j)) {
            hashSet.add("transformPivotX");
        }
        if (c(this.k, lVar.k)) {
            hashSet.add("transformPivotY");
        }
        if (c(this.h, lVar.h)) {
            hashSet.add("scaleX");
        }
        if (c(this.i, lVar.i)) {
            hashSet.add("scaleY");
        }
        if (c(this.l, lVar.l)) {
            hashSet.add("translationX");
        }
        if (c(this.m, lVar.m)) {
            hashSet.add("translationY");
        }
        if (c(this.n, lVar.n)) {
            hashSet.add("translationZ");
        }
    }

    public void e(Rect rect, View view, int i, float f) {
        float f2;
        rect.width();
        rect.height();
        b(view);
        this.j = Float.NaN;
        this.k = Float.NaN;
        if (i == 1) {
            f2 = f - 90.0f;
        } else if (i != 2) {
            return;
        } else {
            f2 = f + 90.0f;
        }
        this.e = f2;
    }

    public void f(Rect rect, androidx.constraintlayout.widget.c cVar, int i, int i2) {
        float f;
        int i3 = rect.left;
        rect.width();
        rect.height();
        c.a t = cVar.t(i2);
        c.d dVar = t.f997c;
        int i4 = dVar.f1013c;
        this.f909b = i4;
        int i5 = dVar.f1012b;
        this.f910c = i5;
        this.f908a = (i5 == 0 || i4 != 0) ? dVar.f1014d : 0.0f;
        c.e eVar = t.f;
        boolean z = eVar.m;
        this.f911d = eVar.n;
        this.e = eVar.f1016b;
        this.f = eVar.f1017c;
        this.g = eVar.f1018d;
        this.h = eVar.e;
        this.i = eVar.f;
        this.j = eVar.g;
        this.k = eVar.h;
        this.l = eVar.j;
        this.m = eVar.k;
        this.n = eVar.l;
        a.f.a.i.a.c.c(t.f998d.f1010d);
        this.o = t.f998d.i;
        this.p = t.f997c.e;
        for (String str : t.g.keySet()) {
            androidx.constraintlayout.widget.a aVar = t.g.get(str);
            if (aVar.f()) {
                this.q.put(str, aVar);
            }
        }
        float f2 = 90.0f;
        if (i != 1) {
            if (i != 2) {
                if (i != 3) {
                    if (i != 4) {
                        return;
                    }
                }
            }
            f = this.e + 90.0f;
            this.e = f;
            if (f > 180.0f) {
                f2 = 360.0f;
                this.e = f - f2;
            }
            return;
        }
        f = this.e;
        this.e = f - f2;
    }

    public void g(View view) {
        view.getX();
        view.getY();
        view.getWidth();
        view.getHeight();
        b(view);
    }
}
