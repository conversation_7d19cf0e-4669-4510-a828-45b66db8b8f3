package androidx.appcompat.widget;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Parcelable;
import android.util.SparseArray;
import android.view.Menu;
import android.view.ViewGroup;
import android.view.Window;
import androidx.appcompat.view.menu.m;

/* loaded from: classes.dex */
public interface p {
    void a();

    int b();

    void c(int i);

    boolean canShowOverflowMenu();

    void collapseActionView();

    void d(SparseArray<Parcelable> sparseArray);

    void e(int i);

    void f(ScrollingTabContainerView scrollingTabContainerView);

    ViewGroup g();

    CharSequence getTitle();

    void h(boolean z);

    boolean hideOverflowMenu();

    Context i();

    boolean isOverflowMenuShowPending();

    boolean isOverflowMenuShowing();

    int j();

    void k(SparseArray<Parcelable> sparseArray);

    a.h.h.s l(int i, long j);

    void m();

    boolean n();

    boolean o();

    boolean p();

    void q();

    void r(boolean z);

    void s(int i);

    void setIcon(int i);

    void setIcon(Drawable drawable);

    void setMenu(Menu menu, m.a aVar);

    void setMenuPrepared();

    void setWindowCallback(Window.Callback callback);

    void setWindowTitle(CharSequence charSequence);

    boolean showOverflowMenu();
}
