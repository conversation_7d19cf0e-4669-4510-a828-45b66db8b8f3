package androidx.constraintlayout.motion.widget;

import android.graphics.Rect;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import androidx.constraintlayout.motion.widget.t;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.g;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Objects;

/* loaded from: classes.dex */
public class u {

    /* renamed from: a, reason: collision with root package name */
    private final MotionLayout f946a;

    /* renamed from: c, reason: collision with root package name */
    private HashSet<View> f948c;
    ArrayList<t.a> e;

    /* renamed from: b, reason: collision with root package name */
    private ArrayList<t> f947b = new ArrayList<>();

    /* renamed from: d, reason: collision with root package name */
    private String f949d = "ViewTransitionController";
    ArrayList<t.a> f = new ArrayList<>();

    class a implements g.a {
        a(u uVar, t tVar, int i, boolean z, int i2) {
        }
    }

    public u(MotionLayout motionLayout) {
        this.f946a = motionLayout;
    }

    private void f(t tVar, boolean z) {
        ConstraintLayout.getSharedValues().a(tVar.e(), new a(this, tVar, tVar.e(), z, tVar.d()));
    }

    public void a(t tVar) {
        boolean z;
        this.f947b.add(tVar);
        this.f948c = null;
        if (tVar.f() == 4) {
            z = true;
        } else if (tVar.f() != 5) {
            return;
        } else {
            z = false;
        }
        f(tVar, z);
    }

    boolean b(int i, n nVar) {
        Iterator<t> it = this.f947b.iterator();
        while (it.hasNext()) {
            t next = it.next();
            if (next.c() == i) {
                next.f.a(nVar);
                return true;
            }
        }
        return false;
    }

    void c(int i, boolean z) {
        Iterator<t> it = this.f947b.iterator();
        while (it.hasNext()) {
            t next = it.next();
            if (next.c() == i) {
                next.k(z);
                return;
            }
        }
    }

    void d() {
        this.f946a.invalidate();
    }

    boolean e(int i) {
        Iterator<t> it = this.f947b.iterator();
        while (it.hasNext()) {
            t next = it.next();
            if (next.c() == i) {
                return next.g();
            }
        }
        return false;
    }

    void g(MotionEvent motionEvent) {
        t tVar;
        int currentState = this.f946a.getCurrentState();
        if (currentState == -1) {
            return;
        }
        if (this.f948c == null) {
            this.f948c = new HashSet<>();
            Iterator<t> it = this.f947b.iterator();
            while (it.hasNext()) {
                t next = it.next();
                int childCount = this.f946a.getChildCount();
                for (int i = 0; i < childCount; i++) {
                    View childAt = this.f946a.getChildAt(i);
                    if (next.i(childAt)) {
                        childAt.getId();
                        this.f948c.add(childAt);
                    }
                }
            }
        }
        float x = motionEvent.getX();
        float y = motionEvent.getY();
        Rect rect = new Rect();
        int action = motionEvent.getAction();
        ArrayList<t.a> arrayList = this.e;
        if (arrayList != null && !arrayList.isEmpty()) {
            Iterator<t.a> it2 = this.e.iterator();
            while (it2.hasNext()) {
                t.a next2 = it2.next();
                Objects.requireNonNull(next2);
                if (action != 1) {
                    if (action == 2) {
                        next2.f945d.f914b.getHitRect(next2.m);
                        if (!next2.m.contains((int) x, (int) y) && !next2.i) {
                            next2.b(true);
                        }
                    }
                } else if (!next2.i) {
                    next2.b(true);
                }
            }
        }
        if (action == 0 || action == 1) {
            androidx.constraintlayout.widget.c constraintSet = this.f946a.getConstraintSet(currentState);
            Iterator<t> it3 = this.f947b.iterator();
            while (it3.hasNext()) {
                t next3 = it3.next();
                if (next3.l(action)) {
                    Iterator<View> it4 = this.f948c.iterator();
                    while (it4.hasNext()) {
                        View next4 = it4.next();
                        if (next3.i(next4)) {
                            next4.getHitRect(rect);
                            if (rect.contains((int) x, (int) y)) {
                                tVar = next3;
                                next3.a(this, this.f946a, currentState, constraintSet, next4);
                            } else {
                                tVar = next3;
                            }
                            next3 = tVar;
                        }
                    }
                }
            }
        }
    }

    void h(int i, View... viewArr) {
        ArrayList arrayList = new ArrayList();
        Iterator<t> it = this.f947b.iterator();
        t tVar = null;
        while (it.hasNext()) {
            t next = it.next();
            if (next.c() == i) {
                for (View view : viewArr) {
                    if (next.b(view)) {
                        arrayList.add(view);
                    }
                }
                if (!arrayList.isEmpty()) {
                    View[] viewArr2 = (View[]) arrayList.toArray(new View[0]);
                    int currentState = this.f946a.getCurrentState();
                    if (next.e == 2) {
                        next.a(this, this.f946a, currentState, null, viewArr2);
                    } else if (currentState == -1) {
                        String str = this.f949d;
                        StringBuilder j = b.b.a.a.a.j("No support for ViewTransition within transition yet. Currently: ");
                        j.append(this.f946a.toString());
                        Log.w(str, j.toString());
                    } else {
                        androidx.constraintlayout.widget.c constraintSet = this.f946a.getConstraintSet(currentState);
                        if (constraintSet != null) {
                            next.a(this, this.f946a, currentState, constraintSet, viewArr2);
                        }
                    }
                    arrayList.clear();
                }
                tVar = next;
            }
        }
        if (tVar == null) {
            Log.e(this.f949d, " Could not find ViewTransition");
        }
    }
}
