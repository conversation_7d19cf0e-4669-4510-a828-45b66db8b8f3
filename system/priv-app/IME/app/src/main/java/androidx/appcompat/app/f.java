package androidx.appcompat.app;

import android.content.Context;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.MenuInflater;
import android.view.View;
import android.view.ViewGroup;
import java.lang.ref.WeakReference;
import java.util.Iterator;

/* loaded from: classes.dex */
public abstract class f {

    /* renamed from: a, reason: collision with root package name */
    private static final a.e.c<WeakReference<f>> f564a = new a.e.c<>();

    /* renamed from: b, reason: collision with root package name */
    private static final Object f565b = new Object();

    /* renamed from: c, reason: collision with root package name */
    public static final /* synthetic */ int f566c = 0;

    f() {
    }

    static void c(f fVar) {
        synchronized (f565b) {
            u(fVar);
            f564a.add(new WeakReference<>(fVar));
        }
    }

    static void t(f fVar) {
        synchronized (f565b) {
            u(fVar);
        }
    }

    private static void u(f fVar) {
        synchronized (f565b) {
            Iterator<WeakReference<f>> it = f564a.iterator();
            while (it.hasNext()) {
                f fVar2 = it.next().get();
                if (fVar2 == fVar || fVar2 == null) {
                    it.remove();
                }
            }
        }
    }

    public abstract void A(CharSequence charSequence);

    public abstract void d(View view, ViewGroup.LayoutParams layoutParams);

    public Context e(Context context) {
        return context;
    }

    public abstract <T extends View> T f(int i);

    public int g() {
        return -100;
    }

    public abstract MenuInflater h();

    public abstract a i();

    public abstract void j();

    public abstract void k();

    public abstract void l(Configuration configuration);

    public abstract void m(Bundle bundle);

    public abstract void n();

    public abstract void o(Bundle bundle);

    public abstract void p();

    public abstract void q(Bundle bundle);

    public abstract void r();

    public abstract void s();

    public abstract boolean v(int i);

    public abstract void w(int i);

    public abstract void x(View view);

    public abstract void y(View view, ViewGroup.LayoutParams layoutParams);

    public void z(int i) {
    }
}
