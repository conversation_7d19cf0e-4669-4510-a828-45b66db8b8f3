package androidx.constraintlayout.widget;

import a.f.a.j.j;
import android.content.Context;
import android.content.res.TypedArray;
import android.content.res.XmlResourceParser;
import android.graphics.drawable.ColorDrawable;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseArray;
import android.util.SparseIntArray;
import android.util.Xml;
import android.view.View;
import android.view.ViewGroup;
import androidx.constraintlayout.motion.widget.MotionLayout;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.Constraints;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.button.MaterialButton;
import com.hozon.inputmethod.speech.RecognitionProgressView;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

/* loaded from: classes.dex */
public class c {
    private static final int[] g = {0, 4, 8};
    private static SparseIntArray h = new SparseIntArray();
    private static SparseIntArray i = new SparseIntArray();
    public static final /* synthetic */ int j = 0;

    /* renamed from: a, reason: collision with root package name */
    public String f991a;

    /* renamed from: b, reason: collision with root package name */
    public String f992b = "";

    /* renamed from: c, reason: collision with root package name */
    public int f993c = 0;

    /* renamed from: d, reason: collision with root package name */
    private HashMap<String, androidx.constraintlayout.widget.a> f994d = new HashMap<>();
    private boolean e = true;
    private HashMap<Integer, a> f = new HashMap<>();

    public static class a {

        /* renamed from: a, reason: collision with root package name */
        int f995a;

        /* renamed from: b, reason: collision with root package name */
        String f996b;

        /* renamed from: c, reason: collision with root package name */
        public final d f997c = new d();

        /* renamed from: d, reason: collision with root package name */
        public final C0034c f998d = new C0034c();
        public final b e = new b();
        public final e f = new e();
        public HashMap<String, androidx.constraintlayout.widget.a> g = new HashMap<>();
        C0033a h;

        /* renamed from: androidx.constraintlayout.widget.c$a$a, reason: collision with other inner class name */
        static class C0033a {

            /* renamed from: a, reason: collision with root package name */
            int[] f999a = new int[10];

            /* renamed from: b, reason: collision with root package name */
            int[] f1000b = new int[10];

            /* renamed from: c, reason: collision with root package name */
            int f1001c = 0;

            /* renamed from: d, reason: collision with root package name */
            int[] f1002d = new int[10];
            float[] e = new float[10];
            int f = 0;
            int[] g = new int[5];
            String[] h = new String[5];
            int i = 0;
            int[] j = new int[4];
            boolean[] k = new boolean[4];
            int l = 0;

            C0033a() {
            }

            void a(int i, float f) {
                int i2 = this.f;
                int[] iArr = this.f1002d;
                if (i2 >= iArr.length) {
                    this.f1002d = Arrays.copyOf(iArr, iArr.length * 2);
                    float[] fArr = this.e;
                    this.e = Arrays.copyOf(fArr, fArr.length * 2);
                }
                int[] iArr2 = this.f1002d;
                int i3 = this.f;
                iArr2[i3] = i;
                float[] fArr2 = this.e;
                this.f = i3 + 1;
                fArr2[i3] = f;
            }

            void b(int i, int i2) {
                int i3 = this.f1001c;
                int[] iArr = this.f999a;
                if (i3 >= iArr.length) {
                    this.f999a = Arrays.copyOf(iArr, iArr.length * 2);
                    int[] iArr2 = this.f1000b;
                    this.f1000b = Arrays.copyOf(iArr2, iArr2.length * 2);
                }
                int[] iArr3 = this.f999a;
                int i4 = this.f1001c;
                iArr3[i4] = i;
                int[] iArr4 = this.f1000b;
                this.f1001c = i4 + 1;
                iArr4[i4] = i2;
            }

            void c(int i, String str) {
                int i2 = this.i;
                int[] iArr = this.g;
                if (i2 >= iArr.length) {
                    this.g = Arrays.copyOf(iArr, iArr.length * 2);
                    String[] strArr = this.h;
                    this.h = (String[]) Arrays.copyOf(strArr, strArr.length * 2);
                }
                int[] iArr2 = this.g;
                int i3 = this.i;
                iArr2[i3] = i;
                String[] strArr2 = this.h;
                this.i = i3 + 1;
                strArr2[i3] = str;
            }

            void d(int i, boolean z) {
                int i2 = this.l;
                int[] iArr = this.j;
                if (i2 >= iArr.length) {
                    this.j = Arrays.copyOf(iArr, iArr.length * 2);
                    boolean[] zArr = this.k;
                    this.k = Arrays.copyOf(zArr, zArr.length * 2);
                }
                int[] iArr2 = this.j;
                int i3 = this.l;
                iArr2[i3] = i;
                boolean[] zArr2 = this.k;
                this.l = i3 + 1;
                zArr2[i3] = z;
            }

            void e(a aVar) {
                for (int i = 0; i < this.f1001c; i++) {
                    int i2 = this.f999a[i];
                    int i3 = this.f1000b[i];
                    int i4 = c.j;
                    if (i2 == 6) {
                        aVar.e.D = i3;
                    } else if (i2 == 7) {
                        aVar.e.E = i3;
                    } else if (i2 == 8) {
                        aVar.e.K = i3;
                    } else if (i2 == 27) {
                        aVar.e.F = i3;
                    } else if (i2 == 28) {
                        aVar.e.H = i3;
                    } else if (i2 == 41) {
                        aVar.e.W = i3;
                    } else if (i2 == 42) {
                        aVar.e.X = i3;
                    } else if (i2 == 61) {
                        aVar.e.A = i3;
                    } else if (i2 == 62) {
                        aVar.e.B = i3;
                    } else if (i2 == 72) {
                        aVar.e.g0 = i3;
                    } else if (i2 == 73) {
                        aVar.e.h0 = i3;
                    } else if (i2 == 2) {
                        aVar.e.J = i3;
                    } else if (i2 == 31) {
                        aVar.e.L = i3;
                    } else if (i2 == 34) {
                        aVar.e.I = i3;
                    } else if (i2 == 38) {
                        aVar.f995a = i3;
                    } else if (i2 == 64) {
                        aVar.f998d.f1008b = i3;
                    } else if (i2 == 66) {
                        aVar.f998d.f = i3;
                    } else if (i2 == 76) {
                        aVar.f998d.e = i3;
                    } else if (i2 == 78) {
                        aVar.f997c.f1013c = i3;
                    } else if (i2 == 97) {
                        aVar.e.p0 = i3;
                    } else if (i2 == 93) {
                        aVar.e.M = i3;
                    } else if (i2 != 94) {
                        switch (i2) {
                            case 11:
                                aVar.e.Q = i3;
                                break;
                            case 12:
                                aVar.e.R = i3;
                                break;
                            case 13:
                                aVar.e.N = i3;
                                break;
                            case 14:
                                aVar.e.P = i3;
                                break;
                            case 15:
                                aVar.e.S = i3;
                                break;
                            case MaterialButton.ICON_GRAVITY_TOP /* 16 */:
                                aVar.e.O = i3;
                                break;
                            case 17:
                                aVar.e.e = i3;
                                break;
                            case 18:
                                aVar.e.f = i3;
                                break;
                            default:
                                switch (i2) {
                                    case 21:
                                        aVar.e.f1006d = i3;
                                        break;
                                    case 22:
                                        aVar.f997c.f1012b = i3;
                                        break;
                                    case 23:
                                        aVar.e.f1005c = i3;
                                        break;
                                    case 24:
                                        aVar.e.G = i3;
                                        break;
                                    default:
                                        switch (i2) {
                                            case 54:
                                                aVar.e.Y = i3;
                                                break;
                                            case 55:
                                                aVar.e.Z = i3;
                                                break;
                                            case 56:
                                                aVar.e.a0 = i3;
                                                break;
                                            case 57:
                                                aVar.e.b0 = i3;
                                                break;
                                            case 58:
                                                aVar.e.c0 = i3;
                                                break;
                                            case 59:
                                                aVar.e.d0 = i3;
                                                break;
                                            default:
                                                switch (i2) {
                                                    case 82:
                                                        aVar.f998d.f1009c = i3;
                                                        break;
                                                    case 83:
                                                        aVar.f.i = i3;
                                                        break;
                                                    case 84:
                                                        aVar.f998d.k = i3;
                                                        break;
                                                    default:
                                                        switch (i2) {
                                                            case 87:
                                                                break;
                                                            case 88:
                                                                aVar.f998d.m = i3;
                                                                break;
                                                            case 89:
                                                                aVar.f998d.n = i3;
                                                                break;
                                                            default:
                                                                Log.w("ConstraintSet", "Unknown attribute 0x");
                                                                break;
                                                        }
                                                }
                                        }
                                }
                        }
                    } else {
                        aVar.e.T = i3;
                    }
                }
                for (int i5 = 0; i5 < this.f; i5++) {
                    int i6 = this.f1002d[i5];
                    float f = this.e[i5];
                    int i7 = c.j;
                    if (i6 == 19) {
                        aVar.e.g = f;
                    } else if (i6 == 20) {
                        aVar.e.x = f;
                    } else if (i6 == 37) {
                        aVar.e.y = f;
                    } else if (i6 == 60) {
                        aVar.f.f1016b = f;
                    } else if (i6 == 63) {
                        aVar.e.C = f;
                    } else if (i6 == 79) {
                        aVar.f998d.g = f;
                    } else if (i6 == 85) {
                        aVar.f998d.j = f;
                    } else if (i6 != 87) {
                        if (i6 == 39) {
                            aVar.e.V = f;
                        } else if (i6 != 40) {
                            switch (i6) {
                                case 43:
                                    aVar.f997c.f1014d = f;
                                    break;
                                case 44:
                                    e eVar = aVar.f;
                                    eVar.n = f;
                                    eVar.m = true;
                                    break;
                                case RecognitionProgressView.BARS_COUNT /* 45 */:
                                    aVar.f.f1017c = f;
                                    break;
                                case 46:
                                    aVar.f.f1018d = f;
                                    break;
                                case 47:
                                    aVar.f.e = f;
                                    break;
                                case 48:
                                    aVar.f.f = f;
                                    break;
                                case 49:
                                    aVar.f.g = f;
                                    break;
                                case 50:
                                    aVar.f.h = f;
                                    break;
                                case 51:
                                    aVar.f.j = f;
                                    break;
                                case 52:
                                    aVar.f.k = f;
                                    break;
                                case 53:
                                    aVar.f.l = f;
                                    break;
                                default:
                                    switch (i6) {
                                        case 67:
                                            aVar.f998d.i = f;
                                            break;
                                        case 68:
                                            aVar.f997c.e = f;
                                            break;
                                        case 69:
                                            aVar.e.e0 = f;
                                            break;
                                        case 70:
                                            aVar.e.f0 = f;
                                            break;
                                        default:
                                            Log.w("ConstraintSet", "Unknown attribute 0x");
                                            break;
                                    }
                            }
                        } else {
                            aVar.e.U = f;
                        }
                    }
                }
                for (int i8 = 0; i8 < this.i; i8++) {
                    int i9 = this.g[i8];
                    String str = this.h[i8];
                    int i10 = c.j;
                    if (i9 == 5) {
                        aVar.e.z = str;
                    } else if (i9 == 65) {
                        aVar.f998d.f1010d = str;
                    } else if (i9 == 74) {
                        b bVar = aVar.e;
                        bVar.k0 = str;
                        bVar.j0 = null;
                    } else if (i9 == 77) {
                        aVar.e.l0 = str;
                    } else if (i9 != 87) {
                        if (i9 != 90) {
                            Log.w("ConstraintSet", "Unknown attribute 0x");
                        } else {
                            aVar.f998d.l = str;
                        }
                    }
                }
                for (int i11 = 0; i11 < this.l; i11++) {
                    int i12 = this.j[i11];
                    boolean z = this.k[i11];
                    int i13 = c.j;
                    if (i12 == 44) {
                        aVar.f.m = z;
                    } else if (i12 == 75) {
                        aVar.e.o0 = z;
                    } else if (i12 != 87) {
                        if (i12 == 80) {
                            aVar.e.m0 = z;
                        } else if (i12 != 81) {
                            Log.w("ConstraintSet", "Unknown attribute 0x");
                        } else {
                            aVar.e.n0 = z;
                        }
                    }
                }
            }
        }

        static void b(a aVar, ConstraintHelper constraintHelper, int i, Constraints.a aVar2) {
            aVar.h(i, aVar2);
            if (constraintHelper instanceof Barrier) {
                b bVar = aVar.e;
                bVar.i0 = 1;
                Barrier barrier = (Barrier) constraintHelper;
                bVar.g0 = barrier.getType();
                aVar.e.j0 = barrier.getReferencedIds();
                aVar.e.h0 = barrier.getMargin();
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void g(int i, ConstraintLayout.a aVar) {
            this.f995a = i;
            b bVar = this.e;
            bVar.i = aVar.e;
            bVar.j = aVar.f;
            bVar.k = aVar.g;
            bVar.l = aVar.h;
            bVar.m = aVar.i;
            bVar.n = aVar.j;
            bVar.o = aVar.k;
            bVar.p = aVar.l;
            bVar.q = aVar.m;
            bVar.r = aVar.n;
            bVar.s = aVar.o;
            bVar.t = aVar.s;
            bVar.u = aVar.t;
            bVar.v = aVar.u;
            bVar.w = aVar.v;
            bVar.x = aVar.E;
            bVar.y = aVar.F;
            bVar.z = aVar.G;
            bVar.A = aVar.p;
            bVar.B = aVar.q;
            bVar.C = aVar.r;
            bVar.D = aVar.T;
            bVar.E = aVar.U;
            bVar.F = aVar.V;
            bVar.g = aVar.f964c;
            b bVar2 = this.e;
            bVar2.e = aVar.f962a;
            bVar2.f = aVar.f963b;
            bVar2.f1005c = ((ViewGroup.MarginLayoutParams) aVar).width;
            bVar2.f1006d = ((ViewGroup.MarginLayoutParams) aVar).height;
            bVar2.G = ((ViewGroup.MarginLayoutParams) aVar).leftMargin;
            bVar2.H = ((ViewGroup.MarginLayoutParams) aVar).rightMargin;
            bVar2.I = ((ViewGroup.MarginLayoutParams) aVar).topMargin;
            bVar2.J = ((ViewGroup.MarginLayoutParams) aVar).bottomMargin;
            bVar2.M = aVar.D;
            bVar2.U = aVar.I;
            bVar2.V = aVar.H;
            bVar2.X = aVar.K;
            bVar2.W = aVar.J;
            bVar2.m0 = aVar.W;
            bVar2.n0 = aVar.X;
            bVar2.Y = aVar.L;
            bVar2.Z = aVar.M;
            bVar2.a0 = aVar.P;
            bVar2.b0 = aVar.Q;
            bVar2.c0 = aVar.N;
            bVar2.d0 = aVar.O;
            bVar2.e0 = aVar.R;
            bVar2.f0 = aVar.S;
            bVar2.l0 = aVar.Y;
            bVar2.O = aVar.x;
            b bVar3 = this.e;
            bVar3.Q = aVar.z;
            bVar3.N = aVar.w;
            bVar3.P = aVar.y;
            bVar3.S = aVar.A;
            bVar3.R = aVar.B;
            bVar3.T = aVar.C;
            bVar3.p0 = aVar.Z;
            bVar3.K = aVar.getMarginEnd();
            this.e.L = aVar.getMarginStart();
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void h(int i, Constraints.a aVar) {
            g(i, aVar);
            this.f997c.f1014d = aVar.r0;
            e eVar = this.f;
            eVar.f1016b = aVar.u0;
            eVar.f1017c = aVar.v0;
            eVar.f1018d = aVar.w0;
            eVar.e = aVar.x0;
            eVar.f = aVar.y0;
            eVar.g = aVar.z0;
            eVar.h = aVar.A0;
            eVar.j = aVar.B0;
            eVar.k = aVar.C0;
            eVar.l = aVar.D0;
            eVar.n = aVar.t0;
            eVar.m = aVar.s0;
        }

        public void d(a aVar) {
            C0033a c0033a = this.h;
            if (c0033a != null) {
                c0033a.e(aVar);
            }
        }

        public void e(ConstraintLayout.a aVar) {
            b bVar = this.e;
            aVar.e = bVar.i;
            aVar.f = bVar.j;
            aVar.g = bVar.k;
            aVar.h = bVar.l;
            aVar.i = bVar.m;
            aVar.j = bVar.n;
            aVar.k = bVar.o;
            aVar.l = bVar.p;
            aVar.m = bVar.q;
            aVar.n = bVar.r;
            aVar.o = bVar.s;
            aVar.s = bVar.t;
            aVar.t = bVar.u;
            aVar.u = bVar.v;
            aVar.v = bVar.w;
            ((ViewGroup.MarginLayoutParams) aVar).leftMargin = bVar.G;
            ((ViewGroup.MarginLayoutParams) aVar).rightMargin = bVar.H;
            ((ViewGroup.MarginLayoutParams) aVar).topMargin = bVar.I;
            ((ViewGroup.MarginLayoutParams) aVar).bottomMargin = bVar.J;
            aVar.A = bVar.S;
            aVar.B = bVar.R;
            aVar.x = bVar.O;
            aVar.z = bVar.Q;
            aVar.E = bVar.x;
            aVar.F = bVar.y;
            b bVar2 = this.e;
            aVar.p = bVar2.A;
            aVar.q = bVar2.B;
            aVar.r = bVar2.C;
            aVar.G = bVar2.z;
            aVar.T = bVar2.D;
            aVar.U = bVar2.E;
            aVar.I = bVar2.U;
            aVar.H = bVar2.V;
            aVar.K = bVar2.X;
            aVar.J = bVar2.W;
            aVar.W = bVar2.m0;
            aVar.X = bVar2.n0;
            aVar.L = bVar2.Y;
            aVar.M = bVar2.Z;
            aVar.P = bVar2.a0;
            aVar.Q = bVar2.b0;
            aVar.N = bVar2.c0;
            aVar.O = bVar2.d0;
            aVar.R = bVar2.e0;
            aVar.S = bVar2.f0;
            aVar.V = bVar2.F;
            aVar.f964c = bVar2.g;
            aVar.f962a = bVar2.e;
            aVar.f963b = bVar2.f;
            ((ViewGroup.MarginLayoutParams) aVar).width = bVar2.f1005c;
            b bVar3 = this.e;
            ((ViewGroup.MarginLayoutParams) aVar).height = bVar3.f1006d;
            String str = bVar3.l0;
            if (str != null) {
                aVar.Y = str;
            }
            aVar.Z = bVar3.p0;
            aVar.setMarginStart(bVar3.L);
            aVar.setMarginEnd(this.e.K);
            aVar.b();
        }

        /* renamed from: f, reason: merged with bridge method [inline-methods] */
        public a clone() {
            a aVar = new a();
            aVar.e.a(this.e);
            aVar.f998d.a(this.f998d);
            aVar.f997c.a(this.f997c);
            aVar.f.a(this.f);
            aVar.f995a = this.f995a;
            aVar.h = this.h;
            return aVar;
        }
    }

    public static class b {
        private static SparseIntArray q0;

        /* renamed from: c, reason: collision with root package name */
        public int f1005c;

        /* renamed from: d, reason: collision with root package name */
        public int f1006d;
        public int[] j0;
        public String k0;
        public String l0;

        /* renamed from: a, reason: collision with root package name */
        public boolean f1003a = false;

        /* renamed from: b, reason: collision with root package name */
        public boolean f1004b = false;
        public int e = -1;
        public int f = -1;
        public float g = -1.0f;
        public boolean h = true;
        public int i = -1;
        public int j = -1;
        public int k = -1;
        public int l = -1;
        public int m = -1;
        public int n = -1;
        public int o = -1;
        public int p = -1;
        public int q = -1;
        public int r = -1;
        public int s = -1;
        public int t = -1;
        public int u = -1;
        public int v = -1;
        public int w = -1;
        public float x = 0.5f;
        public float y = 0.5f;
        public String z = null;
        public int A = -1;
        public int B = 0;
        public float C = 0.0f;
        public int D = -1;
        public int E = -1;
        public int F = -1;
        public int G = 0;
        public int H = 0;
        public int I = 0;
        public int J = 0;
        public int K = 0;
        public int L = 0;
        public int M = 0;
        public int N = RecyclerView.UNDEFINED_DURATION;
        public int O = RecyclerView.UNDEFINED_DURATION;
        public int P = RecyclerView.UNDEFINED_DURATION;
        public int Q = RecyclerView.UNDEFINED_DURATION;
        public int R = RecyclerView.UNDEFINED_DURATION;
        public int S = RecyclerView.UNDEFINED_DURATION;
        public int T = RecyclerView.UNDEFINED_DURATION;
        public float U = -1.0f;
        public float V = -1.0f;
        public int W = 0;
        public int X = 0;
        public int Y = 0;
        public int Z = 0;
        public int a0 = 0;
        public int b0 = 0;
        public int c0 = 0;
        public int d0 = 0;
        public float e0 = 1.0f;
        public float f0 = 1.0f;
        public int g0 = -1;
        public int h0 = 0;
        public int i0 = -1;
        public boolean m0 = false;
        public boolean n0 = false;
        public boolean o0 = true;
        public int p0 = 0;

        static {
            SparseIntArray sparseIntArray = new SparseIntArray();
            q0 = sparseIntArray;
            sparseIntArray.append(43, 24);
            q0.append(44, 25);
            q0.append(46, 28);
            q0.append(47, 29);
            q0.append(52, 35);
            q0.append(51, 34);
            q0.append(24, 4);
            q0.append(23, 3);
            q0.append(19, 1);
            q0.append(61, 6);
            q0.append(62, 7);
            q0.append(31, 17);
            q0.append(32, 18);
            q0.append(33, 19);
            q0.append(15, 90);
            q0.append(0, 26);
            q0.append(48, 31);
            q0.append(49, 32);
            q0.append(30, 10);
            q0.append(29, 9);
            q0.append(66, 13);
            q0.append(69, 16);
            q0.append(67, 14);
            q0.append(64, 11);
            q0.append(68, 15);
            q0.append(65, 12);
            q0.append(55, 38);
            q0.append(41, 37);
            q0.append(40, 39);
            q0.append(54, 40);
            q0.append(39, 20);
            q0.append(53, 36);
            q0.append(28, 5);
            q0.append(42, 91);
            q0.append(50, 91);
            q0.append(45, 91);
            q0.append(22, 91);
            q0.append(18, 91);
            q0.append(3, 23);
            q0.append(5, 27);
            q0.append(7, 30);
            q0.append(8, 8);
            q0.append(4, 33);
            q0.append(6, 2);
            q0.append(1, 22);
            q0.append(2, 21);
            q0.append(56, 41);
            q0.append(34, 42);
            q0.append(17, 41);
            q0.append(16, 42);
            q0.append(71, 76);
            q0.append(25, 61);
            q0.append(27, 62);
            q0.append(26, 63);
            q0.append(60, 69);
            q0.append(38, 70);
            q0.append(12, 71);
            q0.append(10, 72);
            q0.append(11, 73);
            q0.append(13, 74);
            q0.append(9, 75);
        }

        public void a(b bVar) {
            this.f1003a = bVar.f1003a;
            this.f1005c = bVar.f1005c;
            this.f1004b = bVar.f1004b;
            this.f1006d = bVar.f1006d;
            this.e = bVar.e;
            this.f = bVar.f;
            this.g = bVar.g;
            this.h = bVar.h;
            this.i = bVar.i;
            this.j = bVar.j;
            this.k = bVar.k;
            this.l = bVar.l;
            this.m = bVar.m;
            this.n = bVar.n;
            this.o = bVar.o;
            this.p = bVar.p;
            this.q = bVar.q;
            this.r = bVar.r;
            this.s = bVar.s;
            this.t = bVar.t;
            this.u = bVar.u;
            this.v = bVar.v;
            this.w = bVar.w;
            this.x = bVar.x;
            this.y = bVar.y;
            this.z = bVar.z;
            this.A = bVar.A;
            this.B = bVar.B;
            this.C = bVar.C;
            this.D = bVar.D;
            this.E = bVar.E;
            this.F = bVar.F;
            this.G = bVar.G;
            this.H = bVar.H;
            this.I = bVar.I;
            this.J = bVar.J;
            this.K = bVar.K;
            this.L = bVar.L;
            this.M = bVar.M;
            this.N = bVar.N;
            this.O = bVar.O;
            this.P = bVar.P;
            this.Q = bVar.Q;
            this.R = bVar.R;
            this.S = bVar.S;
            this.T = bVar.T;
            this.U = bVar.U;
            this.V = bVar.V;
            this.W = bVar.W;
            this.X = bVar.X;
            this.Y = bVar.Y;
            this.Z = bVar.Z;
            this.a0 = bVar.a0;
            this.b0 = bVar.b0;
            this.c0 = bVar.c0;
            this.d0 = bVar.d0;
            this.e0 = bVar.e0;
            this.f0 = bVar.f0;
            this.g0 = bVar.g0;
            this.h0 = bVar.h0;
            this.i0 = bVar.i0;
            this.l0 = bVar.l0;
            int[] iArr = bVar.j0;
            if (iArr == null || bVar.k0 != null) {
                this.j0 = null;
            } else {
                this.j0 = Arrays.copyOf(iArr, iArr.length);
            }
            this.k0 = bVar.k0;
            this.m0 = bVar.m0;
            this.n0 = bVar.n0;
            this.o0 = bVar.o0;
            this.p0 = bVar.p0;
        }

        void b(Context context, AttributeSet attributeSet) {
            StringBuilder sb;
            String str;
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, f.o);
            this.f1004b = true;
            int indexCount = obtainStyledAttributes.getIndexCount();
            for (int i = 0; i < indexCount; i++) {
                int index = obtainStyledAttributes.getIndex(i);
                int i2 = q0.get(index);
                switch (i2) {
                    case 1:
                        int i3 = this.q;
                        int i4 = c.j;
                        int resourceId = obtainStyledAttributes.getResourceId(index, i3);
                        if (resourceId == -1) {
                            resourceId = obtainStyledAttributes.getInt(index, -1);
                        }
                        this.q = resourceId;
                        break;
                    case 2:
                        this.J = obtainStyledAttributes.getDimensionPixelSize(index, this.J);
                        break;
                    case 3:
                        int i5 = this.p;
                        int i6 = c.j;
                        int resourceId2 = obtainStyledAttributes.getResourceId(index, i5);
                        if (resourceId2 == -1) {
                            resourceId2 = obtainStyledAttributes.getInt(index, -1);
                        }
                        this.p = resourceId2;
                        break;
                    case 4:
                        int i7 = this.o;
                        int i8 = c.j;
                        int resourceId3 = obtainStyledAttributes.getResourceId(index, i7);
                        if (resourceId3 == -1) {
                            resourceId3 = obtainStyledAttributes.getInt(index, -1);
                        }
                        this.o = resourceId3;
                        break;
                    case 5:
                        this.z = obtainStyledAttributes.getString(index);
                        break;
                    case 6:
                        this.D = obtainStyledAttributes.getDimensionPixelOffset(index, this.D);
                        break;
                    case MotionLayout.TOUCH_UP_NEVER_TO_END /* 7 */:
                        this.E = obtainStyledAttributes.getDimensionPixelOffset(index, this.E);
                        break;
                    case 8:
                        this.K = obtainStyledAttributes.getDimensionPixelSize(index, this.K);
                        break;
                    case 9:
                        int i9 = this.w;
                        int i10 = c.j;
                        int resourceId4 = obtainStyledAttributes.getResourceId(index, i9);
                        if (resourceId4 == -1) {
                            resourceId4 = obtainStyledAttributes.getInt(index, -1);
                        }
                        this.w = resourceId4;
                        break;
                    case 10:
                        int i11 = this.v;
                        int i12 = c.j;
                        int resourceId5 = obtainStyledAttributes.getResourceId(index, i11);
                        if (resourceId5 == -1) {
                            resourceId5 = obtainStyledAttributes.getInt(index, -1);
                        }
                        this.v = resourceId5;
                        break;
                    case 11:
                        this.Q = obtainStyledAttributes.getDimensionPixelSize(index, this.Q);
                        break;
                    case 12:
                        this.R = obtainStyledAttributes.getDimensionPixelSize(index, this.R);
                        break;
                    case 13:
                        this.N = obtainStyledAttributes.getDimensionPixelSize(index, this.N);
                        break;
                    case 14:
                        this.P = obtainStyledAttributes.getDimensionPixelSize(index, this.P);
                        break;
                    case 15:
                        this.S = obtainStyledAttributes.getDimensionPixelSize(index, this.S);
                        break;
                    case MaterialButton.ICON_GRAVITY_TOP /* 16 */:
                        this.O = obtainStyledAttributes.getDimensionPixelSize(index, this.O);
                        break;
                    case 17:
                        this.e = obtainStyledAttributes.getDimensionPixelOffset(index, this.e);
                        break;
                    case 18:
                        this.f = obtainStyledAttributes.getDimensionPixelOffset(index, this.f);
                        break;
                    case 19:
                        this.g = obtainStyledAttributes.getFloat(index, this.g);
                        break;
                    case 20:
                        this.x = obtainStyledAttributes.getFloat(index, this.x);
                        break;
                    case 21:
                        this.f1006d = obtainStyledAttributes.getLayoutDimension(index, this.f1006d);
                        break;
                    case 22:
                        this.f1005c = obtainStyledAttributes.getLayoutDimension(index, this.f1005c);
                        break;
                    case 23:
                        this.G = obtainStyledAttributes.getDimensionPixelSize(index, this.G);
                        break;
                    case 24:
                        int i13 = this.i;
                        int i14 = c.j;
                        int resourceId6 = obtainStyledAttributes.getResourceId(index, i13);
                        if (resourceId6 == -1) {
                            resourceId6 = obtainStyledAttributes.getInt(index, -1);
                        }
                        this.i = resourceId6;
                        break;
                    case 25:
                        int i15 = this.j;
                        int i16 = c.j;
                        int resourceId7 = obtainStyledAttributes.getResourceId(index, i15);
                        if (resourceId7 == -1) {
                            resourceId7 = obtainStyledAttributes.getInt(index, -1);
                        }
                        this.j = resourceId7;
                        break;
                    case 26:
                        this.F = obtainStyledAttributes.getInt(index, this.F);
                        break;
                    case 27:
                        this.H = obtainStyledAttributes.getDimensionPixelSize(index, this.H);
                        break;
                    case 28:
                        int i17 = this.k;
                        int i18 = c.j;
                        int resourceId8 = obtainStyledAttributes.getResourceId(index, i17);
                        if (resourceId8 == -1) {
                            resourceId8 = obtainStyledAttributes.getInt(index, -1);
                        }
                        this.k = resourceId8;
                        break;
                    case 29:
                        int i19 = this.l;
                        int i20 = c.j;
                        int resourceId9 = obtainStyledAttributes.getResourceId(index, i19);
                        if (resourceId9 == -1) {
                            resourceId9 = obtainStyledAttributes.getInt(index, -1);
                        }
                        this.l = resourceId9;
                        break;
                    case 30:
                        this.L = obtainStyledAttributes.getDimensionPixelSize(index, this.L);
                        break;
                    case 31:
                        int i21 = this.t;
                        int i22 = c.j;
                        int resourceId10 = obtainStyledAttributes.getResourceId(index, i21);
                        if (resourceId10 == -1) {
                            resourceId10 = obtainStyledAttributes.getInt(index, -1);
                        }
                        this.t = resourceId10;
                        break;
                    case MaterialButton.ICON_GRAVITY_TEXT_TOP /* 32 */:
                        int i23 = this.u;
                        int i24 = c.j;
                        int resourceId11 = obtainStyledAttributes.getResourceId(index, i23);
                        if (resourceId11 == -1) {
                            resourceId11 = obtainStyledAttributes.getInt(index, -1);
                        }
                        this.u = resourceId11;
                        break;
                    case 33:
                        this.I = obtainStyledAttributes.getDimensionPixelSize(index, this.I);
                        break;
                    case 34:
                        int i25 = this.n;
                        int i26 = c.j;
                        int resourceId12 = obtainStyledAttributes.getResourceId(index, i25);
                        if (resourceId12 == -1) {
                            resourceId12 = obtainStyledAttributes.getInt(index, -1);
                        }
                        this.n = resourceId12;
                        break;
                    case 35:
                        int i27 = this.m;
                        int i28 = c.j;
                        int resourceId13 = obtainStyledAttributes.getResourceId(index, i27);
                        if (resourceId13 == -1) {
                            resourceId13 = obtainStyledAttributes.getInt(index, -1);
                        }
                        this.m = resourceId13;
                        break;
                    case 36:
                        this.y = obtainStyledAttributes.getFloat(index, this.y);
                        break;
                    case 37:
                        this.V = obtainStyledAttributes.getFloat(index, this.V);
                        break;
                    case 38:
                        this.U = obtainStyledAttributes.getFloat(index, this.U);
                        break;
                    case 39:
                        this.W = obtainStyledAttributes.getInt(index, this.W);
                        break;
                    case 40:
                        this.X = obtainStyledAttributes.getInt(index, this.X);
                        break;
                    case 41:
                        c.z(this, obtainStyledAttributes, index, 0);
                        break;
                    case 42:
                        c.z(this, obtainStyledAttributes, index, 1);
                        break;
                    default:
                        switch (i2) {
                            case 61:
                                int i29 = this.A;
                                int i30 = c.j;
                                int resourceId14 = obtainStyledAttributes.getResourceId(index, i29);
                                if (resourceId14 == -1) {
                                    resourceId14 = obtainStyledAttributes.getInt(index, -1);
                                }
                                this.A = resourceId14;
                                break;
                            case 62:
                                this.B = obtainStyledAttributes.getDimensionPixelSize(index, this.B);
                                break;
                            case 63:
                                this.C = obtainStyledAttributes.getFloat(index, this.C);
                                break;
                            default:
                                switch (i2) {
                                    case 69:
                                        this.e0 = obtainStyledAttributes.getFloat(index, 1.0f);
                                        continue;
                                    case 70:
                                        this.f0 = obtainStyledAttributes.getFloat(index, 1.0f);
                                        continue;
                                    case 71:
                                        Log.e("ConstraintSet", "CURRENTLY UNSUPPORTED");
                                        continue;
                                    case 72:
                                        this.g0 = obtainStyledAttributes.getInt(index, this.g0);
                                        continue;
                                    case 73:
                                        this.h0 = obtainStyledAttributes.getDimensionPixelSize(index, this.h0);
                                        continue;
                                    case 74:
                                        this.k0 = obtainStyledAttributes.getString(index);
                                        continue;
                                    case 75:
                                        this.o0 = obtainStyledAttributes.getBoolean(index, this.o0);
                                        continue;
                                    case 76:
                                        this.p0 = obtainStyledAttributes.getInt(index, this.p0);
                                        continue;
                                    case 77:
                                        int i31 = this.r;
                                        int i32 = c.j;
                                        int resourceId15 = obtainStyledAttributes.getResourceId(index, i31);
                                        if (resourceId15 == -1) {
                                            resourceId15 = obtainStyledAttributes.getInt(index, -1);
                                        }
                                        this.r = resourceId15;
                                        continue;
                                    case 78:
                                        int i33 = this.s;
                                        int i34 = c.j;
                                        int resourceId16 = obtainStyledAttributes.getResourceId(index, i33);
                                        if (resourceId16 == -1) {
                                            resourceId16 = obtainStyledAttributes.getInt(index, -1);
                                        }
                                        this.s = resourceId16;
                                        continue;
                                    case 79:
                                        this.T = obtainStyledAttributes.getDimensionPixelSize(index, this.T);
                                        continue;
                                    case 80:
                                        this.M = obtainStyledAttributes.getDimensionPixelSize(index, this.M);
                                        continue;
                                    case 81:
                                        this.Y = obtainStyledAttributes.getInt(index, this.Y);
                                        continue;
                                    case 82:
                                        this.Z = obtainStyledAttributes.getInt(index, this.Z);
                                        continue;
                                    case 83:
                                        this.b0 = obtainStyledAttributes.getDimensionPixelSize(index, this.b0);
                                        continue;
                                    case 84:
                                        this.a0 = obtainStyledAttributes.getDimensionPixelSize(index, this.a0);
                                        continue;
                                    case 85:
                                        this.d0 = obtainStyledAttributes.getDimensionPixelSize(index, this.d0);
                                        continue;
                                    case 86:
                                        this.c0 = obtainStyledAttributes.getDimensionPixelSize(index, this.c0);
                                        continue;
                                    case 87:
                                        this.m0 = obtainStyledAttributes.getBoolean(index, this.m0);
                                        continue;
                                    case 88:
                                        this.n0 = obtainStyledAttributes.getBoolean(index, this.n0);
                                        continue;
                                    case 89:
                                        this.l0 = obtainStyledAttributes.getString(index);
                                        continue;
                                    case 90:
                                        this.h = obtainStyledAttributes.getBoolean(index, this.h);
                                        continue;
                                    case 91:
                                        sb = new StringBuilder();
                                        str = "unused attribute 0x";
                                        break;
                                    default:
                                        sb = new StringBuilder();
                                        str = "Unknown attribute 0x";
                                        break;
                                }
                                sb.append(str);
                                sb.append(Integer.toHexString(index));
                                sb.append("   ");
                                sb.append(q0.get(index));
                                Log.w("ConstraintSet", sb.toString());
                                break;
                        }
                }
            }
            obtainStyledAttributes.recycle();
        }
    }

    /* renamed from: androidx.constraintlayout.widget.c$c, reason: collision with other inner class name */
    public static class C0034c {
        private static SparseIntArray o;

        /* renamed from: a, reason: collision with root package name */
        public boolean f1007a = false;

        /* renamed from: b, reason: collision with root package name */
        public int f1008b = -1;

        /* renamed from: c, reason: collision with root package name */
        public int f1009c = 0;

        /* renamed from: d, reason: collision with root package name */
        public String f1010d = null;
        public int e = -1;
        public int f = 0;
        public float g = Float.NaN;
        public int h = -1;
        public float i = Float.NaN;
        public float j = Float.NaN;
        public int k = -1;
        public String l = null;
        public int m = -3;
        public int n = -1;

        static {
            SparseIntArray sparseIntArray = new SparseIntArray();
            o = sparseIntArray;
            sparseIntArray.append(3, 1);
            o.append(5, 2);
            o.append(9, 3);
            o.append(2, 4);
            o.append(1, 5);
            o.append(0, 6);
            o.append(4, 7);
            o.append(8, 8);
            o.append(7, 9);
            o.append(6, 10);
        }

        public void a(C0034c c0034c) {
            this.f1007a = c0034c.f1007a;
            this.f1008b = c0034c.f1008b;
            this.f1010d = c0034c.f1010d;
            this.e = c0034c.e;
            this.f = c0034c.f;
            this.i = c0034c.i;
            this.g = c0034c.g;
            this.h = c0034c.h;
        }

        void b(Context context, AttributeSet attributeSet) {
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, f.q);
            this.f1007a = true;
            int indexCount = obtainStyledAttributes.getIndexCount();
            for (int i = 0; i < indexCount; i++) {
                int index = obtainStyledAttributes.getIndex(i);
                switch (o.get(index)) {
                    case 1:
                        this.i = obtainStyledAttributes.getFloat(index, this.i);
                        break;
                    case 2:
                        this.e = obtainStyledAttributes.getInt(index, this.e);
                        break;
                    case 3:
                        this.f1010d = obtainStyledAttributes.peekValue(index).type == 3 ? obtainStyledAttributes.getString(index) : a.f.a.i.a.c.f140c[obtainStyledAttributes.getInteger(index, 0)];
                        break;
                    case 4:
                        this.f = obtainStyledAttributes.getInt(index, 0);
                        break;
                    case 5:
                        int i2 = this.f1008b;
                        int i3 = c.j;
                        int resourceId = obtainStyledAttributes.getResourceId(index, i2);
                        if (resourceId == -1) {
                            resourceId = obtainStyledAttributes.getInt(index, -1);
                        }
                        this.f1008b = resourceId;
                        break;
                    case 6:
                        this.f1009c = obtainStyledAttributes.getInteger(index, this.f1009c);
                        break;
                    case MotionLayout.TOUCH_UP_NEVER_TO_END /* 7 */:
                        this.g = obtainStyledAttributes.getFloat(index, this.g);
                        break;
                    case 8:
                        this.k = obtainStyledAttributes.getInteger(index, this.k);
                        break;
                    case 9:
                        this.j = obtainStyledAttributes.getFloat(index, this.j);
                        break;
                    case 10:
                        int i4 = obtainStyledAttributes.peekValue(index).type;
                        if (i4 == 1) {
                            int resourceId2 = obtainStyledAttributes.getResourceId(index, -1);
                            this.n = resourceId2;
                            if (resourceId2 == -1) {
                                break;
                            }
                            this.m = -2;
                            break;
                        } else if (i4 != 3) {
                            this.m = obtainStyledAttributes.getInteger(index, this.n);
                            break;
                        } else {
                            String string = obtainStyledAttributes.getString(index);
                            this.l = string;
                            if (string.indexOf("/") <= 0) {
                                this.m = -1;
                                break;
                            } else {
                                this.n = obtainStyledAttributes.getResourceId(index, -1);
                                this.m = -2;
                            }
                        }
                }
            }
            obtainStyledAttributes.recycle();
        }
    }

    public static class d {

        /* renamed from: a, reason: collision with root package name */
        public boolean f1011a = false;

        /* renamed from: b, reason: collision with root package name */
        public int f1012b = 0;

        /* renamed from: c, reason: collision with root package name */
        public int f1013c = 0;

        /* renamed from: d, reason: collision with root package name */
        public float f1014d = 1.0f;
        public float e = Float.NaN;

        public void a(d dVar) {
            this.f1011a = dVar.f1011a;
            this.f1012b = dVar.f1012b;
            this.f1014d = dVar.f1014d;
            this.e = dVar.e;
            this.f1013c = dVar.f1013c;
        }

        void b(Context context, AttributeSet attributeSet) {
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, f.z);
            this.f1011a = true;
            int indexCount = obtainStyledAttributes.getIndexCount();
            for (int i = 0; i < indexCount; i++) {
                int index = obtainStyledAttributes.getIndex(i);
                if (index == 1) {
                    this.f1014d = obtainStyledAttributes.getFloat(index, this.f1014d);
                } else if (index == 0) {
                    this.f1012b = obtainStyledAttributes.getInt(index, this.f1012b);
                    this.f1012b = c.g[this.f1012b];
                } else if (index == 4) {
                    this.f1013c = obtainStyledAttributes.getInt(index, this.f1013c);
                } else if (index == 3) {
                    this.e = obtainStyledAttributes.getFloat(index, this.e);
                }
            }
            obtainStyledAttributes.recycle();
        }
    }

    public static class e {
        private static SparseIntArray o;

        /* renamed from: a, reason: collision with root package name */
        public boolean f1015a = false;

        /* renamed from: b, reason: collision with root package name */
        public float f1016b = 0.0f;

        /* renamed from: c, reason: collision with root package name */
        public float f1017c = 0.0f;

        /* renamed from: d, reason: collision with root package name */
        public float f1018d = 0.0f;
        public float e = 1.0f;
        public float f = 1.0f;
        public float g = Float.NaN;
        public float h = Float.NaN;
        public int i = -1;
        public float j = 0.0f;
        public float k = 0.0f;
        public float l = 0.0f;
        public boolean m = false;
        public float n = 0.0f;

        static {
            SparseIntArray sparseIntArray = new SparseIntArray();
            o = sparseIntArray;
            sparseIntArray.append(6, 1);
            o.append(7, 2);
            o.append(8, 3);
            o.append(4, 4);
            o.append(5, 5);
            o.append(0, 6);
            o.append(1, 7);
            o.append(2, 8);
            o.append(3, 9);
            o.append(9, 10);
            o.append(10, 11);
            o.append(11, 12);
        }

        public void a(e eVar) {
            this.f1015a = eVar.f1015a;
            this.f1016b = eVar.f1016b;
            this.f1017c = eVar.f1017c;
            this.f1018d = eVar.f1018d;
            this.e = eVar.e;
            this.f = eVar.f;
            this.g = eVar.g;
            this.h = eVar.h;
            this.i = eVar.i;
            this.j = eVar.j;
            this.k = eVar.k;
            this.l = eVar.l;
            this.m = eVar.m;
            this.n = eVar.n;
        }

        void b(Context context, AttributeSet attributeSet) {
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, f.C);
            this.f1015a = true;
            int indexCount = obtainStyledAttributes.getIndexCount();
            for (int i = 0; i < indexCount; i++) {
                int index = obtainStyledAttributes.getIndex(i);
                switch (o.get(index)) {
                    case 1:
                        this.f1016b = obtainStyledAttributes.getFloat(index, this.f1016b);
                        break;
                    case 2:
                        this.f1017c = obtainStyledAttributes.getFloat(index, this.f1017c);
                        break;
                    case 3:
                        this.f1018d = obtainStyledAttributes.getFloat(index, this.f1018d);
                        break;
                    case 4:
                        this.e = obtainStyledAttributes.getFloat(index, this.e);
                        break;
                    case 5:
                        this.f = obtainStyledAttributes.getFloat(index, this.f);
                        break;
                    case 6:
                        this.g = obtainStyledAttributes.getDimension(index, this.g);
                        break;
                    case MotionLayout.TOUCH_UP_NEVER_TO_END /* 7 */:
                        this.h = obtainStyledAttributes.getDimension(index, this.h);
                        break;
                    case 8:
                        this.j = obtainStyledAttributes.getDimension(index, this.j);
                        break;
                    case 9:
                        this.k = obtainStyledAttributes.getDimension(index, this.k);
                        break;
                    case 10:
                        this.l = obtainStyledAttributes.getDimension(index, this.l);
                        break;
                    case 11:
                        this.m = true;
                        this.n = obtainStyledAttributes.getDimension(index, this.n);
                        break;
                    case 12:
                        int i2 = this.i;
                        int i3 = c.j;
                        int resourceId = obtainStyledAttributes.getResourceId(index, i2);
                        if (resourceId == -1) {
                            resourceId = obtainStyledAttributes.getInt(index, -1);
                        }
                        this.i = resourceId;
                        break;
                }
            }
            obtainStyledAttributes.recycle();
        }
    }

    static {
        h.append(82, 25);
        h.append(83, 26);
        h.append(85, 29);
        h.append(86, 30);
        h.append(92, 36);
        h.append(91, 35);
        h.append(63, 4);
        h.append(62, 3);
        h.append(58, 1);
        h.append(60, 91);
        h.append(59, 92);
        h.append(101, 6);
        h.append(102, 7);
        h.append(70, 17);
        h.append(71, 18);
        h.append(72, 19);
        h.append(54, 99);
        h.append(0, 27);
        h.append(87, 32);
        h.append(88, 33);
        h.append(69, 10);
        h.append(68, 9);
        h.append(106, 13);
        h.append(109, 16);
        h.append(107, 14);
        h.append(104, 11);
        h.append(108, 15);
        h.append(105, 12);
        h.append(95, 40);
        h.append(80, 39);
        h.append(79, 41);
        h.append(94, 42);
        h.append(78, 20);
        h.append(93, 37);
        h.append(67, 5);
        h.append(81, 87);
        h.append(90, 87);
        h.append(84, 87);
        h.append(61, 87);
        h.append(57, 87);
        h.append(5, 24);
        h.append(7, 28);
        h.append(23, 31);
        h.append(24, 8);
        h.append(6, 34);
        h.append(8, 2);
        h.append(3, 23);
        h.append(4, 21);
        h.append(96, 95);
        h.append(73, 96);
        h.append(2, 22);
        h.append(13, 43);
        h.append(26, 44);
        h.append(21, 45);
        h.append(22, 46);
        h.append(20, 60);
        h.append(18, 47);
        h.append(19, 48);
        h.append(14, 49);
        h.append(15, 50);
        h.append(16, 51);
        h.append(17, 52);
        h.append(25, 53);
        h.append(97, 54);
        h.append(74, 55);
        h.append(98, 56);
        h.append(75, 57);
        h.append(99, 58);
        h.append(76, 59);
        h.append(64, 61);
        h.append(66, 62);
        h.append(65, 63);
        h.append(28, 64);
        h.append(121, 65);
        h.append(35, 66);
        h.append(122, 67);
        h.append(113, 79);
        h.append(1, 38);
        h.append(112, 68);
        h.append(100, 69);
        h.append(77, 70);
        h.append(111, 97);
        h.append(32, 71);
        h.append(30, 72);
        h.append(31, 73);
        h.append(33, 74);
        h.append(29, 75);
        h.append(114, 76);
        h.append(89, 77);
        h.append(123, 78);
        h.append(56, 80);
        h.append(55, 81);
        h.append(116, 82);
        h.append(120, 83);
        h.append(119, 84);
        h.append(118, 85);
        h.append(117, 86);
        i.append(85, 6);
        i.append(85, 7);
        i.append(0, 27);
        i.append(89, 13);
        i.append(92, 16);
        i.append(90, 14);
        i.append(87, 11);
        i.append(91, 15);
        i.append(88, 12);
        i.append(78, 40);
        i.append(71, 39);
        i.append(70, 41);
        i.append(77, 42);
        i.append(69, 20);
        i.append(76, 37);
        i.append(60, 5);
        i.append(72, 87);
        i.append(75, 87);
        i.append(73, 87);
        i.append(57, 87);
        i.append(56, 87);
        i.append(5, 24);
        i.append(7, 28);
        i.append(23, 31);
        i.append(24, 8);
        i.append(6, 34);
        i.append(8, 2);
        i.append(3, 23);
        i.append(4, 21);
        i.append(79, 95);
        i.append(64, 96);
        i.append(2, 22);
        i.append(13, 43);
        i.append(26, 44);
        i.append(21, 45);
        i.append(22, 46);
        i.append(20, 60);
        i.append(18, 47);
        i.append(19, 48);
        i.append(14, 49);
        i.append(15, 50);
        i.append(16, 51);
        i.append(17, 52);
        i.append(25, 53);
        i.append(80, 54);
        i.append(65, 55);
        i.append(81, 56);
        i.append(66, 57);
        i.append(82, 58);
        i.append(67, 59);
        i.append(59, 62);
        i.append(58, 63);
        i.append(28, 64);
        i.append(105, 65);
        i.append(34, 66);
        i.append(106, 67);
        i.append(96, 79);
        i.append(1, 38);
        i.append(97, 98);
        i.append(95, 68);
        i.append(83, 69);
        i.append(68, 70);
        i.append(32, 71);
        i.append(30, 72);
        i.append(31, 73);
        i.append(33, 74);
        i.append(29, 75);
        i.append(98, 76);
        i.append(74, 77);
        i.append(107, 78);
        i.append(55, 80);
        i.append(54, 81);
        i.append(100, 82);
        i.append(104, 83);
        i.append(103, 84);
        i.append(102, 85);
        i.append(101, 86);
        i.append(94, 97);
    }

    static void A(ConstraintLayout.a aVar, String str) {
        if (str != null) {
            int length = str.length();
            int indexOf = str.indexOf(44);
            int i2 = -1;
            if (indexOf > 0 && indexOf < length - 1) {
                String substring = str.substring(0, indexOf);
                i2 = substring.equalsIgnoreCase("W") ? 0 : substring.equalsIgnoreCase("H") ? 1 : -1;
                r2 = indexOf + 1;
            }
            int indexOf2 = str.indexOf(58);
            try {
                if (indexOf2 < 0 || indexOf2 >= length - 1) {
                    String substring2 = str.substring(r2);
                    if (substring2.length() > 0) {
                        Float.parseFloat(substring2);
                    }
                } else {
                    String substring3 = str.substring(r2, indexOf2);
                    String substring4 = str.substring(indexOf2 + 1);
                    if (substring3.length() > 0 && substring4.length() > 0) {
                        float parseFloat = Float.parseFloat(substring3);
                        float parseFloat2 = Float.parseFloat(substring4);
                        if (parseFloat > 0.0f && parseFloat2 > 0.0f) {
                            if (i2 == 1) {
                                Math.abs(parseFloat2 / parseFloat);
                            } else {
                                Math.abs(parseFloat / parseFloat2);
                            }
                        }
                    }
                }
            } catch (NumberFormatException unused) {
            }
        }
        aVar.G = str;
    }

    /* JADX WARN: Code restructure failed: missing block: B:57:0x0129, code lost:
    
        if (r6 == (-1)) goto L66;
     */
    /* JADX WARN: Code restructure failed: missing block: B:58:0x01d9, code lost:
    
        r1.b(r5, r6);
     */
    /* JADX WARN: Code restructure failed: missing block: B:60:0x01d5, code lost:
    
        r6 = r13.getInt(r4, -1);
     */
    /* JADX WARN: Code restructure failed: missing block: B:88:0x01d3, code lost:
    
        if (r6 == (-1)) goto L66;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void B(androidx.constraintlayout.widget.c.a r12, android.content.res.TypedArray r13) {
        /*
            Method dump skipped, instructions count: 1126
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.widget.c.B(androidx.constraintlayout.widget.c$a, android.content.res.TypedArray):void");
    }

    public static a h(Context context, XmlPullParser xmlPullParser) {
        AttributeSet asAttributeSet = Xml.asAttributeSet(xmlPullParser);
        a aVar = new a();
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(asAttributeSet, f.f);
        B(aVar, obtainStyledAttributes);
        obtainStyledAttributes.recycle();
        return aVar;
    }

    private int[] n(View view, String str) {
        int i2;
        Object designInformation;
        String[] split = str.split(",");
        Context context = view.getContext();
        int[] iArr = new int[split.length];
        int i3 = 0;
        int i4 = 0;
        while (i3 < split.length) {
            String trim = split[i3].trim();
            try {
                i2 = androidx.constraintlayout.widget.e.class.getField(trim).getInt(null);
            } catch (Exception unused) {
                i2 = 0;
            }
            if (i2 == 0) {
                i2 = context.getResources().getIdentifier(trim, "id", context.getPackageName());
            }
            if (i2 == 0 && view.isInEditMode() && (view.getParent() instanceof ConstraintLayout) && (designInformation = ((ConstraintLayout) view.getParent()).getDesignInformation(0, trim)) != null && (designInformation instanceof Integer)) {
                i2 = ((Integer) designInformation).intValue();
            }
            iArr[i4] = i2;
            i3++;
            i4++;
        }
        return i4 != split.length ? Arrays.copyOf(iArr, i4) : iArr;
    }

    private a o(Context context, AttributeSet attributeSet, boolean z) {
        C0034c c0034c;
        String str;
        C0034c c0034c2;
        StringBuilder sb;
        String str2;
        a aVar = new a();
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, z ? f.f : f.f1020b);
        if (z) {
            B(aVar, obtainStyledAttributes);
        } else {
            int indexCount = obtainStyledAttributes.getIndexCount();
            for (int i2 = 0; i2 < indexCount; i2++) {
                int index = obtainStyledAttributes.getIndex(i2);
                if (index != 1 && 23 != index && 24 != index) {
                    aVar.f998d.f1007a = true;
                    aVar.e.f1004b = true;
                    aVar.f997c.f1011a = true;
                    aVar.f.f1015a = true;
                }
                switch (h.get(index)) {
                    case 1:
                        b bVar = aVar.e;
                        int resourceId = obtainStyledAttributes.getResourceId(index, bVar.q);
                        if (resourceId == -1) {
                            resourceId = obtainStyledAttributes.getInt(index, -1);
                        }
                        bVar.q = resourceId;
                        continue;
                    case 2:
                        b bVar2 = aVar.e;
                        bVar2.J = obtainStyledAttributes.getDimensionPixelSize(index, bVar2.J);
                        continue;
                    case 3:
                        b bVar3 = aVar.e;
                        int resourceId2 = obtainStyledAttributes.getResourceId(index, bVar3.p);
                        if (resourceId2 == -1) {
                            resourceId2 = obtainStyledAttributes.getInt(index, -1);
                        }
                        bVar3.p = resourceId2;
                        continue;
                    case 4:
                        b bVar4 = aVar.e;
                        int resourceId3 = obtainStyledAttributes.getResourceId(index, bVar4.o);
                        if (resourceId3 == -1) {
                            resourceId3 = obtainStyledAttributes.getInt(index, -1);
                        }
                        bVar4.o = resourceId3;
                        continue;
                    case 5:
                        aVar.e.z = obtainStyledAttributes.getString(index);
                        continue;
                    case 6:
                        b bVar5 = aVar.e;
                        bVar5.D = obtainStyledAttributes.getDimensionPixelOffset(index, bVar5.D);
                        continue;
                    case MotionLayout.TOUCH_UP_NEVER_TO_END /* 7 */:
                        b bVar6 = aVar.e;
                        bVar6.E = obtainStyledAttributes.getDimensionPixelOffset(index, bVar6.E);
                        continue;
                    case 8:
                        b bVar7 = aVar.e;
                        bVar7.K = obtainStyledAttributes.getDimensionPixelSize(index, bVar7.K);
                        continue;
                    case 9:
                        b bVar8 = aVar.e;
                        int resourceId4 = obtainStyledAttributes.getResourceId(index, bVar8.w);
                        if (resourceId4 == -1) {
                            resourceId4 = obtainStyledAttributes.getInt(index, -1);
                        }
                        bVar8.w = resourceId4;
                        continue;
                    case 10:
                        b bVar9 = aVar.e;
                        int resourceId5 = obtainStyledAttributes.getResourceId(index, bVar9.v);
                        if (resourceId5 == -1) {
                            resourceId5 = obtainStyledAttributes.getInt(index, -1);
                        }
                        bVar9.v = resourceId5;
                        continue;
                    case 11:
                        b bVar10 = aVar.e;
                        bVar10.Q = obtainStyledAttributes.getDimensionPixelSize(index, bVar10.Q);
                        continue;
                    case 12:
                        b bVar11 = aVar.e;
                        bVar11.R = obtainStyledAttributes.getDimensionPixelSize(index, bVar11.R);
                        continue;
                    case 13:
                        b bVar12 = aVar.e;
                        bVar12.N = obtainStyledAttributes.getDimensionPixelSize(index, bVar12.N);
                        continue;
                    case 14:
                        b bVar13 = aVar.e;
                        bVar13.P = obtainStyledAttributes.getDimensionPixelSize(index, bVar13.P);
                        continue;
                    case 15:
                        b bVar14 = aVar.e;
                        bVar14.S = obtainStyledAttributes.getDimensionPixelSize(index, bVar14.S);
                        continue;
                    case MaterialButton.ICON_GRAVITY_TOP /* 16 */:
                        b bVar15 = aVar.e;
                        bVar15.O = obtainStyledAttributes.getDimensionPixelSize(index, bVar15.O);
                        continue;
                    case 17:
                        b bVar16 = aVar.e;
                        bVar16.e = obtainStyledAttributes.getDimensionPixelOffset(index, bVar16.e);
                        continue;
                    case 18:
                        b bVar17 = aVar.e;
                        bVar17.f = obtainStyledAttributes.getDimensionPixelOffset(index, bVar17.f);
                        continue;
                    case 19:
                        b bVar18 = aVar.e;
                        bVar18.g = obtainStyledAttributes.getFloat(index, bVar18.g);
                        continue;
                    case 20:
                        b bVar19 = aVar.e;
                        bVar19.x = obtainStyledAttributes.getFloat(index, bVar19.x);
                        continue;
                    case 21:
                        b bVar20 = aVar.e;
                        bVar20.f1006d = obtainStyledAttributes.getLayoutDimension(index, bVar20.f1006d);
                        continue;
                    case 22:
                        d dVar = aVar.f997c;
                        dVar.f1012b = obtainStyledAttributes.getInt(index, dVar.f1012b);
                        d dVar2 = aVar.f997c;
                        dVar2.f1012b = g[dVar2.f1012b];
                        continue;
                    case 23:
                        b bVar21 = aVar.e;
                        bVar21.f1005c = obtainStyledAttributes.getLayoutDimension(index, bVar21.f1005c);
                        continue;
                    case 24:
                        b bVar22 = aVar.e;
                        bVar22.G = obtainStyledAttributes.getDimensionPixelSize(index, bVar22.G);
                        continue;
                    case 25:
                        b bVar23 = aVar.e;
                        int resourceId6 = obtainStyledAttributes.getResourceId(index, bVar23.i);
                        if (resourceId6 == -1) {
                            resourceId6 = obtainStyledAttributes.getInt(index, -1);
                        }
                        bVar23.i = resourceId6;
                        continue;
                    case 26:
                        b bVar24 = aVar.e;
                        int resourceId7 = obtainStyledAttributes.getResourceId(index, bVar24.j);
                        if (resourceId7 == -1) {
                            resourceId7 = obtainStyledAttributes.getInt(index, -1);
                        }
                        bVar24.j = resourceId7;
                        continue;
                    case 27:
                        b bVar25 = aVar.e;
                        bVar25.F = obtainStyledAttributes.getInt(index, bVar25.F);
                        continue;
                    case 28:
                        b bVar26 = aVar.e;
                        bVar26.H = obtainStyledAttributes.getDimensionPixelSize(index, bVar26.H);
                        continue;
                    case 29:
                        b bVar27 = aVar.e;
                        int resourceId8 = obtainStyledAttributes.getResourceId(index, bVar27.k);
                        if (resourceId8 == -1) {
                            resourceId8 = obtainStyledAttributes.getInt(index, -1);
                        }
                        bVar27.k = resourceId8;
                        continue;
                    case 30:
                        b bVar28 = aVar.e;
                        int resourceId9 = obtainStyledAttributes.getResourceId(index, bVar28.l);
                        if (resourceId9 == -1) {
                            resourceId9 = obtainStyledAttributes.getInt(index, -1);
                        }
                        bVar28.l = resourceId9;
                        continue;
                    case 31:
                        b bVar29 = aVar.e;
                        bVar29.L = obtainStyledAttributes.getDimensionPixelSize(index, bVar29.L);
                        continue;
                    case MaterialButton.ICON_GRAVITY_TEXT_TOP /* 32 */:
                        b bVar30 = aVar.e;
                        int resourceId10 = obtainStyledAttributes.getResourceId(index, bVar30.t);
                        if (resourceId10 == -1) {
                            resourceId10 = obtainStyledAttributes.getInt(index, -1);
                        }
                        bVar30.t = resourceId10;
                        continue;
                    case 33:
                        b bVar31 = aVar.e;
                        int resourceId11 = obtainStyledAttributes.getResourceId(index, bVar31.u);
                        if (resourceId11 == -1) {
                            resourceId11 = obtainStyledAttributes.getInt(index, -1);
                        }
                        bVar31.u = resourceId11;
                        continue;
                    case 34:
                        b bVar32 = aVar.e;
                        bVar32.I = obtainStyledAttributes.getDimensionPixelSize(index, bVar32.I);
                        continue;
                    case 35:
                        b bVar33 = aVar.e;
                        int resourceId12 = obtainStyledAttributes.getResourceId(index, bVar33.n);
                        if (resourceId12 == -1) {
                            resourceId12 = obtainStyledAttributes.getInt(index, -1);
                        }
                        bVar33.n = resourceId12;
                        continue;
                    case 36:
                        b bVar34 = aVar.e;
                        int resourceId13 = obtainStyledAttributes.getResourceId(index, bVar34.m);
                        if (resourceId13 == -1) {
                            resourceId13 = obtainStyledAttributes.getInt(index, -1);
                        }
                        bVar34.m = resourceId13;
                        continue;
                    case 37:
                        b bVar35 = aVar.e;
                        bVar35.y = obtainStyledAttributes.getFloat(index, bVar35.y);
                        continue;
                    case 38:
                        aVar.f995a = obtainStyledAttributes.getResourceId(index, aVar.f995a);
                        continue;
                    case 39:
                        b bVar36 = aVar.e;
                        bVar36.V = obtainStyledAttributes.getFloat(index, bVar36.V);
                        continue;
                    case 40:
                        b bVar37 = aVar.e;
                        bVar37.U = obtainStyledAttributes.getFloat(index, bVar37.U);
                        continue;
                    case 41:
                        b bVar38 = aVar.e;
                        bVar38.W = obtainStyledAttributes.getInt(index, bVar38.W);
                        continue;
                    case 42:
                        b bVar39 = aVar.e;
                        bVar39.X = obtainStyledAttributes.getInt(index, bVar39.X);
                        continue;
                    case 43:
                        d dVar3 = aVar.f997c;
                        dVar3.f1014d = obtainStyledAttributes.getFloat(index, dVar3.f1014d);
                        continue;
                    case 44:
                        e eVar = aVar.f;
                        eVar.m = true;
                        eVar.n = obtainStyledAttributes.getDimension(index, eVar.n);
                        continue;
                    case RecognitionProgressView.BARS_COUNT /* 45 */:
                        e eVar2 = aVar.f;
                        eVar2.f1017c = obtainStyledAttributes.getFloat(index, eVar2.f1017c);
                        continue;
                    case 46:
                        e eVar3 = aVar.f;
                        eVar3.f1018d = obtainStyledAttributes.getFloat(index, eVar3.f1018d);
                        continue;
                    case 47:
                        e eVar4 = aVar.f;
                        eVar4.e = obtainStyledAttributes.getFloat(index, eVar4.e);
                        continue;
                    case 48:
                        e eVar5 = aVar.f;
                        eVar5.f = obtainStyledAttributes.getFloat(index, eVar5.f);
                        continue;
                    case 49:
                        e eVar6 = aVar.f;
                        eVar6.g = obtainStyledAttributes.getDimension(index, eVar6.g);
                        continue;
                    case 50:
                        e eVar7 = aVar.f;
                        eVar7.h = obtainStyledAttributes.getDimension(index, eVar7.h);
                        continue;
                    case 51:
                        e eVar8 = aVar.f;
                        eVar8.j = obtainStyledAttributes.getDimension(index, eVar8.j);
                        continue;
                    case 52:
                        e eVar9 = aVar.f;
                        eVar9.k = obtainStyledAttributes.getDimension(index, eVar9.k);
                        continue;
                    case 53:
                        e eVar10 = aVar.f;
                        eVar10.l = obtainStyledAttributes.getDimension(index, eVar10.l);
                        continue;
                    case 54:
                        b bVar40 = aVar.e;
                        bVar40.Y = obtainStyledAttributes.getInt(index, bVar40.Y);
                        continue;
                    case 55:
                        b bVar41 = aVar.e;
                        bVar41.Z = obtainStyledAttributes.getInt(index, bVar41.Z);
                        continue;
                    case 56:
                        b bVar42 = aVar.e;
                        bVar42.a0 = obtainStyledAttributes.getDimensionPixelSize(index, bVar42.a0);
                        continue;
                    case 57:
                        b bVar43 = aVar.e;
                        bVar43.b0 = obtainStyledAttributes.getDimensionPixelSize(index, bVar43.b0);
                        continue;
                    case 58:
                        b bVar44 = aVar.e;
                        bVar44.c0 = obtainStyledAttributes.getDimensionPixelSize(index, bVar44.c0);
                        continue;
                    case 59:
                        b bVar45 = aVar.e;
                        bVar45.d0 = obtainStyledAttributes.getDimensionPixelSize(index, bVar45.d0);
                        continue;
                    case 60:
                        e eVar11 = aVar.f;
                        eVar11.f1016b = obtainStyledAttributes.getFloat(index, eVar11.f1016b);
                        continue;
                    case 61:
                        b bVar46 = aVar.e;
                        int resourceId14 = obtainStyledAttributes.getResourceId(index, bVar46.A);
                        if (resourceId14 == -1) {
                            resourceId14 = obtainStyledAttributes.getInt(index, -1);
                        }
                        bVar46.A = resourceId14;
                        continue;
                    case 62:
                        b bVar47 = aVar.e;
                        bVar47.B = obtainStyledAttributes.getDimensionPixelSize(index, bVar47.B);
                        continue;
                    case 63:
                        b bVar48 = aVar.e;
                        bVar48.C = obtainStyledAttributes.getFloat(index, bVar48.C);
                        continue;
                    case 64:
                        C0034c c0034c3 = aVar.f998d;
                        int resourceId15 = obtainStyledAttributes.getResourceId(index, c0034c3.f1008b);
                        if (resourceId15 == -1) {
                            resourceId15 = obtainStyledAttributes.getInt(index, -1);
                        }
                        c0034c3.f1008b = resourceId15;
                        continue;
                    case 65:
                        if (obtainStyledAttributes.peekValue(index).type == 3) {
                            c0034c = aVar.f998d;
                            str = obtainStyledAttributes.getString(index);
                        } else {
                            c0034c = aVar.f998d;
                            str = a.f.a.i.a.c.f140c[obtainStyledAttributes.getInteger(index, 0)];
                        }
                        c0034c.f1010d = str;
                        continue;
                    case 66:
                        aVar.f998d.f = obtainStyledAttributes.getInt(index, 0);
                        continue;
                    case 67:
                        C0034c c0034c4 = aVar.f998d;
                        c0034c4.i = obtainStyledAttributes.getFloat(index, c0034c4.i);
                        continue;
                    case 68:
                        d dVar4 = aVar.f997c;
                        dVar4.e = obtainStyledAttributes.getFloat(index, dVar4.e);
                        continue;
                    case 69:
                        aVar.e.e0 = obtainStyledAttributes.getFloat(index, 1.0f);
                        continue;
                    case 70:
                        aVar.e.f0 = obtainStyledAttributes.getFloat(index, 1.0f);
                        continue;
                    case 71:
                        Log.e("ConstraintSet", "CURRENTLY UNSUPPORTED");
                        continue;
                    case 72:
                        b bVar49 = aVar.e;
                        bVar49.g0 = obtainStyledAttributes.getInt(index, bVar49.g0);
                        continue;
                    case 73:
                        b bVar50 = aVar.e;
                        bVar50.h0 = obtainStyledAttributes.getDimensionPixelSize(index, bVar50.h0);
                        continue;
                    case 74:
                        aVar.e.k0 = obtainStyledAttributes.getString(index);
                        continue;
                    case 75:
                        b bVar51 = aVar.e;
                        bVar51.o0 = obtainStyledAttributes.getBoolean(index, bVar51.o0);
                        continue;
                    case 76:
                        C0034c c0034c5 = aVar.f998d;
                        c0034c5.e = obtainStyledAttributes.getInt(index, c0034c5.e);
                        continue;
                    case 77:
                        aVar.e.l0 = obtainStyledAttributes.getString(index);
                        continue;
                    case 78:
                        d dVar5 = aVar.f997c;
                        dVar5.f1013c = obtainStyledAttributes.getInt(index, dVar5.f1013c);
                        continue;
                    case 79:
                        C0034c c0034c6 = aVar.f998d;
                        c0034c6.g = obtainStyledAttributes.getFloat(index, c0034c6.g);
                        continue;
                    case 80:
                        b bVar52 = aVar.e;
                        bVar52.m0 = obtainStyledAttributes.getBoolean(index, bVar52.m0);
                        continue;
                    case 81:
                        b bVar53 = aVar.e;
                        bVar53.n0 = obtainStyledAttributes.getBoolean(index, bVar53.n0);
                        continue;
                    case 82:
                        C0034c c0034c7 = aVar.f998d;
                        c0034c7.f1009c = obtainStyledAttributes.getInteger(index, c0034c7.f1009c);
                        continue;
                    case 83:
                        e eVar12 = aVar.f;
                        int resourceId16 = obtainStyledAttributes.getResourceId(index, eVar12.i);
                        if (resourceId16 == -1) {
                            resourceId16 = obtainStyledAttributes.getInt(index, -1);
                        }
                        eVar12.i = resourceId16;
                        continue;
                    case 84:
                        C0034c c0034c8 = aVar.f998d;
                        c0034c8.k = obtainStyledAttributes.getInteger(index, c0034c8.k);
                        continue;
                    case 85:
                        C0034c c0034c9 = aVar.f998d;
                        c0034c9.j = obtainStyledAttributes.getFloat(index, c0034c9.j);
                        continue;
                    case 86:
                        int i3 = obtainStyledAttributes.peekValue(index).type;
                        if (i3 == 1) {
                            aVar.f998d.n = obtainStyledAttributes.getResourceId(index, -1);
                            c0034c2 = aVar.f998d;
                            if (c0034c2.n == -1) {
                                continue;
                            }
                            c0034c2.m = -2;
                            break;
                        } else {
                            C0034c c0034c10 = aVar.f998d;
                            if (i3 != 3) {
                                c0034c10.m = obtainStyledAttributes.getInteger(index, c0034c10.n);
                                break;
                            } else {
                                c0034c10.l = obtainStyledAttributes.getString(index);
                                if (aVar.f998d.l.indexOf("/") <= 0) {
                                    aVar.f998d.m = -1;
                                    break;
                                } else {
                                    aVar.f998d.n = obtainStyledAttributes.getResourceId(index, -1);
                                    c0034c2 = aVar.f998d;
                                    c0034c2.m = -2;
                                }
                            }
                        }
                    case 87:
                        sb = new StringBuilder();
                        str2 = "unused attribute 0x";
                        break;
                    case 88:
                    case 89:
                    case 90:
                    default:
                        sb = new StringBuilder();
                        str2 = "Unknown attribute 0x";
                        break;
                    case 91:
                        b bVar54 = aVar.e;
                        int resourceId17 = obtainStyledAttributes.getResourceId(index, bVar54.r);
                        if (resourceId17 == -1) {
                            resourceId17 = obtainStyledAttributes.getInt(index, -1);
                        }
                        bVar54.r = resourceId17;
                        continue;
                    case 92:
                        b bVar55 = aVar.e;
                        int resourceId18 = obtainStyledAttributes.getResourceId(index, bVar55.s);
                        if (resourceId18 == -1) {
                            resourceId18 = obtainStyledAttributes.getInt(index, -1);
                        }
                        bVar55.s = resourceId18;
                        continue;
                    case 93:
                        b bVar56 = aVar.e;
                        bVar56.M = obtainStyledAttributes.getDimensionPixelSize(index, bVar56.M);
                        continue;
                    case 94:
                        b bVar57 = aVar.e;
                        bVar57.T = obtainStyledAttributes.getDimensionPixelSize(index, bVar57.T);
                        continue;
                    case 95:
                        z(aVar.e, obtainStyledAttributes, index, 0);
                        continue;
                    case 96:
                        z(aVar.e, obtainStyledAttributes, index, 1);
                        continue;
                    case 97:
                        b bVar58 = aVar.e;
                        bVar58.p0 = obtainStyledAttributes.getInt(index, bVar58.p0);
                        continue;
                }
                sb.append(str2);
                sb.append(Integer.toHexString(index));
                sb.append("   ");
                sb.append(h.get(index));
                Log.w("ConstraintSet", sb.toString());
            }
            b bVar59 = aVar.e;
            if (bVar59.k0 != null) {
                bVar59.j0 = null;
            }
        }
        obtainStyledAttributes.recycle();
        return aVar;
    }

    private a p(int i2) {
        if (!this.f.containsKey(Integer.valueOf(i2))) {
            this.f.put(Integer.valueOf(i2), new a());
        }
        return this.f.get(Integer.valueOf(i2));
    }

    /* JADX WARN: Removed duplicated region for block: B:17:0x0033  */
    /* JADX WARN: Removed duplicated region for block: B:23:0x0041  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    static void z(java.lang.Object r8, android.content.res.TypedArray r9, int r10, int r11) {
        /*
            Method dump skipped, instructions count: 365
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.widget.c.z(java.lang.Object, android.content.res.TypedArray, int, int):void");
    }

    public void C(ConstraintLayout constraintLayout) {
        int childCount = constraintLayout.getChildCount();
        for (int i2 = 0; i2 < childCount; i2++) {
            View childAt = constraintLayout.getChildAt(i2);
            ConstraintLayout.a aVar = (ConstraintLayout.a) childAt.getLayoutParams();
            int id = childAt.getId();
            if (this.e && id == -1) {
                throw new RuntimeException("All children of ConstraintLayout must have ids to use ConstraintSet");
            }
            if (!this.f.containsKey(Integer.valueOf(id))) {
                this.f.put(Integer.valueOf(id), new a());
            }
            a aVar2 = this.f.get(Integer.valueOf(id));
            if (aVar2 != null) {
                if (!aVar2.e.f1004b) {
                    aVar2.g(id, aVar);
                    if (childAt instanceof ConstraintHelper) {
                        aVar2.e.j0 = ((ConstraintHelper) childAt).getReferencedIds();
                        if (childAt instanceof Barrier) {
                            Barrier barrier = (Barrier) childAt;
                            aVar2.e.o0 = barrier.getAllowsGoneWidget();
                            aVar2.e.g0 = barrier.getType();
                            aVar2.e.h0 = barrier.getMargin();
                        }
                    }
                    aVar2.e.f1004b = true;
                }
                d dVar = aVar2.f997c;
                if (!dVar.f1011a) {
                    dVar.f1012b = childAt.getVisibility();
                    aVar2.f997c.f1014d = childAt.getAlpha();
                    aVar2.f997c.f1011a = true;
                }
                e eVar = aVar2.f;
                if (!eVar.f1015a) {
                    eVar.f1015a = true;
                    eVar.f1016b = childAt.getRotation();
                    aVar2.f.f1017c = childAt.getRotationX();
                    aVar2.f.f1018d = childAt.getRotationY();
                    aVar2.f.e = childAt.getScaleX();
                    aVar2.f.f = childAt.getScaleY();
                    float pivotX = childAt.getPivotX();
                    float pivotY = childAt.getPivotY();
                    if (pivotX != 0.0d || pivotY != 0.0d) {
                        e eVar2 = aVar2.f;
                        eVar2.g = pivotX;
                        eVar2.h = pivotY;
                    }
                    aVar2.f.j = childAt.getTranslationX();
                    aVar2.f.k = childAt.getTranslationY();
                    aVar2.f.l = childAt.getTranslationZ();
                    e eVar3 = aVar2.f;
                    if (eVar3.m) {
                        eVar3.n = childAt.getElevation();
                    }
                }
            }
        }
    }

    public void D(c cVar) {
        for (Integer num : cVar.f.keySet()) {
            int intValue = num.intValue();
            a aVar = cVar.f.get(num);
            if (!this.f.containsKey(Integer.valueOf(intValue))) {
                this.f.put(Integer.valueOf(intValue), new a());
            }
            a aVar2 = this.f.get(Integer.valueOf(intValue));
            if (aVar2 != null) {
                b bVar = aVar2.e;
                if (!bVar.f1004b) {
                    bVar.a(aVar.e);
                }
                d dVar = aVar2.f997c;
                if (!dVar.f1011a) {
                    dVar.a(aVar.f997c);
                }
                e eVar = aVar2.f;
                if (!eVar.f1015a) {
                    eVar.a(aVar.f);
                }
                C0034c c0034c = aVar2.f998d;
                if (!c0034c.f1007a) {
                    c0034c.a(aVar.f998d);
                }
                for (String str : aVar.g.keySet()) {
                    if (!aVar2.g.containsKey(str)) {
                        aVar2.g.put(str, aVar.g.get(str));
                    }
                }
            }
        }
    }

    public void E(boolean z) {
        this.e = z;
    }

    public void F(int i2, int i3) {
        p(i2).e.f = i3;
        p(i2).e.e = -1;
        p(i2).e.g = -1.0f;
    }

    public void b(ConstraintLayout constraintLayout) {
        a aVar;
        int childCount = constraintLayout.getChildCount();
        for (int i2 = 0; i2 < childCount; i2++) {
            View childAt = constraintLayout.getChildAt(i2);
            int id = childAt.getId();
            if (!this.f.containsKey(Integer.valueOf(id))) {
                StringBuilder j2 = b.b.a.a.a.j("id unknown ");
                j2.append(a.b.a.d(childAt));
                Log.w("ConstraintSet", j2.toString());
            } else {
                if (this.e && id == -1) {
                    throw new RuntimeException("All children of ConstraintLayout must have ids to use ConstraintSet");
                }
                if (this.f.containsKey(Integer.valueOf(id)) && (aVar = this.f.get(Integer.valueOf(id))) != null) {
                    androidx.constraintlayout.widget.a.i(childAt, aVar.g);
                }
            }
        }
    }

    public void c(c cVar) {
        for (a aVar : cVar.f.values()) {
            if (aVar.h != null) {
                if (aVar.f996b != null) {
                    Iterator<Integer> it = this.f.keySet().iterator();
                    while (it.hasNext()) {
                        a q = q(it.next().intValue());
                        String str = q.e.l0;
                        if (str != null && aVar.f996b.matches(str)) {
                            aVar.h.e(q);
                            q.g.putAll((HashMap) aVar.g.clone());
                        }
                    }
                } else {
                    aVar.h.e(q(aVar.f995a));
                }
            }
        }
    }

    public void d(ConstraintLayout constraintLayout) {
        f(constraintLayout, true);
        constraintLayout.setConstraintSet(null);
        constraintLayout.requestLayout();
    }

    public void e(ConstraintHelper constraintHelper, a.f.a.j.e eVar, ConstraintLayout.a aVar, SparseArray<a.f.a.j.e> sparseArray) {
        a aVar2;
        int id = constraintHelper.getId();
        if (this.f.containsKey(Integer.valueOf(id)) && (aVar2 = this.f.get(Integer.valueOf(id))) != null && (eVar instanceof j)) {
            constraintHelper.loadParameters(aVar2, (j) eVar, aVar, sparseArray);
        }
    }

    void f(ConstraintLayout constraintLayout, boolean z) {
        int childCount = constraintLayout.getChildCount();
        HashSet hashSet = new HashSet(this.f.keySet());
        for (int i2 = 0; i2 < childCount; i2++) {
            View childAt = constraintLayout.getChildAt(i2);
            int id = childAt.getId();
            if (!this.f.containsKey(Integer.valueOf(id))) {
                StringBuilder j2 = b.b.a.a.a.j("id unknown ");
                j2.append(a.b.a.d(childAt));
                Log.w("ConstraintSet", j2.toString());
            } else {
                if (this.e && id == -1) {
                    throw new RuntimeException("All children of ConstraintLayout must have ids to use ConstraintSet");
                }
                if (id != -1) {
                    if (this.f.containsKey(Integer.valueOf(id))) {
                        hashSet.remove(Integer.valueOf(id));
                        a aVar = this.f.get(Integer.valueOf(id));
                        if (aVar != null) {
                            if (childAt instanceof Barrier) {
                                aVar.e.i0 = 1;
                                Barrier barrier = (Barrier) childAt;
                                barrier.setId(id);
                                barrier.setType(aVar.e.g0);
                                barrier.setMargin(aVar.e.h0);
                                barrier.setAllowsGoneWidget(aVar.e.o0);
                                b bVar = aVar.e;
                                int[] iArr = bVar.j0;
                                if (iArr != null) {
                                    barrier.setReferencedIds(iArr);
                                } else {
                                    String str = bVar.k0;
                                    if (str != null) {
                                        bVar.j0 = n(barrier, str);
                                        barrier.setReferencedIds(aVar.e.j0);
                                    }
                                }
                            }
                            ConstraintLayout.a aVar2 = (ConstraintLayout.a) childAt.getLayoutParams();
                            aVar2.b();
                            aVar.e(aVar2);
                            if (z) {
                                androidx.constraintlayout.widget.a.i(childAt, aVar.g);
                            }
                            childAt.setLayoutParams(aVar2);
                            d dVar = aVar.f997c;
                            if (dVar.f1013c == 0) {
                                childAt.setVisibility(dVar.f1012b);
                            }
                            childAt.setAlpha(aVar.f997c.f1014d);
                            childAt.setRotation(aVar.f.f1016b);
                            childAt.setRotationX(aVar.f.f1017c);
                            childAt.setRotationY(aVar.f.f1018d);
                            childAt.setScaleX(aVar.f.e);
                            childAt.setScaleY(aVar.f.f);
                            e eVar = aVar.f;
                            if (eVar.i != -1) {
                                if (((View) childAt.getParent()).findViewById(aVar.f.i) != null) {
                                    float bottom = (r4.getBottom() + r4.getTop()) / 2.0f;
                                    float right = (r4.getRight() + r4.getLeft()) / 2.0f;
                                    if (childAt.getRight() - childAt.getLeft() > 0 && childAt.getBottom() - childAt.getTop() > 0) {
                                        childAt.setPivotX(right - childAt.getLeft());
                                        childAt.setPivotY(bottom - childAt.getTop());
                                    }
                                }
                            } else {
                                if (!Float.isNaN(eVar.g)) {
                                    childAt.setPivotX(aVar.f.g);
                                }
                                if (!Float.isNaN(aVar.f.h)) {
                                    childAt.setPivotY(aVar.f.h);
                                }
                            }
                            childAt.setTranslationX(aVar.f.j);
                            childAt.setTranslationY(aVar.f.k);
                            childAt.setTranslationZ(aVar.f.l);
                            e eVar2 = aVar.f;
                            if (eVar2.m) {
                                childAt.setElevation(eVar2.n);
                            }
                        }
                    } else {
                        Log.v("ConstraintSet", "WARNING NO CONSTRAINTS for view " + id);
                    }
                }
            }
        }
        Iterator it = hashSet.iterator();
        while (it.hasNext()) {
            Integer num = (Integer) it.next();
            a aVar3 = this.f.get(num);
            if (aVar3 != null) {
                if (aVar3.e.i0 == 1) {
                    Barrier barrier2 = new Barrier(constraintLayout.getContext());
                    barrier2.setId(num.intValue());
                    b bVar2 = aVar3.e;
                    int[] iArr2 = bVar2.j0;
                    if (iArr2 != null) {
                        barrier2.setReferencedIds(iArr2);
                    } else {
                        String str2 = bVar2.k0;
                        if (str2 != null) {
                            bVar2.j0 = n(barrier2, str2);
                            barrier2.setReferencedIds(aVar3.e.j0);
                        }
                    }
                    barrier2.setType(aVar3.e.g0);
                    barrier2.setMargin(aVar3.e.h0);
                    ConstraintLayout.a generateDefaultLayoutParams = constraintLayout.generateDefaultLayoutParams();
                    barrier2.validateParams();
                    aVar3.e(generateDefaultLayoutParams);
                    constraintLayout.addView(barrier2, generateDefaultLayoutParams);
                }
                if (aVar3.e.f1003a) {
                    View guideline = new Guideline(constraintLayout.getContext());
                    guideline.setId(num.intValue());
                    ConstraintLayout.a generateDefaultLayoutParams2 = constraintLayout.generateDefaultLayoutParams();
                    aVar3.e(generateDefaultLayoutParams2);
                    constraintLayout.addView(guideline, generateDefaultLayoutParams2);
                }
            }
        }
        for (int i3 = 0; i3 < childCount; i3++) {
            View childAt2 = constraintLayout.getChildAt(i3);
            if (childAt2 instanceof ConstraintHelper) {
                ((ConstraintHelper) childAt2).applyLayoutFeaturesInConstraintSet(constraintLayout);
            }
        }
    }

    public void g(int i2, ConstraintLayout.a aVar) {
        a aVar2;
        if (!this.f.containsKey(Integer.valueOf(i2)) || (aVar2 = this.f.get(Integer.valueOf(i2))) == null) {
            return;
        }
        aVar2.e(aVar);
    }

    public void i(int i2, int i3) {
        a aVar;
        if (!this.f.containsKey(Integer.valueOf(i2)) || (aVar = this.f.get(Integer.valueOf(i2))) == null) {
            return;
        }
        switch (i3) {
            case 1:
                b bVar = aVar.e;
                bVar.j = -1;
                bVar.i = -1;
                bVar.G = -1;
                bVar.N = RecyclerView.UNDEFINED_DURATION;
                return;
            case 2:
                b bVar2 = aVar.e;
                bVar2.l = -1;
                bVar2.k = -1;
                bVar2.H = -1;
                bVar2.P = RecyclerView.UNDEFINED_DURATION;
                return;
            case 3:
                b bVar3 = aVar.e;
                bVar3.n = -1;
                bVar3.m = -1;
                bVar3.I = 0;
                bVar3.O = RecyclerView.UNDEFINED_DURATION;
                return;
            case 4:
                b bVar4 = aVar.e;
                bVar4.o = -1;
                bVar4.p = -1;
                bVar4.J = 0;
                bVar4.Q = RecyclerView.UNDEFINED_DURATION;
                return;
            case 5:
                b bVar5 = aVar.e;
                bVar5.q = -1;
                bVar5.r = -1;
                bVar5.s = -1;
                bVar5.M = 0;
                bVar5.T = RecyclerView.UNDEFINED_DURATION;
                return;
            case 6:
                b bVar6 = aVar.e;
                bVar6.t = -1;
                bVar6.u = -1;
                bVar6.L = 0;
                bVar6.S = RecyclerView.UNDEFINED_DURATION;
                return;
            case MotionLayout.TOUCH_UP_NEVER_TO_END /* 7 */:
                b bVar7 = aVar.e;
                bVar7.v = -1;
                bVar7.w = -1;
                bVar7.K = 0;
                bVar7.R = RecyclerView.UNDEFINED_DURATION;
                return;
            case 8:
                b bVar8 = aVar.e;
                bVar8.C = -1.0f;
                bVar8.B = -1;
                bVar8.A = -1;
                return;
            default:
                throw new IllegalArgumentException("unknown constraint");
        }
    }

    public void j(ConstraintLayout constraintLayout) {
        androidx.constraintlayout.widget.a aVar;
        c cVar = this;
        int childCount = constraintLayout.getChildCount();
        cVar.f.clear();
        int i2 = 0;
        while (i2 < childCount) {
            View childAt = constraintLayout.getChildAt(i2);
            ConstraintLayout.a aVar2 = (ConstraintLayout.a) childAt.getLayoutParams();
            int id = childAt.getId();
            if (cVar.e && id == -1) {
                throw new RuntimeException("All children of ConstraintLayout must have ids to use ConstraintSet");
            }
            if (!cVar.f.containsKey(Integer.valueOf(id))) {
                cVar.f.put(Integer.valueOf(id), new a());
            }
            a aVar3 = cVar.f.get(Integer.valueOf(id));
            if (aVar3 != null) {
                HashMap<String, androidx.constraintlayout.widget.a> hashMap = cVar.f994d;
                HashMap<String, androidx.constraintlayout.widget.a> hashMap2 = new HashMap<>();
                Class<?> cls = childAt.getClass();
                for (String str : hashMap.keySet()) {
                    androidx.constraintlayout.widget.a aVar4 = hashMap.get(str);
                    try {
                        if (str.equals("BackgroundColor")) {
                            aVar = new androidx.constraintlayout.widget.a(aVar4, Integer.valueOf(((ColorDrawable) childAt.getBackground()).getColor()));
                        } else {
                            try {
                                aVar = new androidx.constraintlayout.widget.a(aVar4, cls.getMethod("getMap" + str, new Class[0]).invoke(childAt, new Object[0]));
                            } catch (IllegalAccessException e2) {
                                e = e2;
                                e.printStackTrace();
                            } catch (NoSuchMethodException e3) {
                                e = e3;
                                e.printStackTrace();
                            } catch (InvocationTargetException e4) {
                                e = e4;
                                e.printStackTrace();
                            }
                        }
                        hashMap2.put(str, aVar);
                    } catch (IllegalAccessException e5) {
                        e = e5;
                    } catch (NoSuchMethodException e6) {
                        e = e6;
                    } catch (InvocationTargetException e7) {
                        e = e7;
                    }
                }
                aVar3.g = hashMap2;
                aVar3.g(id, aVar2);
                aVar3.f997c.f1012b = childAt.getVisibility();
                aVar3.f997c.f1014d = childAt.getAlpha();
                aVar3.f.f1016b = childAt.getRotation();
                aVar3.f.f1017c = childAt.getRotationX();
                aVar3.f.f1018d = childAt.getRotationY();
                aVar3.f.e = childAt.getScaleX();
                aVar3.f.f = childAt.getScaleY();
                float pivotX = childAt.getPivotX();
                float pivotY = childAt.getPivotY();
                if (pivotX != 0.0d || pivotY != 0.0d) {
                    e eVar = aVar3.f;
                    eVar.g = pivotX;
                    eVar.h = pivotY;
                }
                aVar3.f.j = childAt.getTranslationX();
                aVar3.f.k = childAt.getTranslationY();
                aVar3.f.l = childAt.getTranslationZ();
                e eVar2 = aVar3.f;
                if (eVar2.m) {
                    eVar2.n = childAt.getElevation();
                }
                if (childAt instanceof Barrier) {
                    Barrier barrier = (Barrier) childAt;
                    aVar3.e.o0 = barrier.getAllowsGoneWidget();
                    aVar3.e.j0 = barrier.getReferencedIds();
                    aVar3.e.g0 = barrier.getType();
                    aVar3.e.h0 = barrier.getMargin();
                }
            }
            i2++;
            cVar = this;
        }
    }

    public void k(c cVar) {
        this.f.clear();
        for (Integer num : cVar.f.keySet()) {
            a aVar = cVar.f.get(num);
            if (aVar != null) {
                this.f.put(num, aVar.clone());
            }
        }
    }

    public void l(Constraints constraints) {
        int childCount = constraints.getChildCount();
        this.f.clear();
        for (int i2 = 0; i2 < childCount; i2++) {
            View childAt = constraints.getChildAt(i2);
            Constraints.a aVar = (Constraints.a) childAt.getLayoutParams();
            int id = childAt.getId();
            if (this.e && id == -1) {
                throw new RuntimeException("All children of ConstraintLayout must have ids to use ConstraintSet");
            }
            if (!this.f.containsKey(Integer.valueOf(id))) {
                this.f.put(Integer.valueOf(id), new a());
            }
            a aVar2 = this.f.get(Integer.valueOf(id));
            if (aVar2 != null) {
                if (childAt instanceof ConstraintHelper) {
                    a.b(aVar2, (ConstraintHelper) childAt, id, aVar);
                }
                aVar2.h(id, aVar);
            }
        }
    }

    public void m(int i2, int i3, int i4, float f) {
        b bVar = p(i2).e;
        bVar.A = i3;
        bVar.B = i4;
        bVar.C = f;
    }

    public a q(int i2) {
        if (this.f.containsKey(Integer.valueOf(i2))) {
            return this.f.get(Integer.valueOf(i2));
        }
        return null;
    }

    public int r(int i2) {
        return p(i2).e.f1006d;
    }

    public int[] s() {
        Integer[] numArr = (Integer[]) this.f.keySet().toArray(new Integer[0]);
        int length = numArr.length;
        int[] iArr = new int[length];
        for (int i2 = 0; i2 < length; i2++) {
            iArr[i2] = numArr[i2].intValue();
        }
        return iArr;
    }

    public a t(int i2) {
        return p(i2);
    }

    public int u(int i2) {
        return p(i2).f997c.f1012b;
    }

    public int v(int i2) {
        return p(i2).f997c.f1013c;
    }

    public int w(int i2) {
        return p(i2).e.f1005c;
    }

    public void x(Context context, int i2) {
        XmlResourceParser xml = context.getResources().getXml(i2);
        try {
            for (int eventType = xml.getEventType(); eventType != 1; eventType = xml.next()) {
                if (eventType == 0) {
                    xml.getName();
                } else if (eventType == 2) {
                    String name = xml.getName();
                    a o = o(context, Xml.asAttributeSet(xml), false);
                    if (name.equalsIgnoreCase("Guideline")) {
                        o.e.f1003a = true;
                    }
                    this.f.put(Integer.valueOf(o.f995a), o);
                }
            }
        } catch (IOException e2) {
            e2.printStackTrace();
        } catch (XmlPullParserException e3) {
            e3.printStackTrace();
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:79:0x01cb, code lost:
    
        continue;
     */
    /* JADX WARN: Failed to restore switch over string. Please report as a decompilation issue
    java.lang.NullPointerException: Cannot invoke "java.util.List.iterator()" because the return value of "jadx.core.dex.visitors.regions.SwitchOverStringVisitor$SwitchData.getNewCases()" is null
    	at jadx.core.dex.visitors.regions.SwitchOverStringVisitor.restoreSwitchOverString(SwitchOverStringVisitor.java:109)
    	at jadx.core.dex.visitors.regions.SwitchOverStringVisitor.visitRegion(SwitchOverStringVisitor.java:66)
    	at jadx.core.dex.visitors.regions.DepthRegionTraversal.traverseIterativeStepInternal(DepthRegionTraversal.java:77)
    	at jadx.core.dex.visitors.regions.DepthRegionTraversal.traverseIterativeStepInternal(DepthRegionTraversal.java:82)
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void y(android.content.Context r10, org.xmlpull.v1.XmlPullParser r11) {
        /*
            Method dump skipped, instructions count: 560
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.widget.c.y(android.content.Context, org.xmlpull.v1.XmlPullParser):void");
    }
}
