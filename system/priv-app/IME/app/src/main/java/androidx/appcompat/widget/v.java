package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.database.DataSetObserver;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.PopupWindow;
import androidx.recyclerview.widget.RecyclerView;

/* loaded from: classes.dex */
public class v implements androidx.appcompat.view.menu.p {

    /* renamed from: a, reason: collision with root package name */
    private Context f839a;

    /* renamed from: b, reason: collision with root package name */
    private ListAdapter f840b;

    /* renamed from: c, reason: collision with root package name */
    r f841c;
    private int f;
    private int g;
    private boolean i;
    private boolean j;
    private boolean k;
    private DataSetObserver o;
    private View p;
    private AdapterView.OnItemClickListener q;
    final Handler v;
    private Rect x;
    private boolean y;
    PopupWindow z;

    /* renamed from: d, reason: collision with root package name */
    private int f842d = -2;
    private int e = -2;
    private int h = 1002;
    private int l = 0;
    int m = Integer.MAX_VALUE;
    private int n = 0;
    final e r = new e();
    private final d s = new d();
    private final c t = new c();
    private final a u = new a();
    private final Rect w = new Rect();

    private class a implements Runnable {
        a() {
        }

        @Override // java.lang.Runnable
        public void run() {
            r rVar = v.this.f841c;
            if (rVar != null) {
                rVar.setListSelectionHidden(true);
                rVar.requestLayout();
            }
        }
    }

    private class b extends DataSetObserver {
        b() {
        }

        @Override // android.database.DataSetObserver
        public void onChanged() {
            if (v.this.b()) {
                v.this.f();
            }
        }

        @Override // android.database.DataSetObserver
        public void onInvalidated() {
            v.this.dismiss();
        }
    }

    private class c implements AbsListView.OnScrollListener {
        c() {
        }

        @Override // android.widget.AbsListView.OnScrollListener
        public void onScroll(AbsListView absListView, int i, int i2, int i3) {
        }

        @Override // android.widget.AbsListView.OnScrollListener
        public void onScrollStateChanged(AbsListView absListView, int i) {
            if (i == 1) {
                if ((v.this.z.getInputMethodMode() == 2) || v.this.z.getContentView() == null) {
                    return;
                }
                v vVar = v.this;
                vVar.v.removeCallbacks(vVar.r);
                v.this.r.run();
            }
        }
    }

    private class d implements View.OnTouchListener {
        d() {
        }

        @Override // android.view.View.OnTouchListener
        public boolean onTouch(View view, MotionEvent motionEvent) {
            PopupWindow popupWindow;
            int action = motionEvent.getAction();
            int x = (int) motionEvent.getX();
            int y = (int) motionEvent.getY();
            if (action == 0 && (popupWindow = v.this.z) != null && popupWindow.isShowing() && x >= 0 && x < v.this.z.getWidth() && y >= 0 && y < v.this.z.getHeight()) {
                v vVar = v.this;
                vVar.v.postDelayed(vVar.r, 250L);
                return false;
            }
            if (action != 1) {
                return false;
            }
            v vVar2 = v.this;
            vVar2.v.removeCallbacks(vVar2.r);
            return false;
        }
    }

    private class e implements Runnable {
        e() {
        }

        @Override // java.lang.Runnable
        public void run() {
            r rVar = v.this.f841c;
            if (rVar != null) {
                int i = a.h.h.q.e;
                if (!rVar.isAttachedToWindow() || v.this.f841c.getCount() <= v.this.f841c.getChildCount()) {
                    return;
                }
                int childCount = v.this.f841c.getChildCount();
                v vVar = v.this;
                if (childCount <= vVar.m) {
                    vVar.z.setInputMethodMode(2);
                    v.this.f();
                }
            }
        }
    }

    public v(Context context, AttributeSet attributeSet, int i, int i2) {
        this.f839a = context;
        this.v = new Handler(context.getMainLooper());
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, a.b.b.p, i, i2);
        this.f = obtainStyledAttributes.getDimensionPixelOffset(0, 0);
        int dimensionPixelOffset = obtainStyledAttributes.getDimensionPixelOffset(1, 0);
        this.g = dimensionPixelOffset;
        if (dimensionPixelOffset != 0) {
            this.i = true;
        }
        obtainStyledAttributes.recycle();
        C0104i c0104i = new C0104i(context, attributeSet, i, i2);
        this.z = c0104i;
        c0104i.setInputMethodMode(1);
    }

    public void A(int i) {
        this.l = i;
    }

    public void B(Rect rect) {
        this.x = rect != null ? new Rect(rect) : null;
    }

    public void C(int i) {
        this.z.setInputMethodMode(i);
    }

    public void D(boolean z) {
        this.y = z;
        this.z.setFocusable(z);
    }

    public void E(PopupWindow.OnDismissListener onDismissListener) {
        this.z.setOnDismissListener(onDismissListener);
    }

    public void F(AdapterView.OnItemClickListener onItemClickListener) {
        this.q = onItemClickListener;
    }

    public void G(boolean z) {
        this.k = true;
        this.j = z;
    }

    public void H(int i) {
        this.n = i;
    }

    public void a(int i) {
        this.f = i;
    }

    @Override // androidx.appcompat.view.menu.p
    public boolean b() {
        return this.z.isShowing();
    }

    public int c() {
        return this.f;
    }

    @Override // androidx.appcompat.view.menu.p
    public void dismiss() {
        this.z.dismiss();
        this.z.setContentView(null);
        this.f841c = null;
        this.v.removeCallbacks(this.r);
    }

    @Override // androidx.appcompat.view.menu.p
    public void f() {
        int i;
        int i2;
        int paddingBottom;
        r rVar;
        if (this.f841c == null) {
            r q = q(this.f839a, !this.y);
            this.f841c = q;
            q.setAdapter(this.f840b);
            this.f841c.setOnItemClickListener(this.q);
            this.f841c.setFocusable(true);
            this.f841c.setFocusableInTouchMode(true);
            this.f841c.setOnItemSelectedListener(new u(this));
            this.f841c.setOnScrollListener(this.t);
            this.z.setContentView(this.f841c);
        }
        Drawable background = this.z.getBackground();
        if (background != null) {
            background.getPadding(this.w);
            Rect rect = this.w;
            int i3 = rect.top;
            i = rect.bottom + i3;
            if (!this.i) {
                this.g = -i3;
            }
        } else {
            this.w.setEmpty();
            i = 0;
        }
        int maxAvailableHeight = this.z.getMaxAvailableHeight(this.p, this.g, this.z.getInputMethodMode() == 2);
        if (this.f842d == -1) {
            paddingBottom = maxAvailableHeight + i;
        } else {
            int i4 = this.e;
            if (i4 != -2) {
                i2 = 1073741824;
                if (i4 == -1) {
                    int i5 = this.f839a.getResources().getDisplayMetrics().widthPixels;
                    Rect rect2 = this.w;
                    i4 = i5 - (rect2.left + rect2.right);
                }
            } else {
                int i6 = this.f839a.getResources().getDisplayMetrics().widthPixels;
                Rect rect3 = this.w;
                i4 = i6 - (rect3.left + rect3.right);
                i2 = RecyclerView.UNDEFINED_DURATION;
            }
            int measureHeightOfChildrenCompat = this.f841c.measureHeightOfChildrenCompat(View.MeasureSpec.makeMeasureSpec(i4, i2), 0, -1, maxAvailableHeight + 0, -1);
            paddingBottom = measureHeightOfChildrenCompat + (measureHeightOfChildrenCompat > 0 ? this.f841c.getPaddingBottom() + this.f841c.getPaddingTop() + i + 0 : 0);
        }
        boolean z = this.z.getInputMethodMode() == 2;
        this.z.setWindowLayoutType(this.h);
        if (this.z.isShowing()) {
            View view = this.p;
            int i7 = a.h.h.q.e;
            if (view.isAttachedToWindow()) {
                int i8 = this.e;
                if (i8 == -1) {
                    i8 = -1;
                } else if (i8 == -2) {
                    i8 = this.p.getWidth();
                }
                int i9 = this.f842d;
                if (i9 == -1) {
                    if (!z) {
                        paddingBottom = -1;
                    }
                    if (z) {
                        this.z.setWidth(this.e == -1 ? -1 : 0);
                        this.z.setHeight(0);
                    } else {
                        this.z.setWidth(this.e == -1 ? -1 : 0);
                        this.z.setHeight(-1);
                    }
                } else if (i9 != -2) {
                    paddingBottom = i9;
                }
                this.z.setOutsideTouchable(true);
                this.z.update(this.p, this.f, this.g, i8 < 0 ? -1 : i8, paddingBottom < 0 ? -1 : paddingBottom);
                return;
            }
            return;
        }
        int i10 = this.e;
        if (i10 == -1) {
            i10 = -1;
        } else if (i10 == -2) {
            i10 = this.p.getWidth();
        }
        int i11 = this.f842d;
        if (i11 == -1) {
            paddingBottom = -1;
        } else if (i11 != -2) {
            paddingBottom = i11;
        }
        this.z.setWidth(i10);
        this.z.setHeight(paddingBottom);
        this.z.setIsClippedToScreen(true);
        this.z.setOutsideTouchable(true);
        this.z.setTouchInterceptor(this.s);
        if (this.k) {
            this.z.setOverlapAnchor(this.j);
        }
        this.z.setEpicenterBounds(this.x);
        this.z.showAsDropDown(this.p, this.f, this.g, this.l);
        this.f841c.setSelection(-1);
        if ((!this.y || this.f841c.isInTouchMode()) && (rVar = this.f841c) != null) {
            rVar.setListSelectionHidden(true);
            rVar.requestLayout();
        }
        if (this.y) {
            return;
        }
        this.v.post(this.u);
    }

    public int g() {
        if (this.i) {
            return this.g;
        }
        return 0;
    }

    public Drawable i() {
        return this.z.getBackground();
    }

    @Override // androidx.appcompat.view.menu.p
    public ListView k() {
        return this.f841c;
    }

    public void m(Drawable drawable) {
        this.z.setBackgroundDrawable(drawable);
    }

    public void n(int i) {
        this.g = i;
        this.i = true;
    }

    public void o(ListAdapter listAdapter) {
        DataSetObserver dataSetObserver = this.o;
        if (dataSetObserver == null) {
            this.o = new b();
        } else {
            ListAdapter listAdapter2 = this.f840b;
            if (listAdapter2 != null) {
                listAdapter2.unregisterDataSetObserver(dataSetObserver);
            }
        }
        this.f840b = listAdapter;
        if (listAdapter != null) {
            listAdapter.registerDataSetObserver(this.o);
        }
        r rVar = this.f841c;
        if (rVar != null) {
            rVar.setAdapter(this.f840b);
        }
    }

    r q(Context context, boolean z) {
        return new r(context, z);
    }

    public Object r() {
        if (b()) {
            return this.f841c.getSelectedItem();
        }
        return null;
    }

    public long s() {
        if (b()) {
            return this.f841c.getSelectedItemId();
        }
        return Long.MIN_VALUE;
    }

    public int t() {
        if (b()) {
            return this.f841c.getSelectedItemPosition();
        }
        return -1;
    }

    public View u() {
        if (b()) {
            return this.f841c.getSelectedView();
        }
        return null;
    }

    public int v() {
        return this.e;
    }

    public boolean w() {
        return this.y;
    }

    public void x(View view) {
        this.p = view;
    }

    public void y(int i) {
        this.z.setAnimationStyle(i);
    }

    public void z(int i) {
        Drawable background = this.z.getBackground();
        if (background == null) {
            this.e = i;
            return;
        }
        background.getPadding(this.w);
        Rect rect = this.w;
        this.e = rect.left + rect.right + i;
    }
}
