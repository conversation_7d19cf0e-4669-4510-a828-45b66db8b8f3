package androidx.constraintlayout.helper.widget;

import a.f.a.j.e;
import a.f.a.j.g;
import a.f.a.j.j;
import a.f.a.j.m;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.util.SparseArray;
import android.view.View;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.VirtualLayout;
import androidx.constraintlayout.widget.c;
import androidx.constraintlayout.widget.f;

/* loaded from: classes.dex */
public class Flow extends VirtualLayout {
    public static final int CHAIN_PACKED = 2;
    public static final int CHAIN_SPREAD = 0;
    public static final int CHAIN_SPREAD_INSIDE = 1;
    public static final int HORIZONTAL = 0;
    public static final int HORIZONTAL_ALIGN_CENTER = 2;
    public static final int HORIZONTAL_ALIGN_END = 1;
    public static final int HORIZONTAL_ALIGN_START = 0;
    private static final String TAG = "Flow";
    public static final int VERTICAL = 1;
    public static final int VERTICAL_ALIGN_BASELINE = 3;
    public static final int VERTICAL_ALIGN_BOTTOM = 1;
    public static final int VERTICAL_ALIGN_CENTER = 2;
    public static final int VERTICAL_ALIGN_TOP = 0;
    public static final int WRAP_ALIGNED = 2;
    public static final int WRAP_CHAIN = 1;
    public static final int WRAP_NONE = 0;
    private g mFlow;

    public Flow(Context context) {
        super(context);
    }

    public Flow(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
    }

    public Flow(Context context, AttributeSet attributeSet, int i) {
        super(context, attributeSet, i);
    }

    @Override // androidx.constraintlayout.widget.VirtualLayout, androidx.constraintlayout.widget.ConstraintHelper
    protected void init(AttributeSet attributeSet) {
        super.init(attributeSet);
        this.mFlow = new g();
        if (attributeSet != null) {
            TypedArray obtainStyledAttributes = getContext().obtainStyledAttributes(attributeSet, f.f1021c);
            int indexCount = obtainStyledAttributes.getIndexCount();
            for (int i = 0; i < indexCount; i++) {
                int index = obtainStyledAttributes.getIndex(i);
                if (index == 0) {
                    this.mFlow.c2(obtainStyledAttributes.getInt(index, 0));
                } else if (index == 1) {
                    this.mFlow.m1(obtainStyledAttributes.getDimensionPixelSize(index, 0));
                } else if (index == 18) {
                    this.mFlow.r1(obtainStyledAttributes.getDimensionPixelSize(index, 0));
                } else if (index == 19) {
                    this.mFlow.o1(obtainStyledAttributes.getDimensionPixelSize(index, 0));
                } else if (index == 2) {
                    this.mFlow.p1(obtainStyledAttributes.getDimensionPixelSize(index, 0));
                } else if (index == 3) {
                    this.mFlow.s1(obtainStyledAttributes.getDimensionPixelSize(index, 0));
                } else if (index == 4) {
                    this.mFlow.q1(obtainStyledAttributes.getDimensionPixelSize(index, 0));
                } else if (index == 5) {
                    this.mFlow.n1(obtainStyledAttributes.getDimensionPixelSize(index, 0));
                } else if (index == 54) {
                    this.mFlow.h2(obtainStyledAttributes.getInt(index, 0));
                } else if (index == 44) {
                    this.mFlow.W1(obtainStyledAttributes.getInt(index, 0));
                } else if (index == 53) {
                    this.mFlow.g2(obtainStyledAttributes.getInt(index, 0));
                } else if (index == 38) {
                    this.mFlow.Q1(obtainStyledAttributes.getInt(index, 0));
                } else if (index == 46) {
                    this.mFlow.Y1(obtainStyledAttributes.getInt(index, 0));
                } else if (index == 40) {
                    this.mFlow.S1(obtainStyledAttributes.getInt(index, 0));
                } else if (index == 48) {
                    this.mFlow.a2(obtainStyledAttributes.getInt(index, 0));
                } else if (index == 42) {
                    this.mFlow.U1(obtainStyledAttributes.getFloat(index, 0.5f));
                } else if (index == 37) {
                    this.mFlow.P1(obtainStyledAttributes.getFloat(index, 0.5f));
                } else if (index == 45) {
                    this.mFlow.X1(obtainStyledAttributes.getFloat(index, 0.5f));
                } else if (index == 39) {
                    this.mFlow.R1(obtainStyledAttributes.getFloat(index, 0.5f));
                } else if (index == 47) {
                    this.mFlow.Z1(obtainStyledAttributes.getFloat(index, 0.5f));
                } else if (index == 51) {
                    this.mFlow.e2(obtainStyledAttributes.getFloat(index, 0.5f));
                } else if (index == 41) {
                    this.mFlow.T1(obtainStyledAttributes.getInt(index, 2));
                } else if (index == 50) {
                    this.mFlow.d2(obtainStyledAttributes.getInt(index, 2));
                } else if (index == 43) {
                    this.mFlow.V1(obtainStyledAttributes.getDimensionPixelSize(index, 0));
                } else if (index == 52) {
                    this.mFlow.f2(obtainStyledAttributes.getDimensionPixelSize(index, 0));
                } else if (index == 49) {
                    this.mFlow.b2(obtainStyledAttributes.getInt(index, -1));
                }
            }
            obtainStyledAttributes.recycle();
        }
        this.mHelperWidget = this.mFlow;
        validateParams();
    }

    @Override // androidx.constraintlayout.widget.ConstraintHelper
    public void loadParameters(c.a aVar, j jVar, ConstraintLayout.a aVar2, SparseArray<e> sparseArray) {
        super.loadParameters(aVar, jVar, aVar2, sparseArray);
        if (jVar instanceof g) {
            g gVar = (g) jVar;
            int i = aVar2.V;
            if (i != -1) {
                gVar.c2(i);
            }
        }
    }

    @Override // androidx.constraintlayout.widget.ConstraintHelper, android.view.View
    @SuppressLint({"WrongCall"})
    protected void onMeasure(int i, int i2) {
        onMeasure(this.mFlow, i, i2);
    }

    @Override // androidx.constraintlayout.widget.VirtualLayout
    public void onMeasure(m mVar, int i, int i2) {
        int mode = View.MeasureSpec.getMode(i);
        int size = View.MeasureSpec.getSize(i);
        int mode2 = View.MeasureSpec.getMode(i2);
        int size2 = View.MeasureSpec.getSize(i2);
        if (mVar == null) {
            setMeasuredDimension(0, 0);
        } else {
            mVar.h1(mode, size, mode2, size2);
            setMeasuredDimension(mVar.c1(), mVar.b1());
        }
    }

    @Override // androidx.constraintlayout.widget.ConstraintHelper
    public void resolveRtl(e eVar, boolean z) {
        this.mFlow.Z0(z);
    }

    public void setFirstHorizontalBias(float f) {
        this.mFlow.P1(f);
        requestLayout();
    }

    public void setFirstHorizontalStyle(int i) {
        this.mFlow.Q1(i);
        requestLayout();
    }

    public void setFirstVerticalBias(float f) {
        this.mFlow.R1(f);
        requestLayout();
    }

    public void setFirstVerticalStyle(int i) {
        this.mFlow.S1(i);
        requestLayout();
    }

    public void setHorizontalAlign(int i) {
        this.mFlow.T1(i);
        requestLayout();
    }

    public void setHorizontalBias(float f) {
        this.mFlow.U1(f);
        requestLayout();
    }

    public void setHorizontalGap(int i) {
        this.mFlow.V1(i);
        requestLayout();
    }

    public void setHorizontalStyle(int i) {
        this.mFlow.W1(i);
        requestLayout();
    }

    public void setLastHorizontalBias(float f) {
        this.mFlow.X1(f);
        requestLayout();
    }

    public void setLastHorizontalStyle(int i) {
        this.mFlow.Y1(i);
        requestLayout();
    }

    public void setLastVerticalBias(float f) {
        this.mFlow.Z1(f);
        requestLayout();
    }

    public void setLastVerticalStyle(int i) {
        this.mFlow.a2(i);
        requestLayout();
    }

    public void setMaxElementsWrap(int i) {
        this.mFlow.b2(i);
        requestLayout();
    }

    public void setOrientation(int i) {
        this.mFlow.c2(i);
        requestLayout();
    }

    public void setPadding(int i) {
        this.mFlow.m1(i);
        requestLayout();
    }

    public void setPaddingBottom(int i) {
        this.mFlow.n1(i);
        requestLayout();
    }

    public void setPaddingLeft(int i) {
        this.mFlow.p1(i);
        requestLayout();
    }

    public void setPaddingRight(int i) {
        this.mFlow.q1(i);
        requestLayout();
    }

    public void setPaddingTop(int i) {
        this.mFlow.s1(i);
        requestLayout();
    }

    public void setVerticalAlign(int i) {
        this.mFlow.d2(i);
        requestLayout();
    }

    public void setVerticalBias(float f) {
        this.mFlow.e2(f);
        requestLayout();
    }

    public void setVerticalGap(int i) {
        this.mFlow.f2(i);
        requestLayout();
    }

    public void setVerticalStyle(int i) {
        this.mFlow.g2(i);
        requestLayout();
    }

    public void setWrapMode(int i) {
        this.mFlow.h2(i);
        requestLayout();
    }
}
