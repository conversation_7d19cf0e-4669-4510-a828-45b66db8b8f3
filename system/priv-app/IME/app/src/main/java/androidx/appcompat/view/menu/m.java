package androidx.appcompat.view.menu;

import android.content.Context;
import android.os.Parcelable;

/* loaded from: classes.dex */
public interface m {

    public interface a {
        void a(g gVar, boolean z);

        boolean b(g gVar);
    }

    void a(g gVar, boolean z);

    int c();

    boolean d();

    Parcelable e();

    void g(Context context, g gVar);

    void h(Parcelable parcelable);

    boolean i(g gVar, i iVar);

    boolean j(g gVar, i iVar);

    void l(a aVar);

    boolean m(r rVar);

    void n(boolean z);
}
