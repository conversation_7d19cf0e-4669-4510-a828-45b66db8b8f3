package androidx.constraintlayout.motion.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseIntArray;
import com.google.android.material.button.MaterialButton;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;

/* loaded from: classes.dex */
public class j extends d {
    private String f;
    private int g = -1;
    private float h = Float.NaN;
    private float i = Float.NaN;
    private float j = Float.NaN;
    private float k = Float.NaN;
    private float l = Float.NaN;
    private float m = Float.NaN;
    private float n = Float.NaN;
    private float o = Float.NaN;
    private float p = Float.NaN;
    private float q = Float.NaN;
    private float r = Float.NaN;
    private float s = Float.NaN;
    private int t = 0;
    private float u = Float.NaN;
    private float v = 0.0f;

    private static class a {

        /* renamed from: a, reason: collision with root package name */
        private static SparseIntArray f906a;

        static {
            SparseIntArray sparseIntArray = new SparseIntArray();
            f906a = sparseIntArray;
            sparseIntArray.append(0, 1);
            f906a.append(9, 2);
            f906a.append(5, 4);
            f906a.append(6, 5);
            f906a.append(7, 6);
            f906a.append(3, 7);
            f906a.append(15, 8);
            f906a.append(14, 9);
            f906a.append(13, 10);
            f906a.append(11, 12);
            f906a.append(10, 13);
            f906a.append(4, 14);
            f906a.append(1, 15);
            f906a.append(2, 16);
            f906a.append(8, 17);
            f906a.append(12, 18);
            f906a.append(18, 20);
            f906a.append(17, 21);
            f906a.append(20, 19);
        }

        public static void a(j jVar, TypedArray typedArray) {
            int i;
            int indexCount = typedArray.getIndexCount();
            for (int i2 = 0; i2 < indexCount; i2++) {
                int index = typedArray.getIndex(i2);
                switch (f906a.get(index)) {
                    case 1:
                        jVar.h = typedArray.getFloat(index, jVar.h);
                        break;
                    case 2:
                        jVar.i = typedArray.getDimension(index, jVar.i);
                        break;
                    case 3:
                    case 11:
                    default:
                        StringBuilder j = b.b.a.a.a.j("unused attribute 0x");
                        j.append(Integer.toHexString(index));
                        j.append("   ");
                        j.append(f906a.get(index));
                        Log.e("KeyTimeCycle", j.toString());
                        break;
                    case 4:
                        jVar.j = typedArray.getFloat(index, jVar.j);
                        break;
                    case 5:
                        jVar.k = typedArray.getFloat(index, jVar.k);
                        break;
                    case 6:
                        jVar.l = typedArray.getFloat(index, jVar.l);
                        break;
                    case MotionLayout.TOUCH_UP_NEVER_TO_END /* 7 */:
                        jVar.n = typedArray.getFloat(index, jVar.n);
                        break;
                    case 8:
                        jVar.m = typedArray.getFloat(index, jVar.m);
                        break;
                    case 9:
                        jVar.f = typedArray.getString(index);
                        break;
                    case 10:
                        if (MotionLayout.IS_IN_EDIT_MODE) {
                            int resourceId = typedArray.getResourceId(index, jVar.f898b);
                            jVar.f898b = resourceId;
                            if (resourceId != -1) {
                                break;
                            }
                            jVar.f899c = typedArray.getString(index);
                            break;
                        } else {
                            if (typedArray.peekValue(index).type != 3) {
                                jVar.f898b = typedArray.getResourceId(index, jVar.f898b);
                                break;
                            }
                            jVar.f899c = typedArray.getString(index);
                        }
                    case 12:
                        jVar.f897a = typedArray.getInt(index, jVar.f897a);
                        break;
                    case 13:
                        jVar.g = typedArray.getInteger(index, jVar.g);
                        break;
                    case 14:
                        jVar.o = typedArray.getFloat(index, jVar.o);
                        break;
                    case 15:
                        jVar.p = typedArray.getDimension(index, jVar.p);
                        break;
                    case MaterialButton.ICON_GRAVITY_TOP /* 16 */:
                        jVar.q = typedArray.getDimension(index, jVar.q);
                        break;
                    case 17:
                        jVar.r = typedArray.getDimension(index, jVar.r);
                        break;
                    case 18:
                        jVar.s = typedArray.getFloat(index, jVar.s);
                        break;
                    case 19:
                        if (typedArray.peekValue(index).type == 3) {
                            typedArray.getString(index);
                            i = 7;
                        } else {
                            i = typedArray.getInt(index, jVar.t);
                        }
                        jVar.t = i;
                        break;
                    case 20:
                        jVar.u = typedArray.getFloat(index, jVar.u);
                        break;
                    case 21:
                        jVar.v = typedArray.peekValue(index).type == 5 ? typedArray.getDimension(index, jVar.v) : typedArray.getFloat(index, jVar.v);
                        break;
                }
            }
        }
    }

    public j() {
        this.f900d = 3;
        this.e = new HashMap<>();
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Code restructure failed: missing block: B:105:0x0089, code lost:
    
        if (r1.equals("scaleY") == false) goto L15;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void Q(java.util.HashMap<java.lang.String, a.f.b.a.f> r11) {
        /*
            Method dump skipped, instructions count: 496
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.motion.widget.j.Q(java.util.HashMap):void");
    }

    @Override // androidx.constraintlayout.motion.widget.d
    public void a(HashMap<String, a.f.b.a.d> hashMap) {
        throw new IllegalArgumentException(" KeyTimeCycles do not support SplineSet");
    }

    @Override // androidx.constraintlayout.motion.widget.d
    /* renamed from: b */
    public d clone() {
        j jVar = new j();
        super.c(this);
        jVar.f = this.f;
        jVar.g = this.g;
        jVar.t = this.t;
        jVar.u = this.u;
        jVar.v = this.v;
        jVar.s = this.s;
        jVar.h = this.h;
        jVar.i = this.i;
        jVar.j = this.j;
        jVar.m = this.m;
        jVar.k = this.k;
        jVar.l = this.l;
        jVar.n = this.n;
        jVar.o = this.o;
        jVar.p = this.p;
        jVar.q = this.q;
        jVar.r = this.r;
        return jVar;
    }

    @Override // androidx.constraintlayout.motion.widget.d
    public void d(HashSet<String> hashSet) {
        if (!Float.isNaN(this.h)) {
            hashSet.add("alpha");
        }
        if (!Float.isNaN(this.i)) {
            hashSet.add("elevation");
        }
        if (!Float.isNaN(this.j)) {
            hashSet.add("rotation");
        }
        if (!Float.isNaN(this.k)) {
            hashSet.add("rotationX");
        }
        if (!Float.isNaN(this.l)) {
            hashSet.add("rotationY");
        }
        if (!Float.isNaN(this.p)) {
            hashSet.add("translationX");
        }
        if (!Float.isNaN(this.q)) {
            hashSet.add("translationY");
        }
        if (!Float.isNaN(this.r)) {
            hashSet.add("translationZ");
        }
        if (!Float.isNaN(this.m)) {
            hashSet.add("transitionPathRotate");
        }
        if (!Float.isNaN(this.n)) {
            hashSet.add("scaleX");
        }
        if (!Float.isNaN(this.o)) {
            hashSet.add("scaleY");
        }
        if (!Float.isNaN(this.s)) {
            hashSet.add("progress");
        }
        if (this.e.size() > 0) {
            Iterator<String> it = this.e.keySet().iterator();
            while (it.hasNext()) {
                hashSet.add("CUSTOM," + it.next());
            }
        }
    }

    @Override // androidx.constraintlayout.motion.widget.d
    public void e(Context context, AttributeSet attributeSet) {
        a.a(this, context.obtainStyledAttributes(attributeSet, androidx.constraintlayout.widget.f.m));
    }

    @Override // androidx.constraintlayout.motion.widget.d
    public void g(HashMap<String, Integer> hashMap) {
        if (this.g == -1) {
            return;
        }
        if (!Float.isNaN(this.h)) {
            hashMap.put("alpha", Integer.valueOf(this.g));
        }
        if (!Float.isNaN(this.i)) {
            hashMap.put("elevation", Integer.valueOf(this.g));
        }
        if (!Float.isNaN(this.j)) {
            hashMap.put("rotation", Integer.valueOf(this.g));
        }
        if (!Float.isNaN(this.k)) {
            hashMap.put("rotationX", Integer.valueOf(this.g));
        }
        if (!Float.isNaN(this.l)) {
            hashMap.put("rotationY", Integer.valueOf(this.g));
        }
        if (!Float.isNaN(this.p)) {
            hashMap.put("translationX", Integer.valueOf(this.g));
        }
        if (!Float.isNaN(this.q)) {
            hashMap.put("translationY", Integer.valueOf(this.g));
        }
        if (!Float.isNaN(this.r)) {
            hashMap.put("translationZ", Integer.valueOf(this.g));
        }
        if (!Float.isNaN(this.m)) {
            hashMap.put("transitionPathRotate", Integer.valueOf(this.g));
        }
        if (!Float.isNaN(this.n)) {
            hashMap.put("scaleX", Integer.valueOf(this.g));
        }
        if (!Float.isNaN(this.n)) {
            hashMap.put("scaleY", Integer.valueOf(this.g));
        }
        if (!Float.isNaN(this.s)) {
            hashMap.put("progress", Integer.valueOf(this.g));
        }
        if (this.e.size() > 0) {
            Iterator<String> it = this.e.keySet().iterator();
            while (it.hasNext()) {
                hashMap.put(b.b.a.a.a.g("CUSTOM,", it.next()), Integer.valueOf(this.g));
            }
        }
    }
}
