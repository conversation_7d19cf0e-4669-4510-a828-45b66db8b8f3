package androidx.constraintlayout.motion.widget;

import androidx.constraintlayout.widget.c;
import java.util.LinkedHashMap;

/* loaded from: classes.dex */
class p implements Comparable<p> {
    static String[] r = {"position", "x", "y", "width", "height", "pathRotate"};

    /* renamed from: a, reason: collision with root package name */
    a.f.a.i.a.c f917a;

    /* renamed from: b, reason: collision with root package name */
    int f918b;

    /* renamed from: c, reason: collision with root package name */
    float f919c;

    /* renamed from: d, reason: collision with root package name */
    float f920d;
    float e;
    float f;
    float g;
    float h;
    float i;
    int j;
    int k;
    float l;
    n m;
    LinkedHashMap<String, androidx.constraintlayout.widget.a> n;
    int o;
    double[] p;
    double[] q;

    public p() {
        this.f918b = 0;
        this.i = Float.NaN;
        this.j = -1;
        this.k = -1;
        this.l = Float.NaN;
        this.m = null;
        this.n = new LinkedHashMap<>();
        this.o = 0;
        this.p = new double[18];
        this.q = new double[18];
    }

    /* JADX WARN: Code restructure failed: missing block: B:16:0x0099, code lost:
    
        if (java.lang.Float.isNaN(r20.m) != false) goto L38;
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x00e1, code lost:
    
        r4 = r20.m;
     */
    /* JADX WARN: Code restructure failed: missing block: B:34:0x00de, code lost:
    
        if (java.lang.Float.isNaN(r20.m) != false) goto L38;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public p(int r18, int r19, androidx.constraintlayout.motion.widget.h r20, androidx.constraintlayout.motion.widget.p r21, androidx.constraintlayout.motion.widget.p r22) {
        /*
            Method dump skipped, instructions count: 808
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.motion.widget.p.<init>(int, int, androidx.constraintlayout.motion.widget.h, androidx.constraintlayout.motion.widget.p, androidx.constraintlayout.motion.widget.p):void");
    }

    private boolean b(float f, float f2) {
        return (Float.isNaN(f) || Float.isNaN(f2)) ? Float.isNaN(f) != Float.isNaN(f2) : Math.abs(f - f2) > 1.0E-6f;
    }

    public void a(c.a aVar) {
        this.f917a = a.f.a.i.a.c.c(aVar.f998d.f1010d);
        c.C0034c c0034c = aVar.f998d;
        this.j = c0034c.e;
        this.k = c0034c.f1008b;
        this.i = c0034c.i;
        this.f918b = c0034c.f;
        int i = c0034c.f1009c;
        float f = aVar.f997c.e;
        this.l = aVar.e.C;
        for (String str : aVar.g.keySet()) {
            androidx.constraintlayout.widget.a aVar2 = aVar.g.get(str);
            if (aVar2 != null && aVar2.f()) {
                this.n.put(str, aVar2);
            }
        }
    }

    void c(p pVar, boolean[] zArr, boolean z) {
        boolean b2 = b(this.e, pVar.e);
        boolean b3 = b(this.f, pVar.f);
        zArr[0] = zArr[0] | b(this.f920d, pVar.f920d);
        boolean z2 = z | b2 | b3;
        zArr[1] = zArr[1] | z2;
        zArr[2] = z2 | zArr[2];
        zArr[3] = zArr[3] | b(this.g, pVar.g);
        zArr[4] = b(this.h, pVar.h) | zArr[4];
    }

    @Override // java.lang.Comparable
    public int compareTo(p pVar) {
        return Float.compare(this.f920d, pVar.f920d);
    }

    void d(double d2, int[] iArr, double[] dArr, float[] fArr, int i) {
        float f = this.e;
        float f2 = this.f;
        float f3 = this.g;
        float f4 = this.h;
        for (int i2 = 0; i2 < iArr.length; i2++) {
            float f5 = (float) dArr[i2];
            int i3 = iArr[i2];
            if (i3 == 1) {
                f = f5;
            } else if (i3 == 2) {
                f2 = f5;
            } else if (i3 == 3) {
                f3 = f5;
            } else if (i3 == 4) {
                f4 = f5;
            }
        }
        n nVar = this.m;
        if (nVar != null) {
            float[] fArr2 = new float[2];
            nVar.i(d2, fArr2, new float[2]);
            float f6 = fArr2[0];
            float f7 = fArr2[1];
            double d3 = f;
            double d4 = f2;
            double sin = Math.sin(d4) * d3;
            f2 = (float) ((f7 - (Math.cos(d4) * d3)) - (f4 / 2.0f));
            f = (float) ((sin + f6) - (f3 / 2.0f));
        }
        fArr[i] = (f3 / 2.0f) + f + 0.0f;
        fArr[i + 1] = (f4 / 2.0f) + f2 + 0.0f;
    }

    void e(float f, float f2, float f3, float f4) {
        this.e = f;
        this.f = f2;
        this.g = f3;
        this.h = f4;
    }

    void f(float f, float f2, float[] fArr, int[] iArr, double[] dArr, double[] dArr2) {
        float f3 = 0.0f;
        float f4 = 0.0f;
        float f5 = 0.0f;
        float f6 = 0.0f;
        for (int i = 0; i < iArr.length; i++) {
            float f7 = (float) dArr[i];
            double d2 = dArr2[i];
            int i2 = iArr[i];
            if (i2 == 1) {
                f3 = f7;
            } else if (i2 == 2) {
                f5 = f7;
            } else if (i2 == 3) {
                f4 = f7;
            } else if (i2 == 4) {
                f6 = f7;
            }
        }
        float f8 = f3 - ((0.0f * f4) / 2.0f);
        float f9 = f5 - ((0.0f * f6) / 2.0f);
        fArr[0] = (((f4 * 1.0f) + f8) * f) + ((1.0f - f) * f8) + 0.0f;
        fArr[1] = (((f6 * 1.0f) + f9) * f2) + ((1.0f - f2) * f9) + 0.0f;
    }

    public void g(n nVar, p pVar) {
        double d2 = (((this.g / 2.0f) + this.e) - pVar.e) - (pVar.g / 2.0f);
        double d3 = (((this.h / 2.0f) + this.f) - pVar.f) - (pVar.h / 2.0f);
        this.m = nVar;
        this.e = (float) Math.hypot(d3, d2);
        this.f = (float) (Float.isNaN(this.l) ? Math.atan2(d3, d2) + 1.5707963267948966d : Math.toRadians(this.l));
    }
}
