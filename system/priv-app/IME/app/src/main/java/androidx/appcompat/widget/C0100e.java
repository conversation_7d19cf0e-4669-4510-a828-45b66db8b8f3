package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;

/* renamed from: androidx.appcompat.widget.e, reason: case insensitive filesystem */
/* loaded from: classes.dex */
class C0100e {

    /* renamed from: a, reason: collision with root package name */
    private final View f791a;

    /* renamed from: d, reason: collision with root package name */
    private E f794d;
    private E e;
    private E f;

    /* renamed from: c, reason: collision with root package name */
    private int f793c = -1;

    /* renamed from: b, reason: collision with root package name */
    private final C0102g f792b = C0102g.b();

    C0100e(View view) {
        this.f791a = view;
    }

    void a() {
        Drawable background = this.f791a.getBackground();
        if (background != null) {
            boolean z = true;
            if (this.f794d != null) {
                if (this.f == null) {
                    this.f = new E();
                }
                E e = this.f;
                e.f724a = null;
                e.f727d = false;
                e.f725b = null;
                e.f726c = false;
                View view = this.f791a;
                int i = a.h.h.q.e;
                ColorStateList backgroundTintList = view.getBackgroundTintList();
                if (backgroundTintList != null) {
                    e.f727d = true;
                    e.f724a = backgroundTintList;
                }
                PorterDuff.Mode backgroundTintMode = this.f791a.getBackgroundTintMode();
                if (backgroundTintMode != null) {
                    e.f726c = true;
                    e.f725b = backgroundTintMode;
                }
                if (e.f727d || e.f726c) {
                    int[] drawableState = this.f791a.getDrawableState();
                    int i2 = C0102g.f801d;
                    x.m(background, e, drawableState);
                } else {
                    z = false;
                }
                if (z) {
                    return;
                }
            }
            E e2 = this.e;
            if (e2 != null) {
                int[] drawableState2 = this.f791a.getDrawableState();
                int i3 = C0102g.f801d;
                x.m(background, e2, drawableState2);
            } else {
                E e3 = this.f794d;
                if (e3 != null) {
                    int[] drawableState3 = this.f791a.getDrawableState();
                    int i4 = C0102g.f801d;
                    x.m(background, e3, drawableState3);
                }
            }
        }
    }

    ColorStateList b() {
        E e = this.e;
        if (e != null) {
            return e.f724a;
        }
        return null;
    }

    PorterDuff.Mode c() {
        E e = this.e;
        if (e != null) {
            return e.f725b;
        }
        return null;
    }

    void d(AttributeSet attributeSet, int i) {
        Context context = this.f791a.getContext();
        int[] iArr = a.b.b.B;
        G v = G.v(context, attributeSet, iArr, i, 0);
        View view = this.f791a;
        Context context2 = view.getContext();
        TypedArray r = v.r();
        int i2 = a.h.h.q.e;
        view.saveAttributeDataForStyleable(context2, iArr, attributeSet, r, i, 0);
        try {
            if (v.s(0)) {
                this.f793c = v.n(0, -1);
                ColorStateList f = this.f792b.f(this.f791a.getContext(), this.f793c);
                if (f != null) {
                    g(f);
                }
            }
            if (v.s(1)) {
                this.f791a.setBackgroundTintList(v.c(1));
            }
            if (v.s(2)) {
                this.f791a.setBackgroundTintMode(q.c(v.k(2, -1), null));
            }
        } finally {
            v.w();
        }
    }

    void e() {
        this.f793c = -1;
        g(null);
        a();
    }

    void f(int i) {
        this.f793c = i;
        C0102g c0102g = this.f792b;
        g(c0102g != null ? c0102g.f(this.f791a.getContext(), i) : null);
        a();
    }

    void g(ColorStateList colorStateList) {
        if (colorStateList != null) {
            if (this.f794d == null) {
                this.f794d = new E();
            }
            E e = this.f794d;
            e.f724a = colorStateList;
            e.f727d = true;
        } else {
            this.f794d = null;
        }
        a();
    }

    void h(ColorStateList colorStateList) {
        if (this.e == null) {
            this.e = new E();
        }
        E e = this.e;
        e.f724a = colorStateList;
        e.f727d = true;
        a();
    }

    void i(PorterDuff.Mode mode) {
        if (this.e == null) {
            this.e = new E();
        }
        E e = this.e;
        e.f725b = mode;
        e.f726c = true;
        a();
    }
}
