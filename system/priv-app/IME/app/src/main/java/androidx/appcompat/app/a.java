package androidx.appcompat.app;

import android.content.Context;
import android.content.res.Configuration;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;

/* loaded from: classes.dex */
public abstract class a {

    /* renamed from: androidx.appcompat.app.a$a, reason: collision with other inner class name */
    public static class C0024a extends ViewGroup.MarginLayoutParams {

        /* renamed from: a, reason: collision with root package name */
        public int f558a;

        public C0024a(int i, int i2) {
            super(i, i2);
            this.f558a = 0;
            this.f558a = 8388627;
        }

        public C0024a(Context context, AttributeSet attributeSet) {
            super(context, attributeSet);
            this.f558a = 0;
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, a.b.b.f1b);
            this.f558a = obtainStyledAttributes.getInt(0, 0);
            obtainStyledAttributes.recycle();
        }

        public C0024a(ViewGroup.LayoutParams layoutParams) {
            super(layoutParams);
            this.f558a = 0;
        }

        public C0024a(C0024a c0024a) {
            super((ViewGroup.MarginLayoutParams) c0024a);
            this.f558a = 0;
            this.f558a = c0024a.f558a;
        }
    }

    public interface b {
        void a(boolean z);
    }

    @Deprecated
    public static abstract class c {
        public abstract CharSequence a();

        public abstract View b();

        public abstract Drawable c();

        public abstract CharSequence d();

        public abstract void e();
    }

    public abstract void a(boolean z);

    public abstract Context b();

    public abstract void c(Configuration configuration);

    public abstract void d(boolean z);

    public abstract void e(boolean z);
}
