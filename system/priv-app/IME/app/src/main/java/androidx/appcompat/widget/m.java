package androidx.appcompat.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.PorterDuff;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.os.LocaleList;
import android.text.method.PasswordTransformationMethod;
import android.util.AttributeSet;
import android.widget.TextView;
import java.lang.ref.WeakReference;
import java.util.Objects;

/* loaded from: classes.dex */
class m {

    /* renamed from: a, reason: collision with root package name */
    private final TextView f814a;

    /* renamed from: b, reason: collision with root package name */
    private E f815b;

    /* renamed from: c, reason: collision with root package name */
    private E f816c;

    /* renamed from: d, reason: collision with root package name */
    private E f817d;
    private E e;
    private E f;
    private E g;
    private E h;
    private final n i;
    private int j = 0;
    private int k = -1;
    private Typeface l;
    private boolean m;

    class a extends a.h.b.b.f {

        /* renamed from: a, reason: collision with root package name */
        final /* synthetic */ int f818a;

        /* renamed from: b, reason: collision with root package name */
        final /* synthetic */ int f819b;

        /* renamed from: c, reason: collision with root package name */
        final /* synthetic */ WeakReference f820c;

        a(int i, int i2, WeakReference weakReference) {
            this.f818a = i;
            this.f819b = i2;
            this.f820c = weakReference;
        }

        @Override // a.h.b.b.f
        public void d(int i) {
        }

        @Override // a.h.b.b.f
        public void e(Typeface typeface) {
            int i = this.f818a;
            if (i != -1) {
                typeface = Typeface.create(typeface, i, (this.f819b & 2) != 0);
            }
            m.this.g(this.f820c, typeface);
        }
    }

    m(TextView textView) {
        this.f814a = textView;
        this.i = new n(textView);
    }

    private void a(Drawable drawable, E e) {
        if (drawable == null || e == null) {
            return;
        }
        int[] drawableState = this.f814a.getDrawableState();
        int i = C0102g.f801d;
        x.m(drawable, e, drawableState);
    }

    private static E c(Context context, C0102g c0102g, int i) {
        ColorStateList f = c0102g.f(context, i);
        if (f == null) {
            return null;
        }
        E e = new E();
        e.f727d = true;
        e.f724a = f;
        return e;
    }

    private void l(Context context, G g) {
        String o;
        Typeface create;
        Typeface typeface;
        this.j = g.k(2, this.j);
        int k = g.k(11, -1);
        this.k = k;
        if (k != -1) {
            this.j = (this.j & 2) | 0;
        }
        if (!g.s(10) && !g.s(12)) {
            if (g.s(1)) {
                this.m = false;
                int k2 = g.k(1, 1);
                if (k2 == 1) {
                    typeface = Typeface.SANS_SERIF;
                } else if (k2 == 2) {
                    typeface = Typeface.SERIF;
                } else if (k2 != 3) {
                    return;
                } else {
                    typeface = Typeface.MONOSPACE;
                }
                this.l = typeface;
                return;
            }
            return;
        }
        this.l = null;
        int i = g.s(12) ? 12 : 10;
        int i2 = this.k;
        int i3 = this.j;
        if (!context.isRestricted()) {
            try {
                Typeface j = g.j(i, this.j, new a(i2, i3, new WeakReference(this.f814a)));
                if (j != null) {
                    if (this.k != -1) {
                        j = Typeface.create(Typeface.create(j, 0), this.k, (this.j & 2) != 0);
                    }
                    this.l = j;
                }
                this.m = this.l == null;
            } catch (Resources.NotFoundException | UnsupportedOperationException unused) {
            }
        }
        if (this.l != null || (o = g.o(i)) == null) {
            return;
        }
        if (this.k != -1) {
            create = Typeface.create(Typeface.create(o, 0), this.k, (this.j & 2) != 0);
        } else {
            create = Typeface.create(o, this.j);
        }
        this.l = create;
    }

    void b() {
        if (this.f815b != null || this.f816c != null || this.f817d != null || this.e != null) {
            Drawable[] compoundDrawables = this.f814a.getCompoundDrawables();
            a(compoundDrawables[0], this.f815b);
            a(compoundDrawables[1], this.f816c);
            a(compoundDrawables[2], this.f817d);
            a(compoundDrawables[3], this.e);
        }
        if (this.f == null && this.g == null) {
            return;
        }
        Drawable[] compoundDrawablesRelative = this.f814a.getCompoundDrawablesRelative();
        a(compoundDrawablesRelative[0], this.f);
        a(compoundDrawablesRelative[2], this.g);
    }

    ColorStateList d() {
        E e = this.h;
        if (e != null) {
            return e.f724a;
        }
        return null;
    }

    PorterDuff.Mode e() {
        E e = this.h;
        if (e != null) {
            return e.f725b;
        }
        return null;
    }

    @SuppressLint({"NewApi"})
    void f(AttributeSet attributeSet, int i) {
        boolean z;
        boolean z2;
        String str;
        String str2;
        Context context = this.f814a.getContext();
        C0102g b2 = C0102g.b();
        int[] iArr = a.b.b.i;
        G v = G.v(context, attributeSet, iArr, i, 0);
        TextView textView = this.f814a;
        Context context2 = textView.getContext();
        TypedArray r = v.r();
        int i2 = a.h.h.q.e;
        textView.saveAttributeDataForStyleable(context2, iArr, attributeSet, r, i, 0);
        int n = v.n(0, -1);
        if (v.s(3)) {
            this.f815b = c(context, b2, v.n(3, 0));
        }
        if (v.s(1)) {
            this.f816c = c(context, b2, v.n(1, 0));
        }
        if (v.s(4)) {
            this.f817d = c(context, b2, v.n(4, 0));
        }
        if (v.s(2)) {
            this.e = c(context, b2, v.n(2, 0));
        }
        if (v.s(5)) {
            this.f = c(context, b2, v.n(5, 0));
        }
        if (v.s(6)) {
            this.g = c(context, b2, v.n(6, 0));
        }
        v.w();
        boolean z3 = this.f814a.getTransformationMethod() instanceof PasswordTransformationMethod;
        if (n != -1) {
            G t = G.t(context, n, a.b.b.y);
            if (z3 || !t.s(14)) {
                z = false;
                z2 = false;
            } else {
                z = t.a(14, false);
                z2 = true;
            }
            l(context, t);
            str = t.s(15) ? t.o(15) : null;
            str2 = t.s(13) ? t.o(13) : null;
            t.w();
        } else {
            z = false;
            z2 = false;
            str = null;
            str2 = null;
        }
        G v2 = G.v(context, attributeSet, a.b.b.y, i, 0);
        if (!z3 && v2.s(14)) {
            z = v2.a(14, false);
            z2 = true;
        }
        if (v2.s(15)) {
            str = v2.o(15);
        }
        if (v2.s(13)) {
            str2 = v2.o(13);
        }
        String str3 = str2;
        if (v2.s(0) && v2.f(0, -1) == 0) {
            this.f814a.setTextSize(0, 0.0f);
        }
        l(context, v2);
        v2.w();
        if (!z3 && z2) {
            this.f814a.setAllCaps(z);
        }
        Typeface typeface = this.l;
        if (typeface != null) {
            if (this.k == -1) {
                this.f814a.setTypeface(typeface, this.j);
            } else {
                this.f814a.setTypeface(typeface);
            }
        }
        if (str3 != null) {
            this.f814a.setFontVariationSettings(str3);
        }
        if (str != null) {
            this.f814a.setTextLocales(LocaleList.forLanguageTags(str));
        }
        this.i.g(attributeSet, i);
        if (this.i.f() != 0) {
            int[] e = this.i.e();
            if (e.length > 0) {
                if (this.f814a.getAutoSizeStepGranularity() != -1.0f) {
                    this.f814a.setAutoSizeTextTypeUniformWithConfiguration(this.i.c(), this.i.b(), this.i.d(), 0);
                } else {
                    this.f814a.setAutoSizeTextTypeUniformWithPresetSizes(e, 0);
                }
            }
        }
        G u = G.u(context, attributeSet, a.b.b.j);
        int n2 = u.n(8, -1);
        Drawable c2 = n2 != -1 ? b2.c(context, n2) : null;
        int n3 = u.n(13, -1);
        Drawable c3 = n3 != -1 ? b2.c(context, n3) : null;
        int n4 = u.n(9, -1);
        Drawable c4 = n4 != -1 ? b2.c(context, n4) : null;
        int n5 = u.n(6, -1);
        Drawable c5 = n5 != -1 ? b2.c(context, n5) : null;
        int n6 = u.n(10, -1);
        Drawable c6 = n6 != -1 ? b2.c(context, n6) : null;
        int n7 = u.n(7, -1);
        Drawable c7 = n7 != -1 ? b2.c(context, n7) : null;
        if (c6 != null || c7 != null) {
            Drawable[] compoundDrawablesRelative = this.f814a.getCompoundDrawablesRelative();
            TextView textView2 = this.f814a;
            if (c6 == null) {
                c6 = compoundDrawablesRelative[0];
            }
            if (c3 == null) {
                c3 = compoundDrawablesRelative[1];
            }
            if (c7 == null) {
                c7 = compoundDrawablesRelative[2];
            }
            if (c5 == null) {
                c5 = compoundDrawablesRelative[3];
            }
            textView2.setCompoundDrawablesRelativeWithIntrinsicBounds(c6, c3, c7, c5);
        } else if (c2 != null || c3 != null || c4 != null || c5 != null) {
            Drawable[] compoundDrawablesRelative2 = this.f814a.getCompoundDrawablesRelative();
            if (compoundDrawablesRelative2[0] == null && compoundDrawablesRelative2[2] == null) {
                Drawable[] compoundDrawables = this.f814a.getCompoundDrawables();
                TextView textView3 = this.f814a;
                if (c2 == null) {
                    c2 = compoundDrawables[0];
                }
                if (c3 == null) {
                    c3 = compoundDrawables[1];
                }
                if (c4 == null) {
                    c4 = compoundDrawables[2];
                }
                if (c5 == null) {
                    c5 = compoundDrawables[3];
                }
                textView3.setCompoundDrawablesWithIntrinsicBounds(c2, c3, c4, c5);
            } else {
                TextView textView4 = this.f814a;
                Drawable drawable = compoundDrawablesRelative2[0];
                if (c3 == null) {
                    c3 = compoundDrawablesRelative2[1];
                }
                Drawable drawable2 = compoundDrawablesRelative2[2];
                if (c5 == null) {
                    c5 = compoundDrawablesRelative2[3];
                }
                textView4.setCompoundDrawablesRelativeWithIntrinsicBounds(drawable, c3, drawable2, c5);
            }
        }
        if (u.s(11)) {
            ColorStateList c8 = u.c(11);
            TextView textView5 = this.f814a;
            Objects.requireNonNull(textView5);
            textView5.setCompoundDrawableTintList(c8);
        }
        if (u.s(12)) {
            PorterDuff.Mode c9 = q.c(u.k(12, -1), null);
            TextView textView6 = this.f814a;
            Objects.requireNonNull(textView6);
            textView6.setCompoundDrawableTintMode(c9);
        }
        int f = u.f(14, -1);
        int f2 = u.f(17, -1);
        int f3 = u.f(18, -1);
        u.w();
        if (f != -1) {
            TextView textView7 = this.f814a;
            androidx.core.app.b.b(f);
            textView7.setFirstBaselineToTopHeight(f);
        }
        if (f2 != -1) {
            androidx.core.widget.b.a(this.f814a, f2);
        }
        if (f3 != -1) {
            androidx.core.widget.b.b(this.f814a, f3);
        }
    }

    void g(WeakReference<TextView> weakReference, Typeface typeface) {
        if (this.m) {
            this.l = typeface;
            TextView textView = weakReference.get();
            if (textView != null) {
                textView.setTypeface(typeface, this.j);
            }
        }
    }

    void h(Context context, int i) {
        String o;
        G t = G.t(context, i, a.b.b.y);
        if (t.s(14)) {
            this.f814a.setAllCaps(t.a(14, false));
        }
        if (t.s(0) && t.f(0, -1) == 0) {
            this.f814a.setTextSize(0, 0.0f);
        }
        l(context, t);
        if (t.s(13) && (o = t.o(13)) != null) {
            this.f814a.setFontVariationSettings(o);
        }
        t.w();
        Typeface typeface = this.l;
        if (typeface != null) {
            this.f814a.setTypeface(typeface, this.j);
        }
    }

    void i(boolean z) {
        this.f814a.setAllCaps(z);
    }

    void j(ColorStateList colorStateList) {
        if (this.h == null) {
            this.h = new E();
        }
        E e = this.h;
        e.f724a = colorStateList;
        e.f727d = colorStateList != null;
        this.f815b = e;
        this.f816c = e;
        this.f817d = e;
        this.e = e;
        this.f = e;
        this.g = e;
    }

    void k(PorterDuff.Mode mode) {
        if (this.h == null) {
            this.h = new E();
        }
        E e = this.h;
        e.f725b = mode;
        e.f726c = mode != null;
        this.f815b = e;
        this.f816c = e;
        this.f817d = e;
        this.e = e;
        this.f = e;
        this.g = e;
    }
}
