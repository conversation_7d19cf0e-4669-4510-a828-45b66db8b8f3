package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffColorFilter;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.LayerDrawable;
import androidx.appcompat.widget.x;
import org.libpag.R;

/* renamed from: androidx.appcompat.widget.g, reason: case insensitive filesystem */
/* loaded from: classes.dex */
public final class C0102g {

    /* renamed from: b, reason: collision with root package name */
    private static final PorterDuff.Mode f799b = PorterDuff.Mode.SRC_IN;

    /* renamed from: c, reason: collision with root package name */
    private static C0102g f800c;

    /* renamed from: d, reason: collision with root package name */
    public static final /* synthetic */ int f801d = 0;

    /* renamed from: a, reason: collision with root package name */
    private x f802a;

    /* renamed from: androidx.appcompat.widget.g$a */
    class a implements x.c {

        /* renamed from: a, reason: collision with root package name */
        private final int[] f803a = {R.drawable.abc_textfield_search_default_mtrl_alpha, R.drawable.abc_textfield_default_mtrl_alpha, R.drawable.abc_ab_share_pack_mtrl_alpha};

        /* renamed from: b, reason: collision with root package name */
        private final int[] f804b = {R.drawable.abc_ic_commit_search_api_mtrl_alpha, R.drawable.abc_seekbar_tick_mark_material, R.drawable.abc_ic_menu_share_mtrl_alpha, R.drawable.abc_ic_menu_copy_mtrl_am_alpha, R.drawable.abc_ic_menu_cut_mtrl_alpha, R.drawable.abc_ic_menu_selectall_mtrl_alpha, R.drawable.abc_ic_menu_paste_mtrl_am_alpha};

        /* renamed from: c, reason: collision with root package name */
        private final int[] f805c = {R.drawable.abc_textfield_activated_mtrl_alpha, R.drawable.abc_textfield_search_activated_mtrl_alpha, R.drawable.abc_cab_background_top_mtrl_alpha, R.drawable.abc_text_cursor_material, R.drawable.abc_text_select_handle_left_mtrl_dark, R.drawable.abc_text_select_handle_middle_mtrl_dark, R.drawable.abc_text_select_handle_right_mtrl_dark, R.drawable.abc_text_select_handle_left_mtrl_light, R.drawable.abc_text_select_handle_middle_mtrl_light, R.drawable.abc_text_select_handle_right_mtrl_light};

        /* renamed from: d, reason: collision with root package name */
        private final int[] f806d = {R.drawable.abc_popup_background_mtrl_mult, R.drawable.abc_cab_background_internal_bg, R.drawable.abc_menu_hardkey_panel_mtrl_mult};
        private final int[] e = {R.drawable.abc_tab_indicator_material, R.drawable.abc_textfield_search_material};
        private final int[] f = {R.drawable.abc_btn_check_material, R.drawable.abc_btn_radio_material, R.drawable.abc_btn_check_material_anim, R.drawable.abc_btn_radio_material_anim};

        a() {
        }

        private boolean a(int[] iArr, int i) {
            for (int i2 : iArr) {
                if (i2 == i) {
                    return true;
                }
            }
            return false;
        }

        private ColorStateList b(Context context, int i) {
            int c2 = B.c(context, R.attr.colorControlHighlight);
            return new ColorStateList(new int[][]{B.f720b, B.f722d, B.f721c, B.f}, new int[]{B.b(context, R.attr.colorButtonNormal), a.h.c.a.a(c2, i), a.h.c.a.a(c2, i), i});
        }

        private void d(Drawable drawable, int i, PorterDuff.Mode mode) {
            if (q.a(drawable)) {
                drawable = drawable.mutate();
            }
            if (mode == null) {
                mode = C0102g.f799b;
            }
            drawable.setColorFilter(C0102g.e(i, mode));
        }

        public ColorStateList c(Context context, int i) {
            if (i == R.drawable.abc_edit_text_material) {
                int i2 = a.b.c.a.a.f7d;
                return context.getColorStateList(R.color.abc_tint_edittext);
            }
            if (i == R.drawable.abc_switch_track_mtrl_alpha) {
                int i3 = a.b.c.a.a.f7d;
                return context.getColorStateList(R.color.abc_tint_switch_track);
            }
            if (i == R.drawable.abc_switch_thumb_material) {
                int[][] iArr = new int[3][];
                int[] iArr2 = new int[3];
                ColorStateList d2 = B.d(context, R.attr.colorSwitchThumbNormal);
                if (d2 == null || !d2.isStateful()) {
                    iArr[0] = B.f720b;
                    iArr2[0] = B.b(context, R.attr.colorSwitchThumbNormal);
                    iArr[1] = B.e;
                    iArr2[1] = B.c(context, R.attr.colorControlActivated);
                    iArr[2] = B.f;
                    iArr2[2] = B.c(context, R.attr.colorSwitchThumbNormal);
                } else {
                    iArr[0] = B.f720b;
                    iArr2[0] = d2.getColorForState(iArr[0], 0);
                    iArr[1] = B.e;
                    iArr2[1] = B.c(context, R.attr.colorControlActivated);
                    iArr[2] = B.f;
                    iArr2[2] = d2.getDefaultColor();
                }
                return new ColorStateList(iArr, iArr2);
            }
            if (i == R.drawable.abc_btn_default_mtrl_shape) {
                return b(context, B.c(context, R.attr.colorButtonNormal));
            }
            if (i == R.drawable.abc_btn_borderless_material) {
                return b(context, 0);
            }
            if (i == R.drawable.abc_btn_colored_material) {
                return b(context, B.c(context, R.attr.colorAccent));
            }
            if (i == R.drawable.abc_spinner_mtrl_am_alpha || i == R.drawable.abc_spinner_textfield_background_material) {
                int i4 = a.b.c.a.a.f7d;
                return context.getColorStateList(R.color.abc_tint_spinner);
            }
            if (a(this.f804b, i)) {
                return B.d(context, R.attr.colorControlNormal);
            }
            if (a(this.e, i)) {
                int i5 = a.b.c.a.a.f7d;
                return context.getColorStateList(R.color.abc_tint_default);
            }
            if (a(this.f, i)) {
                int i6 = a.b.c.a.a.f7d;
                return context.getColorStateList(R.color.abc_tint_btn_checkable);
            }
            if (i != R.drawable.abc_seekbar_thumb_material) {
                return null;
            }
            int i7 = a.b.c.a.a.f7d;
            return context.getColorStateList(R.color.abc_tint_seek_thumb);
        }

        public boolean e(Context context, int i, Drawable drawable) {
            LayerDrawable layerDrawable;
            Drawable findDrawableByLayerId;
            int c2;
            if (i == R.drawable.abc_seekbar_track_material) {
                layerDrawable = (LayerDrawable) drawable;
                d(layerDrawable.findDrawableByLayerId(android.R.id.background), B.c(context, R.attr.colorControlNormal), C0102g.f799b);
                findDrawableByLayerId = layerDrawable.findDrawableByLayerId(android.R.id.secondaryProgress);
                c2 = B.c(context, R.attr.colorControlNormal);
            } else {
                if (i != R.drawable.abc_ratingbar_material && i != R.drawable.abc_ratingbar_indicator_material && i != R.drawable.abc_ratingbar_small_material) {
                    return false;
                }
                layerDrawable = (LayerDrawable) drawable;
                d(layerDrawable.findDrawableByLayerId(android.R.id.background), B.b(context, R.attr.colorControlNormal), C0102g.f799b);
                findDrawableByLayerId = layerDrawable.findDrawableByLayerId(android.R.id.secondaryProgress);
                c2 = B.c(context, R.attr.colorControlActivated);
            }
            d(findDrawableByLayerId, c2, C0102g.f799b);
            d(layerDrawable.findDrawableByLayerId(android.R.id.progress), B.c(context, R.attr.colorControlActivated), C0102g.f799b);
            return true;
        }

        /* JADX WARN: Removed duplicated region for block: B:15:0x0065 A[RETURN] */
        /* JADX WARN: Removed duplicated region for block: B:7:0x004a  */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public boolean f(android.content.Context r7, int r8, android.graphics.drawable.Drawable r9) {
            /*
                r6 = this;
                android.graphics.PorterDuff$Mode r0 = androidx.appcompat.widget.C0102g.a()
                int[] r1 = r6.f803a
                boolean r1 = r6.a(r1, r8)
                r2 = 16842801(0x1010031, float:2.3693695E-38)
                r3 = -1
                r4 = 0
                r5 = 1
                if (r1 == 0) goto L18
                r2 = 2130903247(0x7f0300cf, float:1.7413307E38)
            L15:
                r6 = r3
            L16:
                r8 = r5
                goto L48
            L18:
                int[] r1 = r6.f805c
                boolean r1 = r6.a(r1, r8)
                if (r1 == 0) goto L24
                r2 = 2130903245(0x7f0300cd, float:1.7413303E38)
                goto L15
            L24:
                int[] r1 = r6.f806d
                boolean r6 = r6.a(r1, r8)
                if (r6 == 0) goto L2f
                android.graphics.PorterDuff$Mode r0 = android.graphics.PorterDuff.Mode.MULTIPLY
                goto L15
            L2f:
                r6 = 2131165233(0x7f070031, float:1.7944677E38)
                if (r8 != r6) goto L3f
                r2 = 16842800(0x1010030, float:2.3693693E-38)
                r6 = 1109603123(0x42233333, float:40.8)
                int r6 = java.lang.Math.round(r6)
                goto L16
            L3f:
                r6 = 2131165209(0x7f070019, float:1.7944629E38)
                if (r8 != r6) goto L45
                goto L15
            L45:
                r6 = r3
                r8 = r4
                r2 = r8
            L48:
                if (r8 == 0) goto L65
                boolean r8 = androidx.appcompat.widget.q.a(r9)
                if (r8 == 0) goto L54
                android.graphics.drawable.Drawable r9 = r9.mutate()
            L54:
                int r7 = androidx.appcompat.widget.B.c(r7, r2)
                android.graphics.PorterDuffColorFilter r7 = androidx.appcompat.widget.C0102g.e(r7, r0)
                r9.setColorFilter(r7)
                if (r6 == r3) goto L64
                r9.setAlpha(r6)
            L64:
                return r5
            L65:
                return r4
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.C0102g.a.f(android.content.Context, int, android.graphics.drawable.Drawable):boolean");
        }
    }

    public static synchronized C0102g b() {
        C0102g c0102g;
        synchronized (C0102g.class) {
            if (f800c == null) {
                h();
            }
            c0102g = f800c;
        }
        return c0102g;
    }

    public static synchronized PorterDuffColorFilter e(int i, PorterDuff.Mode mode) {
        PorterDuffColorFilter g;
        synchronized (C0102g.class) {
            g = x.g(i, mode);
        }
        return g;
    }

    public static synchronized void h() {
        synchronized (C0102g.class) {
            if (f800c == null) {
                C0102g c0102g = new C0102g();
                f800c = c0102g;
                c0102g.f802a = x.c();
                f800c.f802a.k(new a());
            }
        }
    }

    public synchronized Drawable c(Context context, int i) {
        return this.f802a.e(context, i);
    }

    synchronized Drawable d(Context context, int i, boolean z) {
        return this.f802a.f(context, i, z);
    }

    synchronized ColorStateList f(Context context, int i) {
        return this.f802a.h(context, i);
    }

    public synchronized void g(Context context) {
        this.f802a.j(context);
    }
}
