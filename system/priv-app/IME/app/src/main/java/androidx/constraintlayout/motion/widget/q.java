package androidx.constraintlayout.motion.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.content.res.XmlResourceParser;
import android.graphics.RectF;
import android.util.Log;
import android.util.SparseArray;
import android.util.SparseIntArray;
import android.util.Xml;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.AnimationUtils;
import android.view.animation.AnticipateInterpolator;
import android.view.animation.BounceInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.view.animation.Interpolator;
import android.view.animation.OvershootInterpolator;
import androidx.constraintlayout.motion.widget.MotionLayout;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.libpag.R;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

/* loaded from: classes.dex */
public class q {

    /* renamed from: a, reason: collision with root package name */
    private final MotionLayout f921a;

    /* renamed from: b, reason: collision with root package name */
    androidx.constraintlayout.widget.h f922b;

    /* renamed from: c, reason: collision with root package name */
    b f923c;
    private b f;
    private MotionEvent m;
    private MotionLayout.h p;
    private boolean q;
    final u r;
    float s;
    float t;

    /* renamed from: d, reason: collision with root package name */
    private boolean f924d = false;
    private ArrayList<b> e = new ArrayList<>();
    private ArrayList<b> g = new ArrayList<>();
    private SparseArray<androidx.constraintlayout.widget.c> h = new SparseArray<>();
    private HashMap<String, Integer> i = new HashMap<>();
    private SparseIntArray j = new SparseIntArray();
    private int k = 400;
    private int l = 0;
    private boolean n = false;
    private boolean o = false;

    class a implements Interpolator {

        /* renamed from: a, reason: collision with root package name */
        final /* synthetic */ a.f.a.i.a.c f925a;

        a(q qVar, a.f.a.i.a.c cVar) {
            this.f925a = cVar;
        }

        @Override // android.animation.TimeInterpolator
        public float getInterpolation(float f) {
            return (float) this.f925a.a(f);
        }
    }

    public static class b {

        /* renamed from: a, reason: collision with root package name */
        private int f926a;

        /* renamed from: b, reason: collision with root package name */
        private boolean f927b;

        /* renamed from: c, reason: collision with root package name */
        private int f928c;

        /* renamed from: d, reason: collision with root package name */
        private int f929d;
        private int e;
        private String f;
        private int g;
        private int h;
        private float i;
        private final q j;
        private ArrayList<g> k;
        private r l;
        private ArrayList<a> m;
        private int n;
        private boolean o;
        private int p;
        private int q;
        private int r;

        public static class a implements View.OnClickListener {

            /* renamed from: a, reason: collision with root package name */
            private final b f930a;

            /* renamed from: b, reason: collision with root package name */
            int f931b;

            /* renamed from: c, reason: collision with root package name */
            int f932c;

            public a(Context context, b bVar, XmlPullParser xmlPullParser) {
                this.f931b = -1;
                this.f932c = 17;
                this.f930a = bVar;
                TypedArray obtainStyledAttributes = context.obtainStyledAttributes(Xml.asAttributeSet(xmlPullParser), androidx.constraintlayout.widget.f.x);
                int indexCount = obtainStyledAttributes.getIndexCount();
                for (int i = 0; i < indexCount; i++) {
                    int index = obtainStyledAttributes.getIndex(i);
                    if (index == 1) {
                        this.f931b = obtainStyledAttributes.getResourceId(index, this.f931b);
                    } else if (index == 0) {
                        this.f932c = obtainStyledAttributes.getInt(index, this.f932c);
                    }
                }
                obtainStyledAttributes.recycle();
            }

            /* JADX WARN: Multi-variable type inference failed */
            /* JADX WARN: Type inference failed for: r7v5, types: [android.view.View] */
            public void a(MotionLayout motionLayout, int i, b bVar) {
                int i2 = this.f931b;
                MotionLayout motionLayout2 = motionLayout;
                if (i2 != -1) {
                    motionLayout2 = motionLayout.findViewById(i2);
                }
                if (motionLayout2 == null) {
                    StringBuilder j = b.b.a.a.a.j("OnClick could not find id ");
                    j.append(this.f931b);
                    Log.e("MotionScene", j.toString());
                    return;
                }
                int i3 = bVar.f929d;
                int i4 = bVar.f928c;
                if (i3 == -1) {
                    motionLayout2.setOnClickListener(this);
                    return;
                }
                int i5 = this.f932c;
                boolean z = false;
                boolean z2 = ((i5 & 1) != 0 && i == i3) | ((i5 & 1) != 0 && i == i3) | ((i5 & 256) != 0 && i == i3) | ((i5 & 16) != 0 && i == i4);
                if ((i5 & 4096) != 0 && i == i4) {
                    z = true;
                }
                if (z2 || z) {
                    motionLayout2.setOnClickListener(this);
                }
            }

            public void b(MotionLayout motionLayout) {
                int i = this.f931b;
                if (i == -1) {
                    return;
                }
                View findViewById = motionLayout.findViewById(i);
                if (findViewById != null) {
                    findViewById.setOnClickListener(null);
                    return;
                }
                StringBuilder j = b.b.a.a.a.j(" (*)  could not find id ");
                j.append(this.f931b);
                Log.e("MotionScene", j.toString());
            }

            /* JADX WARN: Removed duplicated region for block: B:43:0x00be  */
            /* JADX WARN: Removed duplicated region for block: B:65:0x0101 A[ORIG_RETURN, RETURN] */
            @Override // android.view.View.OnClickListener
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            public void onClick(android.view.View r9) {
                /*
                    Method dump skipped, instructions count: 258
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.motion.widget.q.b.a.onClick(android.view.View):void");
            }
        }

        public b(int i, q qVar, int i2, int i3) {
            this.f926a = -1;
            this.f927b = false;
            this.f928c = -1;
            this.f929d = -1;
            this.e = 0;
            this.f = null;
            this.g = -1;
            this.h = 400;
            this.i = 0.0f;
            this.k = new ArrayList<>();
            this.l = null;
            this.m = new ArrayList<>();
            this.n = 0;
            this.o = false;
            this.p = -1;
            this.q = 0;
            this.r = 0;
            this.f926a = i;
            this.j = qVar;
            this.f929d = i2;
            this.f928c = i3;
            this.h = qVar.k;
            this.q = qVar.l;
        }

        b(q qVar, Context context, XmlPullParser xmlPullParser) {
            int integer;
            androidx.constraintlayout.widget.c cVar;
            SparseArray sparseArray;
            int i;
            this.f926a = -1;
            this.f927b = false;
            this.f928c = -1;
            this.f929d = -1;
            this.e = 0;
            this.f = null;
            this.g = -1;
            this.h = 400;
            this.i = 0.0f;
            this.k = new ArrayList<>();
            this.l = null;
            this.m = new ArrayList<>();
            this.n = 0;
            this.o = false;
            this.p = -1;
            this.q = 0;
            this.r = 0;
            this.h = qVar.k;
            this.q = qVar.l;
            this.j = qVar;
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(Xml.asAttributeSet(xmlPullParser), androidx.constraintlayout.widget.f.D);
            int indexCount = obtainStyledAttributes.getIndexCount();
            for (int i2 = 0; i2 < indexCount; i2++) {
                int index = obtainStyledAttributes.getIndex(i2);
                if (index == 2) {
                    this.f928c = obtainStyledAttributes.getResourceId(index, -1);
                    String resourceTypeName = context.getResources().getResourceTypeName(this.f928c);
                    if ("layout".equals(resourceTypeName)) {
                        cVar = new androidx.constraintlayout.widget.c();
                        cVar.x(context, this.f928c);
                        sparseArray = qVar.h;
                        i = this.f928c;
                        sparseArray.append(i, cVar);
                    } else {
                        if ("xml".equals(resourceTypeName)) {
                            this.f928c = qVar.D(context, this.f928c);
                        }
                    }
                } else {
                    if (index == 3) {
                        this.f929d = obtainStyledAttributes.getResourceId(index, this.f929d);
                        String resourceTypeName2 = context.getResources().getResourceTypeName(this.f929d);
                        if ("layout".equals(resourceTypeName2)) {
                            cVar = new androidx.constraintlayout.widget.c();
                            cVar.x(context, this.f929d);
                            sparseArray = qVar.h;
                            i = this.f929d;
                            sparseArray.append(i, cVar);
                        } else if ("xml".equals(resourceTypeName2)) {
                            this.f929d = qVar.D(context, this.f929d);
                        }
                    } else if (index == 6) {
                        int i3 = obtainStyledAttributes.peekValue(index).type;
                        if (i3 == 1) {
                            int resourceId = obtainStyledAttributes.getResourceId(index, -1);
                            this.g = resourceId;
                            if (resourceId == -1) {
                            }
                            this.e = -2;
                        } else {
                            if (i3 == 3) {
                                String string = obtainStyledAttributes.getString(index);
                                this.f = string;
                                if (string != null) {
                                    if (string.indexOf("/") > 0) {
                                        this.g = obtainStyledAttributes.getResourceId(index, -1);
                                        this.e = -2;
                                    } else {
                                        integer = -1;
                                    }
                                }
                            } else {
                                integer = obtainStyledAttributes.getInteger(index, this.e);
                            }
                            this.e = integer;
                        }
                    } else if (index == 4) {
                        int i4 = obtainStyledAttributes.getInt(index, this.h);
                        this.h = i4;
                        if (i4 < 8) {
                            this.h = 8;
                        }
                    } else if (index == 8) {
                        this.i = obtainStyledAttributes.getFloat(index, this.i);
                    } else if (index == 1) {
                        this.n = obtainStyledAttributes.getInteger(index, this.n);
                    } else if (index == 0) {
                        this.f926a = obtainStyledAttributes.getResourceId(index, this.f926a);
                    } else if (index == 9) {
                        this.o = obtainStyledAttributes.getBoolean(index, this.o);
                    } else if (index == 7) {
                        this.p = obtainStyledAttributes.getInteger(index, -1);
                    } else if (index == 5) {
                        this.q = obtainStyledAttributes.getInteger(index, 0);
                    } else if (index == 10) {
                        this.r = obtainStyledAttributes.getInteger(index, 0);
                    }
                }
            }
            if (this.f929d == -1) {
                this.f927b = true;
            }
            obtainStyledAttributes.recycle();
        }

        b(q qVar, b bVar) {
            this.f926a = -1;
            this.f927b = false;
            this.f928c = -1;
            this.f929d = -1;
            this.e = 0;
            this.f = null;
            this.g = -1;
            this.h = 400;
            this.i = 0.0f;
            this.k = new ArrayList<>();
            this.l = null;
            this.m = new ArrayList<>();
            this.n = 0;
            this.o = false;
            this.p = -1;
            this.q = 0;
            this.r = 0;
            this.j = qVar;
            this.h = qVar.k;
            if (bVar != null) {
                this.p = bVar.p;
                this.e = bVar.e;
                this.f = bVar.f;
                this.g = bVar.g;
                this.h = bVar.h;
                this.k = bVar.k;
                this.i = bVar.i;
                this.q = bVar.q;
            }
        }

        public boolean A() {
            return !this.o;
        }

        public boolean B(int i) {
            return (this.r & i) != 0;
        }

        public void C(int i) {
            this.h = Math.max(i, 8);
        }

        public void D(boolean z) {
            this.o = !z;
        }

        public void E(int i, String str, int i2) {
            this.e = i;
            this.f = str;
            this.g = i2;
        }

        public void F(int i) {
            r rVar = this.l;
            if (rVar != null) {
                rVar.v(i);
            }
        }

        public void G(int i) {
            this.p = i;
        }

        public void t(g gVar) {
            this.k.add(gVar);
        }

        public void u(Context context, XmlPullParser xmlPullParser) {
            this.m.add(new a(context, this, xmlPullParser));
        }

        public int v() {
            return this.n;
        }

        public int w() {
            return this.f928c;
        }

        public int x() {
            return this.q;
        }

        public int y() {
            return this.f929d;
        }

        public r z() {
            return this.l;
        }
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    q(Context context, MotionLayout motionLayout, int i) {
        int eventType;
        b bVar = null;
        this.f922b = null;
        this.f923c = null;
        this.f = null;
        this.f921a = motionLayout;
        this.r = new u(motionLayout);
        XmlResourceParser xml = context.getResources().getXml(i);
        try {
            eventType = xml.getEventType();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (XmlPullParserException e2) {
            e2.printStackTrace();
        }
        while (true) {
            char c2 = 1;
            if (eventType == 1) {
                this.h.put(R.id.motion_base, new androidx.constraintlayout.widget.c());
                this.i.put("motion_base", Integer.valueOf(R.id.motion_base));
                return;
            }
            if (eventType == 0) {
                xml.getName();
            } else if (eventType == 2) {
                String name = xml.getName();
                switch (name.hashCode()) {
                    case -1349929691:
                        if (name.equals("ConstraintSet")) {
                            c2 = 5;
                            break;
                        }
                        c2 = 65535;
                        break;
                    case -1239391468:
                        if (name.equals("KeyFrameSet")) {
                            c2 = '\b';
                            break;
                        }
                        c2 = 65535;
                        break;
                    case -687739768:
                        if (name.equals("Include")) {
                            c2 = 7;
                            break;
                        }
                        c2 = 65535;
                        break;
                    case 61998586:
                        if (name.equals("ViewTransition")) {
                            c2 = '\t';
                            break;
                        }
                        c2 = 65535;
                        break;
                    case 269306229:
                        if (name.equals("Transition")) {
                            break;
                        }
                        c2 = 65535;
                        break;
                    case 312750793:
                        if (name.equals("OnClick")) {
                            c2 = 3;
                            break;
                        }
                        c2 = 65535;
                        break;
                    case 327855227:
                        if (name.equals("OnSwipe")) {
                            c2 = 2;
                            break;
                        }
                        c2 = 65535;
                        break;
                    case 793277014:
                        if (name.equals("MotionScene")) {
                            c2 = 0;
                            break;
                        }
                        c2 = 65535;
                        break;
                    case 1382829617:
                        if (name.equals("StateSet")) {
                            c2 = 4;
                            break;
                        }
                        c2 = 65535;
                        break;
                    case 1942574248:
                        if (name.equals("include")) {
                            c2 = 6;
                            break;
                        }
                        c2 = 65535;
                        break;
                    default:
                        c2 = 65535;
                        break;
                }
                switch (c2) {
                    case 0:
                        F(context, xml);
                        break;
                    case 1:
                        ArrayList<b> arrayList = this.e;
                        b bVar2 = new b(this, context, xml);
                        arrayList.add(bVar2);
                        if (this.f923c == null && !bVar2.f927b) {
                            this.f923c = bVar2;
                            if (bVar2.l != null) {
                                this.f923c.l.u(this.q);
                            }
                        }
                        if (bVar2.f927b) {
                            if (bVar2.f928c == -1) {
                                this.f = bVar2;
                            } else {
                                this.g.add(bVar2);
                            }
                            this.e.remove(bVar2);
                        }
                        bVar = bVar2;
                        break;
                    case 2:
                        if (bVar == null) {
                            Log.v("MotionScene", " OnSwipe (" + context.getResources().getResourceEntryName(i) + ".xml:" + xml.getLineNumber() + ")");
                        }
                        if (bVar == null) {
                            break;
                        } else {
                            bVar.l = new r(context, this.f921a, xml);
                            break;
                        }
                    case 3:
                        if (bVar == null) {
                            break;
                        } else {
                            bVar.u(context, xml);
                            break;
                        }
                    case 4:
                        this.f922b = new androidx.constraintlayout.widget.h(context, xml);
                        break;
                    case 5:
                        C(context, xml);
                        break;
                    case 6:
                    case MotionLayout.TOUCH_UP_NEVER_TO_END /* 7 */:
                        E(context, xml);
                        break;
                    case '\b':
                        g gVar = new g(context, xml);
                        if (bVar == null) {
                            break;
                        } else {
                            bVar.k.add(gVar);
                            break;
                        }
                    case '\t':
                        this.r.a(new t(context, xml));
                        break;
                }
            }
            eventType = xml.next();
        }
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    private int C(Context context, XmlPullParser xmlPullParser) {
        boolean z;
        boolean z2;
        androidx.constraintlayout.widget.c cVar = new androidx.constraintlayout.widget.c();
        cVar.E(false);
        int attributeCount = xmlPullParser.getAttributeCount();
        int i = -1;
        int i2 = -1;
        for (int i3 = 0; i3 < attributeCount; i3++) {
            String attributeName = xmlPullParser.getAttributeName(i3);
            String attributeValue = xmlPullParser.getAttributeValue(i3);
            attributeName.hashCode();
            switch (attributeName.hashCode()) {
                case -1496482599:
                    if (attributeName.equals("deriveConstraintsFrom")) {
                        z = false;
                        break;
                    }
                    z = -1;
                    break;
                case -1153153640:
                    if (attributeName.equals("constraintRotate")) {
                        z = true;
                        break;
                    }
                    z = -1;
                    break;
                case 3355:
                    if (attributeName.equals("id")) {
                        z = 2;
                        break;
                    }
                    z = -1;
                    break;
                default:
                    z = -1;
                    break;
            }
            switch (z) {
                case false:
                    i2 = n(context, attributeValue);
                    break;
                case true:
                    try {
                        cVar.f993c = Integer.parseInt(attributeValue);
                        break;
                    } catch (NumberFormatException unused) {
                        attributeValue.hashCode();
                        switch (attributeValue.hashCode()) {
                            case -768416914:
                                if (attributeValue.equals("x_left")) {
                                    z2 = false;
                                    break;
                                }
                                z2 = -1;
                                break;
                            case 3317767:
                                if (attributeValue.equals("left")) {
                                    z2 = true;
                                    break;
                                }
                                z2 = -1;
                                break;
                            case 3387192:
                                if (attributeValue.equals("none")) {
                                    z2 = 2;
                                    break;
                                }
                                z2 = -1;
                                break;
                            case 108511772:
                                if (attributeValue.equals("right")) {
                                    z2 = 3;
                                    break;
                                }
                                z2 = -1;
                                break;
                            case 1954540437:
                                if (attributeValue.equals("x_right")) {
                                    z2 = 4;
                                    break;
                                }
                                z2 = -1;
                                break;
                            default:
                                z2 = -1;
                                break;
                        }
                        switch (z2) {
                            case false:
                                cVar.f993c = 4;
                                break;
                            case true:
                                cVar.f993c = 2;
                                break;
                            case true:
                                cVar.f993c = 0;
                                break;
                            case true:
                                cVar.f993c = 1;
                                break;
                            case true:
                                cVar.f993c = 3;
                                break;
                        }
                    }
                case true:
                    i = n(context, attributeValue);
                    HashMap<String, Integer> hashMap = this.i;
                    int indexOf = attributeValue.indexOf(47);
                    if (indexOf >= 0) {
                        attributeValue = attributeValue.substring(indexOf + 1);
                    }
                    hashMap.put(attributeValue, Integer.valueOf(i));
                    cVar.f991a = a.b.a.c(context, i);
                    break;
            }
        }
        if (i != -1) {
            int i4 = this.f921a.mDebugPath;
            cVar.y(context, xmlPullParser);
            if (i2 != -1) {
                this.j.put(i, i2);
            }
            this.h.put(i, cVar);
        }
        return i;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public int D(Context context, int i) {
        XmlResourceParser xml = context.getResources().getXml(i);
        try {
            for (int eventType = xml.getEventType(); eventType != 1; eventType = xml.next()) {
                String name = xml.getName();
                if (2 == eventType && "ConstraintSet".equals(name)) {
                    return C(context, xml);
                }
            }
            return -1;
        } catch (IOException e) {
            e.printStackTrace();
            return -1;
        } catch (XmlPullParserException e2) {
            e2.printStackTrace();
            return -1;
        }
    }

    private void E(Context context, XmlPullParser xmlPullParser) {
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(Xml.asAttributeSet(xmlPullParser), androidx.constraintlayout.widget.f.G);
        int indexCount = obtainStyledAttributes.getIndexCount();
        for (int i = 0; i < indexCount; i++) {
            int index = obtainStyledAttributes.getIndex(i);
            if (index == 0) {
                D(context, obtainStyledAttributes.getResourceId(index, -1));
            }
        }
        obtainStyledAttributes.recycle();
    }

    private void F(Context context, XmlPullParser xmlPullParser) {
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(Xml.asAttributeSet(xmlPullParser), androidx.constraintlayout.widget.f.v);
        int indexCount = obtainStyledAttributes.getIndexCount();
        for (int i = 0; i < indexCount; i++) {
            int index = obtainStyledAttributes.getIndex(i);
            if (index == 0) {
                int i2 = obtainStyledAttributes.getInt(index, this.k);
                this.k = i2;
                if (i2 < 8) {
                    this.k = 8;
                }
            } else if (index == 1) {
                this.l = obtainStyledAttributes.getInteger(index, 0);
            }
        }
        obtainStyledAttributes.recycle();
    }

    private void H(int i, MotionLayout motionLayout) {
        androidx.constraintlayout.widget.c cVar = this.h.get(i);
        cVar.f992b = cVar.f991a;
        int i2 = this.j.get(i);
        if (i2 > 0) {
            H(i2, motionLayout);
            androidx.constraintlayout.widget.c cVar2 = this.h.get(i2);
            if (cVar2 == null) {
                StringBuilder j = b.b.a.a.a.j("ERROR! invalid deriveConstraintsFrom: @id/");
                j.append(a.b.a.c(this.f921a.getContext(), i2));
                Log.e("MotionScene", j.toString());
                return;
            } else {
                cVar.f992b += "/" + cVar2.f992b;
                cVar.D(cVar2);
            }
        } else {
            cVar.f992b += "  layout";
            cVar.C(motionLayout);
        }
        cVar.c(cVar);
    }

    private int n(Context context, String str) {
        int i;
        if (str.contains("/")) {
            i = context.getResources().getIdentifier(str.substring(str.indexOf(47) + 1), "id", context.getPackageName());
        } else {
            i = -1;
        }
        if (i != -1) {
            return i;
        }
        if (str.length() > 1) {
            return Integer.parseInt(str.substring(1));
        }
        Log.e("MotionScene", "error in parsing id");
        return i;
    }

    public int A(String str) {
        Integer num = this.i.get(str);
        if (num == null) {
            return 0;
        }
        return num.intValue();
    }

    public String B(int i) {
        for (Map.Entry<String, Integer> entry : this.i.entrySet()) {
            Integer value = entry.getValue();
            if (value != null && value.intValue() == i) {
                return entry.getKey();
            }
        }
        return null;
    }

    void G(MotionEvent motionEvent, int i, MotionLayout motionLayout) {
        MotionLayout.h hVar;
        MotionLayout.h hVar2;
        MotionEvent motionEvent2;
        b bVar;
        float f;
        float f2;
        MotionEvent motionEvent3;
        RectF d2;
        RectF rectF = new RectF();
        if (this.p == null) {
            this.p = this.f921a.obtainVelocityTracker();
        }
        VelocityTracker velocityTracker = ((MotionLayout.i) this.p).f884a;
        if (velocityTracker != null) {
            velocityTracker.addMovement(motionEvent);
        }
        if (i != -1) {
            int action = motionEvent.getAction();
            if (action == 0) {
                this.s = motionEvent.getRawX();
                this.t = motionEvent.getRawY();
                this.m = motionEvent;
                this.n = false;
                if (this.f923c.l != null) {
                    RectF d3 = this.f923c.l.d(this.f921a, rectF);
                    if (d3 != null && !d3.contains(this.m.getX(), this.m.getY())) {
                        this.m = null;
                        this.n = true;
                        return;
                    } else {
                        RectF n = this.f923c.l.n(this.f921a, rectF);
                        this.o = (n == null || n.contains(this.m.getX(), this.m.getY())) ? false : true;
                        this.f923c.l.t(this.s, this.t);
                        return;
                    }
                }
                return;
            }
            if (action == 2 && !this.n) {
                float rawY = motionEvent.getRawY() - this.t;
                float rawX = motionEvent.getRawX() - this.s;
                if ((rawX == 0.0d && rawY == 0.0d) || (motionEvent2 = this.m) == null) {
                    return;
                }
                if (i != -1) {
                    List<b> z = z(i);
                    float f3 = 0.0f;
                    RectF rectF2 = new RectF();
                    Iterator it = ((ArrayList) z).iterator();
                    bVar = null;
                    while (it.hasNext()) {
                        b bVar2 = (b) it.next();
                        if (!bVar2.o && bVar2.l != null) {
                            bVar2.l.u(this.q);
                            RectF n2 = bVar2.l.n(this.f921a, rectF2);
                            if ((n2 == null || n2.contains(motionEvent2.getX(), motionEvent2.getY())) && ((d2 = bVar2.l.d(this.f921a, rectF2)) == null || d2.contains(motionEvent2.getX(), motionEvent2.getY()))) {
                                float a2 = bVar2.l.a(rawX, rawY);
                                if (bVar2.l.j) {
                                    float x = motionEvent2.getX();
                                    Objects.requireNonNull(bVar2.l);
                                    float y = motionEvent2.getY();
                                    Objects.requireNonNull(bVar2.l);
                                    f = rawY;
                                    f2 = rawX;
                                    motionEvent3 = motionEvent2;
                                    a2 = ((float) (Math.atan2(rawY + r7, rawX + r5) - Math.atan2(x - 0.5f, y - 0.5f))) * 10.0f;
                                } else {
                                    f = rawY;
                                    f2 = rawX;
                                    motionEvent3 = motionEvent2;
                                }
                                float f4 = a2 * (bVar2.f928c == i ? -1.0f : 1.1f);
                                if (f4 > f3) {
                                    f3 = f4;
                                    bVar = bVar2;
                                }
                                rawY = f;
                                rawX = f2;
                                motionEvent2 = motionEvent3;
                            }
                        }
                        f = rawY;
                        f2 = rawX;
                        motionEvent3 = motionEvent2;
                        rawY = f;
                        rawX = f2;
                        motionEvent2 = motionEvent3;
                    }
                } else {
                    bVar = this.f923c;
                }
                if (bVar != null) {
                    motionLayout.setTransition(bVar);
                    RectF n3 = this.f923c.l.n(this.f921a, rectF);
                    this.o = (n3 == null || n3.contains(this.m.getX(), this.m.getY())) ? false : true;
                    this.f923c.l.w(this.s, this.t);
                }
            }
        }
        if (this.n) {
            return;
        }
        b bVar3 = this.f923c;
        if (bVar3 != null && bVar3.l != null && !this.o) {
            this.f923c.l.q(motionEvent, this.p, i, this);
        }
        this.s = motionEvent.getRawX();
        this.t = motionEvent.getRawY();
        if (motionEvent.getAction() != 1 || (hVar = this.p) == null) {
            return;
        }
        MotionLayout.i iVar = (MotionLayout.i) hVar;
        VelocityTracker velocityTracker2 = iVar.f884a;
        if (velocityTracker2 != null) {
            velocityTracker2.recycle();
            hVar2 = null;
            iVar.f884a = null;
        } else {
            hVar2 = null;
        }
        this.p = hVar2;
        int i2 = motionLayout.mCurrentState;
        if (i2 != -1) {
            g(motionLayout, i2);
        }
    }

    void I(MotionLayout motionLayout) {
        boolean z;
        for (int i = 0; i < this.h.size(); i++) {
            int keyAt = this.h.keyAt(i);
            int i2 = this.j.get(keyAt);
            int size = this.j.size();
            while (i2 > 0) {
                if (i2 != keyAt) {
                    int i3 = size - 1;
                    if (size >= 0) {
                        i2 = this.j.get(i2);
                        size = i3;
                    }
                }
                z = true;
                break;
            }
            z = false;
            if (z) {
                Log.e("MotionScene", "Cannot be derived from yourself");
                return;
            }
            H(keyAt, motionLayout);
        }
    }

    public void J(int i, androidx.constraintlayout.widget.c cVar) {
        this.h.put(i, cVar);
    }

    public void K(int i) {
        b bVar = this.f923c;
        if (bVar != null) {
            bVar.C(i);
        } else {
            this.k = i;
        }
    }

    public void L(boolean z) {
        this.q = z;
        b bVar = this.f923c;
        if (bVar == null || bVar.l == null) {
            return;
        }
        this.f923c.l.u(this.q);
    }

    /* JADX WARN: Code restructure failed: missing block: B:7:0x0013, code lost:
    
        if (r2 != (-1)) goto L13;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    void M(int r7, int r8) {
        /*
            r6 = this;
            androidx.constraintlayout.widget.h r0 = r6.f922b
            r1 = -1
            if (r0 == 0) goto L16
            int r0 = r0.b(r7, r1, r1)
            if (r0 == r1) goto Lc
            goto Ld
        Lc:
            r0 = r7
        Ld:
            androidx.constraintlayout.widget.h r2 = r6.f922b
            int r2 = r2.b(r8, r1, r1)
            if (r2 == r1) goto L17
            goto L18
        L16:
            r0 = r7
        L17:
            r2 = r8
        L18:
            androidx.constraintlayout.motion.widget.q$b r3 = r6.f923c
            if (r3 == 0) goto L2b
            int r3 = androidx.constraintlayout.motion.widget.q.b.a(r3)
            if (r3 != r8) goto L2b
            androidx.constraintlayout.motion.widget.q$b r3 = r6.f923c
            int r3 = androidx.constraintlayout.motion.widget.q.b.c(r3)
            if (r3 != r7) goto L2b
            return
        L2b:
            java.util.ArrayList<androidx.constraintlayout.motion.widget.q$b> r3 = r6.e
            java.util.Iterator r3 = r3.iterator()
        L31:
            boolean r4 = r3.hasNext()
            if (r4 == 0) goto L6b
            java.lang.Object r4 = r3.next()
            androidx.constraintlayout.motion.widget.q$b r4 = (androidx.constraintlayout.motion.widget.q.b) r4
            int r5 = androidx.constraintlayout.motion.widget.q.b.a(r4)
            if (r5 != r2) goto L49
            int r5 = androidx.constraintlayout.motion.widget.q.b.c(r4)
            if (r5 == r0) goto L55
        L49:
            int r5 = androidx.constraintlayout.motion.widget.q.b.a(r4)
            if (r5 != r8) goto L31
            int r5 = androidx.constraintlayout.motion.widget.q.b.c(r4)
            if (r5 != r7) goto L31
        L55:
            r6.f923c = r4
            if (r4 == 0) goto L6a
            androidx.constraintlayout.motion.widget.r r7 = androidx.constraintlayout.motion.widget.q.b.l(r4)
            if (r7 == 0) goto L6a
            androidx.constraintlayout.motion.widget.q$b r7 = r6.f923c
            androidx.constraintlayout.motion.widget.r r7 = androidx.constraintlayout.motion.widget.q.b.l(r7)
            boolean r6 = r6.q
            r7.u(r6)
        L6a:
            return
        L6b:
            androidx.constraintlayout.motion.widget.q$b r7 = r6.f
            java.util.ArrayList<androidx.constraintlayout.motion.widget.q$b> r3 = r6.g
            java.util.Iterator r3 = r3.iterator()
        L73:
            boolean r4 = r3.hasNext()
            if (r4 == 0) goto L87
            java.lang.Object r4 = r3.next()
            androidx.constraintlayout.motion.widget.q$b r4 = (androidx.constraintlayout.motion.widget.q.b) r4
            int r5 = androidx.constraintlayout.motion.widget.q.b.a(r4)
            if (r5 != r8) goto L73
            r7 = r4
            goto L73
        L87:
            androidx.constraintlayout.motion.widget.q$b r8 = new androidx.constraintlayout.motion.widget.q$b
            r8.<init>(r6, r7)
            androidx.constraintlayout.motion.widget.q.b.d(r8, r0)
            androidx.constraintlayout.motion.widget.q.b.b(r8, r2)
            if (r0 == r1) goto L99
            java.util.ArrayList<androidx.constraintlayout.motion.widget.q$b> r7 = r6.e
            r7.add(r8)
        L99:
            r6.f923c = r8
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.motion.widget.q.M(int, int):void");
    }

    public void N(b bVar) {
        this.f923c = bVar;
        if (bVar == null || bVar.l == null) {
            return;
        }
        this.f923c.l.u(this.q);
    }

    boolean O() {
        Iterator<b> it = this.e.iterator();
        while (it.hasNext()) {
            if (it.next().l != null) {
                return true;
            }
        }
        b bVar = this.f923c;
        return (bVar == null || bVar.l == null) ? false : true;
    }

    public void f(MotionLayout motionLayout, int i) {
        Iterator<b> it = this.e.iterator();
        while (it.hasNext()) {
            b next = it.next();
            if (next.m.size() > 0) {
                Iterator it2 = next.m.iterator();
                while (it2.hasNext()) {
                    ((b.a) it2.next()).b(motionLayout);
                }
            }
        }
        Iterator<b> it3 = this.g.iterator();
        while (it3.hasNext()) {
            b next2 = it3.next();
            if (next2.m.size() > 0) {
                Iterator it4 = next2.m.iterator();
                while (it4.hasNext()) {
                    ((b.a) it4.next()).b(motionLayout);
                }
            }
        }
        Iterator<b> it5 = this.e.iterator();
        while (it5.hasNext()) {
            b next3 = it5.next();
            if (next3.m.size() > 0) {
                Iterator it6 = next3.m.iterator();
                while (it6.hasNext()) {
                    ((b.a) it6.next()).a(motionLayout, i, next3);
                }
            }
        }
        Iterator<b> it7 = this.g.iterator();
        while (it7.hasNext()) {
            b next4 = it7.next();
            if (next4.m.size() > 0) {
                Iterator it8 = next4.m.iterator();
                while (it8.hasNext()) {
                    ((b.a) it8.next()).a(motionLayout, i, next4);
                }
            }
        }
    }

    boolean g(MotionLayout motionLayout, int i) {
        b bVar;
        MotionLayout.l lVar = MotionLayout.l.FINISHED;
        MotionLayout.l lVar2 = MotionLayout.l.MOVING;
        MotionLayout.l lVar3 = MotionLayout.l.SETUP;
        if ((this.p != null) || this.f924d) {
            return false;
        }
        Iterator<b> it = this.e.iterator();
        while (it.hasNext()) {
            b next = it.next();
            if (next.n != 0 && ((bVar = this.f923c) != next || !bVar.B(2))) {
                if (i == next.f929d && (next.n == 4 || next.n == 2)) {
                    motionLayout.setState(lVar);
                    motionLayout.setTransition(next);
                    if (next.n == 4) {
                        motionLayout.transitionToEnd();
                        motionLayout.setState(lVar3);
                        motionLayout.setState(lVar2);
                    } else {
                        motionLayout.setProgress(1.0f);
                        motionLayout.evaluate(true);
                        motionLayout.setState(lVar3);
                        motionLayout.setState(lVar2);
                        motionLayout.setState(lVar);
                        motionLayout.onNewStateAttachHandlers();
                    }
                    return true;
                }
                if (i == next.f928c && (next.n == 3 || next.n == 1)) {
                    motionLayout.setState(lVar);
                    motionLayout.setTransition(next);
                    if (next.n == 3) {
                        motionLayout.transitionToStart();
                        motionLayout.setState(lVar3);
                        motionLayout.setState(lVar2);
                    } else {
                        motionLayout.setProgress(0.0f);
                        motionLayout.evaluate(true);
                        motionLayout.setState(lVar3);
                        motionLayout.setState(lVar2);
                        motionLayout.setState(lVar);
                        motionLayout.onNewStateAttachHandlers();
                    }
                    return true;
                }
            }
        }
        return false;
    }

    public void h(boolean z) {
        this.f924d = z;
    }

    androidx.constraintlayout.widget.c i(int i) {
        SparseArray<androidx.constraintlayout.widget.c> sparseArray;
        int b2;
        androidx.constraintlayout.widget.h hVar = this.f922b;
        if (hVar != null && (b2 = hVar.b(i, -1, -1)) != -1) {
            i = b2;
        }
        if (this.h.get(i) == null) {
            StringBuilder j = b.b.a.a.a.j("Warning could not find ConstraintSet id/");
            j.append(a.b.a.c(this.f921a.getContext(), i));
            j.append(" In MotionScene");
            Log.e("MotionScene", j.toString());
            sparseArray = this.h;
            i = sparseArray.keyAt(0);
        } else {
            sparseArray = this.h;
        }
        return sparseArray.get(i);
    }

    public int[] j() {
        int size = this.h.size();
        int[] iArr = new int[size];
        for (int i = 0; i < size; i++) {
            iArr[i] = this.h.keyAt(i);
        }
        return iArr;
    }

    public ArrayList<b> k() {
        return this.e;
    }

    public int l() {
        b bVar = this.f923c;
        return bVar != null ? bVar.h : this.k;
    }

    int m() {
        b bVar = this.f923c;
        if (bVar == null) {
            return -1;
        }
        return bVar.f928c;
    }

    public Interpolator o() {
        int i = this.f923c.e;
        if (i == -2) {
            return AnimationUtils.loadInterpolator(this.f921a.getContext(), this.f923c.g);
        }
        if (i == -1) {
            return new a(this, a.f.a.i.a.c.c(this.f923c.f));
        }
        if (i == 0) {
            return new AccelerateDecelerateInterpolator();
        }
        if (i == 1) {
            return new AccelerateInterpolator();
        }
        if (i == 2) {
            return new DecelerateInterpolator();
        }
        if (i == 4) {
            return new BounceInterpolator();
        }
        if (i == 5) {
            return new OvershootInterpolator();
        }
        if (i != 6) {
            return null;
        }
        return new AnticipateInterpolator();
    }

    public void p(n nVar) {
        b bVar = this.f923c;
        if (bVar != null) {
            Iterator it = bVar.k.iterator();
            while (it.hasNext()) {
                ((g) it.next()).b(nVar);
            }
        } else {
            b bVar2 = this.f;
            if (bVar2 != null) {
                Iterator it2 = bVar2.k.iterator();
                while (it2.hasNext()) {
                    ((g) it2.next()).b(nVar);
                }
            }
        }
    }

    float q() {
        b bVar = this.f923c;
        if (bVar == null || bVar.l == null) {
            return 0.0f;
        }
        return this.f923c.l.e();
    }

    float r() {
        b bVar = this.f923c;
        if (bVar == null || bVar.l == null) {
            return 0.0f;
        }
        return this.f923c.l.f();
    }

    int s() {
        b bVar = this.f923c;
        if (bVar == null || bVar.l == null) {
            return 0;
        }
        return this.f923c.l.i();
    }

    float t() {
        b bVar = this.f923c;
        if (bVar == null || bVar.l == null) {
            return 0.0f;
        }
        return this.f923c.l.j();
    }

    float u() {
        b bVar = this.f923c;
        if (bVar == null || bVar.l == null) {
            return 0.0f;
        }
        return this.f923c.l.k();
    }

    float v() {
        b bVar = this.f923c;
        if (bVar == null || bVar.l == null) {
            return 0.0f;
        }
        return this.f923c.l.l();
    }

    float w() {
        b bVar = this.f923c;
        if (bVar == null || bVar.l == null) {
            return 0.0f;
        }
        return this.f923c.l.m();
    }

    int x() {
        b bVar = this.f923c;
        if (bVar == null) {
            return -1;
        }
        return bVar.f929d;
    }

    public b y(int i) {
        Iterator<b> it = this.e.iterator();
        while (it.hasNext()) {
            b next = it.next();
            if (next.f926a == i) {
                return next;
            }
        }
        return null;
    }

    public List<b> z(int i) {
        int b2;
        androidx.constraintlayout.widget.h hVar = this.f922b;
        if (hVar != null && (b2 = hVar.b(i, -1, -1)) != -1) {
            i = b2;
        }
        ArrayList arrayList = new ArrayList();
        Iterator<b> it = this.e.iterator();
        while (it.hasNext()) {
            b next = it.next();
            if (next.f929d == i || next.f928c == i) {
                arrayList.add(next);
            }
        }
        return arrayList;
    }
}
