package androidx.constraintlayout.motion.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.RectF;
import android.util.Log;
import android.util.Xml;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import androidx.core.widget.NestedScrollView;
import org.xmlpull.v1.XmlPullParser;

/* loaded from: classes.dex */
class r {
    private static final float[][] E = {new float[]{0.5f, 0.0f}, new float[]{0.0f, 0.5f}, new float[]{1.0f, 0.5f}, new float[]{0.5f, 1.0f}, new float[]{0.5f, 0.5f}, new float[]{0.0f, 0.5f}, new float[]{1.0f, 0.5f}};
    private static final float[][] F = {new float[]{0.0f, -1.0f}, new float[]{0.0f, 1.0f}, new float[]{-1.0f, 0.0f}, new float[]{1.0f, 0.0f}, new float[]{-1.0f, 0.0f}, new float[]{1.0f, 0.0f}};
    private float A;
    private float B;
    private int C;
    private int D;

    /* renamed from: a, reason: collision with root package name */
    private int f933a;

    /* renamed from: b, reason: collision with root package name */
    private int f934b;

    /* renamed from: c, reason: collision with root package name */
    private int f935c;

    /* renamed from: d, reason: collision with root package name */
    private int f936d;
    private int e;
    private int f;
    private float g;
    private float h;
    private int i;
    boolean j;
    private float k;
    private float l;
    private boolean m = false;
    private float[] n = new float[2];
    private int[] o = new int[2];
    private float p;
    private float q;
    private final MotionLayout r;
    private float s;
    private float t;
    private boolean u;
    private float v;
    private int w;
    private float x;
    private float y;
    private float z;

    class a implements View.OnTouchListener {
        a(r rVar) {
        }

        @Override // android.view.View.OnTouchListener
        public boolean onTouch(View view, MotionEvent motionEvent) {
            return false;
        }
    }

    class b implements NestedScrollView.b {
        b(r rVar) {
        }

        @Override // androidx.core.widget.NestedScrollView.b
        public void a(NestedScrollView nestedScrollView, int i, int i2, int i3, int i4) {
        }
    }

    r(Context context, MotionLayout motionLayout, XmlPullParser xmlPullParser) {
        this.f933a = 0;
        this.f934b = 0;
        this.f935c = 0;
        this.f936d = -1;
        this.e = -1;
        this.f = -1;
        this.g = 0.5f;
        this.h = 0.5f;
        this.i = -1;
        this.j = false;
        this.k = 0.0f;
        this.l = 1.0f;
        this.s = 4.0f;
        this.t = 1.2f;
        this.u = true;
        this.v = 1.0f;
        this.w = 0;
        this.x = 10.0f;
        this.y = 10.0f;
        this.z = 1.0f;
        this.A = Float.NaN;
        this.B = Float.NaN;
        this.C = 0;
        this.D = 0;
        this.r = motionLayout;
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(Xml.asAttributeSet(xmlPullParser), androidx.constraintlayout.widget.f.y);
        int indexCount = obtainStyledAttributes.getIndexCount();
        for (int i = 0; i < indexCount; i++) {
            int index = obtainStyledAttributes.getIndex(i);
            if (index == 16) {
                this.f936d = obtainStyledAttributes.getResourceId(index, this.f936d);
            } else if (index == 17) {
                int i2 = obtainStyledAttributes.getInt(index, this.f933a);
                this.f933a = i2;
                float[][] fArr = E;
                this.h = fArr[i2][0];
                this.g = fArr[i2][1];
            } else if (index == 1) {
                int i3 = obtainStyledAttributes.getInt(index, this.f934b);
                this.f934b = i3;
                float[][] fArr2 = F;
                if (i3 < fArr2.length) {
                    this.k = fArr2[i3][0];
                    this.l = fArr2[i3][1];
                } else {
                    this.l = Float.NaN;
                    this.k = Float.NaN;
                    this.j = true;
                }
            } else if (index == 6) {
                this.s = obtainStyledAttributes.getFloat(index, this.s);
            } else if (index == 5) {
                this.t = obtainStyledAttributes.getFloat(index, this.t);
            } else if (index == 7) {
                this.u = obtainStyledAttributes.getBoolean(index, this.u);
            } else if (index == 2) {
                this.v = obtainStyledAttributes.getFloat(index, this.v);
            } else if (index == 3) {
                this.x = obtainStyledAttributes.getFloat(index, this.x);
            } else if (index == 18) {
                this.e = obtainStyledAttributes.getResourceId(index, this.e);
            } else if (index == 9) {
                this.f935c = obtainStyledAttributes.getInt(index, this.f935c);
            } else if (index == 8) {
                this.w = obtainStyledAttributes.getInteger(index, 0);
            } else if (index == 4) {
                this.f = obtainStyledAttributes.getResourceId(index, 0);
            } else if (index == 10) {
                this.i = obtainStyledAttributes.getResourceId(index, this.i);
            } else if (index == 12) {
                this.y = obtainStyledAttributes.getFloat(index, this.y);
            } else if (index == 13) {
                this.z = obtainStyledAttributes.getFloat(index, this.z);
            } else if (index == 14) {
                this.A = obtainStyledAttributes.getFloat(index, this.A);
            } else if (index == 15) {
                this.B = obtainStyledAttributes.getFloat(index, this.B);
            } else if (index == 11) {
                this.C = obtainStyledAttributes.getInt(index, this.C);
            } else if (index == 0) {
                this.D = obtainStyledAttributes.getInt(index, this.D);
            }
        }
        obtainStyledAttributes.recycle();
    }

    float a(float f, float f2) {
        return (f2 * this.l) + (f * this.k);
    }

    public int b() {
        return this.D;
    }

    public int c() {
        return this.w;
    }

    RectF d(ViewGroup viewGroup, RectF rectF) {
        View findViewById;
        int i = this.f;
        if (i == -1 || (findViewById = viewGroup.findViewById(i)) == null) {
            return null;
        }
        rectF.set(findViewById.getLeft(), findViewById.getTop(), findViewById.getRight(), findViewById.getBottom());
        return rectF;
    }

    float e() {
        return this.t;
    }

    public float f() {
        return this.s;
    }

    boolean g() {
        return this.u;
    }

    float h(float f, float f2) {
        this.r.getAnchorDpDt(this.f936d, this.r.getProgress(), this.h, this.g, this.n);
        float f3 = this.k;
        if (f3 != 0.0f) {
            float[] fArr = this.n;
            if (fArr[0] == 0.0f) {
                fArr[0] = 1.0E-7f;
            }
            return (f * f3) / fArr[0];
        }
        float[] fArr2 = this.n;
        if (fArr2[1] == 0.0f) {
            fArr2[1] = 1.0E-7f;
        }
        return (f2 * this.l) / fArr2[1];
    }

    public int i() {
        return this.C;
    }

    public float j() {
        return this.y;
    }

    public float k() {
        return this.z;
    }

    public float l() {
        return this.A;
    }

    public float m() {
        return this.B;
    }

    RectF n(ViewGroup viewGroup, RectF rectF) {
        View findViewById;
        int i = this.e;
        if (i == -1 || (findViewById = viewGroup.findViewById(i)) == null) {
            return null;
        }
        rectF.set(findViewById.getLeft(), findViewById.getTop(), findViewById.getRight(), findViewById.getBottom());
        return rectF;
    }

    int o() {
        return this.e;
    }

    boolean p() {
        return this.m;
    }

    /* JADX WARN: Removed duplicated region for block: B:100:0x02ab  */
    /* JADX WARN: Removed duplicated region for block: B:61:0x0286  */
    /* JADX WARN: Removed duplicated region for block: B:64:0x02c7  */
    /* JADX WARN: Removed duplicated region for block: B:99:0x02d4  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    void q(android.view.MotionEvent r26, androidx.constraintlayout.motion.widget.MotionLayout.h r27, int r28, androidx.constraintlayout.motion.widget.q r29) {
        /*
            Method dump skipped, instructions count: 1387
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.motion.widget.r.q(android.view.MotionEvent, androidx.constraintlayout.motion.widget.MotionLayout$h, int, androidx.constraintlayout.motion.widget.q):void");
    }

    void r(float f, float f2) {
        float progress = this.r.getProgress();
        if (!this.m) {
            this.m = true;
            this.r.setProgress(progress);
        }
        this.r.getAnchorDpDt(this.f936d, progress, this.h, this.g, this.n);
        float f3 = this.k;
        float[] fArr = this.n;
        if (Math.abs((this.l * fArr[1]) + (f3 * fArr[0])) < 0.01d) {
            float[] fArr2 = this.n;
            fArr2[0] = 0.01f;
            fArr2[1] = 0.01f;
        }
        float f4 = this.k;
        float max = Math.max(Math.min(progress + (f4 != 0.0f ? (f * f4) / this.n[0] : (f2 * this.l) / this.n[1]), 1.0f), 0.0f);
        if (max != this.r.getProgress()) {
            this.r.setProgress(max);
        }
    }

    void s(float f, float f2) {
        this.m = false;
        float progress = this.r.getProgress();
        this.r.getAnchorDpDt(this.f936d, progress, this.h, this.g, this.n);
        float f3 = this.k;
        float[] fArr = this.n;
        float f4 = fArr[0];
        float f5 = this.l;
        float f6 = fArr[1];
        float f7 = f3 != 0.0f ? (f * f3) / fArr[0] : (f2 * f5) / fArr[1];
        if (!Float.isNaN(f7)) {
            progress += f7 / 3.0f;
        }
        if (progress != 0.0f) {
            boolean z = progress != 1.0f;
            int i = this.f935c;
            if ((i != 3) && z) {
                this.r.touchAnimateTo(i, ((double) progress) >= 0.5d ? 1.0f : 0.0f, f7);
            }
        }
    }

    void t(float f, float f2) {
        this.p = f;
        this.q = f2;
    }

    public String toString() {
        if (Float.isNaN(this.k)) {
            return "rotation";
        }
        return this.k + " , " + this.l;
    }

    public void u(boolean z) {
        if (z) {
            float[][] fArr = F;
            fArr[4] = fArr[3];
            fArr[5] = fArr[2];
            float[][] fArr2 = E;
            fArr2[5] = fArr2[2];
            fArr2[6] = fArr2[1];
        } else {
            float[][] fArr3 = F;
            fArr3[4] = fArr3[2];
            fArr3[5] = fArr3[3];
            float[][] fArr4 = E;
            fArr4[5] = fArr4[1];
            fArr4[6] = fArr4[2];
        }
        float[][] fArr5 = E;
        int i = this.f933a;
        this.h = fArr5[i][0];
        this.g = fArr5[i][1];
        int i2 = this.f934b;
        float[][] fArr6 = F;
        if (i2 >= fArr6.length) {
            return;
        }
        this.k = fArr6[i2][0];
        this.l = fArr6[i2][1];
    }

    public void v(int i) {
        this.f935c = i;
    }

    void w(float f, float f2) {
        this.p = f;
        this.q = f2;
        this.m = false;
    }

    void x() {
        View view;
        int i = this.f936d;
        if (i != -1) {
            view = this.r.findViewById(i);
            if (view == null) {
                StringBuilder j = b.b.a.a.a.j("cannot find TouchAnchorId @id/");
                j.append(a.b.a.c(this.r.getContext(), this.f936d));
                Log.e("TouchResponse", j.toString());
            }
        } else {
            view = null;
        }
        if (view instanceof NestedScrollView) {
            NestedScrollView nestedScrollView = (NestedScrollView) view;
            nestedScrollView.setOnTouchListener(new a(this));
            nestedScrollView.setOnScrollChangeListener(new b(this));
        }
    }
}
