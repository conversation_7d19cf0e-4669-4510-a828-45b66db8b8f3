package androidx.cardview.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import androidx.cardview.widget.CardView;

/* loaded from: classes.dex */
class a implements c {
    a() {
    }

    private d o(b bVar) {
        return (d) ((CardView.a) bVar).a();
    }

    @Override // androidx.cardview.widget.c
    public float a(b bVar) {
        return o(bVar).c();
    }

    @Override // androidx.cardview.widget.c
    public float b(b bVar) {
        return o(bVar).d();
    }

    @Override // androidx.cardview.widget.c
    public float c(b bVar) {
        return o(bVar).d() * 2.0f;
    }

    @Override // androidx.cardview.widget.c
    public float d(b bVar) {
        return o(bVar).d() * 2.0f;
    }

    @Override // androidx.cardview.widget.c
    public void e(b bVar) {
        m(bVar, o(bVar).c());
    }

    @Override // androidx.cardview.widget.c
    public ColorStateList f(b bVar) {
        return o(bVar).b();
    }

    @Override // androidx.cardview.widget.c
    public void g(b bVar, float f) {
        CardView.this.setElevation(f);
    }

    @Override // androidx.cardview.widget.c
    public void h(b bVar, Context context, ColorStateList colorStateList, float f, float f2, float f3) {
        CardView.a aVar = (CardView.a) bVar;
        aVar.c(new d(colorStateList, f));
        CardView cardView = CardView.this;
        cardView.setClipToOutline(true);
        cardView.setElevation(f2);
        m(aVar, f3);
    }

    @Override // androidx.cardview.widget.c
    public void i(b bVar, float f) {
        o(bVar).h(f);
    }

    @Override // androidx.cardview.widget.c
    public void j(b bVar) {
        m(bVar, o(bVar).c());
    }

    @Override // androidx.cardview.widget.c
    public void k(b bVar, ColorStateList colorStateList) {
        o(bVar).f(colorStateList);
    }

    @Override // androidx.cardview.widget.c
    public float l(b bVar) {
        return CardView.this.getElevation();
    }

    @Override // androidx.cardview.widget.c
    public void m(b bVar, float f) {
        CardView.a aVar = (CardView.a) bVar;
        o(bVar).g(f, CardView.this.getUseCompatPadding(), aVar.b());
        n(bVar);
    }

    @Override // androidx.cardview.widget.c
    public void n(b bVar) {
        CardView.a aVar = (CardView.a) bVar;
        if (!CardView.this.getUseCompatPadding()) {
            aVar.d(0, 0, 0, 0);
            return;
        }
        float c2 = o(bVar).c();
        float d2 = o(bVar).d();
        int ceil = (int) Math.ceil(e.a(c2, d2, aVar.b()));
        int ceil2 = (int) Math.ceil(e.b(c2, d2, aVar.b()));
        aVar.d(ceil, ceil2, ceil, ceil2);
    }
}
