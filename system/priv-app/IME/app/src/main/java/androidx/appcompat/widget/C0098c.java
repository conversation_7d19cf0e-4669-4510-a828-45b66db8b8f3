package androidx.appcompat.widget;

import a.h.h.b;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.os.Parcel;
import android.os.Parcelable;
import android.util.SparseBooleanArray;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import androidx.appcompat.view.menu.ActionMenuItemView;
import androidx.appcompat.view.menu.m;
import androidx.appcompat.view.menu.n;
import androidx.appcompat.widget.ActionMenuView;
import java.util.ArrayList;
import org.libpag.R;

/* renamed from: androidx.appcompat.widget.c, reason: case insensitive filesystem */
/* loaded from: classes.dex */
class C0098c extends androidx.appcompat.view.menu.b implements b.a {
    d j;
    private Drawable k;
    private boolean l;
    private boolean m;
    private boolean n;
    private int o;
    private int p;
    private int q;
    private boolean r;
    private int s;
    private final SparseBooleanArray t;
    e u;
    a v;
    RunnableC0028c w;
    private b x;
    final f y;
    int z;

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: androidx.appcompat.widget.c$a */
    class a extends androidx.appcompat.view.menu.l {
        public a(Context context, androidx.appcompat.view.menu.r rVar, View view) {
            super(context, rVar, view, false, R.attr.actionOverflowMenuStyle, 0);
            if (!((androidx.appcompat.view.menu.i) rVar.getItem()).k()) {
                View view2 = C0098c.this.j;
                e(view2 == null ? (View) ((androidx.appcompat.view.menu.b) C0098c.this).h : view2);
            }
            i(C0098c.this.y);
        }

        @Override // androidx.appcompat.view.menu.l
        protected void d() {
            C0098c c0098c = C0098c.this;
            c0098c.v = null;
            c0098c.z = 0;
            super.d();
        }
    }

    /* renamed from: androidx.appcompat.widget.c$b */
    private class b extends ActionMenuItemView.b {
        b() {
        }

        @Override // androidx.appcompat.view.menu.ActionMenuItemView.b
        public androidx.appcompat.view.menu.p a() {
            a aVar = C0098c.this.v;
            if (aVar != null) {
                return aVar.b();
            }
            return null;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: androidx.appcompat.widget.c$c, reason: collision with other inner class name */
    class RunnableC0028c implements Runnable {

        /* renamed from: a, reason: collision with root package name */
        private e f786a;

        public RunnableC0028c(e eVar) {
            this.f786a = eVar;
        }

        @Override // java.lang.Runnable
        public void run() {
            if (((androidx.appcompat.view.menu.b) C0098c.this).f628c != null) {
                ((androidx.appcompat.view.menu.b) C0098c.this).f628c.d();
            }
            View view = (View) ((androidx.appcompat.view.menu.b) C0098c.this).h;
            if (view != null && view.getWindowToken() != null && this.f786a.k()) {
                C0098c.this.u = this.f786a;
            }
            C0098c.this.w = null;
        }
    }

    /* renamed from: androidx.appcompat.widget.c$d */
    private class d extends AppCompatImageView implements ActionMenuView.a {

        /* renamed from: androidx.appcompat.widget.c$d$a */
        class a extends t {
            a(View view, C0098c c0098c) {
                super(view);
            }

            @Override // androidx.appcompat.widget.t
            public androidx.appcompat.view.menu.p b() {
                e eVar = C0098c.this.u;
                if (eVar == null) {
                    return null;
                }
                return eVar.b();
            }

            @Override // androidx.appcompat.widget.t
            public boolean c() {
                C0098c.this.K();
                return true;
            }

            @Override // androidx.appcompat.widget.t
            public boolean d() {
                C0098c c0098c = C0098c.this;
                if (c0098c.w != null) {
                    return false;
                }
                c0098c.C();
                return true;
            }
        }

        public d(Context context) {
            super(context, null, R.attr.actionOverflowButtonStyle);
            setClickable(true);
            setFocusable(true);
            setVisibility(0);
            setEnabled(true);
            setTooltipText(getContentDescription());
            setOnTouchListener(new a(this, C0098c.this));
        }

        @Override // androidx.appcompat.widget.ActionMenuView.a
        public boolean needsDividerAfter() {
            return false;
        }

        @Override // androidx.appcompat.widget.ActionMenuView.a
        public boolean needsDividerBefore() {
            return false;
        }

        @Override // android.view.View
        public boolean performClick() {
            if (super.performClick()) {
                return true;
            }
            playSoundEffect(0);
            C0098c.this.K();
            return true;
        }

        @Override // android.widget.ImageView
        protected boolean setFrame(int i, int i2, int i3, int i4) {
            boolean frame = super.setFrame(i, i2, i3, i4);
            Drawable drawable = getDrawable();
            Drawable background = getBackground();
            if (drawable != null && background != null) {
                int width = getWidth();
                int height = getHeight();
                int max = Math.max(width, height) / 2;
                int paddingLeft = (width + (getPaddingLeft() - getPaddingRight())) / 2;
                int paddingTop = (height + (getPaddingTop() - getPaddingBottom())) / 2;
                background.setHotspotBounds(paddingLeft - max, paddingTop - max, paddingLeft + max, paddingTop + max);
            }
            return frame;
        }
    }

    /* renamed from: androidx.appcompat.widget.c$e */
    private class e extends androidx.appcompat.view.menu.l {
        public e(Context context, androidx.appcompat.view.menu.g gVar, View view, boolean z) {
            super(context, gVar, view, z, R.attr.actionOverflowMenuStyle, 0);
            g(8388613);
            i(C0098c.this.y);
        }

        @Override // androidx.appcompat.view.menu.l
        protected void d() {
            if (((androidx.appcompat.view.menu.b) C0098c.this).f628c != null) {
                ((androidx.appcompat.view.menu.b) C0098c.this).f628c.e(true);
            }
            C0098c.this.u = null;
            super.d();
        }
    }

    /* renamed from: androidx.appcompat.widget.c$f */
    private class f implements m.a {
        f() {
        }

        @Override // androidx.appcompat.view.menu.m.a
        public void a(androidx.appcompat.view.menu.g gVar, boolean z) {
            if (gVar instanceof androidx.appcompat.view.menu.r) {
                gVar.q().e(false);
            }
            m.a o = C0098c.this.o();
            if (o != null) {
                o.a(gVar, z);
            }
        }

        @Override // androidx.appcompat.view.menu.m.a
        public boolean b(androidx.appcompat.view.menu.g gVar) {
            if (gVar == ((androidx.appcompat.view.menu.b) C0098c.this).f628c) {
                return false;
            }
            C0098c.this.z = ((androidx.appcompat.view.menu.i) ((androidx.appcompat.view.menu.r) gVar).getItem()).getItemId();
            m.a o = C0098c.this.o();
            if (o != null) {
                return o.b(gVar);
            }
            return false;
        }
    }

    @SuppressLint({"BanParcelableUsage"})
    /* renamed from: androidx.appcompat.widget.c$g */
    private static class g implements Parcelable {

        /* renamed from: a, reason: collision with root package name */
        public int f790a;

        g() {
        }

        @Override // android.os.Parcelable
        public int describeContents() {
            return 0;
        }

        @Override // android.os.Parcelable
        public void writeToParcel(Parcel parcel, int i) {
            parcel.writeInt(this.f790a);
        }
    }

    public C0098c(Context context) {
        super(context, R.layout.abc_action_menu_layout, R.layout.abc_action_menu_item_layout);
        this.t = new SparseBooleanArray();
        this.y = new f();
    }

    public boolean A() {
        boolean z;
        boolean C = C();
        a aVar = this.v;
        if (aVar != null) {
            aVar.a();
            z = true;
        } else {
            z = false;
        }
        return z | C;
    }

    public Drawable B() {
        d dVar = this.j;
        if (dVar != null) {
            return dVar.getDrawable();
        }
        if (this.l) {
            return this.k;
        }
        return null;
    }

    public boolean C() {
        Object obj;
        RunnableC0028c runnableC0028c = this.w;
        if (runnableC0028c != null && (obj = this.h) != null) {
            ((View) obj).removeCallbacks(runnableC0028c);
            this.w = null;
            return true;
        }
        e eVar = this.u;
        if (eVar == null) {
            return false;
        }
        eVar.a();
        return true;
    }

    public boolean D() {
        e eVar = this.u;
        return eVar != null && eVar.c();
    }

    public boolean E() {
        return this.m;
    }

    public void F() {
        this.q = a.b.f.a.b(this.f627b).d();
        androidx.appcompat.view.menu.g gVar = this.f628c;
        if (gVar != null) {
            gVar.x(true);
        }
    }

    public void G(boolean z) {
        this.r = z;
    }

    public void H(ActionMenuView actionMenuView) {
        this.h = actionMenuView;
        actionMenuView.initialize(this.f628c);
    }

    public void I(Drawable drawable) {
        d dVar = this.j;
        if (dVar != null) {
            dVar.setImageDrawable(drawable);
        } else {
            this.l = true;
            this.k = drawable;
        }
    }

    public void J(boolean z) {
        this.m = z;
        this.n = true;
    }

    public boolean K() {
        androidx.appcompat.view.menu.g gVar;
        if (!this.m || D() || (gVar = this.f628c) == null || this.h == null || this.w != null || gVar.p().isEmpty()) {
            return false;
        }
        RunnableC0028c runnableC0028c = new RunnableC0028c(new e(this.f627b, this.f628c, this.j, true));
        this.w = runnableC0028c;
        ((View) this.h).post(runnableC0028c);
        return true;
    }

    @Override // androidx.appcompat.view.menu.b, androidx.appcompat.view.menu.m
    public void a(androidx.appcompat.view.menu.g gVar, boolean z) {
        A();
        super.a(gVar, z);
    }

    @Override // a.h.h.b.a
    public void b(boolean z) {
        if (z) {
            super.m(null);
            return;
        }
        androidx.appcompat.view.menu.g gVar = this.f628c;
        if (gVar != null) {
            gVar.e(false);
        }
    }

    @Override // androidx.appcompat.view.menu.m
    public boolean d() {
        ArrayList<androidx.appcompat.view.menu.i> arrayList;
        int i;
        boolean z;
        boolean z2;
        androidx.appcompat.view.menu.g gVar = this.f628c;
        View view = null;
        boolean z3 = false;
        if (gVar != null) {
            arrayList = gVar.r();
            i = arrayList.size();
        } else {
            arrayList = null;
            i = 0;
        }
        int i2 = this.q;
        int i3 = this.p;
        int makeMeasureSpec = View.MeasureSpec.makeMeasureSpec(0, 0);
        ViewGroup viewGroup = (ViewGroup) this.h;
        int i4 = 0;
        boolean z4 = false;
        int i5 = 0;
        int i6 = 0;
        while (true) {
            z = true;
            if (i4 >= i) {
                break;
            }
            androidx.appcompat.view.menu.i iVar = arrayList.get(i4);
            if (iVar.requiresActionButton()) {
                i5++;
            } else if (iVar.m()) {
                i6++;
            } else {
                z4 = true;
            }
            if (this.r && iVar.isActionViewExpanded()) {
                i2 = 0;
            }
            i4++;
        }
        if (this.m && (z4 || i6 + i5 > i2)) {
            i2--;
        }
        int i7 = i2 - i5;
        SparseBooleanArray sparseBooleanArray = this.t;
        sparseBooleanArray.clear();
        int i8 = 0;
        int i9 = 0;
        while (i8 < i) {
            androidx.appcompat.view.menu.i iVar2 = arrayList.get(i8);
            if (iVar2.requiresActionButton()) {
                View p = p(iVar2, view, viewGroup);
                p.measure(makeMeasureSpec, makeMeasureSpec);
                int measuredWidth = p.getMeasuredWidth();
                i3 -= measuredWidth;
                if (i9 == 0) {
                    i9 = measuredWidth;
                }
                int groupId = iVar2.getGroupId();
                if (groupId != 0) {
                    sparseBooleanArray.put(groupId, z);
                }
                iVar2.r(z);
                z2 = z3;
            } else if (iVar2.m()) {
                int groupId2 = iVar2.getGroupId();
                boolean z5 = sparseBooleanArray.get(groupId2);
                boolean z6 = ((i7 > 0 || z5) && i3 > 0) ? z : z3;
                if (z6) {
                    View p2 = p(iVar2, view, viewGroup);
                    p2.measure(makeMeasureSpec, makeMeasureSpec);
                    int measuredWidth2 = p2.getMeasuredWidth();
                    i3 -= measuredWidth2;
                    if (i9 == 0) {
                        i9 = measuredWidth2;
                    }
                    z6 &= i3 + i9 > 0 ? z : false;
                }
                boolean z7 = z6;
                if (z7 && groupId2 != 0) {
                    sparseBooleanArray.put(groupId2, z);
                } else if (z5) {
                    sparseBooleanArray.put(groupId2, false);
                    for (int i10 = 0; i10 < i8; i10++) {
                        androidx.appcompat.view.menu.i iVar3 = arrayList.get(i10);
                        if (iVar3.getGroupId() == groupId2) {
                            if (iVar3.k()) {
                                i7++;
                            }
                            iVar3.r(false);
                        }
                    }
                }
                if (z7) {
                    i7--;
                }
                iVar2.r(z7);
                z2 = false;
            } else {
                z2 = z3;
                iVar2.r(z2);
            }
            i8++;
            z3 = z2;
            view = null;
            z = true;
        }
        return z;
    }

    @Override // androidx.appcompat.view.menu.m
    public Parcelable e() {
        g gVar = new g();
        gVar.f790a = this.z;
        return gVar;
    }

    @Override // androidx.appcompat.view.menu.b
    public void f(androidx.appcompat.view.menu.i iVar, n.a aVar) {
        aVar.initialize(iVar, 0);
        ActionMenuItemView actionMenuItemView = (ActionMenuItemView) aVar;
        actionMenuItemView.setItemInvoker((ActionMenuView) this.h);
        if (this.x == null) {
            this.x = new b();
        }
        actionMenuItemView.setPopupCallback(this.x);
    }

    @Override // androidx.appcompat.view.menu.b, androidx.appcompat.view.menu.m
    public void g(Context context, androidx.appcompat.view.menu.g gVar) {
        super.g(context, gVar);
        Resources resources = context.getResources();
        a.b.f.a b2 = a.b.f.a.b(context);
        if (!this.n) {
            this.m = true;
        }
        this.o = b2.c();
        this.q = b2.d();
        int i = this.o;
        if (this.m) {
            if (this.j == null) {
                d dVar = new d(this.f626a);
                this.j = dVar;
                if (this.l) {
                    dVar.setImageDrawable(this.k);
                    this.k = null;
                    this.l = false;
                }
                int makeMeasureSpec = View.MeasureSpec.makeMeasureSpec(0, 0);
                this.j.measure(makeMeasureSpec, makeMeasureSpec);
            }
            i -= this.j.getMeasuredWidth();
        } else {
            this.j = null;
        }
        this.p = i;
        this.s = (int) (resources.getDisplayMetrics().density * 56.0f);
    }

    @Override // androidx.appcompat.view.menu.m
    public void h(Parcelable parcelable) {
        int i;
        MenuItem findItem;
        if ((parcelable instanceof g) && (i = ((g) parcelable).f790a) > 0 && (findItem = this.f628c.findItem(i)) != null) {
            m((androidx.appcompat.view.menu.r) findItem.getSubMenu());
        }
    }

    @Override // androidx.appcompat.view.menu.b
    public boolean k(ViewGroup viewGroup, int i) {
        if (viewGroup.getChildAt(i) == this.j) {
            return false;
        }
        viewGroup.removeViewAt(i);
        return true;
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // androidx.appcompat.view.menu.b, androidx.appcompat.view.menu.m
    public boolean m(androidx.appcompat.view.menu.r rVar) {
        boolean z = false;
        if (!rVar.hasVisibleItems()) {
            return false;
        }
        androidx.appcompat.view.menu.r rVar2 = rVar;
        while (rVar2.S() != this.f628c) {
            rVar2 = (androidx.appcompat.view.menu.r) rVar2.S();
        }
        MenuItem item = rVar2.getItem();
        ViewGroup viewGroup = (ViewGroup) this.h;
        View view = null;
        if (viewGroup != null) {
            int childCount = viewGroup.getChildCount();
            int i = 0;
            while (true) {
                if (i >= childCount) {
                    break;
                }
                View childAt = viewGroup.getChildAt(i);
                if ((childAt instanceof n.a) && ((n.a) childAt).getItemData() == item) {
                    view = childAt;
                    break;
                }
                i++;
            }
        }
        if (view == null) {
            return false;
        }
        this.z = ((androidx.appcompat.view.menu.i) rVar.getItem()).getItemId();
        int size = rVar.size();
        int i2 = 0;
        while (true) {
            if (i2 >= size) {
                break;
            }
            MenuItem item2 = rVar.getItem(i2);
            if (item2.isVisible() && item2.getIcon() != null) {
                z = true;
                break;
            }
            i2++;
        }
        a aVar = new a(this.f627b, rVar, view);
        this.v = aVar;
        aVar.f(z);
        if (!this.v.k()) {
            throw new IllegalStateException("MenuPopupHelper cannot be used without an anchor");
        }
        super.m(rVar);
        return true;
    }

    @Override // androidx.appcompat.view.menu.b, androidx.appcompat.view.menu.m
    public void n(boolean z) {
        super.n(z);
        ((View) this.h).requestLayout();
        androidx.appcompat.view.menu.g gVar = this.f628c;
        boolean z2 = false;
        if (gVar != null) {
            ArrayList<androidx.appcompat.view.menu.i> l = gVar.l();
            int size = l.size();
            for (int i = 0; i < size; i++) {
                a.h.h.b b2 = l.get(i).b();
                if (b2 != null) {
                    b2.i(this);
                }
            }
        }
        androidx.appcompat.view.menu.g gVar2 = this.f628c;
        ArrayList<androidx.appcompat.view.menu.i> p = gVar2 != null ? gVar2.p() : null;
        if (this.m && p != null) {
            int size2 = p.size();
            if (size2 == 1) {
                z2 = !p.get(0).isActionViewExpanded();
            } else if (size2 > 0) {
                z2 = true;
            }
        }
        d dVar = this.j;
        if (z2) {
            if (dVar == null) {
                this.j = new d(this.f626a);
            }
            ViewGroup viewGroup = (ViewGroup) this.j.getParent();
            if (viewGroup != this.h) {
                if (viewGroup != null) {
                    viewGroup.removeView(this.j);
                }
                ActionMenuView actionMenuView = (ActionMenuView) this.h;
                actionMenuView.addView(this.j, actionMenuView.generateOverflowButtonLayoutParams());
            }
        } else if (dVar != null) {
            Object parent = dVar.getParent();
            Object obj = this.h;
            if (parent == obj) {
                ((ViewGroup) obj).removeView(this.j);
            }
        }
        ((ActionMenuView) this.h).setOverflowReserved(this.m);
    }

    @Override // androidx.appcompat.view.menu.b
    public View p(androidx.appcompat.view.menu.i iVar, View view, ViewGroup viewGroup) {
        View actionView = iVar.getActionView();
        if (actionView == null || iVar.i()) {
            actionView = super.p(iVar, view, viewGroup);
        }
        actionView.setVisibility(iVar.isActionViewExpanded() ? 8 : 0);
        ActionMenuView actionMenuView = (ActionMenuView) viewGroup;
        ViewGroup.LayoutParams layoutParams = actionView.getLayoutParams();
        if (!actionMenuView.checkLayoutParams(layoutParams)) {
            actionView.setLayoutParams(actionMenuView.generateLayoutParams(layoutParams));
        }
        return actionView;
    }

    @Override // androidx.appcompat.view.menu.b
    public androidx.appcompat.view.menu.n q(ViewGroup viewGroup) {
        androidx.appcompat.view.menu.n nVar = this.h;
        androidx.appcompat.view.menu.n q = super.q(viewGroup);
        if (nVar != q) {
            ((ActionMenuView) q).setPresenter(this);
        }
        return q;
    }

    @Override // androidx.appcompat.view.menu.b
    public boolean s(int i, androidx.appcompat.view.menu.i iVar) {
        return iVar.k();
    }
}
