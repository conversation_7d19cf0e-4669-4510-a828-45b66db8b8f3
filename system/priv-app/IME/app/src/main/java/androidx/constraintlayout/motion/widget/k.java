package androidx.constraintlayout.motion.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseIntArray;
import android.view.View;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Locale;

/* loaded from: classes.dex */
public class k extends d {
    private float s;
    private int f = -1;
    private String g = null;
    private int h = -1;
    private String i = null;
    private String j = null;
    private int k = -1;
    private int l = -1;
    private View m = null;
    float n = 0.1f;
    private boolean o = true;
    private boolean p = true;
    private boolean q = true;
    private float r = Float.NaN;
    private boolean t = false;
    int u = -1;
    int v = -1;
    int w = -1;
    RectF x = new RectF();
    RectF y = new RectF();
    HashMap<String, Method> z = new HashMap<>();

    private static class a {

        /* renamed from: a, reason: collision with root package name */
        private static SparseIntArray f907a;

        static {
            SparseIntArray sparseIntArray = new SparseIntArray();
            f907a = sparseIntArray;
            sparseIntArray.append(0, 8);
            f907a.append(4, 4);
            f907a.append(5, 1);
            f907a.append(6, 2);
            f907a.append(1, 7);
            f907a.append(7, 6);
            f907a.append(9, 5);
            f907a.append(3, 9);
            f907a.append(2, 10);
            f907a.append(8, 11);
            f907a.append(10, 12);
            f907a.append(11, 13);
            f907a.append(12, 14);
        }

        public static void a(k kVar, TypedArray typedArray) {
            int indexCount = typedArray.getIndexCount();
            for (int i = 0; i < indexCount; i++) {
                int index = typedArray.getIndex(i);
                switch (f907a.get(index)) {
                    case 1:
                        kVar.i = typedArray.getString(index);
                        break;
                    case 2:
                        kVar.j = typedArray.getString(index);
                        break;
                    case 3:
                    default:
                        StringBuilder j = b.b.a.a.a.j("unused attribute 0x");
                        j.append(Integer.toHexString(index));
                        j.append("   ");
                        j.append(f907a.get(index));
                        Log.e("KeyTrigger", j.toString());
                        break;
                    case 4:
                        kVar.g = typedArray.getString(index);
                        break;
                    case 5:
                        kVar.n = typedArray.getFloat(index, kVar.n);
                        break;
                    case 6:
                        kVar.k = typedArray.getResourceId(index, kVar.k);
                        break;
                    case MotionLayout.TOUCH_UP_NEVER_TO_END /* 7 */:
                        if (MotionLayout.IS_IN_EDIT_MODE) {
                            int resourceId = typedArray.getResourceId(index, kVar.f898b);
                            kVar.f898b = resourceId;
                            if (resourceId != -1) {
                                break;
                            }
                            kVar.f899c = typedArray.getString(index);
                            break;
                        } else {
                            if (typedArray.peekValue(index).type != 3) {
                                kVar.f898b = typedArray.getResourceId(index, kVar.f898b);
                                break;
                            }
                            kVar.f899c = typedArray.getString(index);
                        }
                    case 8:
                        int integer = typedArray.getInteger(index, kVar.f897a);
                        kVar.f897a = integer;
                        kVar.r = (integer + 0.5f) / 100.0f;
                        break;
                    case 9:
                        kVar.l = typedArray.getResourceId(index, kVar.l);
                        break;
                    case 10:
                        kVar.t = typedArray.getBoolean(index, kVar.t);
                        break;
                    case 11:
                        kVar.h = typedArray.getResourceId(index, kVar.h);
                        break;
                    case 12:
                        kVar.w = typedArray.getResourceId(index, kVar.w);
                        break;
                    case 13:
                        kVar.u = typedArray.getResourceId(index, kVar.u);
                        break;
                    case 14:
                        kVar.v = typedArray.getResourceId(index, kVar.v);
                        break;
                }
            }
        }
    }

    public k() {
        this.f900d = 5;
        this.e = new HashMap<>();
    }

    private void w(String str, View view) {
        Method method;
        if (str == null) {
            return;
        }
        if (str.startsWith(".")) {
            boolean z = str.length() == 1;
            if (!z) {
                str = str.substring(1).toLowerCase(Locale.ROOT);
            }
            for (String str2 : this.e.keySet()) {
                String lowerCase = str2.toLowerCase(Locale.ROOT);
                if (z || lowerCase.matches(str)) {
                    androidx.constraintlayout.widget.a aVar = this.e.get(str2);
                    if (aVar != null) {
                        aVar.a(view);
                    }
                }
            }
            return;
        }
        if (this.z.containsKey(str)) {
            method = this.z.get(str);
            if (method == null) {
                return;
            }
        } else {
            method = null;
        }
        if (method == null) {
            try {
                method = view.getClass().getMethod(str, new Class[0]);
                this.z.put(str, method);
            } catch (NoSuchMethodException unused) {
                this.z.put(str, null);
                Log.e("KeyTrigger", "Could not find method \"" + str + "\"on class " + view.getClass().getSimpleName() + " " + a.b.a.d(view));
                return;
            }
        }
        try {
            method.invoke(view, new Object[0]);
        } catch (Exception unused2) {
            StringBuilder j = b.b.a.a.a.j("Exception in call \"");
            j.append(this.g);
            j.append("\"on class ");
            j.append(view.getClass().getSimpleName());
            j.append(" ");
            j.append(a.b.a.d(view));
            Log.e("KeyTrigger", j.toString());
        }
    }

    private void x(RectF rectF, View view, boolean z) {
        rectF.top = view.getTop();
        rectF.bottom = view.getBottom();
        rectF.left = view.getLeft();
        rectF.right = view.getRight();
        if (z) {
            view.getMatrix().mapRect(rectF);
        }
    }

    @Override // androidx.constraintlayout.motion.widget.d
    public void a(HashMap<String, a.f.b.a.d> hashMap) {
    }

    @Override // androidx.constraintlayout.motion.widget.d
    /* renamed from: b */
    public d clone() {
        k kVar = new k();
        super.c(this);
        kVar.f = this.f;
        kVar.g = this.g;
        kVar.h = this.h;
        kVar.i = this.i;
        kVar.j = this.j;
        kVar.k = this.k;
        kVar.l = this.l;
        kVar.m = this.m;
        kVar.n = this.n;
        kVar.o = this.o;
        kVar.p = this.p;
        kVar.q = this.q;
        kVar.r = this.r;
        kVar.s = this.s;
        kVar.t = this.t;
        kVar.x = this.x;
        kVar.y = this.y;
        kVar.z = this.z;
        return kVar;
    }

    @Override // androidx.constraintlayout.motion.widget.d
    public void d(HashSet<String> hashSet) {
    }

    @Override // androidx.constraintlayout.motion.widget.d
    public void e(Context context, AttributeSet attributeSet) {
        a.a(this, context.obtainStyledAttributes(attributeSet, androidx.constraintlayout.widget.f.n));
    }

    /* JADX WARN: Removed duplicated region for block: B:68:0x008b  */
    /* JADX WARN: Removed duplicated region for block: B:75:0x00b4  */
    /* JADX WARN: Removed duplicated region for block: B:82:0x00cd  */
    /* JADX WARN: Removed duplicated region for block: B:87:0x009f  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void v(float r11, android.view.View r12) {
        /*
            Method dump skipped, instructions count: 345
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.motion.widget.k.v(float, android.view.View):void");
    }
}
