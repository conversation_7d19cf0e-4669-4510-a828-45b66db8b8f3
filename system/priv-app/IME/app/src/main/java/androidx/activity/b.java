package androidx.activity;

import java.util.Iterator;
import java.util.concurrent.CopyOnWriteArrayList;

/* loaded from: classes.dex */
public abstract class b {

    /* renamed from: a, reason: collision with root package name */
    private boolean f546a;

    /* renamed from: b, reason: collision with root package name */
    private CopyOnWriteArrayList<a> f547b = new CopyOnWriteArrayList<>();

    public b(boolean z) {
        this.f546a = z;
    }

    void a(a aVar) {
        this.f547b.add(aVar);
    }

    public abstract void b();

    public final boolean c() {
        return this.f546a;
    }

    public final void d() {
        Iterator<a> it = this.f547b.iterator();
        while (it.hasNext()) {
            it.next().cancel();
        }
    }

    void e(a aVar) {
        this.f547b.remove(aVar);
    }

    public final void f(boolean z) {
        this.f546a = z;
    }
}
