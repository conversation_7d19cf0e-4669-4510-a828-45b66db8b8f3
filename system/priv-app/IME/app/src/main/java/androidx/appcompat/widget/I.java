package androidx.appcompat.widget;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Parcelable;
import android.text.TextUtils;
import android.util.Log;
import android.util.SparseArray;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.Window;
import androidx.appcompat.view.menu.m;
import org.libpag.R;

/* loaded from: classes.dex */
public class I implements p {

    /* renamed from: a, reason: collision with root package name */
    Toolbar f733a;

    /* renamed from: b, reason: collision with root package name */
    private int f734b;

    /* renamed from: c, reason: collision with root package name */
    private View f735c;

    /* renamed from: d, reason: collision with root package name */
    private View f736d;
    private Drawable e;
    private Drawable f;
    private Drawable g;
    private boolean h;
    CharSequence i;
    private CharSequence j;
    private CharSequence k;
    Window.Callback l;
    boolean m;
    private C0098c n;
    private int o;
    private Drawable p;

    class a extends a.h.h.u {

        /* renamed from: a, reason: collision with root package name */
        private boolean f737a = false;

        /* renamed from: b, reason: collision with root package name */
        final /* synthetic */ int f738b;

        a(int i) {
            this.f738b = i;
        }

        @Override // a.h.h.t
        public void a(View view) {
            if (this.f737a) {
                return;
            }
            I.this.f733a.setVisibility(this.f738b);
        }

        @Override // a.h.h.u, a.h.h.t
        public void b(View view) {
            I.this.f733a.setVisibility(0);
        }

        @Override // a.h.h.u, a.h.h.t
        public void c(View view) {
            this.f737a = true;
        }
    }

    public I(Toolbar toolbar, boolean z) {
        Drawable drawable;
        this.o = 0;
        this.f733a = toolbar;
        this.i = toolbar.getTitle();
        this.j = toolbar.getSubtitle();
        this.h = this.i != null;
        this.g = toolbar.getNavigationIcon();
        G v = G.v(toolbar.getContext(), null, a.b.b.f0a, R.attr.actionBarStyle, 0);
        int i = 15;
        this.p = v.g(15);
        if (z) {
            CharSequence p = v.p(27);
            if (!TextUtils.isEmpty(p)) {
                this.h = true;
                this.i = p;
                if ((this.f734b & 8) != 0) {
                    this.f733a.setTitle(p);
                }
            }
            CharSequence p2 = v.p(25);
            if (!TextUtils.isEmpty(p2)) {
                this.j = p2;
                if ((this.f734b & 8) != 0) {
                    this.f733a.setSubtitle(p2);
                }
            }
            Drawable g = v.g(20);
            if (g != null) {
                this.f = g;
                v();
            }
            Drawable g2 = v.g(17);
            if (g2 != null) {
                this.e = g2;
                v();
            }
            if (this.g == null && (drawable = this.p) != null) {
                this.g = drawable;
                u();
            }
            s(v.k(10, 0));
            int n = v.n(9, 0);
            if (n != 0) {
                View inflate = LayoutInflater.from(this.f733a.getContext()).inflate(n, (ViewGroup) this.f733a, false);
                View view = this.f736d;
                if (view != null && (this.f734b & 16) != 0) {
                    this.f733a.removeView(view);
                }
                this.f736d = inflate;
                if (inflate != null && (this.f734b & 16) != 0) {
                    this.f733a.addView(inflate);
                }
                s(this.f734b | 16);
            }
            int m = v.m(13, 0);
            if (m > 0) {
                ViewGroup.LayoutParams layoutParams = this.f733a.getLayoutParams();
                layoutParams.height = m;
                this.f733a.setLayoutParams(layoutParams);
            }
            int e = v.e(7, -1);
            int e2 = v.e(3, -1);
            if (e >= 0 || e2 >= 0) {
                this.f733a.setContentInsetsRelative(Math.max(e, 0), Math.max(e2, 0));
            }
            int n2 = v.n(28, 0);
            if (n2 != 0) {
                Toolbar toolbar2 = this.f733a;
                toolbar2.setTitleTextAppearance(toolbar2.getContext(), n2);
            }
            int n3 = v.n(26, 0);
            if (n3 != 0) {
                Toolbar toolbar3 = this.f733a;
                toolbar3.setSubtitleTextAppearance(toolbar3.getContext(), n3);
            }
            int n4 = v.n(22, 0);
            if (n4 != 0) {
                this.f733a.setPopupTheme(n4);
            }
        } else {
            if (this.f733a.getNavigationIcon() != null) {
                this.p = this.f733a.getNavigationIcon();
            } else {
                i = 11;
            }
            this.f734b = i;
        }
        v.w();
        if (R.string.abc_action_bar_up_description != this.o) {
            this.o = R.string.abc_action_bar_up_description;
            if (TextUtils.isEmpty(this.f733a.getNavigationContentDescription())) {
                int i2 = this.o;
                this.k = i2 != 0 ? i().getString(i2) : null;
                t();
            }
        }
        this.k = this.f733a.getNavigationContentDescription();
        this.f733a.setNavigationOnClickListener(new H(this));
    }

    private void t() {
        if ((this.f734b & 4) != 0) {
            if (TextUtils.isEmpty(this.k)) {
                this.f733a.setNavigationContentDescription(this.o);
            } else {
                this.f733a.setNavigationContentDescription(this.k);
            }
        }
    }

    private void u() {
        if ((this.f734b & 4) == 0) {
            this.f733a.setNavigationIcon((Drawable) null);
            return;
        }
        Toolbar toolbar = this.f733a;
        Drawable drawable = this.g;
        if (drawable == null) {
            drawable = this.p;
        }
        toolbar.setNavigationIcon(drawable);
    }

    private void v() {
        Drawable drawable;
        int i = this.f734b;
        if ((i & 2) == 0) {
            drawable = null;
        } else if ((i & 1) == 0 || (drawable = this.f) == null) {
            drawable = this.e;
        }
        this.f733a.setLogo(drawable);
    }

    @Override // androidx.appcompat.widget.p
    public void a() {
        this.f733a.dismissPopupMenus();
    }

    @Override // androidx.appcompat.widget.p
    public int b() {
        return this.f734b;
    }

    @Override // androidx.appcompat.widget.p
    public void c(int i) {
        this.f733a.setVisibility(i);
    }

    @Override // androidx.appcompat.widget.p
    public boolean canShowOverflowMenu() {
        return this.f733a.canShowOverflowMenu();
    }

    @Override // androidx.appcompat.widget.p
    public void collapseActionView() {
        this.f733a.collapseActionView();
    }

    @Override // androidx.appcompat.widget.p
    public void d(SparseArray<Parcelable> sparseArray) {
        this.f733a.saveHierarchyState(sparseArray);
    }

    @Override // androidx.appcompat.widget.p
    public void e(int i) {
        this.f = i != 0 ? a.b.c.a.a.a(i(), i) : null;
        v();
    }

    @Override // androidx.appcompat.widget.p
    public void f(ScrollingTabContainerView scrollingTabContainerView) {
        View view = this.f735c;
        if (view != null) {
            ViewParent parent = view.getParent();
            Toolbar toolbar = this.f733a;
            if (parent == toolbar) {
                toolbar.removeView(this.f735c);
            }
        }
        this.f735c = null;
    }

    @Override // androidx.appcompat.widget.p
    public ViewGroup g() {
        return this.f733a;
    }

    @Override // androidx.appcompat.widget.p
    public CharSequence getTitle() {
        return this.f733a.getTitle();
    }

    @Override // androidx.appcompat.widget.p
    public void h(boolean z) {
    }

    @Override // androidx.appcompat.widget.p
    public boolean hideOverflowMenu() {
        return this.f733a.hideOverflowMenu();
    }

    @Override // androidx.appcompat.widget.p
    public Context i() {
        return this.f733a.getContext();
    }

    @Override // androidx.appcompat.widget.p
    public boolean isOverflowMenuShowPending() {
        return this.f733a.isOverflowMenuShowPending();
    }

    @Override // androidx.appcompat.widget.p
    public boolean isOverflowMenuShowing() {
        return this.f733a.isOverflowMenuShowing();
    }

    @Override // androidx.appcompat.widget.p
    public int j() {
        return 0;
    }

    @Override // androidx.appcompat.widget.p
    public void k(SparseArray<Parcelable> sparseArray) {
        this.f733a.restoreHierarchyState(sparseArray);
    }

    @Override // androidx.appcompat.widget.p
    public a.h.h.s l(int i, long j) {
        a.h.h.s c2 = a.h.h.q.c(this.f733a);
        c2.a(i == 0 ? 1.0f : 0.0f);
        c2.d(j);
        c2.f(new a(i));
        return c2;
    }

    @Override // androidx.appcompat.widget.p
    public void m() {
        Log.i("ToolbarWidgetWrapper", "Progress display unsupported");
    }

    @Override // androidx.appcompat.widget.p
    public boolean n() {
        return this.e != null;
    }

    @Override // androidx.appcompat.widget.p
    public boolean o() {
        return this.f733a.hasExpandedActionView();
    }

    @Override // androidx.appcompat.widget.p
    public boolean p() {
        return this.f != null;
    }

    @Override // androidx.appcompat.widget.p
    public void q() {
        Log.i("ToolbarWidgetWrapper", "Progress display unsupported");
    }

    @Override // androidx.appcompat.widget.p
    public void r(boolean z) {
        this.f733a.setCollapsible(z);
    }

    @Override // androidx.appcompat.widget.p
    public void s(int i) {
        View view;
        CharSequence charSequence;
        Toolbar toolbar;
        int i2 = this.f734b ^ i;
        this.f734b = i;
        if (i2 != 0) {
            if ((i2 & 4) != 0) {
                if ((i & 4) != 0) {
                    t();
                }
                u();
            }
            if ((i2 & 3) != 0) {
                v();
            }
            if ((i2 & 8) != 0) {
                if ((i & 8) != 0) {
                    this.f733a.setTitle(this.i);
                    toolbar = this.f733a;
                    charSequence = this.j;
                } else {
                    charSequence = null;
                    this.f733a.setTitle((CharSequence) null);
                    toolbar = this.f733a;
                }
                toolbar.setSubtitle(charSequence);
            }
            if ((i2 & 16) == 0 || (view = this.f736d) == null) {
                return;
            }
            int i3 = i & 16;
            Toolbar toolbar2 = this.f733a;
            if (i3 != 0) {
                toolbar2.addView(view);
            } else {
                toolbar2.removeView(view);
            }
        }
    }

    @Override // androidx.appcompat.widget.p
    public void setIcon(int i) {
        this.e = i != 0 ? a.b.c.a.a.a(i(), i) : null;
        v();
    }

    @Override // androidx.appcompat.widget.p
    public void setIcon(Drawable drawable) {
        this.e = drawable;
        v();
    }

    @Override // androidx.appcompat.widget.p
    public void setMenu(Menu menu, m.a aVar) {
        if (this.n == null) {
            C0098c c0098c = new C0098c(this.f733a.getContext());
            this.n = c0098c;
            c0098c.r(R.id.action_menu_presenter);
        }
        this.n.l(aVar);
        this.f733a.setMenu((androidx.appcompat.view.menu.g) menu, this.n);
    }

    @Override // androidx.appcompat.widget.p
    public void setMenuPrepared() {
        this.m = true;
    }

    @Override // androidx.appcompat.widget.p
    public void setWindowCallback(Window.Callback callback) {
        this.l = callback;
    }

    @Override // androidx.appcompat.widget.p
    public void setWindowTitle(CharSequence charSequence) {
        if (this.h) {
            return;
        }
        this.i = charSequence;
        if ((this.f734b & 8) != 0) {
            this.f733a.setTitle(charSequence);
        }
    }

    @Override // androidx.appcompat.widget.p
    public boolean showOverflowMenu() {
        return this.f733a.showOverflowMenu();
    }
}
