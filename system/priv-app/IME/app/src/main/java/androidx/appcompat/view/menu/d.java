package androidx.appcompat.view.menu;

import android.content.Context;
import android.content.res.Resources;
import android.os.Handler;
import android.os.Parcelable;
import android.os.SystemClock;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.HeaderViewListAdapter;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.PopupWindow;
import androidx.appcompat.view.menu.m;
import androidx.appcompat.widget.MenuPopupWindow;
import androidx.appcompat.widget.w;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import org.libpag.R;

/* loaded from: classes.dex */
final class d extends k implements m, View.OnKeyListener, PopupWindow.OnDismissListener {
    boolean A;

    /* renamed from: b, reason: collision with root package name */
    private final Context f633b;

    /* renamed from: c, reason: collision with root package name */
    private final int f634c;

    /* renamed from: d, reason: collision with root package name */
    private final int f635d;
    private final int e;
    private final boolean f;
    final Handler g;
    private View o;
    View p;
    private int q;
    private boolean r;
    private boolean s;
    private int t;
    private int u;
    private boolean w;
    private m.a x;
    ViewTreeObserver y;
    private PopupWindow.OnDismissListener z;
    private final List<g> h = new ArrayList();
    final List<C0026d> i = new ArrayList();
    final ViewTreeObserver.OnGlobalLayoutListener j = new a();
    private final View.OnAttachStateChangeListener k = new b();
    private final w l = new c();
    private int m = 0;
    private int n = 0;
    private boolean v = false;

    class a implements ViewTreeObserver.OnGlobalLayoutListener {
        a() {
        }

        @Override // android.view.ViewTreeObserver.OnGlobalLayoutListener
        public void onGlobalLayout() {
            if (!d.this.b() || d.this.i.size() <= 0 || d.this.i.get(0).f643a.w()) {
                return;
            }
            View view = d.this.p;
            if (view == null || !view.isShown()) {
                d.this.dismiss();
                return;
            }
            Iterator<C0026d> it = d.this.i.iterator();
            while (it.hasNext()) {
                it.next().f643a.f();
            }
        }
    }

    class b implements View.OnAttachStateChangeListener {
        b() {
        }

        @Override // android.view.View.OnAttachStateChangeListener
        public void onViewAttachedToWindow(View view) {
        }

        @Override // android.view.View.OnAttachStateChangeListener
        public void onViewDetachedFromWindow(View view) {
            ViewTreeObserver viewTreeObserver = d.this.y;
            if (viewTreeObserver != null) {
                if (!viewTreeObserver.isAlive()) {
                    d.this.y = view.getViewTreeObserver();
                }
                d dVar = d.this;
                dVar.y.removeGlobalOnLayoutListener(dVar.j);
            }
            view.removeOnAttachStateChangeListener(this);
        }
    }

    class c implements w {

        class a implements Runnable {

            /* renamed from: a, reason: collision with root package name */
            final /* synthetic */ C0026d f639a;

            /* renamed from: b, reason: collision with root package name */
            final /* synthetic */ MenuItem f640b;

            /* renamed from: c, reason: collision with root package name */
            final /* synthetic */ g f641c;

            a(C0026d c0026d, MenuItem menuItem, g gVar) {
                this.f639a = c0026d;
                this.f640b = menuItem;
                this.f641c = gVar;
            }

            @Override // java.lang.Runnable
            public void run() {
                C0026d c0026d = this.f639a;
                if (c0026d != null) {
                    d.this.A = true;
                    c0026d.f644b.e(false);
                    d.this.A = false;
                }
                if (this.f640b.isEnabled() && this.f640b.hasSubMenu()) {
                    this.f641c.y(this.f640b, 4);
                }
            }
        }

        c() {
        }

        @Override // androidx.appcompat.widget.w
        public void e(g gVar, MenuItem menuItem) {
            d.this.g.removeCallbacksAndMessages(null);
            int size = d.this.i.size();
            int i = 0;
            while (true) {
                if (i >= size) {
                    i = -1;
                    break;
                } else if (gVar == d.this.i.get(i).f644b) {
                    break;
                } else {
                    i++;
                }
            }
            if (i == -1) {
                return;
            }
            int i2 = i + 1;
            d.this.g.postAtTime(new a(i2 < d.this.i.size() ? d.this.i.get(i2) : null, menuItem, gVar), gVar, SystemClock.uptimeMillis() + 200);
        }

        @Override // androidx.appcompat.widget.w
        public void h(g gVar, MenuItem menuItem) {
            d.this.g.removeCallbacksAndMessages(gVar);
        }
    }

    /* renamed from: androidx.appcompat.view.menu.d$d, reason: collision with other inner class name */
    private static class C0026d {

        /* renamed from: a, reason: collision with root package name */
        public final MenuPopupWindow f643a;

        /* renamed from: b, reason: collision with root package name */
        public final g f644b;

        /* renamed from: c, reason: collision with root package name */
        public final int f645c;

        public C0026d(MenuPopupWindow menuPopupWindow, g gVar, int i) {
            this.f643a = menuPopupWindow;
            this.f644b = gVar;
            this.f645c = i;
        }

        public ListView a() {
            return this.f643a.k();
        }
    }

    public d(Context context, View view, int i, int i2, boolean z) {
        this.f633b = context;
        this.o = view;
        this.f635d = i;
        this.e = i2;
        this.f = z;
        int i3 = a.h.h.q.e;
        this.q = view.getLayoutDirection() != 1 ? 1 : 0;
        Resources resources = context.getResources();
        this.f634c = Math.max(resources.getDisplayMetrics().widthPixels / 2, resources.getDimensionPixelSize(R.dimen.abc_config_prefDialogWidth));
        this.g = new Handler();
    }

    /* JADX WARN: Code restructure failed: missing block: B:21:0x0124, code lost:
    
        if (((r8.getWidth() + r10[0]) + r4) > r12.right) goto L53;
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x012c, code lost:
    
        r11 = 1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:48:0x012e, code lost:
    
        r11 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:50:0x012a, code lost:
    
        if ((r10[0] - r4) < 0) goto L52;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private void B(androidx.appcompat.view.menu.g r17) {
        /*
            Method dump skipped, instructions count: 440
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.view.menu.d.B(androidx.appcompat.view.menu.g):void");
    }

    @Override // androidx.appcompat.view.menu.m
    public void a(g gVar, boolean z) {
        int i;
        int size = this.i.size();
        int i2 = 0;
        while (true) {
            if (i2 >= size) {
                i2 = -1;
                break;
            } else if (gVar == this.i.get(i2).f644b) {
                break;
            } else {
                i2++;
            }
        }
        if (i2 < 0) {
            return;
        }
        int i3 = i2 + 1;
        if (i3 < this.i.size()) {
            this.i.get(i3).f644b.e(false);
        }
        C0026d remove = this.i.remove(i2);
        remove.f644b.B(this);
        if (this.A) {
            remove.f643a.J(null);
            remove.f643a.y(0);
        }
        remove.f643a.dismiss();
        int size2 = this.i.size();
        if (size2 > 0) {
            i = this.i.get(size2 - 1).f645c;
        } else {
            View view = this.o;
            int i4 = a.h.h.q.e;
            i = view.getLayoutDirection() == 1 ? 0 : 1;
        }
        this.q = i;
        if (size2 != 0) {
            if (z) {
                this.i.get(0).f644b.e(false);
                return;
            }
            return;
        }
        dismiss();
        m.a aVar = this.x;
        if (aVar != null) {
            aVar.a(gVar, true);
        }
        ViewTreeObserver viewTreeObserver = this.y;
        if (viewTreeObserver != null) {
            if (viewTreeObserver.isAlive()) {
                this.y.removeGlobalOnLayoutListener(this.j);
            }
            this.y = null;
        }
        this.p.removeOnAttachStateChangeListener(this.k);
        this.z.onDismiss();
    }

    @Override // androidx.appcompat.view.menu.p
    public boolean b() {
        return this.i.size() > 0 && this.i.get(0).f643a.b();
    }

    @Override // androidx.appcompat.view.menu.m
    public boolean d() {
        return false;
    }

    @Override // androidx.appcompat.view.menu.p
    public void dismiss() {
        int size = this.i.size();
        if (size > 0) {
            C0026d[] c0026dArr = (C0026d[]) this.i.toArray(new C0026d[size]);
            for (int i = size - 1; i >= 0; i--) {
                C0026d c0026d = c0026dArr[i];
                if (c0026d.f643a.b()) {
                    c0026d.f643a.dismiss();
                }
            }
        }
    }

    @Override // androidx.appcompat.view.menu.m
    public Parcelable e() {
        return null;
    }

    @Override // androidx.appcompat.view.menu.p
    public void f() {
        if (b()) {
            return;
        }
        Iterator<g> it = this.h.iterator();
        while (it.hasNext()) {
            B(it.next());
        }
        this.h.clear();
        View view = this.o;
        this.p = view;
        if (view != null) {
            boolean z = this.y == null;
            ViewTreeObserver viewTreeObserver = view.getViewTreeObserver();
            this.y = viewTreeObserver;
            if (z) {
                viewTreeObserver.addOnGlobalLayoutListener(this.j);
            }
            this.p.addOnAttachStateChangeListener(this.k);
        }
    }

    @Override // androidx.appcompat.view.menu.m
    public void h(Parcelable parcelable) {
    }

    @Override // androidx.appcompat.view.menu.p
    public ListView k() {
        if (this.i.isEmpty()) {
            return null;
        }
        return this.i.get(r1.size() - 1).a();
    }

    @Override // androidx.appcompat.view.menu.m
    public void l(m.a aVar) {
        this.x = aVar;
    }

    @Override // androidx.appcompat.view.menu.m
    public boolean m(r rVar) {
        for (C0026d c0026d : this.i) {
            if (rVar == c0026d.f644b) {
                c0026d.a().requestFocus();
                return true;
            }
        }
        if (!rVar.hasVisibleItems()) {
            return false;
        }
        rVar.c(this, this.f633b);
        if (b()) {
            B(rVar);
        } else {
            this.h.add(rVar);
        }
        m.a aVar = this.x;
        if (aVar != null) {
            aVar.b(rVar);
        }
        return true;
    }

    @Override // androidx.appcompat.view.menu.m
    public void n(boolean z) {
        Iterator<C0026d> it = this.i.iterator();
        while (it.hasNext()) {
            ListAdapter adapter = it.next().a().getAdapter();
            if (adapter instanceof HeaderViewListAdapter) {
                adapter = ((HeaderViewListAdapter) adapter).getWrappedAdapter();
            }
            ((f) adapter).notifyDataSetChanged();
        }
    }

    @Override // androidx.appcompat.view.menu.k
    public void o(g gVar) {
        gVar.c(this, this.f633b);
        if (b()) {
            B(gVar);
        } else {
            this.h.add(gVar);
        }
    }

    @Override // android.widget.PopupWindow.OnDismissListener
    public void onDismiss() {
        C0026d c0026d;
        int size = this.i.size();
        int i = 0;
        while (true) {
            if (i >= size) {
                c0026d = null;
                break;
            }
            c0026d = this.i.get(i);
            if (!c0026d.f643a.b()) {
                break;
            } else {
                i++;
            }
        }
        if (c0026d != null) {
            c0026d.f644b.e(false);
        }
    }

    @Override // android.view.View.OnKeyListener
    public boolean onKey(View view, int i, KeyEvent keyEvent) {
        if (keyEvent.getAction() != 1 || i != 82) {
            return false;
        }
        dismiss();
        return true;
    }

    @Override // androidx.appcompat.view.menu.k
    protected boolean p() {
        return false;
    }

    @Override // androidx.appcompat.view.menu.k
    public void s(View view) {
        if (this.o != view) {
            this.o = view;
            int i = this.m;
            int i2 = a.h.h.q.e;
            this.n = Gravity.getAbsoluteGravity(i, view.getLayoutDirection());
        }
    }

    @Override // androidx.appcompat.view.menu.k
    public void u(boolean z) {
        this.v = z;
    }

    @Override // androidx.appcompat.view.menu.k
    public void v(int i) {
        if (this.m != i) {
            this.m = i;
            View view = this.o;
            int i2 = a.h.h.q.e;
            this.n = Gravity.getAbsoluteGravity(i, view.getLayoutDirection());
        }
    }

    @Override // androidx.appcompat.view.menu.k
    public void w(int i) {
        this.r = true;
        this.t = i;
    }

    @Override // androidx.appcompat.view.menu.k
    public void x(PopupWindow.OnDismissListener onDismissListener) {
        this.z = onDismissListener;
    }

    @Override // androidx.appcompat.view.menu.k
    public void y(boolean z) {
        this.w = z;
    }

    @Override // androidx.appcompat.view.menu.k
    public void z(int i) {
        this.s = true;
        this.u = i;
    }
}
