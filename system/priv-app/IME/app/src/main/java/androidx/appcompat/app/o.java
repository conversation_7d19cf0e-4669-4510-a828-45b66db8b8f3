package androidx.appcompat.app;

import android.content.Context;
import android.location.Location;
import android.location.LocationManager;
import android.util.Log;
import java.util.Calendar;

/* loaded from: classes.dex */
class o {

    /* renamed from: d, reason: collision with root package name */
    private static o f607d;

    /* renamed from: a, reason: collision with root package name */
    private final Context f608a;

    /* renamed from: b, reason: collision with root package name */
    private final LocationManager f609b;

    /* renamed from: c, reason: collision with root package name */
    private final a f610c = new a();

    private static class a {

        /* renamed from: a, reason: collision with root package name */
        boolean f611a;

        /* renamed from: b, reason: collision with root package name */
        long f612b;

        a() {
        }
    }

    o(Context context, LocationManager locationManager) {
        this.f608a = context;
        this.f609b = locationManager;
    }

    static o a(Context context) {
        if (f607d == null) {
            Context applicationContext = context.getApplicationContext();
            f607d = new o(applicationContext, (LocationManager) applicationContext.getSystemService("location"));
        }
        return f607d;
    }

    private Location b(String str) {
        try {
            if (this.f609b.isProviderEnabled(str)) {
                return this.f609b.getLastKnownLocation(str);
            }
            return null;
        } catch (Exception e) {
            Log.d("TwilightManager", "Failed to get last known location", e);
            return null;
        }
    }

    boolean c() {
        long j;
        a aVar = this.f610c;
        if (aVar.f612b > System.currentTimeMillis()) {
            return aVar.f611a;
        }
        Location b2 = androidx.core.app.b.d(this.f608a, "android.permission.ACCESS_COARSE_LOCATION") == 0 ? b("network") : null;
        Location b3 = androidx.core.app.b.d(this.f608a, "android.permission.ACCESS_FINE_LOCATION") == 0 ? b("gps") : null;
        if (b3 == null || b2 == null ? b3 != null : b3.getTime() > b2.getTime()) {
            b2 = b3;
        }
        if (b2 == null) {
            Log.i("TwilightManager", "Could not get last known location. This is probably because the app does not have any location permissions. Falling back to hardcoded sunrise/sunset values.");
            int i = Calendar.getInstance().get(11);
            return i < 6 || i >= 22;
        }
        a aVar2 = this.f610c;
        long currentTimeMillis = System.currentTimeMillis();
        n b4 = n.b();
        b4.a(currentTimeMillis - 86400000, b2.getLatitude(), b2.getLongitude());
        b4.a(currentTimeMillis, b2.getLatitude(), b2.getLongitude());
        boolean z = b4.f606c == 1;
        long j2 = b4.f605b;
        long j3 = b4.f604a;
        b4.a(currentTimeMillis + 86400000, b2.getLatitude(), b2.getLongitude());
        long j4 = b4.f605b;
        if (j2 == -1 || j3 == -1) {
            j = 43200000 + currentTimeMillis;
        } else {
            j = (currentTimeMillis > j3 ? j4 + 0 : currentTimeMillis > j2 ? j3 + 0 : j2 + 0) + 60000;
        }
        aVar2.f611a = z;
        aVar2.f612b = j;
        return aVar.f611a;
    }
}
