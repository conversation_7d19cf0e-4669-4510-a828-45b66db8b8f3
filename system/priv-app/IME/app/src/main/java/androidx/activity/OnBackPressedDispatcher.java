package androidx.activity;

import androidx.lifecycle.d;
import androidx.lifecycle.e;
import androidx.lifecycle.g;
import java.util.ArrayDeque;
import java.util.Iterator;

/* loaded from: classes.dex */
public final class OnBackPressedDispatcher {

    /* renamed from: a, reason: collision with root package name */
    private final Runnable f538a;

    /* renamed from: b, reason: collision with root package name */
    final ArrayDeque<b> f539b = new ArrayDeque<>();

    private class LifecycleOnBackPressedCancellable implements e, androidx.activity.a {

        /* renamed from: a, reason: collision with root package name */
        private final d f540a;

        /* renamed from: b, reason: collision with root package name */
        private final b f541b;

        /* renamed from: c, reason: collision with root package name */
        private androidx.activity.a f542c;

        LifecycleOnBackPressedCancellable(d dVar, b bVar) {
            this.f540a = dVar;
            this.f541b = bVar;
            dVar.a(this);
        }

        @Override // androidx.activity.a
        public void cancel() {
            this.f540a.c(this);
            this.f541b.e(this);
            androidx.activity.a aVar = this.f542c;
            if (aVar != null) {
                aVar.cancel();
                this.f542c = null;
            }
        }

        @Override // androidx.lifecycle.e
        public void d(g gVar, d.a aVar) {
            if (aVar == d.a.ON_START) {
                OnBackPressedDispatcher onBackPressedDispatcher = OnBackPressedDispatcher.this;
                b bVar = this.f541b;
                onBackPressedDispatcher.f539b.add(bVar);
                a aVar2 = onBackPressedDispatcher.new a(bVar);
                bVar.a(aVar2);
                this.f542c = aVar2;
                return;
            }
            if (aVar != d.a.ON_STOP) {
                if (aVar == d.a.ON_DESTROY) {
                    cancel();
                }
            } else {
                androidx.activity.a aVar3 = this.f542c;
                if (aVar3 != null) {
                    aVar3.cancel();
                }
            }
        }
    }

    private class a implements androidx.activity.a {

        /* renamed from: a, reason: collision with root package name */
        private final b f544a;

        a(b bVar) {
            this.f544a = bVar;
        }

        @Override // androidx.activity.a
        public void cancel() {
            OnBackPressedDispatcher.this.f539b.remove(this.f544a);
            this.f544a.e(this);
        }
    }

    public OnBackPressedDispatcher(Runnable runnable) {
        this.f538a = runnable;
    }

    public void a(g gVar, b bVar) {
        d a2 = gVar.a();
        if (a2.b() == d.b.DESTROYED) {
            return;
        }
        bVar.a(new LifecycleOnBackPressedCancellable(a2, bVar));
    }

    public void b() {
        Iterator<b> descendingIterator = this.f539b.descendingIterator();
        while (descendingIterator.hasNext()) {
            b next = descendingIterator.next();
            if (next.c()) {
                next.b();
                return;
            }
        }
        Runnable runnable = this.f538a;
        if (runnable != null) {
            runnable.run();
        }
    }
}
