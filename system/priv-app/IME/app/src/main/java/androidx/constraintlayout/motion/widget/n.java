package androidx.constraintlayout.motion.widget;

import a.f.b.a.c;
import a.f.b.a.d;
import a.f.b.a.f;
import android.content.Context;
import android.graphics.Rect;
import android.util.Log;
import android.util.SparseArray;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.AnimationUtils;
import android.view.animation.BounceInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.view.animation.Interpolator;
import android.view.animation.OvershootInterpolator;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.c;
import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Objects;

/* loaded from: classes.dex */
public class n {
    private HashMap<String, a.f.b.a.c> A;
    private k[] B;

    /* renamed from: b, reason: collision with root package name */
    View f914b;

    /* renamed from: c, reason: collision with root package name */
    int f915c;
    private a.f.a.i.a.b[] j;
    private a.f.a.i.a.b k;
    private int[] o;
    private double[] p;
    private double[] q;
    private String[] r;
    private int[] s;
    private HashMap<String, a.f.b.a.f> y;
    private HashMap<String, a.f.b.a.d> z;

    /* renamed from: a, reason: collision with root package name */
    Rect f913a = new Rect();

    /* renamed from: d, reason: collision with root package name */
    boolean f916d = false;
    private int e = -1;
    private p f = new p();
    private p g = new p();
    private l h = new l();
    private l i = new l();
    float l = Float.NaN;
    float m = 0.0f;
    float n = 1.0f;
    private int t = 4;
    private float[] u = new float[4];
    private ArrayList<p> v = new ArrayList<>();
    private float[] w = new float[1];
    private ArrayList<d> x = new ArrayList<>();
    private int C = -1;
    private int D = -1;
    private View E = null;
    private int F = -1;
    private float G = Float.NaN;
    private Interpolator H = null;
    private boolean I = false;

    n(View view) {
        this.f914b = view;
        this.f915c = view.getId();
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        if (layoutParams instanceof ConstraintLayout.a) {
            String str = ((ConstraintLayout.a) layoutParams).Y;
        }
    }

    private float g(float f, float[] fArr) {
        float f2 = 0.0f;
        if (fArr != null) {
            fArr[0] = 1.0f;
        } else {
            float f3 = this.n;
            if (f3 != 1.0d) {
                float f4 = this.m;
                if (f < f4) {
                    f = 0.0f;
                }
                if (f > f4 && f < 1.0d) {
                    f = Math.min((f - f4) * f3, 1.0f);
                }
            }
        }
        a.f.a.i.a.c cVar = this.f.f917a;
        float f5 = Float.NaN;
        Iterator<p> it = this.v.iterator();
        while (it.hasNext()) {
            p next = it.next();
            a.f.a.i.a.c cVar2 = next.f917a;
            if (cVar2 != null) {
                float f6 = next.f919c;
                if (f6 < f) {
                    cVar = cVar2;
                    f2 = f6;
                } else if (Float.isNaN(f5)) {
                    f5 = next.f919c;
                }
            }
        }
        if (cVar == null) {
            return f;
        }
        float f7 = (Float.isNaN(f5) ? 1.0f : f5) - f2;
        double d2 = (f - f2) / f7;
        float a2 = f2 + (((float) cVar.a(d2)) * f7);
        if (fArr != null) {
            fArr[0] = (float) cVar.b(d2);
        }
        return a2;
    }

    private void s(p pVar) {
        pVar.e((int) this.f914b.getX(), (int) this.f914b.getY(), this.f914b.getWidth(), this.f914b.getHeight());
    }

    public void A(int i, int i2, long j) {
        ArrayList arrayList;
        String[] strArr;
        Iterator<String> it;
        char c2;
        Class<double> cls;
        int i3;
        String str;
        double[] dArr;
        double[][] dArr2;
        int[] iArr;
        androidx.constraintlayout.widget.a aVar;
        a.f.b.a.f g;
        androidx.constraintlayout.widget.a aVar2;
        Integer num;
        a.f.b.a.d e;
        androidx.constraintlayout.widget.a aVar3;
        Class<double> cls2 = double.class;
        new HashSet();
        HashSet<String> hashSet = new HashSet<>();
        HashSet<String> hashSet2 = new HashSet<>();
        HashSet<String> hashSet3 = new HashSet<>();
        HashMap<String, Integer> hashMap = new HashMap<>();
        int i4 = this.C;
        if (i4 != -1) {
            this.f.j = i4;
        }
        this.h.d(this.i, hashSet2);
        ArrayList<d> arrayList2 = this.x;
        if (arrayList2 != null) {
            Iterator<d> it2 = arrayList2.iterator();
            arrayList = null;
            while (it2.hasNext()) {
                d next = it2.next();
                if (next instanceof h) {
                    h hVar = (h) next;
                    p pVar = new p(i, i2, hVar, this.f, this.g);
                    if (Collections.binarySearch(this.v, pVar) == 0) {
                        StringBuilder j2 = b.b.a.a.a.j(" KeyPath position \"");
                        j2.append(pVar.f920d);
                        j2.append("\" outside of range");
                        Log.e("MotionController", j2.toString());
                    }
                    this.v.add((-r11) - 1, pVar);
                    int i5 = hVar.f;
                    if (i5 != -1) {
                        this.e = i5;
                    }
                } else if (next instanceof f) {
                    next.d(hashSet3);
                } else if (next instanceof j) {
                    next.d(hashSet);
                } else if (next instanceof k) {
                    if (arrayList == null) {
                        arrayList = new ArrayList();
                    }
                    arrayList.add((k) next);
                } else {
                    next.g(hashMap);
                    next.d(hashSet2);
                }
            }
        } else {
            arrayList = null;
        }
        int i6 = 0;
        if (arrayList != null) {
            this.B = (k[]) arrayList.toArray(new k[0]);
        }
        char c3 = 1;
        if (!hashSet2.isEmpty()) {
            this.z = new HashMap<>();
            Iterator<String> it3 = hashSet2.iterator();
            while (it3.hasNext()) {
                String next2 = it3.next();
                if (next2.startsWith("CUSTOM,")) {
                    SparseArray sparseArray = new SparseArray();
                    String str2 = next2.split(",")[c3];
                    Iterator<d> it4 = this.x.iterator();
                    while (it4.hasNext()) {
                        d next3 = it4.next();
                        HashMap<String, androidx.constraintlayout.widget.a> hashMap2 = next3.e;
                        if (hashMap2 != null && (aVar3 = hashMap2.get(str2)) != null) {
                            sparseArray.append(next3.f897a, aVar3);
                        }
                    }
                    e = new d.b(next2, sparseArray);
                } else {
                    e = a.f.b.a.d.e(next2);
                }
                if (e != null) {
                    e.c(next2);
                    this.z.put(next2, e);
                }
                c3 = 1;
            }
            ArrayList<d> arrayList3 = this.x;
            if (arrayList3 != null) {
                Iterator<d> it5 = arrayList3.iterator();
                while (it5.hasNext()) {
                    d next4 = it5.next();
                    if (next4 instanceof e) {
                        next4.a(this.z);
                    }
                }
            }
            this.h.a(this.z, 0);
            this.i.a(this.z, 100);
            for (String str3 : this.z.keySet()) {
                int intValue = (!hashMap.containsKey(str3) || (num = hashMap.get(str3)) == null) ? 0 : num.intValue();
                a.f.b.a.d dVar = this.z.get(str3);
                if (dVar != null) {
                    dVar.d(intValue);
                }
            }
        }
        if (!hashSet.isEmpty()) {
            if (this.y == null) {
                this.y = new HashMap<>();
            }
            Iterator<String> it6 = hashSet.iterator();
            while (it6.hasNext()) {
                String next5 = it6.next();
                if (!this.y.containsKey(next5)) {
                    if (next5.startsWith("CUSTOM,")) {
                        SparseArray sparseArray2 = new SparseArray();
                        String str4 = next5.split(",")[1];
                        Iterator<d> it7 = this.x.iterator();
                        while (it7.hasNext()) {
                            d next6 = it7.next();
                            HashMap<String, androidx.constraintlayout.widget.a> hashMap3 = next6.e;
                            if (hashMap3 != null && (aVar2 = hashMap3.get(str4)) != null) {
                                sparseArray2.append(next6.f897a, aVar2);
                            }
                        }
                        g = new f.b(next5, sparseArray2);
                    } else {
                        g = a.f.b.a.f.g(next5, j);
                    }
                    if (g != null) {
                        g.d(next5);
                        this.y.put(next5, g);
                    }
                }
            }
            ArrayList<d> arrayList4 = this.x;
            if (arrayList4 != null) {
                Iterator<d> it8 = arrayList4.iterator();
                while (it8.hasNext()) {
                    d next7 = it8.next();
                    if (next7 instanceof j) {
                        ((j) next7).Q(this.y);
                    }
                }
            }
            for (String str5 : this.y.keySet()) {
                this.y.get(str5).e(hashMap.containsKey(str5) ? hashMap.get(str5).intValue() : 0);
            }
        }
        char c4 = 2;
        int size = this.v.size() + 2;
        p[] pVarArr = new p[size];
        pVarArr[0] = this.f;
        pVarArr[size - 1] = this.g;
        if (this.v.size() > 0 && this.e == -1) {
            this.e = 0;
        }
        Iterator<p> it9 = this.v.iterator();
        int i7 = 1;
        while (it9.hasNext()) {
            pVarArr[i7] = it9.next();
            i7++;
        }
        HashSet hashSet4 = new HashSet();
        for (String str6 : this.g.n.keySet()) {
            if (this.f.n.containsKey(str6)) {
                if (!hashSet2.contains("CUSTOM," + str6)) {
                    hashSet4.add(str6);
                }
            }
        }
        String[] strArr2 = (String[]) hashSet4.toArray(new String[0]);
        this.r = strArr2;
        this.s = new int[strArr2.length];
        int i8 = 0;
        while (true) {
            strArr = this.r;
            if (i8 >= strArr.length) {
                break;
            }
            String str7 = strArr[i8];
            this.s[i8] = 0;
            int i9 = 0;
            while (true) {
                if (i9 >= size) {
                    break;
                }
                if (pVarArr[i9].n.containsKey(str7) && (aVar = pVarArr[i9].n.get(str7)) != null) {
                    int[] iArr2 = this.s;
                    iArr2[i8] = aVar.g() + iArr2[i8];
                    break;
                }
                i9++;
            }
            i8++;
        }
        boolean z = pVarArr[0].j != -1;
        int length = 18 + strArr.length;
        boolean[] zArr = new boolean[length];
        for (int i10 = 1; i10 < size; i10++) {
            pVarArr[i10].c(pVarArr[i10 - 1], zArr, z);
        }
        int i11 = 0;
        for (int i12 = 1; i12 < length; i12++) {
            if (zArr[i12]) {
                i11++;
            }
        }
        this.o = new int[i11];
        int max = Math.max(2, i11);
        this.p = new double[max];
        this.q = new double[max];
        int i13 = 0;
        for (int i14 = 1; i14 < length; i14++) {
            if (zArr[i14]) {
                this.o[i13] = i14;
                i13++;
            }
        }
        double[][] dArr3 = (double[][]) Array.newInstance((Class<?>) cls2, size, this.o.length);
        double[] dArr4 = new double[size];
        int i15 = 0;
        while (i6 < size) {
            p pVar2 = pVarArr[i6];
            double[] dArr5 = dArr3[i6];
            int[] iArr3 = this.o;
            int i16 = 6;
            float[] fArr = new float[6];
            fArr[i15] = pVar2.f920d;
            fArr[1] = pVar2.e;
            fArr[c4] = pVar2.f;
            fArr[3] = pVar2.g;
            fArr[4] = pVar2.h;
            fArr[5] = pVar2.i;
            int i17 = i15;
            while (i15 < iArr3.length) {
                if (iArr3[i15] < i16) {
                    iArr = iArr3;
                    dArr5[i17] = fArr[iArr3[i15]];
                    i17++;
                } else {
                    iArr = iArr3;
                }
                i15++;
                i16 = 6;
                iArr3 = iArr;
            }
            dArr4[i6] = pVarArr[i6].f919c;
            i6++;
            c4 = 2;
            i15 = 0;
        }
        int i18 = 0;
        while (true) {
            int[] iArr4 = this.o;
            if (i18 >= iArr4.length) {
                break;
            }
            if (iArr4[i18] < p.r.length) {
                String str8 = p.r[this.o[i18]] + " [";
                for (int i19 = 0; i19 < size; i19++) {
                    StringBuilder j3 = b.b.a.a.a.j(str8);
                    j3.append(dArr3[i19][i18]);
                    str8 = j3.toString();
                }
            }
            i18++;
        }
        this.j = new a.f.a.i.a.b[this.r.length + 1];
        int i20 = 0;
        while (true) {
            String[] strArr3 = this.r;
            if (i20 >= strArr3.length) {
                break;
            }
            String str9 = strArr3[i20];
            int i21 = 0;
            int i22 = 0;
            double[] dArr6 = null;
            double[][] dArr7 = null;
            while (i21 < size) {
                if (pVarArr[i21].n.containsKey(str9)) {
                    if (dArr7 == null) {
                        dArr6 = new double[size];
                        androidx.constraintlayout.widget.a aVar4 = pVarArr[i21].n.get(str9);
                        dArr7 = (double[][]) Array.newInstance((Class<?>) cls2, size, aVar4 == null ? 0 : aVar4.g());
                    }
                    dArr6[i22] = pVarArr[i21].f919c;
                    p pVar3 = pVarArr[i21];
                    double[] dArr8 = dArr7[i22];
                    androidx.constraintlayout.widget.a aVar5 = pVar3.n.get(str9);
                    if (aVar5 == null) {
                        cls = cls2;
                        i3 = size;
                        str = str9;
                        dArr = dArr6;
                        dArr2 = dArr7;
                    } else {
                        str = str9;
                        if (aVar5.g() == 1) {
                            dArr = dArr6;
                            dArr2 = dArr7;
                            dArr8[0] = aVar5.d();
                        } else {
                            dArr = dArr6;
                            dArr2 = dArr7;
                            int g2 = aVar5.g();
                            aVar5.e(new float[g2]);
                            int i23 = 0;
                            int i24 = 0;
                            while (i23 < g2) {
                                dArr8[i24] = r11[i23];
                                i23++;
                                g2 = g2;
                                i24++;
                                cls2 = cls2;
                                size = size;
                            }
                        }
                        cls = cls2;
                        i3 = size;
                    }
                    i22++;
                    dArr6 = dArr;
                    dArr7 = dArr2;
                } else {
                    cls = cls2;
                    i3 = size;
                    str = str9;
                }
                i21++;
                str9 = str;
                cls2 = cls;
                size = i3;
            }
            i20++;
            this.j[i20] = a.f.a.i.a.b.a(this.e, Arrays.copyOf(dArr6, i22), (double[][]) Arrays.copyOf(dArr7, i22));
            cls2 = cls2;
            size = size;
        }
        Class<double> cls3 = cls2;
        int i25 = size;
        this.j[0] = a.f.a.i.a.b.a(this.e, dArr4, dArr3);
        if (pVarArr[0].j != -1) {
            int[] iArr5 = new int[i25];
            double[] dArr9 = new double[i25];
            double[][] dArr10 = (double[][]) Array.newInstance((Class<?>) cls3, i25, 2);
            for (int i26 = 0; i26 < i25; i26++) {
                iArr5[i26] = pVarArr[i26].j;
                dArr9[i26] = pVarArr[i26].f919c;
                dArr10[i26][0] = pVarArr[i26].e;
                dArr10[i26][1] = pVarArr[i26].f;
            }
            this.k = new a.f.a.i.a.a(iArr5, dArr9, dArr10);
        }
        this.A = new HashMap<>();
        if (this.x != null) {
            Iterator<String> it10 = hashSet3.iterator();
            float f = Float.NaN;
            while (it10.hasNext()) {
                String next8 = it10.next();
                a.f.b.a.c h = a.f.b.a.c.h(next8);
                if (h != null) {
                    if ((h.e == 1) && Float.isNaN(f)) {
                        float[] fArr2 = new float[2];
                        float f2 = 1.0f / 99;
                        double d2 = 0.0d;
                        double d3 = 0.0d;
                        int i27 = 0;
                        float f3 = 0.0f;
                        for (int i28 = 100; i27 < i28; i28 = 100) {
                            float f4 = i27 * f2;
                            double d4 = f4;
                            a.f.a.i.a.c cVar = this.f.f917a;
                            Iterator<p> it11 = this.v.iterator();
                            float f5 = Float.NaN;
                            float f6 = 0.0f;
                            while (it11.hasNext()) {
                                p next9 = it11.next();
                                Iterator<String> it12 = it10;
                                a.f.a.i.a.c cVar2 = next9.f917a;
                                if (cVar2 != null) {
                                    float f7 = next9.f919c;
                                    if (f7 < f4) {
                                        f6 = f7;
                                        cVar = cVar2;
                                    } else if (Float.isNaN(f5)) {
                                        f5 = next9.f919c;
                                    }
                                }
                                it10 = it12;
                            }
                            Iterator<String> it13 = it10;
                            if (cVar != null) {
                                if (Float.isNaN(f5)) {
                                    f5 = 1.0f;
                                }
                                d4 = (((float) cVar.a((f4 - f6) / r18)) * (f5 - f6)) + f6;
                            }
                            this.j[0].c(d4, this.p);
                            float f8 = f3;
                            int i29 = i27;
                            this.f.d(d4, this.o, this.p, fArr2, 0);
                            if (i29 > 0) {
                                c2 = 0;
                                f3 = (float) (Math.hypot(d3 - fArr2[1], d2 - fArr2[0]) + f8);
                            } else {
                                c2 = 0;
                                f3 = f8;
                            }
                            d2 = fArr2[c2];
                            i27 = i29 + 1;
                            it10 = it13;
                            d3 = fArr2[1];
                        }
                        it = it10;
                        f = f3;
                    } else {
                        it = it10;
                    }
                    h.f(next8);
                    this.A.put(next8, h);
                    it10 = it;
                }
            }
            Iterator<d> it14 = this.x.iterator();
            while (it14.hasNext()) {
                d next10 = it14.next();
                if (next10 instanceof f) {
                    ((f) next10).V(this.A);
                }
            }
            Iterator<a.f.b.a.c> it15 = this.A.values().iterator();
            while (it15.hasNext()) {
                it15.next().g(f);
            }
        }
    }

    public void B(n nVar) {
        this.f.g(nVar, nVar.f);
        this.g.g(nVar, nVar.g);
    }

    public void a(d dVar) {
        this.x.add(dVar);
    }

    void b(ArrayList<d> arrayList) {
        this.x.addAll(arrayList);
    }

    int c(float[] fArr, int[] iArr) {
        if (fArr == null) {
            return 0;
        }
        double[] g = this.j[0].g();
        if (iArr != null) {
            Iterator<p> it = this.v.iterator();
            int i = 0;
            while (it.hasNext()) {
                iArr[i] = it.next().o;
                i++;
            }
        }
        int i2 = 0;
        for (int i3 = 0; i3 < g.length; i3++) {
            this.j[0].c(g[i3], this.p);
            this.f.d(g[i3], this.o, this.p, fArr, i2);
            i2 += 2;
        }
        return i2 / 2;
    }

    void d(float[] fArr, int i) {
        double d2;
        float f = 1.0f;
        float f2 = 1.0f / (i - 1);
        HashMap<String, a.f.b.a.d> hashMap = this.z;
        a.f.b.a.d dVar = hashMap == null ? null : hashMap.get("translationX");
        HashMap<String, a.f.b.a.d> hashMap2 = this.z;
        a.f.b.a.d dVar2 = hashMap2 == null ? null : hashMap2.get("translationY");
        HashMap<String, a.f.b.a.c> hashMap3 = this.A;
        a.f.b.a.c cVar = hashMap3 == null ? null : hashMap3.get("translationX");
        HashMap<String, a.f.b.a.c> hashMap4 = this.A;
        a.f.b.a.c cVar2 = hashMap4 != null ? hashMap4.get("translationY") : null;
        int i2 = 0;
        while (i2 < i) {
            float f3 = i2 * f2;
            float f4 = this.n;
            if (f4 != f) {
                float f5 = this.m;
                if (f3 < f5) {
                    f3 = 0.0f;
                }
                if (f3 > f5 && f3 < 1.0d) {
                    f3 = Math.min((f3 - f5) * f4, f);
                }
            }
            float f6 = f3;
            double d3 = f6;
            a.f.a.i.a.c cVar3 = this.f.f917a;
            float f7 = Float.NaN;
            Iterator<p> it = this.v.iterator();
            float f8 = 0.0f;
            while (it.hasNext()) {
                p next = it.next();
                a.f.a.i.a.c cVar4 = next.f917a;
                double d4 = d3;
                if (cVar4 != null) {
                    float f9 = next.f919c;
                    if (f9 < f6) {
                        f8 = f9;
                        cVar3 = cVar4;
                    } else if (Float.isNaN(f7)) {
                        f7 = next.f919c;
                    }
                }
                d3 = d4;
            }
            double d5 = d3;
            if (cVar3 != null) {
                if (Float.isNaN(f7)) {
                    f7 = 1.0f;
                }
                d2 = (((float) cVar3.a((f6 - f8) / r5)) * (f7 - f8)) + f8;
            } else {
                d2 = d5;
            }
            this.j[0].c(d2, this.p);
            a.f.a.i.a.b bVar = this.k;
            if (bVar != null) {
                double[] dArr = this.p;
                if (dArr.length > 0) {
                    bVar.c(d2, dArr);
                }
            }
            int i3 = i2 * 2;
            int i4 = i2;
            this.f.d(d2, this.o, this.p, fArr, i3);
            if (cVar != null) {
                fArr[i3] = cVar.a(f6) + fArr[i3];
            } else if (dVar != null) {
                fArr[i3] = dVar.a(f6) + fArr[i3];
            }
            if (cVar2 != null) {
                int i5 = i3 + 1;
                fArr[i5] = cVar2.a(f6) + fArr[i5];
            } else if (dVar2 != null) {
                int i6 = i3 + 1;
                fArr[i6] = dVar2.a(f6) + fArr[i6];
            }
            i2 = i4 + 1;
            f = 1.0f;
        }
    }

    void e(float f, float[] fArr, int i) {
        this.j[0].c(g(f, null), this.p);
        p pVar = this.f;
        int[] iArr = this.o;
        double[] dArr = this.p;
        float f2 = pVar.e;
        float f3 = pVar.f;
        float f4 = pVar.g;
        float f5 = pVar.h;
        for (int i2 = 0; i2 < iArr.length; i2++) {
            float f6 = (float) dArr[i2];
            int i3 = iArr[i2];
            if (i3 == 1) {
                f2 = f6;
            } else if (i3 == 2) {
                f3 = f6;
            } else if (i3 == 3) {
                f4 = f6;
            } else if (i3 == 4) {
                f5 = f6;
            }
        }
        n nVar = pVar.m;
        if (nVar != null) {
            Objects.requireNonNull(nVar);
            double d2 = 0.0f;
            double d3 = f2;
            double d4 = f3;
            float sin = (float) (((Math.sin(d4) * d3) + d2) - (f4 / 2.0f));
            f3 = (float) ((d2 - (Math.cos(d4) * d3)) - (f5 / 2.0f));
            f2 = sin;
        }
        float f7 = f4 + f2;
        float f8 = f5 + f3;
        Float.isNaN(Float.NaN);
        Float.isNaN(Float.NaN);
        float f9 = f2 + 0.0f;
        float f10 = f3 + 0.0f;
        float f11 = f7 + 0.0f;
        float f12 = f8 + 0.0f;
        int i4 = i + 1;
        fArr[i] = f9;
        int i5 = i4 + 1;
        fArr[i4] = f10;
        int i6 = i5 + 1;
        fArr[i5] = f11;
        int i7 = i6 + 1;
        fArr[i6] = f10;
        int i8 = i7 + 1;
        fArr[i7] = f11;
        int i9 = i8 + 1;
        fArr[i8] = f12;
        fArr[i9] = f9;
        fArr[i9 + 1] = f12;
    }

    void f(boolean z) {
        if (!"button".equals(a.b.a.d(this.f914b)) || this.B == null) {
            return;
        }
        int i = 0;
        while (true) {
            k[] kVarArr = this.B;
            if (i >= kVarArr.length) {
                return;
            }
            kVarArr[i].v(z ? -100.0f : 100.0f, this.f914b);
            i++;
        }
    }

    public int h() {
        return this.f.k;
    }

    public void i(double d2, float[] fArr, float[] fArr2) {
        double[] dArr = new double[4];
        double[] dArr2 = new double[4];
        this.j[0].c(d2, dArr);
        this.j[0].f(d2, dArr2);
        float f = 0.0f;
        Arrays.fill(fArr2, 0.0f);
        p pVar = this.f;
        int[] iArr = this.o;
        float f2 = pVar.e;
        float f3 = pVar.f;
        float f4 = pVar.g;
        float f5 = pVar.h;
        float f6 = 0.0f;
        float f7 = 0.0f;
        int i = 0;
        float f8 = 0.0f;
        while (i < iArr.length) {
            float f9 = (float) dArr[i];
            float f10 = (float) dArr2[i];
            int i2 = iArr[i];
            int[] iArr2 = iArr;
            if (i2 == 1) {
                f = f10;
                f2 = f9;
            } else if (i2 == 2) {
                f8 = f10;
                f3 = f9;
            } else if (i2 == 3) {
                f6 = f10;
                f4 = f9;
            } else if (i2 == 4) {
                f7 = f10;
                f5 = f9;
            }
            i++;
            iArr = iArr2;
        }
        float f11 = (f6 / 2.0f) + f;
        float f12 = (f7 / 2.0f) + f8;
        n nVar = pVar.m;
        if (nVar != null) {
            float[] fArr3 = new float[2];
            float[] fArr4 = new float[2];
            nVar.i(d2, fArr3, fArr4);
            float f13 = fArr3[0];
            float f14 = fArr3[1];
            float f15 = fArr4[0];
            float f16 = fArr4[1];
            double d3 = f2;
            double d4 = f3;
            float sin = (float) (((Math.sin(d4) * d3) + f13) - (f4 / 2.0f));
            f3 = (float) ((f14 - (Math.cos(d4) * d3)) - (f5 / 2.0f));
            double d5 = f;
            double d6 = f8;
            float cos = (float) ((Math.cos(d4) * d6) + (Math.sin(d4) * d5) + f15);
            f12 = (float) ((Math.sin(d4) * d6) + (f16 - (Math.cos(d4) * d5)));
            f2 = sin;
            f11 = cos;
        }
        fArr[0] = (f4 / 2.0f) + f2 + 0.0f;
        fArr[1] = (f5 / 2.0f) + f3 + 0.0f;
        fArr2[0] = f11;
        fArr2[1] = f12;
    }

    void j(float f, float f2, float f3, float[] fArr) {
        double[] dArr;
        float g = g(f, this.w);
        a.f.a.i.a.b[] bVarArr = this.j;
        int i = 0;
        if (bVarArr == null) {
            p pVar = this.g;
            float f4 = pVar.e;
            p pVar2 = this.f;
            float f5 = f4 - pVar2.e;
            float f6 = pVar.f - pVar2.f;
            float f7 = pVar.g - pVar2.g;
            float f8 = (pVar.h - pVar2.h) + f6;
            fArr[0] = ((f7 + f5) * f2) + ((1.0f - f2) * f5);
            fArr[1] = (f8 * f3) + ((1.0f - f3) * f6);
            return;
        }
        double d2 = g;
        bVarArr[0].f(d2, this.q);
        this.j[0].c(d2, this.p);
        float f9 = this.w[0];
        while (true) {
            dArr = this.q;
            if (i >= dArr.length) {
                break;
            }
            dArr[i] = dArr[i] * f9;
            i++;
        }
        a.f.a.i.a.b bVar = this.k;
        if (bVar == null) {
            this.f.f(f2, f3, fArr, this.o, dArr, this.p);
            return;
        }
        double[] dArr2 = this.p;
        if (dArr2.length > 0) {
            bVar.c(d2, dArr2);
            this.k.f(d2, this.q);
            this.f.f(f2, f3, fArr, this.o, this.q, this.p);
        }
    }

    public int k() {
        int i = this.f.f918b;
        Iterator<p> it = this.v.iterator();
        while (it.hasNext()) {
            i = Math.max(i, it.next().f918b);
        }
        return Math.max(i, this.g.f918b);
    }

    public float l() {
        return this.g.e;
    }

    public float m() {
        return this.g.f;
    }

    p n(int i) {
        return this.v.get(i);
    }

    void o(float f, int i, int i2, float f2, float f3, float[] fArr) {
        float g = g(f, this.w);
        HashMap<String, a.f.b.a.d> hashMap = this.z;
        a.f.b.a.d dVar = hashMap == null ? null : hashMap.get("translationX");
        HashMap<String, a.f.b.a.d> hashMap2 = this.z;
        a.f.b.a.d dVar2 = hashMap2 == null ? null : hashMap2.get("translationY");
        HashMap<String, a.f.b.a.d> hashMap3 = this.z;
        a.f.b.a.d dVar3 = hashMap3 == null ? null : hashMap3.get("rotation");
        HashMap<String, a.f.b.a.d> hashMap4 = this.z;
        a.f.b.a.d dVar4 = hashMap4 == null ? null : hashMap4.get("scaleX");
        HashMap<String, a.f.b.a.d> hashMap5 = this.z;
        a.f.b.a.d dVar5 = hashMap5 == null ? null : hashMap5.get("scaleY");
        HashMap<String, a.f.b.a.c> hashMap6 = this.A;
        a.f.b.a.c cVar = hashMap6 == null ? null : hashMap6.get("translationX");
        HashMap<String, a.f.b.a.c> hashMap7 = this.A;
        a.f.b.a.c cVar2 = hashMap7 == null ? null : hashMap7.get("translationY");
        HashMap<String, a.f.b.a.c> hashMap8 = this.A;
        a.f.b.a.c cVar3 = hashMap8 == null ? null : hashMap8.get("rotation");
        HashMap<String, a.f.b.a.c> hashMap9 = this.A;
        a.f.b.a.c cVar4 = hashMap9 == null ? null : hashMap9.get("scaleX");
        HashMap<String, a.f.b.a.c> hashMap10 = this.A;
        a.f.b.a.c cVar5 = hashMap10 != null ? hashMap10.get("scaleY") : null;
        a.f.a.i.a.p pVar = new a.f.a.i.a.p();
        pVar.b();
        pVar.d(dVar3, g);
        pVar.h(dVar, dVar2, g);
        pVar.f(dVar4, dVar5, g);
        pVar.c(cVar3, g);
        pVar.g(cVar, cVar2, g);
        pVar.e(cVar4, cVar5, g);
        a.f.a.i.a.b bVar = this.k;
        if (bVar != null) {
            double[] dArr = this.p;
            if (dArr.length > 0) {
                double d2 = g;
                bVar.c(d2, dArr);
                this.k.f(d2, this.q);
                this.f.f(f2, f3, fArr, this.o, this.q, this.p);
            }
            pVar.a(f2, f3, i, i2, fArr);
            return;
        }
        int i3 = 0;
        if (this.j == null) {
            p pVar2 = this.g;
            float f4 = pVar2.e;
            p pVar3 = this.f;
            float f5 = f4 - pVar3.e;
            a.f.b.a.c cVar6 = cVar5;
            float f6 = pVar2.f - pVar3.f;
            a.f.b.a.c cVar7 = cVar4;
            float f7 = pVar2.g - pVar3.g;
            float f8 = (pVar2.h - pVar3.h) + f6;
            fArr[0] = ((f7 + f5) * f2) + ((1.0f - f2) * f5);
            fArr[1] = (f8 * f3) + ((1.0f - f3) * f6);
            pVar.b();
            pVar.d(dVar3, g);
            pVar.h(dVar, dVar2, g);
            pVar.f(dVar4, dVar5, g);
            pVar.c(cVar3, g);
            pVar.g(cVar, cVar2, g);
            pVar.e(cVar7, cVar6, g);
            pVar.a(f2, f3, i, i2, fArr);
            return;
        }
        double g2 = g(g, this.w);
        this.j[0].f(g2, this.q);
        this.j[0].c(g2, this.p);
        float f9 = this.w[0];
        while (true) {
            double[] dArr2 = this.q;
            if (i3 >= dArr2.length) {
                this.f.f(f2, f3, fArr, this.o, dArr2, this.p);
                pVar.a(f2, f3, i, i2, fArr);
                return;
            } else {
                dArr2[i3] = dArr2[i3] * f9;
                i3++;
            }
        }
    }

    public float p() {
        return this.f.e;
    }

    public float q() {
        return this.f.f;
    }

    /* JADX WARN: Multi-variable type inference failed */
    boolean r(View view, float f, long j, a.f.a.i.a.d dVar) {
        boolean z;
        float f2;
        n nVar;
        int i;
        boolean z2;
        f.d dVar2;
        float f3;
        boolean z3;
        double d2;
        int i2;
        float f4;
        float f5;
        boolean z4;
        float f6;
        f.d dVar3 = null;
        float g = g(f, null);
        int i3 = this.F;
        float f7 = 1.0f;
        if (i3 != -1) {
            float f8 = 1.0f / i3;
            float floor = ((float) Math.floor(g / f8)) * f8;
            float f9 = (g % f8) / f8;
            if (!Float.isNaN(this.G)) {
                f9 = (f9 + this.G) % 1.0f;
            }
            Interpolator interpolator = this.H;
            if (interpolator != null) {
                f7 = interpolator.getInterpolation(f9);
            } else if (f9 <= 0.5d) {
                f7 = 0.0f;
            }
            g = (f7 * f8) + floor;
        }
        float f10 = g;
        HashMap<String, a.f.b.a.d> hashMap = this.z;
        if (hashMap != null) {
            Iterator<a.f.b.a.d> it = hashMap.values().iterator();
            while (it.hasNext()) {
                it.next().f(view, f10);
            }
        }
        HashMap<String, a.f.b.a.f> hashMap2 = this.y;
        if (hashMap2 != null) {
            f.d dVar4 = null;
            boolean z5 = false;
            for (a.f.b.a.f fVar : hashMap2.values()) {
                if (fVar instanceof f.d) {
                    dVar4 = (f.d) fVar;
                } else {
                    z5 |= fVar.h(view, f10, j, dVar);
                }
            }
            dVar3 = dVar4;
            z = z5;
        } else {
            z = false;
        }
        a.f.a.i.a.b[] bVarArr = this.j;
        if (bVarArr != null) {
            double d3 = f10;
            bVarArr[0].c(d3, this.p);
            this.j[0].f(d3, this.q);
            a.f.a.i.a.b bVar = this.k;
            if (bVar != null) {
                double[] dArr = this.p;
                if (dArr.length > 0) {
                    bVar.c(d3, dArr);
                    this.k.f(d3, this.q);
                }
            }
            if (this.I) {
                dVar2 = dVar3;
                f3 = f10;
                z3 = z;
                d2 = d3;
                nVar = this;
            } else {
                p pVar = this.f;
                int[] iArr = this.o;
                double[] dArr2 = this.p;
                double[] dArr3 = this.q;
                boolean z6 = this.f916d;
                float f11 = pVar.e;
                float f12 = pVar.f;
                float f13 = pVar.g;
                float f14 = pVar.h;
                if (iArr.length != 0) {
                    f5 = f12;
                    if (pVar.p.length <= iArr[iArr.length - 1]) {
                        int i4 = iArr[iArr.length - 1] + 1;
                        pVar.p = new double[i4];
                        pVar.q = new double[i4];
                    }
                } else {
                    f5 = f12;
                }
                float f15 = f13;
                Arrays.fill(pVar.p, Double.NaN);
                for (int i5 = 0; i5 < iArr.length; i5++) {
                    pVar.p[iArr[i5]] = dArr2[i5];
                    pVar.q[iArr[i5]] = dArr3[i5];
                }
                float f16 = Float.NaN;
                int i6 = 0;
                float f17 = 0.0f;
                float f18 = f14;
                float f19 = 0.0f;
                float f20 = 0.0f;
                float f21 = f11;
                z3 = z;
                float f22 = f5;
                float f23 = 0.0f;
                float f24 = f22;
                while (true) {
                    double[] dArr4 = pVar.p;
                    dVar2 = dVar3;
                    if (i6 >= dArr4.length) {
                        break;
                    }
                    if (Double.isNaN(dArr4[i6])) {
                        f6 = f10;
                    } else {
                        f6 = f10;
                        float f25 = (float) (Double.isNaN(pVar.p[i6]) ? 0.0d : pVar.p[i6] + 0.0d);
                        float f26 = (float) pVar.q[i6];
                        if (i6 == 1) {
                            f21 = f25;
                            f20 = f26;
                        } else if (i6 == 2) {
                            f24 = f25;
                            f19 = f26;
                        } else if (i6 == 3) {
                            f15 = f25;
                            f23 = f26;
                        } else if (i6 == 4) {
                            f18 = f25;
                            f17 = f26;
                        } else if (i6 == 5) {
                            f16 = f25;
                        }
                    }
                    i6++;
                    dVar3 = dVar2;
                    f10 = f6;
                }
                f3 = f10;
                n nVar2 = pVar.m;
                if (nVar2 != null) {
                    float[] fArr = new float[2];
                    float[] fArr2 = new float[2];
                    nVar2.i(d3, fArr, fArr2);
                    float f27 = fArr[0];
                    float f28 = fArr[1];
                    float f29 = fArr2[0];
                    float f30 = fArr2[1];
                    d2 = d3;
                    double d4 = f21;
                    z4 = z6;
                    double d5 = f24;
                    float sin = (float) (((Math.sin(d5) * d4) + f27) - (f15 / 2.0f));
                    float cos = (float) ((f28 - (Math.cos(d5) * d4)) - (f18 / 2.0f));
                    double d6 = f20;
                    double d7 = f19;
                    float cos2 = (float) ((Math.cos(d5) * d4 * d7) + (Math.sin(d5) * d6) + f29);
                    float sin2 = (float) ((Math.sin(d5) * d4 * d7) + (f30 - (Math.cos(d5) * d6)));
                    if (dArr3.length >= 2) {
                        dArr3[0] = cos2;
                        dArr3[1] = sin2;
                    }
                    if (!Float.isNaN(f16)) {
                        view.setRotation((float) (Math.toDegrees(Math.atan2(sin2, cos2)) + f16));
                    }
                    f24 = cos;
                    f21 = sin;
                } else {
                    z4 = z6;
                    d2 = d3;
                    if (!Float.isNaN(f16)) {
                        view.setRotation((float) (Math.toDegrees(Math.atan2((f17 / 2.0f) + f19, (f23 / 2.0f) + f20)) + f16 + 0.0f));
                    }
                }
                if (view instanceof c) {
                    ((c) view).layout(f21, f24, f15 + f21, f24 + f18);
                } else {
                    float f31 = f21 + 0.5f;
                    int i7 = (int) f31;
                    float f32 = f24 + 0.5f;
                    int i8 = (int) f32;
                    int i9 = (int) (f31 + f15);
                    int i10 = (int) (f32 + f18);
                    int i11 = i9 - i7;
                    int i12 = i10 - i8;
                    if (((i11 == view.getMeasuredWidth() && i12 == view.getMeasuredHeight()) ? false : true) || z4) {
                        view.measure(View.MeasureSpec.makeMeasureSpec(i11, 1073741824), View.MeasureSpec.makeMeasureSpec(i12, 1073741824));
                    }
                    view.layout(i7, i8, i9, i10);
                }
                nVar = this;
                nVar.f916d = false;
            }
            if (nVar.D != -1) {
                if (nVar.E == null) {
                    nVar.E = ((View) view.getParent()).findViewById(nVar.D);
                }
                if (nVar.E != null) {
                    float bottom = (nVar.E.getBottom() + r0.getTop()) / 2.0f;
                    float right = (nVar.E.getRight() + nVar.E.getLeft()) / 2.0f;
                    if (view.getRight() - view.getLeft() > 0 && view.getBottom() - view.getTop() > 0) {
                        view.setPivotX(right - view.getLeft());
                        view.setPivotY(bottom - view.getTop());
                    }
                }
            }
            HashMap<String, a.f.b.a.d> hashMap3 = nVar.z;
            if (hashMap3 != null) {
                for (a.f.b.a.d dVar5 : hashMap3.values()) {
                    if (dVar5 instanceof d.C0005d) {
                        double[] dArr5 = nVar.q;
                        if (dArr5.length > 1) {
                            f4 = f3;
                            view.setRotation(((d.C0005d) dVar5).a(f4) + ((float) Math.toDegrees(Math.atan2(dArr5[1], dArr5[0]))));
                            f3 = f4;
                        }
                    }
                    f4 = f3;
                    f3 = f4;
                }
            }
            f2 = f3;
            if (dVar2 != null) {
                double[] dArr6 = nVar.q;
                i = 1;
                z2 = z3 | dVar2.i(view, dVar, f2, j, dArr6[0], dArr6[1]);
            } else {
                i = 1;
                z2 = z3;
            }
            int i13 = i;
            while (true) {
                a.f.a.i.a.b[] bVarArr2 = nVar.j;
                if (i13 >= bVarArr2.length) {
                    break;
                }
                bVarArr2[i13].d(d2, nVar.u);
                a.f.b.a.a.b(nVar.f.n.get(nVar.r[i13 - 1]), view, nVar.u);
                i13++;
            }
            l lVar = nVar.h;
            if (lVar.f909b == 0) {
                if (f2 > 0.0f) {
                    if (f2 >= 1.0f) {
                        lVar = nVar.i;
                    } else if (nVar.i.f910c != lVar.f910c) {
                        i2 = 0;
                        view.setVisibility(i2);
                    }
                }
                i2 = lVar.f910c;
                view.setVisibility(i2);
            }
            if (nVar.B != null) {
                int i14 = 0;
                while (true) {
                    k[] kVarArr = nVar.B;
                    if (i14 >= kVarArr.length) {
                        break;
                    }
                    kVarArr[i14].v(f2, view);
                    i14++;
                }
            }
        } else {
            boolean z7 = z;
            f2 = f10;
            nVar = this;
            i = 1;
            p pVar2 = nVar.f;
            float f33 = pVar2.e;
            p pVar3 = nVar.g;
            float a2 = b.b.a.a.a.a(pVar3.e, f33, f2, f33);
            float f34 = pVar2.f;
            float a3 = b.b.a.a.a.a(pVar3.f, f34, f2, f34);
            float f35 = pVar2.g;
            float f36 = pVar3.g;
            float a4 = b.b.a.a.a.a(f36, f35, f2, f35);
            float f37 = pVar2.h;
            float f38 = pVar3.h;
            float f39 = a2 + 0.5f;
            int i15 = (int) f39;
            float f40 = a3 + 0.5f;
            int i16 = (int) f40;
            int i17 = (int) (f39 + a4);
            int a5 = (int) (f40 + b.b.a.a.a.a(f38, f37, f2, f37));
            int i18 = i17 - i15;
            int i19 = a5 - i16;
            if (f36 != f35 || f38 != f37 || nVar.f916d) {
                view.measure(View.MeasureSpec.makeMeasureSpec(i18, 1073741824), View.MeasureSpec.makeMeasureSpec(i19, 1073741824));
                nVar.f916d = false;
            }
            view.layout(i15, i16, i17, a5);
            z2 = z7;
        }
        HashMap<String, a.f.b.a.c> hashMap4 = nVar.A;
        if (hashMap4 != null) {
            for (a.f.b.a.c cVar : hashMap4.values()) {
                if (cVar instanceof c.d) {
                    double[] dArr7 = nVar.q;
                    view.setRotation(((c.d) cVar).a(f2) + ((float) Math.toDegrees(Math.atan2(dArr7[i], dArr7[0]))));
                } else {
                    cVar.i(view, f2);
                }
            }
        }
        return z2;
    }

    void t(Rect rect, Rect rect2, int i, int i2, int i3) {
        int i4;
        int width;
        int i5;
        int i6;
        int i7;
        if (i != 1) {
            if (i == 2) {
                i5 = rect.left + rect.right;
                i6 = rect.top;
                i7 = rect.bottom;
            } else if (i == 3) {
                i4 = rect.left + rect.right;
                width = ((rect.height() / 2) + rect.top) - (i4 / 2);
            } else {
                if (i != 4) {
                    return;
                }
                i5 = rect.left + rect.right;
                i6 = rect.bottom;
                i7 = rect.top;
            }
            rect2.left = i2 - ((rect.width() + (i6 + i7)) / 2);
            rect2.top = (i5 - rect.height()) / 2;
            rect2.right = rect.width() + rect2.left;
            rect2.bottom = rect.height() + rect2.top;
        }
        i4 = rect.left + rect.right;
        width = ((rect.top + rect.bottom) - rect.width()) / 2;
        rect2.left = width;
        rect2.top = i3 - ((rect.height() + i4) / 2);
        rect2.right = rect.width() + rect2.left;
        rect2.bottom = rect.height() + rect2.top;
    }

    public String toString() {
        StringBuilder j = b.b.a.a.a.j(" start: x: ");
        j.append(this.f.e);
        j.append(" y: ");
        j.append(this.f.f);
        j.append(" end: x: ");
        j.append(this.g.e);
        j.append(" y: ");
        j.append(this.g.f);
        return j.toString();
    }

    void u(View view) {
        p pVar = this.f;
        pVar.f919c = 0.0f;
        pVar.f920d = 0.0f;
        this.I = true;
        pVar.e(view.getX(), view.getY(), view.getWidth(), view.getHeight());
        this.g.e(view.getX(), view.getY(), view.getWidth(), view.getHeight());
        this.h.g(view);
        this.i.g(view);
    }

    void v(Rect rect, androidx.constraintlayout.widget.c cVar, int i, int i2) {
        int i3 = cVar.f993c;
        if (i3 != 0) {
            t(rect, this.f913a, i3, i, i2);
            rect = this.f913a;
        }
        p pVar = this.g;
        pVar.f919c = 1.0f;
        pVar.f920d = 1.0f;
        s(pVar);
        this.g.e(rect.left, rect.top, rect.width(), rect.height());
        this.g.a(cVar.t(this.f915c));
        this.i.f(rect, cVar, i3, this.f915c);
    }

    public void w(int i) {
        this.C = i;
    }

    void x(View view) {
        p pVar = this.f;
        pVar.f919c = 0.0f;
        pVar.f920d = 0.0f;
        pVar.e(view.getX(), view.getY(), view.getWidth(), view.getHeight());
        this.h.g(view);
    }

    void y(Rect rect, androidx.constraintlayout.widget.c cVar, int i, int i2) {
        int i3 = cVar.f993c;
        if (i3 != 0) {
            t(rect, this.f913a, i3, i, i2);
        }
        p pVar = this.f;
        pVar.f919c = 0.0f;
        pVar.f920d = 0.0f;
        s(pVar);
        this.f.e(rect.left, rect.top, rect.width(), rect.height());
        c.a t = cVar.t(this.f915c);
        this.f.a(t);
        this.l = t.f998d.g;
        this.h.f(rect, cVar, i3, this.f915c);
        this.D = t.f.i;
        c.C0034c c0034c = t.f998d;
        this.F = c0034c.k;
        this.G = c0034c.j;
        Context context = this.f914b.getContext();
        c.C0034c c0034c2 = t.f998d;
        int i4 = c0034c2.m;
        this.H = i4 != -2 ? i4 != -1 ? i4 != 0 ? i4 != 1 ? i4 != 2 ? i4 != 4 ? i4 != 5 ? null : new OvershootInterpolator() : new BounceInterpolator() : new DecelerateInterpolator() : new AccelerateInterpolator() : new AccelerateDecelerateInterpolator() : new m(a.f.a.i.a.c.c(c0034c2.l)) : AnimationUtils.loadInterpolator(context, c0034c2.n);
    }

    public void z(a.f.b.a.e eVar, View view, int i, int i2, int i3) {
        int a2;
        p pVar = this.f;
        pVar.f919c = 0.0f;
        pVar.f920d = 0.0f;
        Rect rect = new Rect();
        if (i != 1) {
            if (i == 2) {
                int i4 = eVar.f255b + eVar.f257d;
                rect.left = i3 - ((eVar.b() + (eVar.f256c + eVar.e)) / 2);
                a2 = (i4 - eVar.a()) / 2;
            }
            this.f.e(rect.left, rect.top, rect.width(), rect.height());
            this.h.e(rect, view, i, eVar.f254a);
        }
        int i5 = eVar.f255b + eVar.f257d;
        rect.left = ((eVar.f256c + eVar.e) - eVar.b()) / 2;
        a2 = i2 - ((eVar.a() + i5) / 2);
        rect.top = a2;
        rect.right = eVar.b() + rect.left;
        rect.bottom = eVar.a() + rect.top;
        this.f.e(rect.left, rect.top, rect.width(), rect.height());
        this.h.e(rect, view, i, eVar.f254a);
    }
}
