package androidx.appcompat.app;

import a.b.f.b;
import android.app.Dialog;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;

/* loaded from: classes.dex */
public class l extends Dialog implements e {

    /* renamed from: a, reason: collision with root package name */
    private f f592a;

    /* renamed from: b, reason: collision with root package name */
    private final a.h.h.d f593b;

    class a implements a.h.h.d {
        a() {
        }

        @Override // a.h.h.d
        public boolean d(KeyEvent keyEvent) {
            return l.this.b(keyEvent);
        }
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public l(android.content.Context r5, int r6) {
        /*
            r4 = this;
            r0 = 1
            r1 = 2130903332(0x7f030124, float:1.741348E38)
            if (r6 != 0) goto L15
            android.util.TypedValue r2 = new android.util.TypedValue
            r2.<init>()
            android.content.res.Resources$Theme r3 = r5.getTheme()
            r3.resolveAttribute(r1, r2, r0)
            int r2 = r2.resourceId
            goto L16
        L15:
            r2 = r6
        L16:
            r4.<init>(r5, r2)
            androidx.appcompat.app.l$a r2 = new androidx.appcompat.app.l$a
            r2.<init>()
            r4.f593b = r2
            androidx.appcompat.app.f r4 = r4.a()
            if (r6 != 0) goto L34
            android.util.TypedValue r6 = new android.util.TypedValue
            r6.<init>()
            android.content.res.Resources$Theme r5 = r5.getTheme()
            r5.resolveAttribute(r1, r6, r0)
            int r6 = r6.resourceId
        L34:
            r4.z(r6)
            r5 = 0
            r4.m(r5)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.app.l.<init>(android.content.Context, int):void");
    }

    public f a() {
        if (this.f592a == null) {
            int i = f.f566c;
            this.f592a = new g(this, this);
        }
        return this.f592a;
    }

    @Override // android.app.Dialog
    public void addContentView(View view, ViewGroup.LayoutParams layoutParams) {
        a().d(view, layoutParams);
    }

    boolean b(KeyEvent keyEvent) {
        return super.dispatchKeyEvent(keyEvent);
    }

    @Override // android.app.Dialog, android.content.DialogInterface
    public void dismiss() {
        super.dismiss();
        a().n();
    }

    @Override // android.app.Dialog, android.view.Window.Callback
    public boolean dispatchKeyEvent(KeyEvent keyEvent) {
        getWindow().getDecorView();
        a.h.h.d dVar = this.f593b;
        if (dVar == null) {
            return false;
        }
        return dVar.d(keyEvent);
    }

    @Override // androidx.appcompat.app.e
    public void f(a.b.f.b bVar) {
    }

    @Override // android.app.Dialog
    public <T extends View> T findViewById(int i) {
        return (T) a().f(i);
    }

    @Override // androidx.appcompat.app.e
    public void h(a.b.f.b bVar) {
    }

    @Override // androidx.appcompat.app.e
    public a.b.f.b i(b.a aVar) {
        return null;
    }

    @Override // android.app.Dialog
    public void invalidateOptionsMenu() {
        a().k();
    }

    @Override // android.app.Dialog
    protected void onCreate(Bundle bundle) {
        a().j();
        super.onCreate(bundle);
        a().m(bundle);
    }

    @Override // android.app.Dialog
    protected void onStop() {
        super.onStop();
        a().s();
    }

    @Override // android.app.Dialog
    public void setContentView(int i) {
        a().w(i);
    }

    @Override // android.app.Dialog
    public void setContentView(View view) {
        a().x(view);
    }

    @Override // android.app.Dialog
    public void setContentView(View view, ViewGroup.LayoutParams layoutParams) {
        a().y(view, layoutParams);
    }

    @Override // android.app.Dialog
    public void setTitle(int i) {
        super.setTitle(i);
        a().A(getContext().getString(i));
    }

    @Override // android.app.Dialog
    public void setTitle(CharSequence charSequence) {
        super.setTitle(charSequence);
        a().A(charSequence);
    }
}
