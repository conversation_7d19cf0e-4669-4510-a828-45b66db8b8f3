package androidx.appcompat.view.menu;

import android.content.Context;
import android.graphics.Point;
import android.graphics.Rect;
import android.view.Display;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.PopupWindow;
import androidx.appcompat.view.menu.m;
import org.libpag.R;

/* loaded from: classes.dex */
public class l {

    /* renamed from: a, reason: collision with root package name */
    private final Context f677a;

    /* renamed from: b, reason: collision with root package name */
    private final g f678b;

    /* renamed from: c, reason: collision with root package name */
    private final boolean f679c;

    /* renamed from: d, reason: collision with root package name */
    private final int f680d;
    private final int e;
    private View f;
    private boolean h;
    private m.a i;
    private k j;
    private PopupWindow.OnDismissListener k;
    private int g = 8388611;
    private final PopupWindow.OnDismissListener l = new a();

    class a implements PopupWindow.OnDismissListener {
        a() {
        }

        @Override // android.widget.PopupWindow.OnDismissListener
        public void onDismiss() {
            l.this.d();
        }
    }

    public l(Context context, g gVar, View view, boolean z, int i, int i2) {
        this.f677a = context;
        this.f678b = gVar;
        this.f = view;
        this.f679c = z;
        this.f680d = i;
        this.e = i2;
    }

    private void j(int i, int i2, boolean z, boolean z2) {
        k b2 = b();
        b2.y(z2);
        if (z) {
            int i3 = this.g;
            View view = this.f;
            int i4 = a.h.h.q.e;
            if ((Gravity.getAbsoluteGravity(i3, view.getLayoutDirection()) & 7) == 5) {
                i -= this.f.getWidth();
            }
            b2.w(i);
            b2.z(i2);
            int i5 = (int) ((this.f677a.getResources().getDisplayMetrics().density * 48.0f) / 2.0f);
            b2.t(new Rect(i - i5, i2 - i5, i + i5, i2 + i5));
        }
        b2.f();
    }

    public void a() {
        if (c()) {
            this.j.dismiss();
        }
    }

    public k b() {
        if (this.j == null) {
            Display defaultDisplay = ((WindowManager) this.f677a.getSystemService("window")).getDefaultDisplay();
            Point point = new Point();
            defaultDisplay.getRealSize(point);
            k dVar = Math.min(point.x, point.y) >= this.f677a.getResources().getDimensionPixelSize(R.dimen.abc_cascading_menus_min_smallest_width) ? new d(this.f677a, this.f, this.f680d, this.e, this.f679c) : new q(this.f677a, this.f678b, this.f, this.f680d, this.e, this.f679c);
            dVar.o(this.f678b);
            dVar.x(this.l);
            dVar.s(this.f);
            dVar.l(this.i);
            dVar.u(this.h);
            dVar.v(this.g);
            this.j = dVar;
        }
        return this.j;
    }

    public boolean c() {
        k kVar = this.j;
        return kVar != null && kVar.b();
    }

    protected void d() {
        this.j = null;
        PopupWindow.OnDismissListener onDismissListener = this.k;
        if (onDismissListener != null) {
            onDismissListener.onDismiss();
        }
    }

    public void e(View view) {
        this.f = view;
    }

    public void f(boolean z) {
        this.h = z;
        k kVar = this.j;
        if (kVar != null) {
            kVar.u(z);
        }
    }

    public void g(int i) {
        this.g = i;
    }

    public void h(PopupWindow.OnDismissListener onDismissListener) {
        this.k = onDismissListener;
    }

    public void i(m.a aVar) {
        this.i = aVar;
        k kVar = this.j;
        if (kVar != null) {
            kVar.l(aVar);
        }
    }

    public boolean k() {
        if (c()) {
            return true;
        }
        if (this.f == null) {
            return false;
        }
        j(0, 0, false, false);
        return true;
    }

    public boolean l(int i, int i2) {
        if (c()) {
            return true;
        }
        if (this.f == null) {
            return false;
        }
        j(i, i2, true, true);
        return true;
    }
}
