package androidx.constraintlayout.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.Log;
import android.util.SparseArray;
import android.util.Xml;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

/* loaded from: classes.dex */
public class h {

    /* renamed from: a, reason: collision with root package name */
    int f1024a;

    /* renamed from: b, reason: collision with root package name */
    private SparseArray<a> f1025b = new SparseArray<>();

    static class a {

        /* renamed from: a, reason: collision with root package name */
        int f1026a;

        /* renamed from: b, reason: collision with root package name */
        ArrayList<b> f1027b = new ArrayList<>();

        /* renamed from: c, reason: collision with root package name */
        int f1028c;

        public a(Context context, XmlPullParser xmlPullParser) {
            this.f1028c = -1;
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(Xml.asAttributeSet(xmlPullParser), f.A);
            int indexCount = obtainStyledAttributes.getIndexCount();
            for (int i = 0; i < indexCount; i++) {
                int index = obtainStyledAttributes.getIndex(i);
                if (index == 0) {
                    this.f1026a = obtainStyledAttributes.getResourceId(index, this.f1026a);
                } else if (index == 1) {
                    this.f1028c = obtainStyledAttributes.getResourceId(index, this.f1028c);
                    String resourceTypeName = context.getResources().getResourceTypeName(this.f1028c);
                    context.getResources().getResourceName(this.f1028c);
                    "layout".equals(resourceTypeName);
                }
            }
            obtainStyledAttributes.recycle();
        }

        public int a(float f, float f2) {
            for (int i = 0; i < this.f1027b.size(); i++) {
                if (this.f1027b.get(i).a(f, f2)) {
                    return i;
                }
            }
            return -1;
        }
    }

    static class b {

        /* renamed from: a, reason: collision with root package name */
        float f1029a;

        /* renamed from: b, reason: collision with root package name */
        float f1030b;

        /* renamed from: c, reason: collision with root package name */
        float f1031c;

        /* renamed from: d, reason: collision with root package name */
        float f1032d;
        int e;

        public b(Context context, XmlPullParser xmlPullParser) {
            this.f1029a = Float.NaN;
            this.f1030b = Float.NaN;
            this.f1031c = Float.NaN;
            this.f1032d = Float.NaN;
            this.e = -1;
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(Xml.asAttributeSet(xmlPullParser), f.E);
            int indexCount = obtainStyledAttributes.getIndexCount();
            for (int i = 0; i < indexCount; i++) {
                int index = obtainStyledAttributes.getIndex(i);
                if (index == 0) {
                    this.e = obtainStyledAttributes.getResourceId(index, this.e);
                    String resourceTypeName = context.getResources().getResourceTypeName(this.e);
                    context.getResources().getResourceName(this.e);
                    "layout".equals(resourceTypeName);
                } else if (index == 1) {
                    this.f1032d = obtainStyledAttributes.getDimension(index, this.f1032d);
                } else if (index == 2) {
                    this.f1030b = obtainStyledAttributes.getDimension(index, this.f1030b);
                } else if (index == 3) {
                    this.f1031c = obtainStyledAttributes.getDimension(index, this.f1031c);
                } else if (index == 4) {
                    this.f1029a = obtainStyledAttributes.getDimension(index, this.f1029a);
                } else {
                    Log.v("ConstraintLayoutStates", "Unknown tag");
                }
            }
            obtainStyledAttributes.recycle();
        }

        boolean a(float f, float f2) {
            if (!Float.isNaN(this.f1029a) && f < this.f1029a) {
                return false;
            }
            if (!Float.isNaN(this.f1030b) && f2 < this.f1030b) {
                return false;
            }
            if (Float.isNaN(this.f1031c) || f <= this.f1031c) {
                return Float.isNaN(this.f1032d) || f2 <= this.f1032d;
            }
            return false;
        }
    }

    public h(Context context, XmlPullParser xmlPullParser) {
        this.f1024a = -1;
        new SparseArray();
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(Xml.asAttributeSet(xmlPullParser), f.B);
        int indexCount = obtainStyledAttributes.getIndexCount();
        for (int i = 0; i < indexCount; i++) {
            int index = obtainStyledAttributes.getIndex(i);
            if (index == 0) {
                this.f1024a = obtainStyledAttributes.getResourceId(index, this.f1024a);
            }
        }
        obtainStyledAttributes.recycle();
        a aVar = null;
        try {
            int eventType = xmlPullParser.getEventType();
            while (true) {
                char c2 = 1;
                if (eventType == 1) {
                    return;
                }
                if (eventType == 0) {
                    xmlPullParser.getName();
                } else if (eventType == 2) {
                    String name = xmlPullParser.getName();
                    switch (name.hashCode()) {
                        case 80204913:
                            if (name.equals("State")) {
                                c2 = 2;
                                break;
                            }
                            c2 = 65535;
                            break;
                        case 1301459538:
                            if (name.equals("LayoutDescription")) {
                                c2 = 0;
                                break;
                            }
                            c2 = 65535;
                            break;
                        case 1382829617:
                            if (name.equals("StateSet")) {
                                break;
                            }
                            c2 = 65535;
                            break;
                        case 1901439077:
                            if (name.equals("Variant")) {
                                c2 = 3;
                                break;
                            }
                            c2 = 65535;
                            break;
                        default:
                            c2 = 65535;
                            break;
                    }
                    if (c2 == 2) {
                        aVar = new a(context, xmlPullParser);
                        this.f1025b.put(aVar.f1026a, aVar);
                    } else if (c2 == 3) {
                        b bVar = new b(context, xmlPullParser);
                        if (aVar != null) {
                            aVar.f1027b.add(bVar);
                        }
                    }
                } else if (eventType != 3) {
                    continue;
                } else if ("StateSet".equals(xmlPullParser.getName())) {
                    return;
                }
                eventType = xmlPullParser.next();
            }
        } catch (IOException e) {
            e.printStackTrace();
        } catch (XmlPullParserException e2) {
            e2.printStackTrace();
        }
    }

    public int a(int i, int i2, float f, float f2) {
        a aVar = this.f1025b.get(i2);
        if (aVar == null) {
            return i2;
        }
        if (f == -1.0f || f2 == -1.0f) {
            if (aVar.f1028c == i) {
                return i;
            }
            Iterator<b> it = aVar.f1027b.iterator();
            while (it.hasNext()) {
                if (i == it.next().e) {
                    return i;
                }
            }
            return aVar.f1028c;
        }
        b bVar = null;
        Iterator<b> it2 = aVar.f1027b.iterator();
        while (it2.hasNext()) {
            b next = it2.next();
            if (next.a(f, f2)) {
                if (i == next.e) {
                    return i;
                }
                bVar = next;
            }
        }
        return bVar != null ? bVar.e : aVar.f1028c;
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x0021, code lost:
    
        r1 = r1.f1028c;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x0024, code lost:
    
        r1 = r1.f1027b.get(r2).e;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x003d, code lost:
    
        if (r2 == (-1)) goto L14;
     */
    /* JADX WARN: Code restructure failed: missing block: B:9:0x001f, code lost:
    
        if (r2 == (-1)) goto L14;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public int b(int r2, int r3, int r4) {
        /*
            r1 = this;
            float r3 = (float) r3
            float r4 = (float) r4
            android.util.SparseArray<androidx.constraintlayout.widget.h$a> r1 = r1.f1025b
            r0 = -1
            if (r0 != r2) goto L30
            if (r2 != r0) goto Lf
            r2 = 0
            java.lang.Object r1 = r1.valueAt(r2)
            goto L13
        Lf:
            java.lang.Object r1 = r1.get(r0)
        L13:
            androidx.constraintlayout.widget.h$a r1 = (androidx.constraintlayout.widget.h.a) r1
            if (r1 != 0) goto L18
            goto L40
        L18:
            int r2 = r1.a(r3, r4)
            if (r0 != r2) goto L1f
            goto L40
        L1f:
            if (r2 != r0) goto L24
        L21:
            int r1 = r1.f1028c
            goto L2e
        L24:
            java.util.ArrayList<androidx.constraintlayout.widget.h$b> r1 = r1.f1027b
            java.lang.Object r1 = r1.get(r2)
            androidx.constraintlayout.widget.h$b r1 = (androidx.constraintlayout.widget.h.b) r1
            int r1 = r1.e
        L2e:
            r0 = r1
            goto L40
        L30:
            java.lang.Object r1 = r1.get(r2)
            androidx.constraintlayout.widget.h$a r1 = (androidx.constraintlayout.widget.h.a) r1
            if (r1 != 0) goto L39
            goto L40
        L39:
            int r2 = r1.a(r3, r4)
            if (r2 != r0) goto L24
            goto L21
        L40:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.widget.h.b(int, int, int):int");
    }
}
