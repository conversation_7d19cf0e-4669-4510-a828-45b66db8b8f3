package androidx.activity;

import android.app.Activity;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import androidx.lifecycle.d;
import androidx.lifecycle.e;
import androidx.lifecycle.g;
import java.lang.reflect.Field;

/* loaded from: classes.dex */
final class ImmLeaksCleaner implements e {

    /* renamed from: b, reason: collision with root package name */
    private static int f534b;

    /* renamed from: c, reason: collision with root package name */
    private static Field f535c;

    /* renamed from: d, reason: collision with root package name */
    private static Field f536d;
    private static Field e;

    /* renamed from: a, reason: collision with root package name */
    private Activity f537a;

    @Override // androidx.lifecycle.e
    public void d(g gVar, d.a aVar) {
        if (aVar != d.a.ON_DESTROY) {
            return;
        }
        if (f534b == 0) {
            try {
                f534b = 2;
                Field declaredField = InputMethodManager.class.getDeclaredField("mServedView");
                f536d = declaredField;
                declaredField.setAccessible(true);
                Field declaredField2 = InputMethodManager.class.getDeclaredField("mNextServedView");
                e = declaredField2;
                declaredField2.setAccessible(true);
                Field declaredField3 = InputMethodManager.class.getDeclaredField("mH");
                f535c = declaredField3;
                declaredField3.setAccessible(true);
                f534b = 1;
            } catch (NoSuchFieldException unused) {
            }
        }
        if (f534b == 1) {
            InputMethodManager inputMethodManager = (InputMethodManager) this.f537a.getSystemService("input_method");
            try {
                Object obj = f535c.get(inputMethodManager);
                if (obj == null) {
                    return;
                }
                synchronized (obj) {
                    try {
                        try {
                            View view = (View) f536d.get(inputMethodManager);
                            if (view == null) {
                                return;
                            }
                            if (view.isAttachedToWindow()) {
                                return;
                            }
                            try {
                                e.set(inputMethodManager, null);
                                inputMethodManager.isActive();
                            } catch (IllegalAccessException unused2) {
                            }
                        } catch (ClassCastException unused3) {
                        } catch (IllegalAccessException unused4) {
                        }
                    } catch (Throwable th) {
                        throw th;
                    }
                }
            } catch (IllegalAccessException unused5) {
            }
        }
    }
}
