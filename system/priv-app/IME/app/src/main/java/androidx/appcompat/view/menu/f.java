package androidx.appcompat.view.menu;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import androidx.appcompat.view.menu.n;
import java.util.ArrayList;

/* loaded from: classes.dex */
public class f extends BaseAdapter {

    /* renamed from: a, reason: collision with root package name */
    g f652a;

    /* renamed from: b, reason: collision with root package name */
    private int f653b = -1;

    /* renamed from: c, reason: collision with root package name */
    private boolean f654c;

    /* renamed from: d, reason: collision with root package name */
    private final boolean f655d;
    private final LayoutInflater e;
    private final int f;

    public f(g gVar, LayoutInflater layoutInflater, boolean z, int i) {
        this.f655d = z;
        this.e = layoutInflater;
        this.f652a = gVar;
        this.f = i;
        a();
    }

    void a() {
        i o = this.f652a.o();
        if (o != null) {
            ArrayList<i> p = this.f652a.p();
            int size = p.size();
            for (int i = 0; i < size; i++) {
                if (p.get(i) == o) {
                    this.f653b = i;
                    return;
                }
            }
        }
        this.f653b = -1;
    }

    public g b() {
        return this.f652a;
    }

    @Override // android.widget.Adapter
    /* renamed from: c, reason: merged with bridge method [inline-methods] */
    public i getItem(int i) {
        ArrayList<i> p = this.f655d ? this.f652a.p() : this.f652a.r();
        int i2 = this.f653b;
        if (i2 >= 0 && i >= i2) {
            i++;
        }
        return p.get(i);
    }

    public void d(boolean z) {
        this.f654c = z;
    }

    @Override // android.widget.Adapter
    public int getCount() {
        return this.f653b < 0 ? (this.f655d ? this.f652a.p() : this.f652a.r()).size() : r0.size() - 1;
    }

    @Override // android.widget.Adapter
    public long getItemId(int i) {
        return i;
    }

    @Override // android.widget.Adapter
    public View getView(int i, View view, ViewGroup viewGroup) {
        if (view == null) {
            view = this.e.inflate(this.f, viewGroup, false);
        }
        int groupId = getItem(i).getGroupId();
        int i2 = i - 1;
        ListMenuItemView listMenuItemView = (ListMenuItemView) view;
        listMenuItemView.setGroupDividerEnabled(this.f652a.s() && groupId != (i2 >= 0 ? getItem(i2).getGroupId() : groupId));
        n.a aVar = (n.a) view;
        if (this.f654c) {
            listMenuItemView.setForceShowIcon(true);
        }
        aVar.initialize(getItem(i), 0);
        return view;
    }

    @Override // android.widget.BaseAdapter
    public void notifyDataSetChanged() {
        a();
        super.notifyDataSetChanged();
    }
}
