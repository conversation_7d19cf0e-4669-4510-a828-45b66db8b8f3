package androidx.appcompat.view.menu;

import android.content.Context;
import android.view.MenuItem;
import android.view.SubMenu;

/* loaded from: classes.dex */
abstract class c {

    /* renamed from: a, reason: collision with root package name */
    final Context f630a;

    /* renamed from: b, reason: collision with root package name */
    private a.e.h<a.h.d.a.b, MenuItem> f631b;

    /* renamed from: c, reason: collision with root package name */
    private a.e.h<a.h.d.a.c, SubMenu> f632c;

    c(Context context) {
        this.f630a = context;
    }

    final MenuItem c(MenuItem menuItem) {
        if (!(menuItem instanceof a.h.d.a.b)) {
            return menuItem;
        }
        a.h.d.a.b bVar = (a.h.d.a.b) menuItem;
        if (this.f631b == null) {
            this.f631b = new a.e.h<>();
        }
        MenuItem orDefault = this.f631b.getOrDefault(menuItem, null);
        if (orDefault != null) {
            return orDefault;
        }
        j jVar = new j(this.f630a, bVar);
        this.f631b.put(bVar, jVar);
        return jVar;
    }

    final SubMenu d(SubMenu subMenu) {
        if (!(subMenu instanceof a.h.d.a.c)) {
            return subMenu;
        }
        a.h.d.a.c cVar = (a.h.d.a.c) subMenu;
        if (this.f632c == null) {
            this.f632c = new a.e.h<>();
        }
        SubMenu subMenu2 = this.f632c.get(cVar);
        if (subMenu2 != null) {
            return subMenu2;
        }
        s sVar = new s(this.f630a, cVar);
        this.f632c.put(cVar, sVar);
        return sVar;
    }

    final void e() {
        a.e.h<a.h.d.a.b, MenuItem> hVar = this.f631b;
        if (hVar != null) {
            hVar.clear();
        }
        a.e.h<a.h.d.a.c, SubMenu> hVar2 = this.f632c;
        if (hVar2 != null) {
            hVar2.clear();
        }
    }

    final void f(int i) {
        if (this.f631b == null) {
            return;
        }
        int i2 = 0;
        while (i2 < this.f631b.size()) {
            if (this.f631b.h(i2).getGroupId() == i) {
                this.f631b.i(i2);
                i2--;
            }
            i2++;
        }
    }

    final void g(int i) {
        if (this.f631b == null) {
            return;
        }
        for (int i2 = 0; i2 < this.f631b.size(); i2++) {
            if (this.f631b.h(i2).getItemId() == i) {
                this.f631b.i(i2);
                return;
            }
        }
    }
}
