package androidx.appcompat.app;

import a.h.h.q;
import a.h.h.u;
import android.view.View;

/* loaded from: classes.dex */
class k extends u {

    /* renamed from: a, reason: collision with root package name */
    final /* synthetic */ g f591a;

    k(g gVar) {
        this.f591a = gVar;
    }

    @Override // a.h.h.t
    public void a(View view) {
        this.f591a.p.setAlpha(1.0f);
        this.f591a.s.f(null);
        this.f591a.s = null;
    }

    @Override // a.h.h.u, a.h.h.t
    public void b(View view) {
        this.f591a.p.setVisibility(0);
        this.f591a.p.sendAccessibilityEvent(32);
        if (this.f591a.p.getParent() instanceof View) {
            View view2 = (View) this.f591a.p.getParent();
            int i = q.e;
            view2.requestApplyInsets();
        }
    }
}
