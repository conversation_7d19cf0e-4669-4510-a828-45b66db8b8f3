package androidx.constraintlayout.motion.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseIntArray;
import com.google.android.material.button.MaterialButton;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;

/* loaded from: classes.dex */
public class e extends d {
    private int f = -1;
    private boolean g = false;
    private float h = Float.NaN;
    private float i = Float.NaN;
    private float j = Float.NaN;
    private float k = Float.NaN;
    private float l = Float.NaN;
    private float m = Float.NaN;
    private float n = Float.NaN;
    private float o = Float.NaN;
    private float p = Float.NaN;
    private float q = Float.NaN;
    private float r = Float.NaN;
    private float s = Float.NaN;
    private float t = Float.NaN;
    private float u = Float.NaN;

    private static class a {

        /* renamed from: a, reason: collision with root package name */
        private static SparseIntArray f901a;

        static {
            SparseIntArray sparseIntArray = new SparseIntArray();
            f901a = sparseIntArray;
            sparseIntArray.append(0, 1);
            f901a.append(11, 2);
            f901a.append(7, 4);
            f901a.append(8, 5);
            f901a.append(9, 6);
            f901a.append(1, 19);
            f901a.append(2, 20);
            f901a.append(5, 7);
            f901a.append(18, 8);
            f901a.append(17, 9);
            f901a.append(15, 10);
            f901a.append(13, 12);
            f901a.append(12, 13);
            f901a.append(6, 14);
            f901a.append(3, 15);
            f901a.append(4, 16);
            f901a.append(10, 17);
            f901a.append(14, 18);
        }

        public static void a(e eVar, TypedArray typedArray) {
            int indexCount = typedArray.getIndexCount();
            for (int i = 0; i < indexCount; i++) {
                int index = typedArray.getIndex(i);
                switch (f901a.get(index)) {
                    case 1:
                        eVar.h = typedArray.getFloat(index, eVar.h);
                        break;
                    case 2:
                        eVar.i = typedArray.getDimension(index, eVar.i);
                        break;
                    case 3:
                    case 11:
                    default:
                        StringBuilder j = b.b.a.a.a.j("unused attribute 0x");
                        j.append(Integer.toHexString(index));
                        j.append("   ");
                        j.append(f901a.get(index));
                        Log.e("KeyAttribute", j.toString());
                        break;
                    case 4:
                        eVar.j = typedArray.getFloat(index, eVar.j);
                        break;
                    case 5:
                        eVar.k = typedArray.getFloat(index, eVar.k);
                        break;
                    case 6:
                        eVar.l = typedArray.getFloat(index, eVar.l);
                        break;
                    case MotionLayout.TOUCH_UP_NEVER_TO_END /* 7 */:
                        eVar.p = typedArray.getFloat(index, eVar.p);
                        break;
                    case 8:
                        eVar.o = typedArray.getFloat(index, eVar.o);
                        break;
                    case 9:
                        typedArray.getString(index);
                        break;
                    case 10:
                        if (MotionLayout.IS_IN_EDIT_MODE) {
                            int resourceId = typedArray.getResourceId(index, eVar.f898b);
                            eVar.f898b = resourceId;
                            if (resourceId != -1) {
                                break;
                            }
                            eVar.f899c = typedArray.getString(index);
                            break;
                        } else {
                            if (typedArray.peekValue(index).type != 3) {
                                eVar.f898b = typedArray.getResourceId(index, eVar.f898b);
                                break;
                            }
                            eVar.f899c = typedArray.getString(index);
                        }
                    case 12:
                        eVar.f897a = typedArray.getInt(index, eVar.f897a);
                        break;
                    case 13:
                        eVar.f = typedArray.getInteger(index, eVar.f);
                        break;
                    case 14:
                        eVar.q = typedArray.getFloat(index, eVar.q);
                        break;
                    case 15:
                        eVar.r = typedArray.getDimension(index, eVar.r);
                        break;
                    case MaterialButton.ICON_GRAVITY_TOP /* 16 */:
                        eVar.s = typedArray.getDimension(index, eVar.s);
                        break;
                    case 17:
                        eVar.t = typedArray.getDimension(index, eVar.t);
                        break;
                    case 18:
                        eVar.u = typedArray.getFloat(index, eVar.u);
                        break;
                    case 19:
                        eVar.m = typedArray.getDimension(index, eVar.m);
                        break;
                    case 20:
                        eVar.n = typedArray.getDimension(index, eVar.n);
                        break;
                }
            }
        }
    }

    public e() {
        this.f900d = 1;
        this.e = new HashMap<>();
    }

    public void N(String str, Object obj) {
        switch (str) {
            case "motionProgress":
                this.u = h(obj);
                break;
            case "transitionEasing":
                obj.toString();
                break;
            case "rotationX":
                this.k = h(obj);
                break;
            case "rotationY":
                this.l = h(obj);
                break;
            case "translationX":
                this.r = h(obj);
                break;
            case "translationY":
                this.s = h(obj);
                break;
            case "translationZ":
                this.t = h(obj);
                break;
            case "scaleX":
                this.p = h(obj);
                break;
            case "scaleY":
                this.q = h(obj);
                break;
            case "transformPivotX":
                this.m = h(obj);
                break;
            case "transformPivotY":
                this.n = h(obj);
                break;
            case "rotation":
                this.j = h(obj);
                break;
            case "elevation":
                this.i = h(obj);
                break;
            case "transitionPathRotate":
                this.o = h(obj);
                break;
            case "alpha":
                this.h = h(obj);
                break;
            case "curveFit":
                this.f = i(obj);
                break;
            case "visibility":
                this.g = obj instanceof Boolean ? ((Boolean) obj).booleanValue() : Boolean.parseBoolean(obj.toString());
                break;
        }
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Code restructure failed: missing block: B:121:0x009a, code lost:
    
        if (r1.equals("scaleY") == false) goto L15;
     */
    @Override // androidx.constraintlayout.motion.widget.d
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void a(java.util.HashMap<java.lang.String, a.f.b.a.d> r7) {
        /*
            Method dump skipped, instructions count: 524
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.motion.widget.e.a(java.util.HashMap):void");
    }

    @Override // androidx.constraintlayout.motion.widget.d
    /* renamed from: b */
    public d clone() {
        e eVar = new e();
        super.c(this);
        eVar.f = this.f;
        eVar.g = this.g;
        eVar.h = this.h;
        eVar.i = this.i;
        eVar.j = this.j;
        eVar.k = this.k;
        eVar.l = this.l;
        eVar.m = this.m;
        eVar.n = this.n;
        eVar.o = this.o;
        eVar.p = this.p;
        eVar.q = this.q;
        eVar.r = this.r;
        eVar.s = this.s;
        eVar.t = this.t;
        eVar.u = this.u;
        return eVar;
    }

    @Override // androidx.constraintlayout.motion.widget.d
    public void d(HashSet<String> hashSet) {
        if (!Float.isNaN(this.h)) {
            hashSet.add("alpha");
        }
        if (!Float.isNaN(this.i)) {
            hashSet.add("elevation");
        }
        if (!Float.isNaN(this.j)) {
            hashSet.add("rotation");
        }
        if (!Float.isNaN(this.k)) {
            hashSet.add("rotationX");
        }
        if (!Float.isNaN(this.l)) {
            hashSet.add("rotationY");
        }
        if (!Float.isNaN(this.m)) {
            hashSet.add("transformPivotX");
        }
        if (!Float.isNaN(this.n)) {
            hashSet.add("transformPivotY");
        }
        if (!Float.isNaN(this.r)) {
            hashSet.add("translationX");
        }
        if (!Float.isNaN(this.s)) {
            hashSet.add("translationY");
        }
        if (!Float.isNaN(this.t)) {
            hashSet.add("translationZ");
        }
        if (!Float.isNaN(this.o)) {
            hashSet.add("transitionPathRotate");
        }
        if (!Float.isNaN(this.p)) {
            hashSet.add("scaleX");
        }
        if (!Float.isNaN(this.q)) {
            hashSet.add("scaleY");
        }
        if (!Float.isNaN(this.u)) {
            hashSet.add("progress");
        }
        if (this.e.size() > 0) {
            Iterator<String> it = this.e.keySet().iterator();
            while (it.hasNext()) {
                hashSet.add("CUSTOM," + it.next());
            }
        }
    }

    @Override // androidx.constraintlayout.motion.widget.d
    public void e(Context context, AttributeSet attributeSet) {
        a.a(this, context.obtainStyledAttributes(attributeSet, androidx.constraintlayout.widget.f.j));
    }

    @Override // androidx.constraintlayout.motion.widget.d
    public void g(HashMap<String, Integer> hashMap) {
        if (this.f == -1) {
            return;
        }
        if (!Float.isNaN(this.h)) {
            hashMap.put("alpha", Integer.valueOf(this.f));
        }
        if (!Float.isNaN(this.i)) {
            hashMap.put("elevation", Integer.valueOf(this.f));
        }
        if (!Float.isNaN(this.j)) {
            hashMap.put("rotation", Integer.valueOf(this.f));
        }
        if (!Float.isNaN(this.k)) {
            hashMap.put("rotationX", Integer.valueOf(this.f));
        }
        if (!Float.isNaN(this.l)) {
            hashMap.put("rotationY", Integer.valueOf(this.f));
        }
        if (!Float.isNaN(this.m)) {
            hashMap.put("transformPivotX", Integer.valueOf(this.f));
        }
        if (!Float.isNaN(this.n)) {
            hashMap.put("transformPivotY", Integer.valueOf(this.f));
        }
        if (!Float.isNaN(this.r)) {
            hashMap.put("translationX", Integer.valueOf(this.f));
        }
        if (!Float.isNaN(this.s)) {
            hashMap.put("translationY", Integer.valueOf(this.f));
        }
        if (!Float.isNaN(this.t)) {
            hashMap.put("translationZ", Integer.valueOf(this.f));
        }
        if (!Float.isNaN(this.o)) {
            hashMap.put("transitionPathRotate", Integer.valueOf(this.f));
        }
        if (!Float.isNaN(this.p)) {
            hashMap.put("scaleX", Integer.valueOf(this.f));
        }
        if (!Float.isNaN(this.q)) {
            hashMap.put("scaleY", Integer.valueOf(this.f));
        }
        if (!Float.isNaN(this.u)) {
            hashMap.put("progress", Integer.valueOf(this.f));
        }
        if (this.e.size() > 0) {
            Iterator<String> it = this.e.keySet().iterator();
            while (it.hasNext()) {
                hashMap.put(b.b.a.a.a.g("CUSTOM,", it.next()), Integer.valueOf(this.f));
            }
        }
    }
}
