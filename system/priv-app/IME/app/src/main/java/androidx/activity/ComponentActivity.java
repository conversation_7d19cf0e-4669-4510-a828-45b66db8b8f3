package androidx.activity;

import android.os.Bundle;
import android.view.View;
import android.view.Window;
import androidx.lifecycle.d;
import androidx.lifecycle.e;
import androidx.lifecycle.g;
import androidx.lifecycle.h;
import androidx.lifecycle.o;
import androidx.lifecycle.r;
import androidx.lifecycle.s;
import androidx.savedstate.SavedStateRegistry;

/* loaded from: classes.dex */
public class ComponentActivity extends androidx.core.app.c implements g, s, androidx.savedstate.b, c {

    /* renamed from: b, reason: collision with root package name */
    private final h f527b;

    /* renamed from: c, reason: collision with root package name */
    private final androidx.savedstate.a f528c;

    /* renamed from: d, reason: collision with root package name */
    private r f529d;
    private final OnBackPressedDispatcher e;

    class a implements Runnable {
        a() {
        }

        @Override // java.lang.Runnable
        public void run() {
            ComponentActivity.super.onBackPressed();
        }
    }

    static final class b {

        /* renamed from: a, reason: collision with root package name */
        r f533a;

        b() {
        }
    }

    public ComponentActivity() {
        h hVar = new h(this);
        this.f527b = hVar;
        this.f528c = androidx.savedstate.a.a(this);
        this.e = new OnBackPressedDispatcher(new a());
        hVar.a(new e() { // from class: androidx.activity.ComponentActivity.2
            @Override // androidx.lifecycle.e
            public void d(g gVar, d.a aVar) {
                if (aVar == d.a.ON_STOP) {
                    Window window = ComponentActivity.this.getWindow();
                    View peekDecorView = window != null ? window.peekDecorView() : null;
                    if (peekDecorView != null) {
                        peekDecorView.cancelPendingInputEvents();
                    }
                }
            }
        });
        hVar.a(new e() { // from class: androidx.activity.ComponentActivity.3
            @Override // androidx.lifecycle.e
            public void d(g gVar, d.a aVar) {
                if (aVar != d.a.ON_DESTROY || ComponentActivity.this.isChangingConfigurations()) {
                    return;
                }
                ComponentActivity.this.g().a();
            }
        });
    }

    @Override // androidx.lifecycle.g
    public d a() {
        return this.f527b;
    }

    @Override // androidx.activity.c
    public final OnBackPressedDispatcher b() {
        return this.e;
    }

    @Override // androidx.savedstate.b
    public final SavedStateRegistry c() {
        return this.f528c.b();
    }

    @Override // androidx.lifecycle.s
    public r g() {
        if (getApplication() == null) {
            throw new IllegalStateException("Your activity is not yet attached to the Application instance. You can't request ViewModel before onCreate call.");
        }
        if (this.f529d == null) {
            b bVar = (b) getLastNonConfigurationInstance();
            if (bVar != null) {
                this.f529d = bVar.f533a;
            }
            if (this.f529d == null) {
                this.f529d = new r();
            }
        }
        return this.f529d;
    }

    @Override // android.app.Activity
    public void onBackPressed() {
        this.e.b();
    }

    @Override // androidx.core.app.c, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        this.f528c.c(bundle);
        o.b(this);
    }

    @Override // android.app.Activity
    public final Object onRetainNonConfigurationInstance() {
        b bVar;
        r rVar = this.f529d;
        if (rVar == null && (bVar = (b) getLastNonConfigurationInstance()) != null) {
            rVar = bVar.f533a;
        }
        if (rVar == null) {
            return null;
        }
        b bVar2 = new b();
        bVar2.f533a = rVar;
        return bVar2;
    }

    @Override // androidx.core.app.c, android.app.Activity
    protected void onSaveInstanceState(Bundle bundle) {
        h hVar = this.f527b;
        if (hVar instanceof h) {
            hVar.k(d.b.CREATED);
        }
        super.onSaveInstanceState(bundle);
        this.f528c.d(bundle);
    }
}
