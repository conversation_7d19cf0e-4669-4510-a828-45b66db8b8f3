package androidx.constraintlayout.motion.widget;

import android.view.animation.Interpolator;

/* loaded from: classes.dex */
class s implements Interpolator {

    /* renamed from: a, reason: collision with root package name */
    final /* synthetic */ a.f.a.i.a.c f937a;

    s(t tVar, a.f.a.i.a.c cVar) {
        this.f937a = cVar;
    }

    @Override // android.animation.TimeInterpolator
    public float getInterpolation(float f) {
        return (float) this.f937a.a(f);
    }
}
