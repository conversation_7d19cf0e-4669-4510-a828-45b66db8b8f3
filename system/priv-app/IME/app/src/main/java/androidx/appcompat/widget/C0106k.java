package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.widget.SeekBar;

/* renamed from: androidx.appcompat.widget.k, reason: case insensitive filesystem */
/* loaded from: classes.dex */
class C0106k extends C0105j {

    /* renamed from: d, reason: collision with root package name */
    private final SeekBar f812d;
    private Drawable e;
    private ColorStateList f;
    private PorterDuff.Mode g;
    private boolean h;
    private boolean i;

    C0106k(SeekBar seekBar) {
        super(seekBar);
        this.f = null;
        this.g = null;
        this.h = false;
        this.i = false;
        this.f812d = seekBar;
    }

    private void d() {
        Drawable drawable = this.e;
        if (drawable != null) {
            if (this.h || this.i) {
                Drawable mutate = drawable.mutate();
                this.e = mutate;
                if (this.h) {
                    mutate.setTintList(this.f);
                }
                if (this.i) {
                    this.e.setTintMode(this.g);
                }
                if (this.e.isStateful()) {
                    this.e.setState(this.f812d.getDrawableState());
                }
            }
        }
    }

    @Override // androidx.appcompat.widget.C0105j
    void b(AttributeSet attributeSet, int i) {
        super.b(attributeSet, i);
        Context context = this.f812d.getContext();
        int[] iArr = a.b.b.h;
        G v = G.v(context, attributeSet, iArr, i, 0);
        SeekBar seekBar = this.f812d;
        Context context2 = seekBar.getContext();
        TypedArray r = v.r();
        int i2 = a.h.h.q.e;
        seekBar.saveAttributeDataForStyleable(context2, iArr, attributeSet, r, i, 0);
        Drawable h = v.h(0);
        if (h != null) {
            this.f812d.setThumb(h);
        }
        Drawable g = v.g(1);
        Drawable drawable = this.e;
        if (drawable != null) {
            drawable.setCallback(null);
        }
        this.e = g;
        if (g != null) {
            g.setCallback(this.f812d);
            g.setLayoutDirection(this.f812d.getLayoutDirection());
            if (g.isStateful()) {
                g.setState(this.f812d.getDrawableState());
            }
            d();
        }
        this.f812d.invalidate();
        if (v.s(3)) {
            this.g = q.c(v.k(3, -1), this.g);
            this.i = true;
        }
        if (v.s(2)) {
            this.f = v.c(2);
            this.h = true;
        }
        v.w();
        d();
    }

    void e(Canvas canvas) {
        if (this.e != null) {
            int max = this.f812d.getMax();
            if (max > 1) {
                int intrinsicWidth = this.e.getIntrinsicWidth();
                int intrinsicHeight = this.e.getIntrinsicHeight();
                int i = intrinsicWidth >= 0 ? intrinsicWidth / 2 : 1;
                int i2 = intrinsicHeight >= 0 ? intrinsicHeight / 2 : 1;
                this.e.setBounds(-i, -i2, i, i2);
                float width = ((this.f812d.getWidth() - this.f812d.getPaddingLeft()) - this.f812d.getPaddingRight()) / max;
                int save = canvas.save();
                canvas.translate(this.f812d.getPaddingLeft(), this.f812d.getHeight() / 2);
                for (int i3 = 0; i3 <= max; i3++) {
                    this.e.draw(canvas);
                    canvas.translate(width, 0.0f);
                }
                canvas.restoreToCount(save);
            }
        }
    }

    void f() {
        Drawable drawable = this.e;
        if (drawable != null && drawable.isStateful() && drawable.setState(this.f812d.getDrawableState())) {
            this.f812d.invalidateDrawable(drawable);
        }
    }

    void g() {
        Drawable drawable = this.e;
        if (drawable != null) {
            drawable.jumpToCurrentState();
        }
    }
}
