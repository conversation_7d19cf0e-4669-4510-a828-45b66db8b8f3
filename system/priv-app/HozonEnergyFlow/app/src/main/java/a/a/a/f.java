package a.a.a;

import a.a.a.c;
import android.content.Context;
import android.util.Log;
import java.io.File;
import java.io.IOException;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Set;

/* loaded from: classes.dex */
public class f {

    /* renamed from: a, reason: collision with root package name */
    protected final Set<String> f11a;
    protected final c.b b;
    protected final c.a c;
    protected boolean d;
    protected boolean e;
    protected c.d f;

    protected f() {
        this(new g(), new a());
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void c(Context context, String str, String str2) {
        if (this.f11a.contains(str) && !this.d) {
            a("%s already loaded previously!", str);
            return;
        }
        try {
            this.b.b(str);
            this.f11a.add(str);
            a("%s (%s) was loaded normally!", str, str2);
        } catch (UnsatisfiedLinkError e) {
            a("Loading the library normally failed: %s", Log.getStackTraceString(e));
            a("%s (%s) was not loaded normally, re-linking...", str, str2);
            File b = b(context, str, str2);
            if (!b.exists() || this.d) {
                if (this.d) {
                    a("Forcing a re-link of %s (%s)...", str, str2);
                }
                a(context, str, str2);
                this.c.a(context, this.b.a(), this.b.a(str), b, this);
            }
            try {
                if (this.e) {
                    a.a.a.a.f fVar = null;
                    try {
                        a.a.a.a.f fVar2 = new a.a.a.a.f(b);
                        try {
                            List<String> b2 = fVar2.b();
                            fVar2.close();
                            Iterator<String> it = b2.iterator();
                            while (it.hasNext()) {
                                a(context, this.b.c(it.next()));
                            }
                        } catch (Throwable th) {
                            th = th;
                            fVar = fVar2;
                            fVar.close();
                            throw th;
                        }
                    } catch (Throwable th2) {
                        th = th2;
                    }
                }
            } catch (IOException unused) {
            }
            this.b.d(b.getAbsolutePath());
            this.f11a.add(str);
            a("%s (%s) was re-linked!", str, str2);
        }
    }

    protected File b(Context context, String str, String str2) {
        String a2 = this.b.a(str);
        return h.a(str2) ? new File(a(context), a2) : new File(a(context), a2 + "." + str2);
    }

    protected f(c.b bVar, c.a aVar) {
        this.f11a = new HashSet();
        if (bVar == null) {
            throw new IllegalArgumentException("Cannot pass null library loader");
        }
        if (aVar == null) {
            throw new IllegalArgumentException("Cannot pass null library installer");
        }
        this.b = bVar;
        this.c = aVar;
    }

    public void a(Context context, String str) {
        a(context, str, (String) null, (c.InterfaceC0002c) null);
    }

    public void a(Context context, String str, String str2, c.InterfaceC0002c interfaceC0002c) {
        if (context != null) {
            if (!h.a(str)) {
                a("Beginning load of %s...", str);
                if (interfaceC0002c == null) {
                    c(context, str, str2);
                    return;
                }
                try {
                    new Thread(new d(this, context, str, str2, interfaceC0002c)).start();
                    return;
                } catch (Error | Exception e) {
                    interfaceC0002c.a(e);
                    return;
                }
            }
            throw new IllegalArgumentException("Given library is either null or empty");
        }
        throw new IllegalArgumentException("Given context is null");
    }

    protected File a(Context context) {
        return context.getDir("lib", 0);
    }

    protected void a(Context context, String str, String str2) {
        File a2 = a(context);
        File b = b(context, str, str2);
        File[] listFiles = a2.listFiles(new e(this, this.b.a(str)));
        if (listFiles == null) {
            return;
        }
        for (File file : listFiles) {
            if (this.d || !file.getAbsolutePath().equals(b.getAbsolutePath())) {
                try {
                    file.delete();
                } catch (SecurityException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public void a(String str, Object... objArr) {
        a(String.format(Locale.US, str, objArr));
    }

    public void a(String str) {
        c.d dVar = this.f;
        if (dVar != null) {
            dVar.a(str);
        }
    }
}
